// import Api from "@/utils/Api"
// import ApiGeo from "@/utils/ApiGeo";

// // export const loginService = async (body) => {
// //     const response = await Api.post('/login', body)
// //     return response
// // }

// export const loginService = async (body) => {
//     try {
//         // Request pertama ke endpoint pertama (BASEURL default)
//         const response1 = await Api.post('/admin/auth/login', body);


//         // Request kedua ke endpoint kedua (dengan baseURL dari environment variable)
//         const response2 = await ApiGeo.post('/iam/login', {
//             username: body.login_name, // Sesuaikan field username jika perlu
//             password: body.password,
//         });

//         // Gabungkan kedua respon
//         const combinedResponse = {
//             ...response1, // Data dari endpoint pertama
//             ...response2, // Data dari endpoint kedua
//         };

//         // Return data gabungan
//         console.log("response geo",response2);
//         console.log("combineRespon",combinedResponse);

//         return combinedResponse;
//     } catch (error) {
//         console.error("Login service error:", error);
//         throw error;
//     }
// };

// export const logoutService = async (body) => {
//     const response = await Api.delete('/logout', body)
//     return response
// }

// export const getMeService = async (token) => {
//     const response = await Api.get('/user/me', {
//         headers: {
//             'Authorization': `Bearer ${token}` // Send token in the header
//         }
//     });
//     return response
// }

// export const forgotPasswordService = async (body) => {
//     const response = await Api.post(`/forgot_password`, body)
//     return response
// }

// export const resetPasswordService = async (body) => {
//     const response = await Api.post(`/reset_password`, body)
//     return response
// }

// AuthServices.js
import Api from "@/utils/Api";

export const loginService = async (body) => {
    try {
        const response = await Api.post('/login', body);
        console.log(response);
        return response;
    } catch (error) {
        console.error("Login service error:", error);
        throw error;
    }
};

export const logoutService = async (body) => {
    const response = await Api.delete('/logout', body)
    return response
}

export const getMeService = async (token) => {
    const response = await Api.get('/user/me', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });
    return response
}

export const forgotPasswordService = async (body) => {
    const response = await Api.post(`/forgot_password`, body)
    return response
}

export const resetPasswordService = async (body) => {
    const response = await Api.post(`/reset_password`, body)
    return response
}
