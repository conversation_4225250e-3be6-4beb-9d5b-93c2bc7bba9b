import { NextResponse } from 'next/server';
import { waf<PERSON><PERSON><PERSON>, is<PERSON><PERSON><PERSON><PERSON><PERSON> } from './lib/waf';

// Cookie security configuration
// const COOKIE_CONFIG = {
//   httpOnly: true,          // Prevent JavaScript access
//   secure: true,           // Only send over HTTPS
//   sameSite: 'strict',     // Strict same-site policy
//   path: '/',              // Cookie accessible on all paths
//   maxAge: 7200,           // 2 hours expiry
// };

// // Function to set secure cookie
// const setSecureCookie = (response, name, value, options = {}) => {
//   response.cookies.set(name, value, {
//     ...COOKIE_CONFIG,
//     ...options,
//     // Ensure these security attributes can't be overridden
//     secure: true,
//     sameSite: 'strict',
//     httpOnly: true
//   });
// };

// // Function to clear cookie securely
// const clearCookie = (response, name) => {
//   response.cookies.set(name, '', {
//     ...COOKIE_CONFIG,
//     expires: new Date(0),
//     maxAge: 0
//   });
// };

// // Function to validate cookie names
// const isValidCookieName = (name) => {
//   return /^[a-zA-Z0-9_-]+$/.test(name);
// };

// // Function to validate cookie values
// const isValidCookieValue = (value) => {
//   // Prevent potentially dangerous characters
//   return typeof value === 'string' && 
//          !/[<>"'%;)(&+]/.test(value) && 
//          value.length < 4096; // Reasonable size limit
// };

// Header normalization and deduplication
function normalizeHeaders(request) {
  const dangerousHeaders = [
    'proxy-connection', 'keep-alive', 'x-http-method-override',
    'transfer-encoding', 'te', 'connection',
    'content-length', 'upgrade', 'via'
  ];
  const newHeaders = new Headers();
  for (const [key, value] of request.headers.entries()) {
    const lowerKey = key.toLowerCase();
    if (!dangerousHeaders.includes(lowerKey)) {
      if (!newHeaders.has(lowerKey)) {
        newHeaders.set(lowerKey, value);
      }
      // If duplicate, drop it (deduplication)
    }
    // Optionally, log or count dangerous headers
  }
  return newHeaders;
}

// Block legacy HTTP/1.0 requests and suspicious method/path
function isLegacyOrSuspicious(request) {
  const method = request.method;
  const url = request.url;
  const allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'];
  if (!allowedMethods.includes(method)) return true;
  try {
    const { pathname } = new URL(url);
    // Allow root path and empty path for standard methods
    if (pathname === '/' || pathname === '') return false;
    // Block encoded newlines, non-printable ASCII
    if (/%0d|%0a|[\r\n]/i.test(pathname)) return true;
    if (/[^\x20-\x7E]/.test(pathname)) return true;
  } catch (e) {
    return true;
  }
  return false;
}

// Helper function to validate Content-Length
const validateContentLength = (contentLength) => {
  if (!contentLength) return true;
  const length = parseInt(contentLength);
  return !isNaN(length) && length >= 0 && length < 1000000; // Max 1MB
};

// Enhanced function to check for pipelined requests and smuggling attempts
const checkForRequestSmuggling = (request) => {
  const headers = request.headers;
  const contentLength = headers.get('content-length');
  const transferEncoding = headers.get('transfer-encoding');
  const connection = headers.get('connection');
  const contentType = headers.get('content-type');
  const te = headers.get('te');
  
  // Check for conflicting headers (CL.TE vulnerability)
  if (contentLength && transferEncoding) {
    return true; // Potential smuggling attempt
  }
  
  // Check for suspicious Transfer-Encoding values or variations
  if (transferEncoding) {
    const teValue = transferEncoding.toLowerCase();
    // Check for malformed chunked encoding or obfuscation attempts
    if (!teValue.includes('chunked') || 
        teValue.includes('identity') || 
        teValue.includes(',') || 
        teValue.includes(';') || 
        teValue.includes('\n') || 
        teValue.includes('\r')) {
      return true;
    }
  }
  
  // Check for TE header which can be used in request smuggling
  if (te && (te.includes('chunked') || te.includes('trailers'))) {
    return true;
  }
  
  // Strict check for Connection header variations
  if (connection) {
    const connValue = connection.toLowerCase();
    if (connValue.includes('upgrade') || 
        connValue.includes('keep-alive') || 
        connValue.includes('transfer-encoding') || 
        connValue.includes('content-length')) {
      return true;
    }
  }
  
  // Check for multiple Content-Length headers or suspicious values
  if (contentLength) {
    if (contentLength.includes(',') || 
        contentLength.includes(';') || 
        contentLength.includes(' ') || 
        contentLength.includes('\n') || 
        contentLength.includes('\r') || 
        isNaN(parseInt(contentLength))) {
      return true;
    }
  }
  
  // Enhanced protection for /be path and all its subpaths against client-side desync
  if (securedRequest.nextUrl.pathname.startsWith('/be/')) {
    // Stricter validation for /be path
    if (contentType && contentType.includes(';')) {
      // Suspicious content-type with parameters
      return true;
    }
    
    // Check for URL-based desync attacks in /be path
    const url = request.url;
    if (url.includes('%0d') || url.includes('%0a') || url.includes('\r') || url.includes('\n')) {
      return true; // URL contains CR/LF which could be used for header injection
    }
    
    // Check for path traversal attempts in complex paths
    const pathname = request.nextUrl.pathname;
    if (pathname.includes('..') || pathname.includes('%2e%2e') || pathname.includes('./') || pathname.includes('/.')) {
      return true; // Path traversal attempt
    }
    
    // Check for suspicious URL encoding that might be used to bypass filters
    if (pathname.includes('%00') || pathname.includes('%0d') || pathname.includes('%0a')) {
      return true; // Null byte or CRLF injection attempt
    }
    
    // Deep path protection for nested subpaths (CSD protection)
    // Split path into segments and check each segment individually
    const pathSegments = pathname.split('/');
    for (const segment of pathSegments) {
      // Check for suspicious characters in individual path segments
      if (segment.includes(':') || segment.includes('@') || 
          segment.includes('&') || segment.includes('=') || 
          segment.includes('+') || segment.includes('\\')) {
        return true; // Suspicious characters in path segment
      }
      
      // Check for overlong UTF-8 encodings that could be used to bypass filters
      if (/(%[cC]0%[aA]|%[cC]1%[89]|%[eE]0)/i.test(segment)) {
        return true; // Overlong UTF-8 encoding detected
      }
    }
  }
  
  return false;
};

// Function to check for malicious request patterns
const checkForMaliciousPatterns = async (request) => {
  try {
    // Only read body for POST/PUT/PATCH requests to avoid breaking GET requests
    if (!['POST', 'PUT', 'PATCH', 'DELETE'].includes(request.method)) {
      return false;
    }
    
    const body = await request.text();
    
    // Check for HTTP request smuggling patterns
    const smugglingPatterns = [
      /HTTP\/1\.[01]/i,
      /GET\s+\/.*HTTP\/1\.[01]/i,
      /POST\s+\/.*HTTP\/1\.[01]/i,
      /PUT\s+\/.*HTTP\/1\.[01]/i,
      /DELETE\s+\/.*HTTP\/1\.[01]/i,
      /\r\n\r\n.*HTTP\//i,
      /Transfer-Encoding:\s*chunked.*Content-Length:/i,
      /Content-Length:.*Transfer-Encoding:\s*chunked/i,
      // Enhanced patterns to detect the specific attack pattern shown in the example
      /GET\s+\/[^\s]*\?[^\s]*=[^\s]*/i, // Detect GET requests with query parameters in body
      /Host:\s*[^\r\n]+/i, // Detect Host header in request body
      /Accept[\-\w]*:\s*[^\r\n]+/i, // Detect Accept headers in request body
      /User-Agent:\s*[^\r\n]+/i, // Detect User-Agent header in request body
      /Connection:\s*[^\r\n]+/i, // Detect Connection header in request body
      /Cache-Control:\s*[^\r\n]+/i // Detect Cache-Control header in request body
    ];
    
    // For /be paths, apply extra strict checking for smuggled requests
    const { pathname } = new URL(request.url);
    if (pathname.startsWith('/be/')) {
      // Check for a complete second HTTP request in the body
      // This detects the exact pattern in the example attack
      if (/GET\s+\/[^\s]+\s+HTTP\/[\d\.]+[\s\S]*Host:\s*[^\r\n]+/i.test(body)) {
        return true;
      }
    }
    
    return smugglingPatterns.some(pattern => pattern.test(body));
  } catch (error) {
    // If we can't read the body, assume it's safe but log the error
    console.error('Error reading request body:', error);
    return false;
  }
};

// Generate cryptographically secure CSRF token
const generateCsrfToken = () => {
  const buffer = new Uint8Array(48);
  crypto.getRandomValues(buffer);
  return Array.from(buffer)
    .map(byte => byte.toString(16).padStart(2, '0'))
    .join('');
};

// Validate CSRF token format
const isValidCsrfToken = (token) => {
  return typeof token === 'string' && /^[a-zA-Z0-9_-]{32,128}$/.test(token);
};

// Verify CSRF token matches
const verifyCsrfToken = (request) => {
  const cookieToken = request.cookies.get('XSRF-TOKEN')?.value;
  const headerToken = request.headers.get('X-CSRF-Token');
  
  if (!cookieToken || !headerToken) return false;
  if (!isValidCsrfToken(cookieToken) || !isValidCsrfToken(headerToken)) return false;
  
  return cookieToken === headerToken;
};

export async function middleware(request) {
  // Early connection validation for CSD prevention
  const requestHeaders = new Headers(request.headers);
  const { pathname } = new URL(request.url);
  
  // Create base response for cookie handling
  const response = NextResponse.next();

  // Secure cookie attributes for all paths
  response.headers.set('Set-Cookie', []);
  
  // Cookie security headers
  response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
  response.headers.set('Pragma', 'no-cache');
  response.headers.set('Expires', '0');
  
  // // Session cookie handling
  // const sessionToken = request.cookies.get('session')?.value;
  // if (sessionToken) {
  //   // Rotate session cookie with new expiry
  //   setSecureCookie(response, 'session', sessionToken, {
  //     maxAge: 7200 // 2 hours
  //   });
  // }
  
  // Force connection close for sensitive paths
  if (pathname.startsWith('/be/') || pathname.startsWith('/api/') || pathname.startsWith('/admin/')) {
    requestHeaders.set('Connection', 'close');
    // Prevent keep-alive for sensitive paths
    requestHeaders.delete('Keep-Alive');
    // Enforce strict transfer encoding
    requestHeaders.delete('Transfer-Encoding');
    
    // CSRF protection for sensitive paths
    // Use robust isAuthRoute and log decision
    const exemptAuth = isAuthRoute(pathname);
    console.log('[CSRF] Path:', pathname, 'isAuthRoute:', exemptAuth);
    if (request.method !== 'GET' && !exemptAuth) {
      if (!verifyCsrfToken(request)) {
        return new NextResponse(
          JSON.stringify({ error: 'Invalid CSRF token' }),
          { 
            status: 419,
            headers: {
              'Content-Type': 'application/json',
              'Connection': 'close'
            }
          }
        );
      }
    }

    // Strict Content-Type validation for protected paths (CSD mitigation)
    if (["POST", "PUT", "PATCH"].includes(request.method)) {
      const contentType = request.headers.get("content-type") || "";
      if (!contentType.includes("application/json") && !contentType.includes("multipart/form-data")) {
        return new NextResponse(
          JSON.stringify({ error: "Invalid Content-Type" }),
          {
            status: 415,
            headers: {
              "Content-Type": "application/json",
              "Connection": "close"
            }
          }
        );
      }
    }

    // Block dangerous headers for protected paths (CSD/smuggling mitigation)
    // Allow x-method-override ONLY for authentication routes
    const isAuth = isAuthRoute(pathname);
    const blockedHeaders = isAuth
      ? ['te', 'trailer', 'upgrade', 'proxy-connection', 'x-http-method-override', 'via']
      : ['te', 'trailer', 'upgrade', 'proxy-connection', 'x-http-method-override', 'x-method-override', 'via'];
    for (const h of blockedHeaders) {
      if (request.headers.has(h)) {
        return new NextResponse(
          JSON.stringify({ error: `Blocked dangerous header: ${h}` }),
          { status: 400, headers: { 'Content-Type': 'application/json', 'Connection': 'close' } }
        );
      }
    }
    // Block both Content-Length and Transfer-Encoding present
    if (request.headers.has('content-length') && request.headers.has('transfer-encoding')) {
      return new NextResponse(
        JSON.stringify({ error: 'Conflicting Content-Length and Transfer-Encoding headers' }),
        { status: 400, headers: { 'Content-Type': 'application/json', 'Connection': 'close' } }
      );
    }
  }
  // Normalize headers and block dangerous/duplicate headers
  const normalizedHeaders = normalizeHeaders(request);
  // Block legacy/suspicious requests
  if (isLegacyOrSuspicious(request)) {
    console.warn('[SECURITY] Blocked legacy or suspicious HTTP request:', {
      url: request.url,
      method: request.method,
      headers: Array.from(normalizedHeaders.entries())
    });
    return new NextResponse('Blocked: legacy or suspicious HTTP request', { status: 400 });
  }
  // Reconstruct request with normalized headers for downstream checks
  const securedRequest = new Request(request, { headers: normalizedHeaders });

  //Force redirect from root to /user with security headers
  if (pathname === '/' || pathname === '') {
    const response = NextResponse.redirect(new URL('/user', request.url));
    
    // Set security headers for the redirect
    response.headers.set('Connection', 'close');
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    response.headers.set('X-Content-Type-Options', 'nosniff');
    
    // Clear any existing insecure cookies
    // const cookies = request.cookies.getAll();
    // cookies.forEach(cookie => {
    //   if (!isValidCookieName(cookie.name) || !isValidCookieValue(cookie.value)) {
    //     clearCookie(response, cookie.name);
    //   }
    // });
    
    return response;
  }

  // Special handling for /user paths to ensure navigation works properly
  if (pathname.startsWith('/user')) {
    // Allow all /user paths to pass through without WAF checks
    const response = NextResponse.next();
    
    // Apply security headers for user paths
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'SAMEORIGIN');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // Ensure cookies are secure even on user paths
    // const cookies = request.cookies.getAll();
    // cookies.forEach(cookie => {
    //   if (cookie.name !== 'session' && !cookie.secure) {
    //     // Upgrade insecure cookies to secure
    //     setSecureCookie(response, cookie.name, cookie.value);
    //   }
    // });
    
    return response;
  }
  
  // Handle CSRF token generation request
  if (pathname === '/csrf') {
    const csrfToken = generateCsrfToken();
    const response = NextResponse.json({ csrfToken });
    
    // Set CSRF cookie with secure attributes
    response.cookies.set('XSRF-TOKEN', csrfToken, {
      httpOnly: false, // Needs to be accessible by JavaScript
      secure: true,
      sameSite: 'strict',
      path: '/',
      maxAge: 7200 // 2 hours
    });
    
    // Add security headers
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    
    return response;
  }
  
  // Special handling for /admin paths to ensure they work properly
  if (pathname === '/admin' || pathname === '/admin/' || pathname.startsWith('/admin/')) {
    // Allow all /admin paths to pass through without any checks
    const response = NextResponse.next();
    
    // Apply only minimal security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    
    console.log('[MIDDLEWARE] Allowing admin path:', pathname);
    return response;
  }
  
  // Apply WAF protection to /be and /api paths only
  // /admin paths are now handled separately above
  if (pathname.startsWith('/be/') || 
      pathname.startsWith('/api/')) {
    
    // Skip WAF checks for main landing pages to prevent false positives
    // /admin paths are now handled separately above
    const isMainPage = pathname === '/be' || pathname === '/be/';
    
    if (!isMainPage && !isAuthRoute(pathname)) {
      // Apply WAF protection
      const wafResult = await wafHandler(request.clone());
      if (wafResult.blocked) {
        return new NextResponse(JSON.stringify({
          error: wafResult.message,
          status: wafResult.status,
          timestamp: wafResult.timestamp,
          threat_id: wafResult.threat_id
        }), {
          status: wafResult.status,
          headers: {
            'Content-Type': 'application/json',
            'Connection': 'close'
          }
        });
      }
    }
    
    // User navigation pages are handled separately at the beginning of the middleware
    
    // Check for request smuggling attempts as a secondary layer of protection
    if (checkForRequestSmuggling(request)) {
      return new NextResponse(null, {
        status: 400,
        statusText: 'Bad Request: Request Smuggling Detected'
      });
    }
    
    // Get request headers
    const contentLength = request.headers.get('content-length');
    const transferEncoding = request.headers.get('transfer-encoding');
    const host = request.headers.get('host');
    
    // Block suspicious User-Agent patterns
    if (userAgent && (userAgent.length > 1000 || userAgent.includes('\n') || userAgent.includes('\r'))) {
      return new NextResponse(null, {
        status: 400,
        statusText: 'Bad Request: Invalid User-Agent'
      });
    }

    // Create response with strict security headers
    const response = NextResponse.next();

    // Set strict anti-smuggling headers
    response.headers.set('Connection', 'close');
    response.headers.delete('Keep-Alive'); // Remove completely to prevent keep-alive connections
    response.headers.delete('Transfer-Encoding');
    response.headers.delete('TE');
    response.headers.delete('Trailer'); // Remove trailer header which can be used in smuggling
    // response.headers.set('Content-Security-Policy', "default-src 'self'");
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
    
    // Enhanced headers for all /be and /be/** paths to prevent client-side desync and HTTP smuggling
    if (pathname === '/be' || pathname === '/be/' || pathname.startsWith('/be/')) {
      // Strict anti-caching headers
      response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      response.headers.set('Pragma', 'no-cache');
      response.headers.set('Expires', '0');
      response.headers.set('Surrogate-Control', 'no-store');
      // Prevent chunked encoding confusion and enforce identity encoding
      response.headers.delete('Transfer-Encoding');
      response.headers.set('Transfer-Encoding-Policy', 'identity-only');
      // Force content type for all /be responses
      response.headers.set('Content-Type', 'application/json; charset=utf-8');
      // Prevent MIME sniffing and other browser attacks
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-XSS-Protection', '1; mode=block');
      response.headers.set('Content-Security-Policy', "default-src 'self'");
      response.headers.set('X-Download-Options', 'noopen');
      response.headers.set('X-DNS-Prefetch-Control', 'off');
      // Set a safe Content-Length header if possible
      if (!response.headers.has('Content-Length')) {
        response.headers.set('Content-Length', '0'); // fallback for static responses
      }
    }
    
    // Prevent caching of sensitive responses
    if (pathname.startsWith('/be/') || pathname.startsWith('/api/')) {
      // Strict timing validation for /be paths
      const requestTimestamp = Date.now();
      const requestDelay = requestTimestamp - (Number(request.headers.get('x-request-time')) || requestTimestamp);
      
      // Reject suspiciously delayed requests (potential desync attempts)
      if (requestDelay > 10000) { // 10 seconds threshold
        return new NextResponse(JSON.stringify({
          error: 'Invalid request timing',
          status: 400
        }), { status: 400 });
      }

      // Enforce strict content length validation
      const contentLength = request.headers.get('content-length');
      if (contentLength && !/^\d+$/.test(contentLength)) {
        return new NextResponse(JSON.stringify({
          error: 'Invalid content length',
          status: 400
        }), { status: 400 });
      }
      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      response.headers.set('Pragma', 'no-cache');
      response.headers.set('Expires', '0');
    }
    
    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/', '/be/:path*', '/user/:path*', '/admin/:path*', '/api/:path*']
};