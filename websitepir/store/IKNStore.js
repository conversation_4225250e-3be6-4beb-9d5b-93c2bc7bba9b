import {  getDataIKNDetails } from "@/services/getHomeServices";
import { getDataPeluangInvestasi } from "@/services/PagePeluangInvestasi/getPeluangInvestasiService";
import { getCardList } from "@/services/PagePeluangInvestasi/getPeluangInvestasiService";
import { use } from "react";
import { get } from "react-hook-form";
import { create } from "zustand"; 

const useIKNStore = create((set) => ({

    iknDetails: {
        "ikn": {
        "nama": "KOTA DUNIA UNTUK SEMUA update 4",
        "lon": 116.710639,
        "lat": -0.975604,
        "image": process.env.NEXT_PUBLIC_BASE_URL + "/uploads/ikn/202410150551401042.webp",
        "lokasi": "KalimantanTimur",
        "icon": process.env.NEXT_PUBLIC_BASE_URL + "/uploads/icon-web/Lokasi_56_putih.svg"
        },
        "indikator": [
            {
                "nama": "Luas",
                "deskripsi": "<p>45 HA</p>"
            },
            {
                "nama": "<PERSON><PERSON><PERSON>",
                "deskripsi": "<p>4.811.000</p>"
            }
        ], 
        "peluang": [
            {
                "nama": "Infrastruktur  Penyediaan dan Pengelolaan Air",
                "nama_kabkot": "Balikpapan",
                "nama_provinsi": "Kalimantan Timur",
                "nama_sektor": "Infrastruktur",
                "nilai_irr": "10.78%",
                "nilai_investasi": "Rp 481.7 Miliar",
                "nilai_npv": "Rp 106.9 Miliar",
                "image": process.env.NEXT_PUBLIC_BASE_URL + "/uploads/peluang/6471/peluang/2023/1/1.jpg"
            },
            {
                "nama": "Logistik dan Pergudangan Terintegrasi",
                "nama_kabkot": "Balikpapan",
                "nama_provinsi": "Kalimantan Timur",
                "nama_sektor": "Infrastruktur",
                "nilai_irr": "20.74%",
                "nilai_investasi": "Rp 666.0 Miliar",
                "nilai_npv": "Rp 553.1 Miliar",
                "image": process.env.NEXT_PUBLIC_BASE_URL + "/uploads/peluang/6471/peluang/2023/1/Logistik 1.jpg"
            }
        ],
        "peraturan": [
            {
                "nama": "Undang-Undang Republik Indonesia Nomor 3 Tahun 2022 Tentang Ibu Kota Negara",
                "file": process.env.NEXT_PUBLIC_BASE_URL + "/uploads/ikn/34ad7958-4b2e-44e6-83af-96057023811c.pdf"
            },
            {
                "nama": "Peraturan Pemerintah Republik Indonesia Nomor 17 Tahun 2022 Tentang Pendanaan dan Pengelolaan Anggaran Dalam Rangka Persiapan, Pembangunan, dan Pemindahan Ibu Kota Negara Serta Penyelenggaraan Pemerintahan Daerah Khusus Ibu Kota Nusantara",
                "file": process.env.NEXT_PUBLIC_BASE_URL + "/uploads/ikn/401e796a-76ee-48b8-a1bd-b508bc6c4521.pdf"
            },
            {
                "nama": "Peraturan Presiden Republik Indonesia Nomor 63 Tahun 2022 Tentang Perincian Rencana Induk Ibu Kota Nusantara",
                "file": process.env.NEXT_PUBLIC_BASE_URL + "/uploads/ikn/36825b0e-d94d-4d5c-ac18-31c93a4f6348.pdf"
            }]
        ,"profil": [
            {
                "nama": "Struktur Organisasi",
                "deskripsi": "<p><span style=\"font-size: 12px;\">*Berdasarkan Peraturan Kepala Otorita Ibu Kota Nusantara (IKN) Nomor 1 Tahun 2022 tentang Organisasi dan Tata Kerja Otorita IKN, susunan organisasi Otorita IKN terdiri dari Kepala dan Wakil Kepala Otorita IKN, serta sembilan Jabatan Tinggi Madya, yakni 1 Sekretaris, 7 Deputi, serta 1 Kepala Unit Kerja Hukum dan Kepatuhan.</span><br></p><p><br></p>"
            }],
        "galeri": [
            {
                "image": process.env.NEXT_PUBLIC_BASE_URL + "/uploads/ikn/4cc155cb-605f-4f06-bc41-15deac38342e.jpg"
            },
            {
                "image": process.env.NEXT_PUBLIC_BASE_URL + "/uploads/ikn/33bb0e85-62b9-417e-a4ab-91b598df8da2.jpg"
            }]
    },
    // setIKNDetails: getDataIKNDetails(language).then((data) => set({ iknDetails: data })),
    setIKNDetails: async (language) => {
        try {
            const data = await getDataIKNDetails(language);
            set({ iknDetails: data });
        } catch (error) {
            console.error('Error fetching IKN:', error);
        }
    },

}));

export default useIKNStore;