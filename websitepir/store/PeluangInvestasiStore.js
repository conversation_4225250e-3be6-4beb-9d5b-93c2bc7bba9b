import useLanguageStore from "@/app/user/stores/languageStore";
import {
  getCardListDetail,
  getDataPeluangInvestasi,
  getDataPeluangInvestasiIPRO,
} from "@/services/PagePeluangInvestasi/getPeluangInvestasiService";
import { getCardList } from "@/services/PagePeluangInvestasi/getPeluangInvestasiService";
import { getPeluangInvestasiList } from "@/services/getHomeServices";
import { get } from "react-hook-form";
import { create } from "zustand";

const PeluangInvestasiStore = create((set) => ({
  peluangInvestasiList: Array(19)
    .fill()
    .map(() => ({ nama: "PELUANG INVESTASI" })),
  // setPeluangInvestasiList: getPeluangInvestasiList().then((data) =>
  //   set({ peluangInvestasiList: data })
  // ),
  setPeluangInvestasiList: async () => {
    const language = useLanguageStore.getState().language;
    const data = await getPeluangInvestasiList(language);
    set({ peluangInvestasiList: data });
  },

  selectedPeluangInvestasiId: 1,
  setSelectedPeluangInvestasiId: (newId) =>
    set({ selectedPeluangInvestasiId: newId }),

  selectedPeluangInvestasiName: "Primer",
  setSelectedPeluangInvestasiName: (newName) =>
    set({ selectedPeluangInvestasiName: newName }),

  selectedPeluangInvestasiLogo:
    `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/icon-web/Kawasan_Industri_56_putih.svg`,
  setSelectedPeluangInvestasiLogo: (newLogo) =>
    set({ selectedPeluangInvestasiLogo: newLogo }),
  // getPeluangInvestasiDetails()
  // setSelectedPeluangInvestasi: (newId) => set((state) => ({
  //     selectedPeluangInvestasiDetails: getPeluangInvestasiDetails(newId),
  // })),
  peluangInvestasiCard: {
    data: {
      "Card 1": {
        nama: "Industri Terintegrasi Kawasan",
        proyek: 9,
        jumlah_proyek: "9 Proyek",
        icon: `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/icon-web/Kawasan_Industri_Dan_REaal_Estate_56_putih.svg`,
      },
      "Card 2": {
        nama: "Industri Terintegrasi Kawasan",
        proyek: 9,
        jumlah_proyek: "9 Proyek",
        icon: `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/icon-web/Kawasan_Industri_Dan_REaal_Estate_56_putih.svg`,
      },
      "Card 3": {
        nama: "Industri Terintegrasi Kawasan",
        proyek: 9,
        jumlah_proyek: "9 Proyek",
        icon: `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/icon-web/Kawasan_Industri_Dan_REaal_Estate_56_putih.svg`,
      },
    },
  },
  // setPeluangInvestasiCard: getCardList().then((data) =>
  //   set({ peluangInvestasiCard: data })
  // ),
  setPeluangInvestasiCard: async () => {
    const language = useLanguageStore.getState().language;
    const data = await getCardList(language);
    set({ peluangInvestasiCard: data });
  },
  selectedPeluangInvestasiCardName: null,
  setSelectedPeluangInvestasiCardName: (name) =>
    set({ selectedPeluangInvestasiCardName: name }),

  selectedPeluangInvestasiCardDetail: {
    detail: [
      {
        nama: "Infrastruktur",
        nilai: 50167906794149,
      },
      {
        nama: "Industri",
        nilai: 167926449006175,
      },
      {
        nama: "Agro Industri",
        nilai: 9484056155954,
      },
      {
        nama: "Pariwisata",
        nilai: 7740315666465,
      },
      {
        nama: "Kawasan Industri dan Real Estate",
        nilai: 3312990850749,
      },
      {
        nama: "Energi Terbarukan",
        nilai: 868231653594,
      },
    ],
  },
  setSelectedPeluangInvestasiCardDetail: (detail) =>
    set({ selectedPeluangInvestasiCardDetail: { detail } }),

  setDataPeluangInvestasi: getDataPeluangInvestasi().then((data) =>
    set({ dataPeluangInvestasi: data })
  ),
}));

export default PeluangInvestasiStore;
