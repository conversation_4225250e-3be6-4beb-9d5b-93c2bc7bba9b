import useLanguageStore from "@/app/user/stores/languageStore";
import { getDataPeluangInvestasi } from "@/services/PagePeluangInvestasi/getPeluangInvestasiService";
import { getCardList } from "@/services/PagePeluangInvestasi/getPeluangInvestasiService";
import {
  getSektorUnggulanNasionalRef,
  getSektorUnggulanNasionalDetail,
} from "@/services/getHomeServices";
import { get } from "react-hook-form";
import { create } from "zustand";

const useSektorUnggulanStore = create((set) => ({
  sectors: Array(19)
    .fill()
    .map(() => ({ nama: "Sektor Unggulan" })),
  // setSectors: getSektorUnggulanNasionalRef().then((data) =>
  //   set({ sectors: data })
  // ),
  // setSectors: async () => {
  //   const language = useLanguageStore.getState().language;
  //   const data = await getSektorUnggulanNasionalRef(language);
  //   set({ sectors: data });
  // },
  setSectors: async (language) => {
    const data = await getSektorUnggulanNasionalRef(language);
    set({ sectors: data });
  },
  isLoading: false,
  error: null,
  selectedDiagramIndex: 0,

  selectedSectorId: 1,
  setSelectedDiagramIndex: (newIndex) => set({ selectedDiagramIndex: newIndex }),
  setSelectedSectorId: (newId) =>
    set({
      selectedSectorId: newId,
    }),

  sectorDetails: {
    detail: {
      nama_sektor: "Pertanian",
      file_icon: "/icons/Pertanian 56 putih.svg",
      image: "/peluang_investasi/udang.png",
      deskripsi:
        "Pada tahun 2022, Indonesia telah mendapatkan penghargaan dalam sektor pertanian dari IRRI bertajuk “Penghargaan Sistem Pertanian-Pangan Tangguh dan Swasembada Beras Tahun 2019-2021 melalui Penggunaan Teknologi Inovasi Padi”. Ke depan, Indonesia membuka sektor swasta nasional maupun internasional untuk bersama-sama merealisasikan tekad menjadikan pertanian sebagai sektor yang maju, mandiri, dan modern. Total produksi hasil pertanian ditargetkan terus meningkat dalam rangka memenuhi kebutuhan konsumsi maupun untuk menyuplai kebutuhan industri.",
    },
    nilai: [
      {
        id_sektor: 1,
        nama_sektor: "PERTANIAN",
        nama_detail: "FDI",
        tahun: 2022,
        numeric_value: "Rp 1.8 Miliar",
        satuan: "USD",
      },
      {
        id_sektor: 1,
        nama_sektor: "PERTANIAN",
        nama_detail: "Kontribusi Ekspor terhadap PDB",
        tahun: 2021,
        numeric_value: 7.15,
        satuan: "%",
      },
    ],
    potensi: {
      judul: "Potensi Pasar Nasional Sektor Pertanian",
      image:
        `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/sektor/2081e253-bbbe-47f1-851e-2a4a228feb1c.jpg`,
    },
    insentif: [
      {
        judul: "Peraturan Pemerintah Nomor 30 Tahun 2021",
        nama: "Insentif Perlindungan Lahan Pertanian Pangan Berkelanjutan ",
        file: `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/insentif/4a5aeaf3-8792-4884-8a13-40e66dfebed3.pdf`,
      },
      {
        judul: "Peraturan Presiden Republik Indonesia Nomor 55 Tahun 2019",
        nama: "Insentif Penurunan Pungutan Ekspor CPO ",
        file: `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/insentif/b57301b3-c86d-4299-8ce5-8558553e58c1.pdf`,
      },
    ],
    diagram: [
      {
        nama_sektor: "PADI",
        image:
          `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/sektor/c9099ffd-0ae7-410e-9970-1f77193f1db8.jpg`,
      },
      {
        nama_sektor: "JAGUNG",
        image:
          `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/sektor/5fae103f-f136-439b-9869-0afa330e6859.jpg`,
      },
    ],
    sub_sektor: [
      {
        deskripsi:
          '<span id="docs-internal-guid-1a757bea-7fff-2837-4e15-8f61f4ff80d8"><span style="font-variant-numeric: normal; font-variant-east-asian: normal; vertical-align: baseline;">Kebutuhan pangan dalam negeri akan terus meningkat seiring dengan bertambahnya jumlah penduduk secara signifikan. Indonesia bertekad meningkatkan produksi hasil tanaman pangan untuk memenuhi kebutuhan pangan nasional maupun untuk memenuhi kebutuhan industri. Industri pakan dan tepung membutuhkan jagung dan padi dalam jumlah yang besar sebagai bahan baku. Kedelai yang produktivitasnya masih rendah perlu perluasan lahan untuk memenuhi kebutuhan dalam negeri. </span></span>',
        nama_sektor: "TANAMAN PANGAN",
        file_icon:
          `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/sektor/9f5012bf-9873-4ff3-9039-0f9695fde433.png`,
        file_image: `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/sektor/no_image.png`,
        image: [
          `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/sektor/50d15dce-0ee4-4db4-a673-7823de9d6723.jpeg`,
          `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/sektor/b0924998-ccec-41cd-a594-bc673b2a656a.jpeg`,
        ],
        komoditi: [
          {
            id_komoditi_nasional: 4,
            id_komoditi: 6,
            nama_komoditi: "PADI",
            icon: `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/sektor/cfcf0efc-5370-4d80-9fa7-7820d70c9a6a.png`,
            detail: [
              {
                nama: "Luas Panen",
                value: "1 Juta Hektar",
                tahun: 2021,
              },
              {
                nama: "Produktifitas",
                value: "5.14 ton/hektar",
                tahun: 2021,
              },
            ],
          },
        ],
      },
      {
        deskripsi:
          '<span id="docs-internal-guid-1a757bea-7fff-2837-4e15-8f61f4ff80d8"><span style="font-variant-numeric: normal; font-variant-east-asian: normal; vertical-align: baseline;">Kebutuhan pangan dalam negeri akan terus meningkat seiring dengan bertambahnya jumlah penduduk secara signifikan. Indonesia bertekad meningkatkan produksi hasil tanaman pangan untuk memenuhi kebutuhan pangan nasional maupun untuk memenuhi kebutuhan industri. Industri pakan dan tepung membutuhkan jagung dan padi dalam jumlah yang besar sebagai bahan baku. Kedelai yang produktivitasnya masih rendah perlu perluasan lahan untuk memenuhi kebutuhan dalam negeri. </span></span>',
        nama_sektor: "TANAMAN PANGAN",
        file_icon:
          `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/sektor/9f5012bf-9873-4ff3-9039-0f9695fde433.png`,
        file_image: `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/sektor/no_image.png`,
        image: [
          `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/sektor/50d15dce-0ee4-4db4-a673-7823de9d6723.jpeg`,
          `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/sektor/b0924998-ccec-41cd-a594-bc673b2a656a.jpeg`,
        ],
        komoditi: [
          {
            id_komoditi_nasional: 4,
            id_komoditi: 6,
            nama_komoditi: "PADI",
            icon: `${process.env.NEXT_PUBLIC_BASE_URL}/uploads/sektor/cfcf0efc-5370-4d80-9fa7-7820d70c9a6a.png`,
            detail: [
              {
                nama: "Luas Panen",
                value: "1 Juta Hektar",
                tahun: 2021,
              },
              {
                nama: "Produktifitas",
                value: "5.14 ton/hektar",
                tahun: 2021,
              },
            ],
          },
        ],
      },
    ],
  },
  // setSelectedSectorDetails: async (newId) => {
  //   set({ isLoading: true, error: null });
  //   try {
  //     const details = await getSektorUnggulanNasionalDetail(newId);
  //     set({
  //       selectedSectorId: newId,
  //       sectorDetails: details,
  //       isLoading: false,
  //       selectedDiagramIndex: 0,
  //     });
  //   } catch (error) {
  //     set({ error: error.message, isLoading: false });
  //   }
  // },
  // setSelectedSectorDetails: async (newId) => {
  //   set({ isLoading: true, error: null });
  //   try {
  //     const language = useLanguageStore.getState().language;
  //     const details = await getSektorUnggulanNasionalDetail(newId, language);
  //     set({
  //       selectedSectorId: newId,
  //       sectorDetails: details,
  //       isLoading: false,
  //       selectedDiagramIndex: 0,
  //     });
  //   } catch (error) {
  //     set({ error: error.message, isLoading: false });
  //   }
  // },
  setSelectedSectorDetails: async (newId, language) => {
    set({ isLoading: true, error: null });
    try {
      const details = await getSektorUnggulanNasionalDetail(newId, language);
      set({
        selectedSectorId: newId,
        sectorDetails: details,
        isLoading: false,
        selectedDiagramIndex: 0,
      });
    } catch (error) {
      set({ error: error.message, isLoading: false });
    }
  },
}));

export default useSektorUnggulanStore;
