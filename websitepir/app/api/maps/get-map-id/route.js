import { NextResponse } from "next/server";
import { cookies } from "next/headers";

// Mark this route as dynamic to prevent static generation
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Input validation function
const validateMapUid = (mapUid) => {
  if (!mapUid) return false;
  // Only allow alphanumeric characters, hyphens, and underscores
  const validPattern = /^[a-zA-Z0-9_-]+$/;
  return validPattern.test(mapUid) && mapUid.length <= 100;
};

// Validate URL to prevent SSRF
const validateApiUrl = (url) => {
  try {
    const parsedUrl = new URL(url);
    const allowedHosts = [
      process.env.NEXT_PUBLIC_API_BASE_URL_GEO?.replace(/^https?:\/\//, ''),
      'localhost',
      '127.0.0.1'
    ].filter(Boolean);
    
    return allowedHosts.some(host => parsedUrl.hostname === host);
  } catch {
    return false;
  }
};

export async function GET(request) {
  try {
    const cookieStore = cookies();
    const geoAccessToken = cookieStore.get("geoAccessToken")?.value;
    const mapUid = request.nextUrl.searchParams.get("mapUid");

    // Validate inputs
    if (!geoAccessToken) {
      return new NextResponse("Missing geoAccessToken", { status: 401 });
    }

    if (!validateMapUid(mapUid)) {
      return new NextResponse("Invalid mapUid parameter", { status: 400 });
    }

    // Construct and validate the API URL
    const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/cms/map/${mapUid}/info`;
    
    if (!validateApiUrl(apiUrl)) {
      return new NextResponse("Invalid API URL", { status: 400 });
    }

    const res = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${geoAccessToken}`,
        "User-Agent": "NextJS-API-Client/1.0",
      },
      // Add timeout and other security options
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    if (!res.ok) {
      console.error("API responded with an error", res.status, await res.text());
      return new NextResponse("Failed to fetch data", { status: res.status });
    }

    const data = await res.json();
    
    // Create response with security headers
    const response = NextResponse.json(data, { status: 200 });
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    
    return response;
  } catch (error) {
    console.error("Internal Server Error:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
