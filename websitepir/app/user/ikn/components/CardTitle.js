"use client";

import useIKNStore from "@/store/IKNStore";
import { MapPin } from "lucide-react";
import Image from "next/image";
import React from "react";

const CardTitle = () => {
  const { iknDetails } = useIKNStore();

  return (
    <>
      <div className="flex items-center justify-center gap-48 pt-20 mx-30 max-w-8xl">
        <div className="flex flex-col">
          <span className="text-[#4CA85F] font-montserrat font-bold text-2xl">
            Kota Dunia Untuk Semua
          </span>
          <div className="flex text-[#2D3A96] font-bold font-montserrat">
            <MapPin />
            <span>Kalimantan Timur</span>
          </div>
        </div>
        <div className="pb-10 max-w-content">
          <Image
            src={`${process.env.NEXT_PUBLIC_BASE_URL}/uploads/ikn/202410150551401042.webp`}
            width={600}
            height={200}
            className="rounded-xl w-[600px ] h-[330px] object-cover"
            alt="image"
          />
        </div>
      </div>
      <div className="flex justify-center">
        <div className="flex flex-wrap items-center justify-center gap-4 pt-10 mb-8 text-center max-w-7xl md:grid-cols-4">
          {iknDetails.indikator.map((item, index) => (
            <div
              key={index}
              className="bg-[#4CA85F] w-96 py-[20.5px] px-[8px] rounded drop-shadow-lg"
            >
              <p className="text-xl font-bold text-white">
                {item.nama}
                <span> </span>
                {item.satuan}
              </p>
              <p className="text-sm text-white">
                {item.deskripsi.replace(/(<([^>]+)>)/gi, "")}
              </p>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default CardTitle;
