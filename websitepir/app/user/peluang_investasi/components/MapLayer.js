"use client";

import { getReferensiService } from "@/services/AllService";
import { getTokenGeoportal } from "@/services/GetInfoGeoportal";
import usePeluangInvestasiMapStore from "@/store/PeluangInvestasiMapStore";
import ApiGeo from "@/utils/ApiGeo";
import { decryptToken } from "@/utils/decryptToken";
import maplibregl from "maplibre-gl";
import React, { useEffect, useRef, useState } from "react";

const MapLayer = () => {
  const mapContainerRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const [geoAccessToken, setGeoAccessToken] = useState(null);
  const { mapCenter, mapZoom } = usePeluangInvestasiMapStore();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const layerUids = [
    "UxZMrtPkivQpGPcs",
    "FzSlmxkTfBDcKDHp",
    "ZlMvBZEjgInDPeYR",
    // "rcKgJiksVFYpOWyL",
    // "OQduxzGOXFpwABcL",
    // "YbhZZMPWCuGIJCbC",
  ];

  const API_KEY = "npXcGxdghNnPva1tMhQW";
  const initialStyle = "streets";

  // Validation helper
  const isValidCoordinate = (lon, lat) => {
    const parsedLon = parseFloat(lon);
    const parsedLat = parseFloat(lat);
    return (
      !isNaN(parsedLon) &&
      !isNaN(parsedLat) &&
      parsedLon >= -180 &&
      parsedLon <= 180 &&
      parsedLat >= -90 &&
      parsedLat <= 90
    );
  };

  // Fetch access token
  const fetchGeoToken = async () => {
    // Retrieve credentials from environment variables
    const username = process.env.NEXT_PUBLIC_GEO_USERNAME;
    const password = process.env.NEXT_PUBLIC_GEO_PASSWORD;

    if (!username || !password) {
      console.error('Missing geo credentials in environment variables');
      return;
    }

    try {
      // const response = await ApiGeo.post("/iam/login", {
      //   username: username,
      //   password: password,
      // });
      // return response.accessToken;
      // const response = await getTokenGeoportal();
      // const decryptedToken = await decryptToken(response.token);
      // return decryptedToken;
      const response = await ApiGeo.post("/iam/login", {
        username: username,
        password: password,
      });
      return response.accessToken;
    } catch (error) {
      console.error("Failed to login:", error);
      return null;
    }
  };

  // Fetch reference points - removed token dependency
  const fetchReferencePoints = async () => {
    try {
      const [kabkotResponse, daerahResponse, kawasanResponse] =
        await Promise.all([
          getReferensiService("vw_peluang_kabkot_id"),
          getReferensiService("vw_peluang_daerah_id"),
          getReferensiService("vw_kawasan"),
        ]);

      if (!kabkotResponse?.data || !daerahResponse?.data) {
        throw new Error("Invalid response format");
      }

      const kabkotPoints = kabkotResponse.data;
      const daerahPoints = daerahResponse.data;
      const kawasanPoints = kawasanResponse.data;

      return kabkotPoints;
    } catch (error) {
      console.error("Error fetching reference points:", error);
      return [];
    }
  };

  // Add markers to map - removed token dependency
  const addMarkersToMap = (mapInstance, points) => {
    points.forEach((point) => {
      if (isValidCoordinate(point.lon, point.lat)) {
        const iconUrl = point.iconmap
          ? `/pinPeluangSektor/${point.iconmap}`
          : "/pinPeluangSektor/default-pin.png";
        const markerElement = createCustomMarker(iconUrl);

        new maplibregl.Marker({ element: markerElement })
          .setLngLat([parseFloat(point.lon), parseFloat(point.lat)])
          .addTo(mapInstance);
      }
    });
  };

  // Initialize map
  useEffect(() => {
    const initializeMap = async () => {
      try {
        setIsLoading(true);

        // Initialize base map first
        if (!mapInstanceRef.current) {
          const mapInstance = new maplibregl.Map({
            container: mapContainerRef.current,
            style: `https://api.maptiler.com/maps/${initialStyle}/style.json?key=${API_KEY}`,
            center: mapCenter,
            zoom: mapZoom,
          });

          mapInstanceRef.current = mapInstance;

          mapInstance.setMaxBounds([
            [90, -15],
            [145, 10],
          ]);

          // Wait for map to load
          await new Promise((resolve) => mapInstance.on("load", resolve));

          // Fetch and add reference points immediately after map loads
          const referencePoints = await fetchReferencePoints();
          if (referencePoints.length > 0) {
            addMarkersToMap(mapInstance, referencePoints);
          }
        }

        // Try to get token for additional layers only
        const token = await fetchGeoToken();
        setGeoAccessToken(token);

        if (token) {
          // Add layers only if we have a token
          for (const [index, layerUid] of layerUids.entries()) {
            try {
              await addLayerToMap(
                mapInstanceRef.current,
                layerUid,
                index,
                token
              );
            } catch (layerError) {
              console.error(`Failed to add layer ${layerUid}:`, layerError);
            }
          }
        }
      } catch (err) {
        setError(err.message);
        console.error("Map initialization error:", err);
      } finally {
        setIsLoading(false);
      }
    };

    initializeMap();

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, []);

  // Rest of your component remains the same
  useEffect(() => {
    if (mapInstanceRef.current) {
      mapInstanceRef.current.flyTo({
        center: mapCenter,
        zoom: mapZoom,
        duration: 2000,
        essential: true,
      });
    }
  }, [mapCenter, mapZoom]);

  const createCustomMarker = (iconUrl) => {
    const marker = document.createElement("div");
    marker.style.backgroundImage = `url('${iconUrl}')`;
    marker.style.width = "20px";
    marker.style.height = "24px";
    marker.style.backgroundSize = "contain";
    return marker;
  };

  if (error) {
    return <div className="text-red-500">Error loading map: {error}</div>;
  }

  return (
    <div className="flex flex-1 border rounded-lg shadow-lg">
      <div ref={mapContainerRef} className="h-[690px] w-full rounded-lg" />
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50">
          Loading map...
        </div>
      )}
    </div>
  );
};

export default MapLayer;

// const MapLayer = () => {
//   const mapContainerRef = useRef(null);
//   const mapInstanceRef = useRef(null);
//   const [geoAccessToken, setGeoAccessToken] = useState(null);
//   const { mapCenter, mapZoom } = useMapStore();
//   const [isLoading, setIsLoading] = useState(true);
//   const [error, setError] = useState(null);

//   const layerUids = [
//     "UxZMrtPkivQpGPcs",
//     "FzSlmxkTfBDcKDHp",
//     "ZlMvBZEjgInDPeYR",
//     // "rcKgJiksVFYpOWyL",
//     // "OQduxzGOXFpwABcL",
//     // "YbhZZMPWCuGIJCbC",
//   ];

//   const API_KEY = "npXcGxdghNnPva1tMhQW";
//   const initialStyle = "streets";

//   // Validation helper
//   const isValidCoordinate = (lon, lat) => {
//     const parsedLon = parseFloat(lon);
//     const parsedLat = parseFloat(lat);
//     return (
//       !isNaN(parsedLon) &&
//       !isNaN(parsedLat) &&
//       parsedLon >= -180 &&
//       parsedLon <= 180 &&
//       parsedLat >= -90 &&
//       parsedLat <= 90
//     );
//   };

//   // Fetch access token
//   const fetchGeoToken = async () => {
//     try {
//       const response = await ApiGeo.post("/iam/login", {
//         username: username,
//         password: password,
//       });
//       return response.accessToken;
//     } catch (error) {
//       console.error("Failed to login:", error);
//       throw new Error("Failed to get access token");
//     }
//   };

//   // Fetch reference points
//   const fetchReferencePoints = async () => {
//     try {
//       // const response = await getReferensiService("vw_peluang_kabkot_id");
//       // if (!response?.data) throw new Error("Invalid response format");
//       const [kabkotResponse, daerahResponse, kawasanResponse] = await Promise.all([
//         getReferensiService("vw_peluang_kabkot_id"),
//         getReferensiService("vw_peluang_daerah_id"),
//         getReferensiService("vw_kawasan"),
//       ]);

//       // return response.data.filter(point => point.status === "99");
//       if (!kabkotResponse?.data || !daerahResponse?.data) {
//         throw new Error("Invalid response format");
//       }

//       // Filter valid data
//       const kabkotPoints = kabkotResponse.data;
//       const daerahPoints = daerahResponse.data;
//       const kawasanPoints = kawasanResponse.data;

//       // Combine both datasets
//       // return [...kabkotPoints, ...daerahPoints, ...kawasanPoints];
//       return kabkotPoints;
//     } catch (error) {
//       console.error("Error fetching reference points:", error);
//       throw error;
//     }
//   };

//   // Add markers to map
//   const addMarkersToMap = (mapInstance, points) => {
//     points.forEach(point => {
//       if (isValidCoordinate(point.lon, point.lat) && point.iconmap) {
//         const markerElement = createCustomMarker(`/pinPeluangSektor/${point.iconmap}`);
//     

//         new maplibregl.Marker({ element: markerElement })
//           .setLngLat([parseFloat(point.lon), parseFloat(point.lat)])
//           .addTo(mapInstance);
//       }
//     });
//   };

//   // Add layer to map
//   const addLayerToMap = async (mapInstance, layerUid, index, token) => {
//     try {
//       const response = await fetch(
//         `${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/cms/layer/${layerUid}/info`,
//         {
//           headers: {
//             "Content-Type": "application/json",
//             Authorization: `Bearer ${token}`,
//           },
//         }
//       );

//       if (!response.ok) throw new Error(`Failed to fetch layer: ${layerUid}`);

//       const layerInfo = await response.json();
//       const layerName = layerInfo.data[0].layerName;

//       if (layerUid !== "YbhZZMPWCuGIJCbC") {
//         const sourceId = `geoserverSource-${index}`;
//         const layerId = `geoserver-layer-${index}`;

//         mapInstance.addSource(sourceId, {
//           type: "raster",
//           tiles: [
//             `https://pirdev.webgis.app/geoserver/wms?service=WMS&version=1.1.0&request=GetMap&layers=${layerName}&bbox={bbox-epsg-3857}&width=512&height=512&srs=EPSG:3857&styles=&format=image%2Fpng&transparent=true&token=${token}`,
//           ],
//           tileSize: 256,
//         });

//         mapInstance.addLayer({
//           id: layerId,
//           type: "raster",
//           source: sourceId,
//           minzoom: 0,
//           maxzoom: 19,
//           paint: { "raster-opacity": 0.8 },
//         });
//       }
//     } catch (error) {
//       console.error(`Error adding layer ${layerUid}:`, error);
//       throw error;
//     }
//   };

//   // Initialize map
//   useEffect(() => {
//     const initializeMap = async () => {
//       try {
//         setIsLoading(true);
//         const token = await fetchGeoToken();
//         setGeoAccessToken(token);

//         if (!mapInstanceRef.current) {
//           const mapInstance = new maplibregl.Map({
//             container: mapContainerRef.current,
//             style: `https://api.maptiler.com/maps/${initialStyle}/style.json?key=${API_KEY}`,
//             center: mapCenter,
//             zoom: mapZoom,
//           });

//           mapInstanceRef.current = mapInstance;

//           mapInstance.setMaxBounds([
//             [90, -15],
//             [145, 10],
//           ]);

//           // Wait for map to load
//           await new Promise((resolve) => mapInstance.on("load", resolve));

//           // Add reference points
//           const referencePoints = await fetchReferencePoints();

//           addMarkersToMap(mapInstance, referencePoints);

//           // Add layers
//           for (const [index, layerUid] of layerUids.entries()) {
//             await addLayerToMap(mapInstance, layerUid, index, token);
//           }
//         }
//       } catch (err) {
//         setError(err.message);
//         console.error("Map initialization error:", err);
//       } finally {
//         setIsLoading(false);
//       }
//     };

//     initializeMap();

//     return () => {
//       if (mapInstanceRef.current) {
//         mapInstanceRef.current.remove();
//         mapInstanceRef.current = null;
//       }
//     };
//   }, []);

//   // Handle map position updates
//   useEffect(() => {
//     if (mapInstanceRef.current) {
//       mapInstanceRef.current.flyTo({
//         center: mapCenter,
//         zoom: mapZoom,
//         duration: 2000,
//         essential: true,
//       });
//     }
//   }, [mapCenter, mapZoom]);

//   // Helper functions remain the same
//   const getMarkerIcon = (sektor) => {
//     // ... your existing getMarkerIcon function
//   };

//   const createCustomMarker = (iconUrl) => {
//     const marker = document.createElement("div");
//     marker.style.backgroundImage = `url('${iconUrl}')`;
//     marker.style.width = "20px";
//     marker.style.height = "24px";
//     marker.style.backgroundSize = "contain";
//     return marker;
//   };

//   if (error) {
//     return <div className="text-red-500">Error loading map: {error}</div>;
//   }

//   return (
//     <div className="flex flex-1 border rounded-lg shadow-lg">
//       <div ref={mapContainerRef} className="h-[690px] w-full rounded-lg" />
//       {isLoading && (
//         <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50">
//           Loading map...
//         </div>
//       )}
//     </div>
//   );
// };

// export default MapLayer;
