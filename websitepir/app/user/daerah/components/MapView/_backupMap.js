"use client";

import { getReferensiService } from "@/services/AllService";
import useMapStore from "@/store/UseMapStore";
import ApiGeo from "@/utils/ApiGeo";
import maplibregl from "maplibre-gl";
import React, { useEffect, useRef, useState } from "react";
import "./style.css";

const MapView = ({
  setValue,
  defaultValue = { lat: 0, lng: 0 },
  lngFormName = 'long',
  latFormName = 'lat',
  arrOfLayersUid = []
}) => {
  const mapContainerRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const lastMarkerRef = useRef(null);
  const lastCoordsRef = useRef(null);
  const activeLayersRef = useRef(new Set());

  const DEFAULT_LAYER = {
    layer: "MeSYCcOVvDPKjyCF",
    nama_layer: "Default Layer",
    map: "tested"
  };

  const [geoAccessToken, setGeoAccessToken] = useState(null);
  const [lngLat, setLngLat] = useState({ lng: 0, lat: 0 });
  const { mapCenter, mapZoom } = useMapStore();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const API_KEY = "npXcGxdghNnPva1tMhQW";
  const initialStyle = "streets";

  // Validation helper
  const isValidCoordinate = (lon, lat) => {
    const parsedLon = parseFloat(lon);
    const parsedLat = parseFloat(lat);
    return (
      !isNaN(parsedLon) &&
      !isNaN(parsedLat) &&
      parsedLon >= -180 &&
      parsedLon <= 180 &&
      parsedLat >= -90 &&
      parsedLat <= 90
    );
  };

  // Fetch access token
  const fetchGeoToken = async () => {
    // Validate environment variables
    const username = process.env.NEXT_PUBLIC_GEO_USERNAME;
    const password = process.env.NEXT_PUBLIC_GEO_PASSWORD;
    
    if (!username || !password) {
      console.error('Missing geo credentials in environment variables');
      return;
    }

    try {
      const response = await ApiGeo.post("/iam/login", {
        username: username,
        password: password,
      });
      return response.accessToken;
    } catch (error) {
      console.error("Failed to login:", error);
      throw new Error("Failed to get access token");
    }
  };

  // Add layer to map
  const addLayerToMap = async (mapInstance, layerObject, index, token) => {
    try {
      const layerUid = layerObject.layer;

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/cms/layer/${layerUid}/info`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) throw new Error(`Failed to fetch layer: ${layerUid}`);

      const layerInfo = await response.json();
      const layerName = layerInfo.data[0].layerName;

      if (layerUid !== "YbhZZMPWCuGIJCbC") {
        const sourceId = `geoserverSource-${layerUid}`;
        const layerId = `geoserver-layer-${layerUid}`;

        // Remove existing layer and source if they exist
        if (mapInstance.getLayer(layerId)) {
          mapInstance.removeLayer(layerId);
        }
        if (mapInstance.getSource(sourceId)) {
          mapInstance.removeSource(sourceId);
        }

        mapInstance.addSource(sourceId, {
          type: "raster",
          tiles: [
            `${process.env.NEXT_PUBLIC_BASE_URL2}/geoserver/wms?service=WMS&version=1.1.0&request=GetMap&layers=${layerName}&bbox={bbox-epsg-3857}&width=512&height=512&srs=EPSG:3857&styles=&format=image%2Fpng&transparent=true&token=${token}`,
          ],
          tileSize: 256,
        });

        mapInstance.addLayer({
          id: layerId,
          type: "raster",
          source: sourceId,
          minzoom: 0,
          maxzoom: 19,
          paint: { "raster-opacity": 0.8 },
        });

        activeLayersRef.current.add(layerUid);
      }
    } catch (error) {
      console.error(`Error adding layer:`, error);
      throw error;
    }
  };

  // Remove layer from map
  const removeLayerFromMap = (mapInstance, layerUid) => {
    const layerId = `geoserver-layer-${layerUid}`;
    const sourceId = `geoserverSource-${layerUid}`;

    if (mapInstance.getLayer(layerId)) {
      mapInstance.removeLayer(layerId);
    }
    if (mapInstance.getSource(sourceId)) {
      mapInstance.removeSource(sourceId);
    }

    activeLayersRef.current.delete(layerUid);
  };

  // Handle layer updates based on arrOfLayersUid prop and default layer
  useEffect(() => {
    const updateLayers = async () => {
      if (!mapInstanceRef.current || !geoAccessToken) return;

      try {
        // Combine default layer with prop layers
        const allLayers = [DEFAULT_LAYER, ...arrOfLayersUid];
        const newLayerIds = allLayers.map(layer => layer.layer);

        // Remove layers that are no longer in the combined list
        for (const activeLayerId of activeLayersRef.current) {
          if (!newLayerIds.includes(activeLayerId)) {
            removeLayerFromMap(mapInstanceRef.current, activeLayerId);
          }
        }

        // Add new layers, starting with default layer
        for (const [index, layerObject] of allLayers.entries()) {
          if (!activeLayersRef.current.has(layerObject.layer)) {
            await addLayerToMap(mapInstanceRef.current, layerObject, index, geoAccessToken);
          }
        }
      } catch (error) {
        console.error("Error updating layers:", error);
        setError("Failed to update map layers");
      }
    };

    updateLayers();
  }, [arrOfLayersUid, geoAccessToken]);

  // Initialize map
  useEffect(() => {
    const initializeMap = async () => {
      try {
        setIsLoading(true);
        const token = await fetchGeoToken();
        setGeoAccessToken(token);

        if (!mapInstanceRef.current) {
          const mapInstance = new maplibregl.Map({
            container: mapContainerRef.current,
            style: `https://api.maptiler.com/maps/${initialStyle}/style.json?key=${API_KEY}`,
            center: mapCenter,
            zoom: mapZoom,
          });

          mapInstanceRef.current = mapInstance;

          mapInstanceRef.current.on("click", (event) => {
            const { lng, lat } = event.lngLat;

            if (
              lastCoordsRef.current &&
              lastCoordsRef.current.lng === lng &&
              lastCoordsRef.current.lat === lat
            ) {
              if (lastMarkerRef.current) {
                lastMarkerRef.current.remove();
                lastMarkerRef.current = null;
                lastCoordsRef.current = null;
                setValue(latFormName, 0);
                setValue(lngFormName, 0);
              }
              return;
            }

            if (lastMarkerRef.current) {
              lastMarkerRef.current.remove();
            }

            const newMarker = new maplibregl.Marker()
              .setLngLat([lng, lat])
              .addTo(mapInstance);

            setLngLat({ lng, lat });
            lastMarkerRef.current = newMarker;
            lastCoordsRef.current = { lng, lat };
          });

          if (defaultValue?.lat !== 0 && defaultValue?.lng !== 0) {
            const newMarker = new maplibregl.Marker()
              .setLngLat([defaultValue.lng, defaultValue.lat])
              .addTo(mapInstance);
            setLngLat({ lng: defaultValue.lng, lat: defaultValue.lat });
            mapInstance.setCenter([defaultValue.lng, defaultValue.lat]);
            lastMarkerRef.current = newMarker;
            lastCoordsRef.current = { lng: defaultValue.lng, lat: defaultValue.lat };
          }

          mapInstance.setMaxBounds([
            [90, -15],
            [145, 10],
          ]);

          await new Promise((resolve) => mapInstance.on("load", resolve));

          // Fetch and add reference points
          const referencePoints = await fetchReferencePoints();
          addMarkersToMap(mapInstance, referencePoints);
        }
      } catch (err) {
        setError(err.message);
        console.error("Map initialization error:", err);
      } finally {
        setIsLoading(false);
      }
    };

    initializeMap();

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, []);

  // Rest of your component code...

  return (
    <div className="flex flex-1 border rounded-lg shadow-lg">
      <div ref={mapContainerRef} className="h-[590px] w-full rounded-lg overflow-hidden" />
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50">
          Loading map...
        </div>
      )}
    </div>
  );
};

export default MapView;