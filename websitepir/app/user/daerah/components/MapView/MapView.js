"use client";

import { getReferensiService } from "@/services/AllService";
import useMapStore from "@/store/UseMapStore";
import ApiGeo from "@/utils/ApiGeo";
import maplibregl from "maplibre-gl";
import React, { useCallback, useEffect, useRef, useState } from "react";
import "./style.css";
import {
  getInfoGeoportal,
  getTokenGeoportal,
} from "@/services/GetInfoGeoportal";
import { decryptToken } from "@/utils/decryptToken";
import { debounce } from "lodash";

const MapView = ({
  setValue,
  defaultValue = { lat: 0, lng: 0 },
  lngFormName = "long",
  latFormName = "lat",
  arrOfLayersUid = [],
}) => {
  const mapContainerRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const lastMarkerRef = useRef(null);
  const lastCoordsRef = useRef(null);

  const [geoAccessToken, setGeoAccessToken] = useState(null);
  const [lngLat, setLngLat] = useState({ lng: 0, lat: 0 });
  const { mapCenter, mapZoom } = useMapStore();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [layerUids, setLayerUids] = useState([]);

  const API_KEY = "npXcGxdghNnPva1tMhQW";
  const initialStyle = "streets";

  const isValidCoordinate = (lon, lat) => {
    const parsedLon = parseFloat(lon);
    const parsedLat = parseFloat(lat);
    return (
      !isNaN(parsedLon) &&
      !isNaN(parsedLat) &&
      parsedLon >= -180 &&
      parsedLon <= 180 &&
      parsedLat >= -90 &&
      parsedLat <= 90
    );
  };

  const fetchGeoToken = async () => {
    // Validate environment variables
    const username = process.env.NEXT_PUBLIC_GEO_USERNAME;
    const password = process.env.NEXT_PUBLIC_GEO_PASSWORD;
    
    if (!username || !password) {
      console.error('Missing geo credentials in environment variables');
      return;
    }

    try {
      // const response = await getTokenGeoportal();
      // const decryptedToken = await decryptToken(response.token);
      // return decryptedToken;
      const response = await ApiGeo.post("/iam/login", {
        username: username,
        password: password,
      });
      return response.accessToken;
    } catch (error) {
      throw new Error("Failed to get access token");
    }
  };

  const fetchReferencePoints = async () => {
    try {
      const [kabkotResponse, daerahResponse, kawasanResponse] =
        await Promise.all([
          getReferensiService("vw_peluang_kabkot_id"),
          getReferensiService("vw_peluang_daerah_id"),
          getReferensiService("vw_kawasan"),
        ]);

      if (!kabkotResponse?.data || !daerahResponse?.data) {
        throw new Error("Invalid response format");
      }

      const kabkotPoints = kabkotResponse.data;
      const daerahPoints = daerahResponse.data;
      const kawasanPoints = kawasanResponse.data;

      return [...kabkotPoints, ...daerahPoints, ...kawasanPoints];
    } catch (error) {
      throw error;
    }
  };

  const createCustomMarker = (iconUrl) => {
    const marker = document.createElement("div");
    marker.style.backgroundImage = `url('${iconUrl}')`;
    marker.style.width = "20px";
    marker.style.height = "24px";
    marker.style.backgroundSize = "contain";
    return marker;
  };

  const addMarkersToMap = (mapInstance, points) => {
    points.forEach((point) => {
      if (isValidCoordinate(point.lon, point.lat) && point.iconmap) {
        const markerElement = createCustomMarker(
          `/pinPeluangSektor/${point.iconmap}`
        );
        new maplibregl.Marker({ element: markerElement })
          .setLngLat([parseFloat(point.lon), parseFloat(point.lat)])
          .addTo(mapInstance);
      }
    });
  };

  const addLayerToMap = async (mapInstance, layerUid, index, token) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/cms/layer/${layerUid}/info`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      // const response = await getInfoGeoportal(layerUid);
      // if (!response?.data)
      //   throw new Error(`Failed to fetch layer: ${layerUid}`);
      // const layerName = response.data[0].layerName;
      // const layerBbox = response.data[0].layerBbox;

      if (!response.ok) throw new Error(`Failed to fetch layer: ${layerUid}`);

      const layerInfo = await response.json();
      const layerName = layerInfo.data[0].layerName;

      if (layerUid !== "YbhZZMPWCuGIJCbC") {
        const sourceId = `geoserverSource-${index}`;
        const layerId = `geoserver-layer-${index}`;

        mapInstance.addSource(sourceId, {
          type: "raster",
          tiles: [
            `${process.env.NEXT_PUBLIC_BASE_URL2}/geoserver/wms?service=WMS&version=1.1.0&request=GetMap&layers=${layerName}&bbox={bbox-epsg-3857}&width=512&height=512&srs=EPSG:3857&styles=&format=image%2Fpng&transparent=true&token=${token}`,
          ],
          tileSize: 256,
        });

        mapInstance.addLayer({
          id: layerId,
          type: "raster",
          source: sourceId,
          minzoom: 0,
          maxzoom: 19,
          paint: { "raster-opacity": 0.8 },
        });
      }
    } catch (error) {
      throw error;
    }
  };

  const updateLayer = async (
    mapInstance,
    layerUid,
    index,
    token,
    opacity = 0.8
  ) => {
    if (layerUid) {
      try {
        const _getToken = await fetchGeoToken();
        if (!mapInstance) throw new Error("Map instance is required");

        const sourceId = `geoserverSource-${index}`;
        const layerId = `geoserver-layer-${index}`;

        if (mapInstance.getLayer(layerId)) {
          mapInstance.removeLayer(layerId);
        }
        if (mapInstance.getSource(sourceId)) {
          mapInstance.removeSource(sourceId);
        }

        if (layerUid === "YbhZZMPWCuGIJCbC") {
          return;
        }

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/cms/layer/${layerUid}/info`,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token || geoAccessToken || _getToken}`,
            },
          }
        );

        if (!response.ok) throw new Error(`Failed to fetch layer: ${layerUid}`);

        // const response = await getInfoGeoportal(layerUid);
        // if (!response?.data)
        //   throw new Error(`Failed to fetch layer: ${layerUid}`);
        // const layerName = response.data[0].layerName;
        // const layerBbox = response.data[0].layerBbox;

        const layerInfo = await response.json();
        const layerName = layerInfo.data[0].layerName;

        mapInstance.addSource(sourceId, {
          type: "raster",
          tiles: [
            `${process.env.NEXT_PUBLIC_BASE_URL2}/geoserver/wms?service=WMS&version=1.1.0&request=GetMap&layers=${layerName}&bbox={bbox-epsg-3857}&width=512&height=512&srs=EPSG:3857&styles=&format=image%2Fpng&transparent=true&token=${token}`,
          ],
          tileSize: 256,
        });

        mapInstance.addLayer({
          id: layerId,
          type: "raster",
          source: sourceId,
          minzoom: 0,
          maxzoom: 19,
          paint: { "raster-opacity": opacity },
        });

        return { sourceId, layerId };
      } catch (error) {
        throw error;
      }
    }
  };

  const debouncedLayerUpdate = useCallback(
    debounce(async (layers) => {
      const layerUids = layers?.map((item) => item?.layer);
      for (const [index, layerUid] of layerUids.entries()) {
        await updateLayer(
          mapInstanceRef.current,
          layerUid,
          index,
          geoAccessToken
        );
      }
      setLayerUids((prev) => [...prev, ...layerUids]);
    }, 300),
    []
  );

  useEffect(() => {
    if (arrOfLayersUid) {
      debouncedLayerUpdate(arrOfLayersUid);
    }

    return () => {
      debouncedLayerUpdate.cancel();
    };
  }, [arrOfLayersUid, debouncedLayerUpdate, geoAccessToken]);

  useEffect(() => {
    const initializeMap = async () => {
      try {
        setIsLoading(true);
        const token = await fetchGeoToken();
        setGeoAccessToken(token);

        if (!mapInstanceRef.current) {
          const mapInstance = new maplibregl.Map({
            container: mapContainerRef.current,
            style: `https://api.maptiler.com/maps/${initialStyle}/style.json?key=${API_KEY}`,
            center: mapCenter,
            zoom: mapZoom,
          });

          mapInstanceRef.current = mapInstance;
          mapInstanceRef.current.on("click", (event) => {
            const { lng, lat } = event.lngLat;

            if (
              lastCoordsRef.current &&
              lastCoordsRef.current.lng === lng &&
              lastCoordsRef.current.lat === lat
            ) {
              if (lastMarkerRef.current) {
                lastMarkerRef.current.remove();
                lastMarkerRef.current = null;
                lastCoordsRef.current = null;
                setValue(latFormName, 0);
                setValue(lngFormName, 0);
              }
              return;
            }

            if (lastMarkerRef.current) {
              lastMarkerRef.current.remove();
            }

            const newMarker = new maplibregl.Marker()
              .setLngLat([lng, lat])
              .addTo(mapInstance);

            setLngLat({ lng, lat });
            lastMarkerRef.current = newMarker;
            lastCoordsRef.current = { lng, lat };
          });

          if (defaultValue?.lat !== 0 && defaultValue?.lng !== 0) {
            const newMarker = new maplibregl.Marker()
              .setLngLat([defaultValue?.lng, defaultValue?.lat])
              .addTo(mapInstance);
            setLngLat({ lng: defaultValue?.lng, lat: defaultValue?.lat });
            mapInstance.setCenter([defaultValue?.lng, defaultValue?.lat]);
            lastMarkerRef.current = newMarker;
            lastCoordsRef.current = {
              lng: defaultValue?.lng,
              lat: defaultValue?.lat,
            };
          }

          mapInstance.setMaxBounds([
            [90, -15],
            [145, 10],
          ]);

          await new Promise((resolve) => mapInstance.on("load", resolve));

          const referencePoints = await fetchReferencePoints();
          addMarkersToMap(mapInstance, referencePoints);

          if (layerUids?.length > 0) {
            for (const [index, layerUid] of layerUids.entries()) {
              await addLayerToMap(mapInstance, layerUid, index, token);
            }
          }
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    initializeMap();
  }, []);

  useEffect(() => {
    if (mapInstanceRef.current) {
      mapInstanceRef.current.flyTo({
        center: mapCenter,
        zoom: mapZoom,
        duration: 2000,
        essential: true,
      });
    }
  }, [mapCenter, mapZoom]);

  useEffect(() => {
    setValue(latFormName, lngLat.lat);
    setValue(lngFormName, lngLat.lng);
  }, [lngLat]);

  if (error) {
    return <div className="text-red-500">Error loading map: {error}</div>;
  }

  return (
    <div className="flex flex-1 border rounded-lg shadow-lg">
      <div
        ref={mapContainerRef}
        className="h-[590px] w-full rounded-lg overflow-hidden"
      />
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50">
          Loading map...
        </div>
      )}
    </div>
  );
};

export default MapView;
