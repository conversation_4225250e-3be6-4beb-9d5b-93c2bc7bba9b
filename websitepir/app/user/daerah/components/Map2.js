"use client";

import ApiGeo from "@/utils/ApiGeo";
import { Eye, EyeOff, Layers2Icon } from "lucide-react";
import maplibregl from "maplibre-gl";
import React, { useEffect, useRef, useState } from "react";

const Map2 = ({ id }) => {
  const mapContainerRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const [layerData, setLayerData] = useState([]);
  const [titikZoom, setTitikZoom] = useState([]);
  const [popupVisible, setPopupVisible] = useState(false);
  const [layerVisibility, setLayerVisibility] = useState({});
  const [geoAccessToken, setGeoAccessToken] = useState(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [layerUidsToFetch, setLayerUidsToFetch] = useState([]);

  // Fetch access token
  useEffect(() => {
    const fetchToken = async () => {
    // Validate environment variables
    const username = process.env.NEXT_PUBLIC_GEO_USERNAME;
    const password = process.env.NEXT_PUBLIC_GEO_PASSWORD;
    
    if (!username || !password) {
      console.error('Missing geo credentials in environment variables');
      return;
    }

      try {
        const response = await ApiGeo.post("/iam/login", {
          username: username,
          password: password,
        });
        setGeoAccessToken(response.accessToken);
      } catch (error) {
        console.error("Failed to login and retrieve token:", error);
      }
    };

    fetchToken();
  }, []);

  // Fetch initial layer UIDs
  const getLayerGeo = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/cms/map/xQmd3dlo85oJxOhh/info`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${geoAccessToken}`,
          },
        }
      );
      if (!response.ok) {
        throw new Error(`Failed to fetch layer data: ${response.status}`);
      }
      const data = await response.json();

      // Set layer UIDs to fetch
      setLayerUidsToFetch(data.data.mapLayerUids);

      // Initialize visibility state for all layers as false
      const initialVisibility = {};
      data.data.mapLayerUids.forEach((_, index) => {
        initialVisibility[index] = false;
      });
      setLayerVisibility(initialVisibility);

      // Clear existing layer data
      setLayerData([]);
    } catch (error) {
      console.error("Error fetching layer data:", error);
    }
  };

  // const getLayerPeluang = async () => {
  //   try {
  //     const response = await fetch(
  //       `${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/cms/layer/YbhZZMPWCuGIJCbC/data/0/200`,
  //       {
  //         method: "GET",
  //         headers: {
  //           "Content-Type": "application/json",
  //           Authorization: `Bearer ${geoAccessToken}`,
  //         },
  //       }
  //     );
  //     if (!response.ok) {
  //       throw new Error(`Failed to fetch layer data: ${response.status}`);
  //     }
  //     const data = await response.json();
  //     const titikZoom = data.data.find((item) => item.id_peluang === parseInt(id));
  //     setTitikZoom(titikZoom);
  //   } catch (error) {
  //     console.error("Error fetching layer data:", error);
  //   }
  // };

  // Fetch layer details once we have the UIDs
  useEffect(() => {
    const fetchLayerDetails = async () => {
      if (!geoAccessToken || layerUidsToFetch.length === 0) return;

      try {
        const layerDetailsPromises = layerUidsToFetch.map(async (layerUid) => {
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/cms/layer/${layerUid}/info`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${geoAccessToken}`,
              },
            }
          );
          if (!response.ok) {
            throw new Error(`Failed to fetch layer data for UID ${layerUid}`);
          }
          return response.json();
        });

        const layerDetails = await Promise.all(layerDetailsPromises);
        setLayerData(layerDetails);
      } catch (error) {
        console.error("Error fetching layer details:", error);
      }
    };

    fetchLayerDetails();
  }, [layerUidsToFetch, geoAccessToken]);

  // Fetch layer UIDs when we have the token
  useEffect(() => {
    if (geoAccessToken) {
      getLayerGeo();
      // getLayerPeluang();
    }
  }, [geoAccessToken]);

  // Initialize and manage map
  useEffect(() => {
    if (layerData.length === 0 || !geoAccessToken || mapInstanceRef.current)
      return;

    const mapInstance = new maplibregl.Map({
      container: mapContainerRef.current,
      style: {
        version: 8,
        sources: {
          osm: {
            type: "raster",
            tiles: ["https://tile.openstreetmap.org/{z}/{x}/{y}.png"],
            tileSize: 256,
          },
        },
        layers: [
          {
            id: "osm",
            type: "raster",
            source: "osm",
            minzoom: 0,
            maxzoom: 19,
          },
        ],
      },
      center: [118.0149, -2.5489],
      zoom: 4,
    });

    mapInstanceRef.current = mapInstance;

    mapInstance.on("load", () => {
      setMapLoaded(true);

      // Add sources for all layers
      layerData.forEach((layer, index) => {
        const layerName = layer.data[0].layerName;
        if (!mapInstance.getSource(`geoserverSource-${index}`)) {
          mapInstance.addSource(`geoserverSource-${index}`, {
            type: "raster",
            tiles: [
              `${process.env.NEXT_PUBLIC_BASE_URL2}/geoserver/wms?service=WMS&version=1.1.0&request=GetMap&layers=${layerName}&bbox={bbox-epsg-3857}&width=512&height=512&srs=EPSG:3857&styles=&format=image%2Fpng&transparent=true&token=${geoAccessToken}`,
            ],
            tileSize: 256,
          });
        }
      });
    });

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [layerData, geoAccessToken]);

  // Handle layer visibility changes
  // useEffect(() => {
  //   if (!mapInstanceRef.current || !mapLoaded) return;

  //   layerData.forEach((layer, index) => {
  //     const layerId = `geoserver-layer-${index}`;
  //     const isVisible = layerVisibility[index];
  //     const map = mapInstanceRef.current;

  //     if (isVisible) {
  //       if (!map.getLayer(layerId)) {
  //         map.addLayer({
  //           id: layerId,
  //           type: "raster",
  //           source: `geoserverSource-${index}`,
  //           minzoom: 0,
  //           maxzoom: 19,
  //         });

  //         // Check if the layer is the one we want to zoom to
  //         if (layer.data[0].layerName === "1305 Blok Plan Hortikultura") {
  //           // Get the layer bounds and zoom the map to it
  //           map.fitBounds(layer.data[0].bounds, {
  //             padding: 50,
  //             duration: 2000, // Animation duration in milliseconds
  //           });
  //         }
  //       }
  //     } else {
  //       if (map.getLayer(layerId)) {
  //         map.removeLayer(layerId);
  //       }
  //     }
  //   });
  // }, [layerVisibility, mapLoaded]);

  // Handle layer visibility changes and zoom to specific opportunity area
  useEffect(() => {
    if (!mapInstanceRef.current || !mapLoaded) return;

    const map = mapInstanceRef.current;

    // Set map center and zoom if titikZoom is available when the map is loaded
    // if (titikZoom && titikZoom.lat && titikZoom.lon) {
    //   map.setCenter([titikZoom.lon, titikZoom.lat]);
    //   map.setZoom(15); // Set an appropriate zoom level (adjust this value as needed)
    // }

    layerData.forEach((layer, index) => {
      const layerId = `geoserver-layer-${index}`;
      const isVisible = layerVisibility[index];

      if (isVisible) {
        if (!map.getLayer(layerId)) {
          map.addLayer({
            id: layerId,
            type: "raster",
            source: `geoserverSource-${index}`,
            minzoom: 0,
            maxzoom: 19,
          });
        }
      } else {
        if (map.getLayer(layerId)) {
          map.removeLayer(layerId);
        }
      }
    });
  }, [layerVisibility, mapLoaded]);

  const togglePopup = () => {
    setPopupVisible((prev) => !prev);
  };

  const handleIconClick = (index) => {
    setLayerVisibility((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  return (
    <div style={{ position: "relative" }}>
      <div
        ref={mapContainerRef}
        style={{ width: "80%", height: "500px", margin: "0 auto" }}
      />
      <button
        onClick={togglePopup}
        style={{
          position: "absolute",
          top: "10px",
          right: "10px",
          backgroundColor: "white",
          border: "none",
          borderRadius: "5px",
          padding: "10px",
          cursor: "pointer",
          boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
        }}
      >
        <Layers2Icon size={20} style={{ marginRight: "5px", color: "gray" }} />
      </button>
      {popupVisible && (
        <div
          style={{
            position: "absolute",
            top: "50px",
            right: "10px",
            backgroundColor: "white",
            borderRadius: "5px",
            boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
            padding: "10px",
            zIndex: 1000,
          }}
        >
          <h4>Layer List</h4>
          <ul>
            {layerData.map((layer, index) => (
              <li
                key={index}
                style={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                }}
              >
                {layerVisibility[index] ? (
                  <Eye
                    size={14}
                    style={{ marginRight: "5px", color: "gray" }}
                    onClick={() => handleIconClick(index)}
                  />
                ) : (
                  <EyeOff
                    size={14}
                    style={{ marginRight: "5px", color: "gray" }}
                    onClick={() => handleIconClick(index)}
                  />
                )}
                <span>{layer.data[0].layerTitle}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default Map2;
