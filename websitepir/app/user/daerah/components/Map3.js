"use client";

import { getReferensiService } from "@/services/AllService";
import {
  getInfoGeoportal,
  getTokenGeoportal,
} from "@/services/GetInfoGeoportal";
import useMapStore from "@/store/UseMapStore";
import ApiGeo from "@/utils/ApiGeo";
import { decryptToken } from "@/utils/decryptToken";
import maplibregl from "maplibre-gl";
import React, { useEffect, useRef, useState } from "react";

class LayerControl {
  constructor(options) {
    const defaultOptions = {
      layers: [],
    };
    this._options = !options ? defaultOptions : options;
    this._layers = this._options.layers;
    this._isOpen = false; // Defaultkan legend tertutup
  }

  onAdd(map) {
    this._map = map;

    // Create container
    this._container = document.createElement("div");
    Object.assign(this._container.style, {
      backgroundColor: "white",
      padding: "10px",
      borderRadius: "4px",
      boxShadow: "0 0 10px rgba(0,0,0,0.1)",
      position: "absolute",
      top: "10px",
      right: "10px",
      zIndex: 1000, // Ensure it's above other map controls
      minWidth: "200px",
    });

    // Create header
    const header = document.createElement("div");
    Object.assign(header.style, {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: "10px",
    });

    // Create title
    const title = document.createElement("span");
    title.textContent = "Legend";
    Object.assign(title.style, {
      fontWeight: "600",
      fontSize: "14px",
      fontFamily: '"Montserrat", sans-serif',
    });

    // Create toggle button
    this._btn = document.createElement("button");
    this._btn.type = "button";
    this._btn.innerHTML = "▼"; // Initial state, button to open
    Object.assign(this._btn.style, {
      background: "transparent",
      border: "none",
      cursor: "pointer",
      padding: "2px 6px",
      fontSize: "12px",
      zIndex: 1001, // Ensure it's clickable and above other content
      pointerEvents: "auto", // Ensure the button is clickable
    });

    header.appendChild(title);
    header.appendChild(this._btn);

    // Create icon list container
    this._iconList = document.createElement("div");
    Object.assign(this._iconList.style, {
      display: "none", // Make sure the legend is initially closed
      marginTop: "5px",
      transition: "all 0.3s ease",
      pointerEvents: "auto", // Allow interactions
      zIndex: 999,
    });

    // Add icons to legend list
    this._layers.forEach((layer) => {
      const layerItem = document.createElement("div");
      Object.assign(layerItem.style, {
        margin: "8px 0",
        display: "flex",
        alignItems: "center",
        gap: "8px",
        cursor: "default", // Disable the click interaction on items
      });

      const iconContainer = document.createElement("div");
      Object.assign(iconContainer.style, {
        width: "20px",
        height: "24px",
        backgroundImage: `url('/pinPeluangSektor/${layer.iconmap}')`,
        backgroundSize: "contain",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center",
      });

      const iconLabel = document.createElement("span");
      const readableName =
        layer.name ||
        layer.iconmap
          .replace(/\.[^/.]+$/, "")
          .replace(/[_-]/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase());

      iconLabel.textContent = readableName;
      Object.assign(iconLabel.style, {
        fontSize: "14px",
        color: "#333",
        flex: "1",
      });

      layerItem.appendChild(iconContainer);
      layerItem.appendChild(iconLabel);
      this._iconList.appendChild(layerItem);

      // Remove the click event listener from items (so they can't be clicked)
      layerItem.addEventListener("click", (e) => {
        e.preventDefault(); // Prevent the default behavior of clicking
      });
    });

    // Append header and icon list to container
    this._container.appendChild(header);
    this._container.appendChild(this._iconList);

    // Toggle visibility of icon list on button click
    this._btn.addEventListener("click", () => {
      this._isOpen = !this._isOpen;
      this._iconList.style.display = this._isOpen ? "block" : "none";
      this._btn.innerHTML = this._isOpen ? "▼" : "▶";
    });

    return this._container;
  }

  onRemove() {
    this._container.parentNode.removeChild(this._container);
    this._map = undefined;
  }
}

const Map3 = () => {
  const mapContainerRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const [geoAccessToken, setGeoAccessToken] = useState(null);
  const { mapCenter, mapZoom, selectedZona } = useMapStore();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isLegendVisible, setIsLegendVisible] = useState(false);
  const [legendData, setLegendData] = useState([]);
  const [iconLayers, setIconLayers] = useState([]);

  const layerUids = ["ZlMvBZEjgInDPeYR"];

  const API_KEY = "npXcGxdghNnPva1tMhQW";
  const initialStyle = "streets";

  const isValidCoordinate = (lon, lat) => {
    const parsedLon = parseFloat(lon);
    const parsedLat = parseFloat(lat);
    return (
      !isNaN(parsedLon) &&
      !isNaN(parsedLat) &&
      parsedLon >= -180 &&
      parsedLon <= 180 &&
      parsedLat >= -90 &&
      parsedLat <= 90
    );
  };

  const fetchGeoToken = async () => {
    // Validate environment variables
    const username = process.env.NEXT_PUBLIC_GEO_USERNAME;
    const password = process.env.NEXT_PUBLIC_GEO_PASSWORD;
    
    if (!username || !password) {
      console.error('Missing geo credentials in environment variables');
      return;
    }

    try {
      // const response = await getTokenGeoportal();
      // const decryptedToken = await decryptToken(response.token);
      // return decryptedToken;
      const response = await ApiGeo.post("/iam/login", {
        username: username,
        password: password,
      });
      return response.accessToken;
    } catch (error) {
      console.error("Failed to login:", error);
      return null; // Return null instead of throwing error
    }
  };

  const fetchReferencePoints = async () => {
    try {
      const [kabkotResponse, daerahResponse, kawasanResponse] =
        await Promise.all([
          getReferensiService("vw_peluang_kabkot_id"),
          getReferensiService("vw_peluang_daerah_id"),
          getReferensiService("vw_kawasan"),
        ]);

      if (!kabkotResponse?.data || !daerahResponse?.data) {
        throw new Error("Invalid response format");
      }

      const kabkotPoints = kabkotResponse.data;
      const daerahPoints = daerahResponse.data;
      const kawasanPoints = kawasanResponse.data;

      return [...kabkotPoints, ...daerahPoints, ...kawasanPoints];
    } catch (error) {
      console.error("Error fetching reference points:", error);
      throw error;
    }
  };

  const addMarkersToMap = (mapInstance, points) => {
    const layers = [];

    points.forEach((point) => {
      if (isValidCoordinate(point.lon, point.lat) && point.iconmap) {
        const iconName = point.iconmap.replace(".svg", "");
        const markerElement = createCustomMarker(
          `/pinPeluangSektor/${point.iconmap}`
        );

        if (!layers.some((layer) => layer.iconmap === point.iconmap)) {
          layers.push({
            iconmap: point.iconmap,
            name: iconName,
          });
        }

        new maplibregl.Marker({ element: markerElement })
          .setLngLat([parseFloat(point.lon), parseFloat(point.lat)])
          .addTo(mapInstance);
      }
    });

    setIconLayers(layers);
  };

  const addLayerToMap = async (mapInstance, layerUid, index, token) => {
    if (!token) return; // Skip layer addition if no token

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/cms/layer/${layerUid}/info`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      // const response = await getInfoGeoportal(layerUid);
      // if (!response?.data)
      //   throw new Error(`Failed to fetch layer: ${layerUid}`);
      // const layerName = response.data[0].layerName;
      // const layerBbox = response.data[0].layerBbox;

      if (!response.ok) throw new Error(`Failed to fetch layer: ${layerUid}`);

      const layerInfo = await response.json();
      const layerName = layerInfo.data[0].layerName;
      const layerBbox = layerInfo.data[0].layerBbox;


      const sourceId = `geoserverSource-${index}`;
      const layerId = `geoserver-layer-${index}`;

      if (mapInstance.getLayer(layerId)) {
        mapInstance.removeLayer(layerId);
      }
      if (mapInstance.getSource(sourceId)) {
        mapInstance.removeSource(sourceId);
      }

      mapInstance.addSource(sourceId, {
        type: "raster",
        tiles: [
          `${process.env.NEXT_PUBLIC_BASE_URL2}/geoserver/wms?service=WMS&version=1.1.0&request=GetMap&layers=${layerName}&bbox={bbox-epsg-3857}&width=512&height=512&srs=EPSG:3857&styles=&format=image%2Fpng&transparent=true&token=${token}`,
        ],
        tileSize: 256,
      });

      mapInstance.addLayer({
        id: layerId,
        type: "raster",
        source: sourceId,
        minzoom: 0,
        maxzoom: 19,
        paint: { "raster-opacity": 0.8 },
      });

      if (layerBbox && layerBbox.length === 4) {
        const bounds = new maplibregl.LngLatBounds(
          [layerBbox[0], layerBbox[1]],
          [layerBbox[2], layerBbox[3]]
        );

        mapInstance.fitBounds(bounds, {
          padding: { top: 50, bottom: 50, left: 50, right: 50 },
          duration: 1000,
        });
      }
    } catch (error) {
      console.error(`Error adding layer ${layerUid}:`, error);
    }
  };

  useEffect(() => {
    const initializeMap = async () => {
      try {
        setIsLoading(true);

        if (!mapInstanceRef.current) {
          const mapInstance = new maplibregl.Map({
            container: mapContainerRef.current,
            style: `https://api.maptiler.com/maps/${initialStyle}/style.json?key=${API_KEY}`,
            center: mapCenter,
            zoom: mapZoom,
          });

          mapInstanceRef.current = mapInstance;

          mapInstance.setMaxBounds([
            [85, -20],
            [155, 15],
          ]);

          await new Promise((resolve) => mapInstance.on("load", resolve));

          // Always fetch and display points
          const referencePoints = await fetchReferencePoints();
          addMarkersToMap(mapInstance, referencePoints);

          // Try to get token for layers
          const token = await fetchGeoToken();
          setGeoAccessToken(token);

          // Only add layers if we have a token
          if (token) {
            for (const [index, layerUid] of layerUids.entries()) {
              await addLayerToMap(mapInstance, layerUid, index, token);
            }
          }
        }
      } catch (err) {
        setError(err.message);
        console.error("Map initialization error:", err);
      } finally {
        setIsLoading(false);
      }
    };

    initializeMap();

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, []);
  useEffect(() => {
    const updateLayerBasedOnZona = async () => {
      if (!mapInstanceRef.current) return;

      const allLayers = [
        "UzqvFzNKdKwqHdVS",
        "rVWHLDywkDsNrxtw",
        "SeliAFmdDopWUDDq",
        "KJPnBNNfbcnAuoJc",
      ];

      const layerMapping = {
        1: "UzqvFzNKdKwqHdVS",
        2: "rVWHLDywkDsNrxtw",
        3: "SeliAFmdDopWUDDq",
      };

      // Remove existing layers
      allLayers.forEach((layerUid, index) => {
        const layerId = `geoserver-layer-${index}`;
        const sourceId = `geoserverSource-${index}`;

        if (mapInstanceRef.current.getLayer(layerId)) {
          mapInstanceRef.current.removeLayer(layerId);
        }
        if (mapInstanceRef.current.getSource(sourceId)) {
          mapInstanceRef.current.removeSource(sourceId);
        }
      });

      // Only proceed if we have a token
      if (geoAccessToken) {
        try {
          if (selectedZona === null) {
            // Add all layers when no zone is selected
            for (let index = 0; index < allLayers.length; index++) {
              const layerUid = allLayers[index];
              await addLayerToMap(
                mapInstanceRef.current,
                layerUid,
                index,
                geoAccessToken
              );
            }
          } else {
            // Add only the layer corresponding to the selected zone
            const selectedLayer = layerMapping[selectedZona];
            const selectedLayerIndex = allLayers.indexOf(selectedLayer);

            if (selectedLayerIndex !== -1) {
              await addLayerToMap(
                mapInstanceRef.current,
                selectedLayer,
                selectedLayerIndex,
                geoAccessToken
              );
            }
          }
        } catch (error) {
          console.error("Error updating layers:", error);
        }
      }
    };

    updateLayerBasedOnZona();
  }, [selectedZona, geoAccessToken]);

  useEffect(() => {
    if (mapInstanceRef.current) {
      mapInstanceRef.current.flyTo({
        center: mapCenter,
        zoom: mapZoom,
        duration: 2000,
        essential: true,
      });
    }
  }, [mapCenter, mapZoom]);

  const createCustomMarker = (iconUrl) => {
    const marker = document.createElement("div");
    marker.style.backgroundImage = `url('${iconUrl}')`;
    marker.style.width = "20px";
    marker.style.height = "24px";
    marker.style.backgroundSize = "contain";
    return marker;
  };

  useEffect(() => {
    if (mapInstanceRef.current && iconLayers.length) {
      const layerControl = new LayerControl({ layers: iconLayers });
      mapInstanceRef.current.addControl(layerControl);
    }
  }, [iconLayers]);

  if (error) {
    return <div className="text-red-500">Error loading map: {error}</div>;
  }

  return (
    <div className="relative rounded-lg">
      <div ref={mapContainerRef} className="h-[590px] w-full rounded-lg" />
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50">
          Loading map...
        </div>
      )}
    </div>
  );
};

export default Map3;

// const Map3 = () => {
//   const mapContainerRef = useRef(null);
//   const mapInstanceRef = useRef(null);
//   const [geoAccessToken, setGeoAccessToken] = useState(null);
//   const { mapCenter, mapZoom, selectedZona } = useMapStore();
//   const [isLoading, setIsLoading] = useState(true);
//   const [error, setError] = useState(null);
//   const [isLegendVisible, setIsLegendVisible] = useState(false);
//   const [legendData, setLegendData] = useState([]);

//   const [iconLayers, setIconLayers] = useState([]);

//   console.log("select zona ", selectedZona);

//   const layerUids = [
//     "ZlMvBZEjgInDPeYR",
//   ];

//   const API_KEY = "npXcGxdghNnPva1tMhQW";
//   const initialStyle = "streets";

//   // Validation helper
//   const isValidCoordinate = (lon, lat) => {
//     const parsedLon = parseFloat(lon);
//     const parsedLat = parseFloat(lat);
//     return (
//       !isNaN(parsedLon) &&
//       !isNaN(parsedLat) &&
//       parsedLon >= -180 &&
//       parsedLon <= 180 &&
//       parsedLat >= -90 &&
//       parsedLat <= 90
//     );
//   };

//   // Fetch access token
//   const fetchGeoToken = async () => {
//     try {
//       const response = await ApiGeo.post("/iam/login", {
//         username: username,
//         password: password,
//       });
//       return response.accessToken;
//     } catch (error) {
//       console.error("Failed to login:", error);
//       throw new Error("Failed to get access token");
//     }
//   };

//   // Fetch reference points
//   const fetchReferencePoints = async () => {
//     try {

//       const [kabkotResponse, daerahResponse, kawasanResponse] =
//         await Promise.all([
//           getReferensiService("vw_peluang_kabkot_id"),
//           getReferensiService("vw_peluang_daerah_id"),
//           getReferensiService("vw_kawasan"),
//         ]);

//       // return response.data.filter(point => point.status === "99");
//       if (!kabkotResponse?.data || !daerahResponse?.data) {
//         throw new Error("Invalid response format");
//       }

//       // Filter valid data
//       const kabkotPoints = kabkotResponse.data;
//       const daerahPoints = daerahResponse.data;
//       const kawasanPoints = kawasanResponse.data;

//       // Combine both datasets
//       return [...kabkotPoints, ...daerahPoints, ...kawasanPoints];
//       // return kawasanPoints;
//     } catch (error) {
//       console.error("Error fetching reference points:", error);
//       throw error;
//     }
//   };

//   const addMarkersToMap = (mapInstance, points) => {
//     const layers = [];

//     points.forEach((point) => {
//       if (isValidCoordinate(point.lon, point.lat) && point.iconmap) {
//         const iconName = point.iconmap.replace(".svg", ""); // Remove .svg extension
//         const markerElement = createCustomMarker(`/pinPeluangSektor/${point.iconmap}`);

//         // Store the icon name for the control
//         if (!layers.some((layer) => layer.iconmap === point.iconmap)) {
//           layers.push({
//             iconmap: point.iconmap,
//             name: iconName
//           });
//         }

//         new maplibregl.Marker({ element: markerElement })
//           .setLngLat([parseFloat(point.lon), parseFloat(point.lat)])
//           .addTo(mapInstance);
//       }
//     });

//     setIconLayers(layers); // Store the icon layers
//   };

//   // Add layer to map
//   const addLayerToMap = async (mapInstance, layerUid, index, token) => {
//     try {
//       const response = await fetch(
//         `${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/cms/layer/${layerUid}/info`,
//         {
//           headers: {
//             "Content-Type": "application/json",
//             Authorization: `Bearer ${token}`,
//           },
//         }
//       );

//       if (!response.ok) throw new Error(`Failed to fetch layer: ${layerUid}`);

//       const layerInfo = await response.json();
//       const layerName = layerInfo.data[0].layerName;
//       const layerBbox = layerInfo.data[0].layerBbox;

//       const sourceId = `geoserverSource-${index}`;
//       const layerId = `geoserver-layer-${index}`;

//       // Remove existing source and layer if they exist
//       if (mapInstance.getLayer(layerId)) {
//         mapInstance.removeLayer(layerId);
//       }
//       if (mapInstance.getSource(sourceId)) {
//         mapInstance.removeSource(sourceId);
//       }

//       mapInstance.addSource(sourceId, {
//         type: "raster",
//         tiles: [
//           `https://pirdev.webgis.app/geoserver/wms?service=WMS&version=1.1.0&request=GetMap&layers=${layerName}&bbox={bbox-epsg-3857}&width=512&height=512&srs=EPSG:3857&styles=&format=image%2Fpng&transparent=true&token=${token}`,
//         ],
//         tileSize: 256,
//       });

//       mapInstance.addLayer({
//         id: layerId,
//         type: "raster",
//         source: sourceId,
//         minzoom: 0,
//         maxzoom: 19,
//         paint: { "raster-opacity": 0.8 },
//       });

//     // Auto-zoom based on bbox
//     if (layerBbox && layerBbox.length === 4) {
//       const bounds = new maplibregl.LngLatBounds(
//         [layerBbox[0], layerBbox[1]], // [minLng, minLat]
//         [layerBbox[2], layerBbox[3]]  // [maxLng, maxLat]
//       );

//       mapInstance.fitBounds(bounds, {
//         padding: { top: 50, bottom: 50, left: 50, right: 50 },
//         duration: 1000, // Animation duration in ms
//       });
//     }
//     } catch (error) {
//       console.error(`Error adding layer ${layerUid}:`, error);
//       throw error;
//     }
//   };

//   // Initialize map
//   useEffect(() => {
//     const initializeMap = async () => {
//       try {
//         setIsLoading(true);
//         const token = await fetchGeoToken();
//         setGeoAccessToken(token);

//         if (!mapInstanceRef.current) {
//           const mapInstance = new maplibregl.Map({
//             container: mapContainerRef.current,
//             style: `https://api.maptiler.com/maps/${initialStyle}/style.json?key=${API_KEY}`,
//             center: mapCenter,
//             zoom: mapZoom,
//           });

//           mapInstanceRef.current = mapInstance;

//           mapInstance.setMaxBounds([
//             [85, -20],
//             [155, 15],
//           ]);

//           // Wait for map to load
//           await new Promise((resolve) => mapInstance.on("load", resolve));

//           // Add reference points
//           const referencePoints = await fetchReferencePoints();
//           console.log("data pel", referencePoints);

//           addMarkersToMap(mapInstance, referencePoints);

//           // Add layers
//           for (const [index, layerUid] of layerUids.entries()) {
//             await addLayerToMap(mapInstance, layerUid, index, token);
//           }
//         }
//       } catch (err) {
//         setError(err.message);
//         console.error("Map initialization error:", err);
//       } finally {
//         setIsLoading(false);
//       }
//     };

//     initializeMap();

//     return () => {
//       if (mapInstanceRef.current) {
//         mapInstanceRef.current.remove();
//         mapInstanceRef.current = null;
//       }
//     };
//   }, []);

//   // Tambahkan useEffect baru ini ke dalam komponen Map3
// useEffect(() => {
//   const updateLayerBasedOnZona = async () => {
//     if (!mapInstanceRef.current || !geoAccessToken) return;

//     const allLayers = [
//       "ZlMvBZEjgInDPeYR", // Tambahkan layer yang selalu ingin Anda tampilkan
//       "IxnmGGGeZclecERz",
//       "CklNJiYwLiycMGoB",
//       "cDLTCtxjrnrYYADm",
//     ];

//     const layerMapping = {
//       1: "IxnmGGGeZclecERz",
//       2: "CklNJiYwLiycMGoB",
//       3: "cDLTCtxjrnrYYADm",
//     };

//     // Remove all existing layers and sources
//     allLayers.forEach((layerUid, index) => {
//       const layerId = `geoserver-layer-${index}`;
//       const sourceId = `geoserverSource-${index}`;

//       if (mapInstanceRef.current.getLayer(layerId)) {
//         mapInstanceRef.current.removeLayer(layerId);
//       }

//       if (mapInstanceRef.current.getSource(sourceId)) {
//         mapInstanceRef.current.removeSource(sourceId);
//       }
//     });

//     // Selalu tambahkan layer ZlMvBZEjgInDPeYR
//     try {
//       await addLayerToMap(
//         mapInstanceRef.current,
//         "ZlMvBZEjgInDPeYR",
//         allLayers.indexOf("ZlMvBZEjgInDPeYR"),
//         geoAccessToken
//       );

//       // Tambahkan layer lain sesuai kondisi sebelumnya
//       if (selectedZona === null) {
//         for (let index = 0; index < allLayers.length; index++) {
//           const layerUid = allLayers[index];
//           if (layerUid !== "ZlMvBZEjgInDPeYR") {
//             await addLayerToMap(
//               mapInstanceRef.current,
//               layerUid,
//               index,
//               geoAccessToken
//             );
//           }
//         }
//       } else {
//         const selectedLayer = layerMapping[selectedZona];
//         const selectedLayerIndex = allLayers.indexOf(selectedLayer);

//         if (selectedLayerIndex !== -1) {
//           await addLayerToMap(
//             mapInstanceRef.current,
//             selectedLayer,
//             selectedLayerIndex,
//             geoAccessToken
//           );
//         }
//       }
//     } catch (error) {
//       console.error("Error adding layers:", error);
//     }
//   };

//   updateLayerBasedOnZona();
// }, [selectedZona, geoAccessToken]); // Pantau perubahan selectedZona dan geoAccessToken

//   // Handle map position updates
//   useEffect(() => {
//     if (mapInstanceRef.current) {
//       mapInstanceRef.current.flyTo({
//         center: mapCenter,
//         zoom: mapZoom,
//         duration: 2000,
//         essential: true,
//       });
//     }
//   }, [mapCenter, mapZoom]);

//   const createCustomMarker = (iconUrl) => {
//     const marker = document.createElement("div");
//     marker.style.backgroundImage = `url('${iconUrl}')`;
//     marker.style.width = "20px";
//     marker.style.height = "24px";
//     marker.style.backgroundSize = "contain";
//     return marker;
//   };

//   useEffect(() => {
//     if (mapInstanceRef.current && iconLayers.length) {
//       const layerControl = new LayerControl({ layers: iconLayers });
//       mapInstanceRef.current.addControl(layerControl);
//     }
//   }, [iconLayers]);

//   if (error) {
//     return <div className="text-red-500">Error loading map: {error}</div>;
//   }

//   return (

//     <div className="relative rounded-lg">
//       <div ref={mapContainerRef} className="h-[590px] w-full rounded-lg" />
//       {isLoading && (
//         <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50">
//           Loading map...
//         </div>
//       )}

//     </div>
//   );
// };

// export default Map3;
