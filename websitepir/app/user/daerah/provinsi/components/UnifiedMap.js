"use client";

import maplibregl from "maplibre-gl";
import React, { useRef, useEffect, useState } from "react";
import "maplibre-gl/dist/maplibre-gl.css";
import { getReferensiService } from "@/services/AllService";
import { getInfoGeoportal, getTokenGeoportal } from "@/services/GetInfoGeoportal";
import useMapStore from "@/store/UseMapStore";
import ApiGeo from "@/utils/ApiGeo";
import { queryFeatures } from "@esri/arcgis-rest-feature-layer";
import { request } from "@esri/arcgis-rest-request";
import * as turf from "@turf/turf";
import { decryptToken } from "@/utils/decryptToken";

class LayerControl {
  constructor(options) {
    const defaultOptions = {
      layers: [],
    };
    this._options = !options ? defaultOptions : options;
    this._layers = this._options.layers;
    this._isOpen = false; // Defaultkan legend tertutup
  }

  onAdd(map) {
    this._map = map;

    // Create container
    this._container = document.createElement("div");
    Object.assign(this._container.style, {
      backgroundColor: "white",
      padding: "10px",
      borderRadius: "4px",
      boxShadow: "0 0 10px rgba(0,0,0,0.1)",
      position: "absolute",
      top: "10px",
      right: "10px",
      zIndex: 1000, // Ensure it's above other map controls
      minWidth: "200px",
    });

    // Create header
    const header = document.createElement("div");
    Object.assign(header.style, {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: "10px",
    });

    // Create title
    const title = document.createElement("span");
    title.textContent = "Legend";
    Object.assign(title.style, {
      fontWeight: "bold",
      fontSize: "14px",
    });

    // Create toggle button
    this._btn = document.createElement("button");
    this._btn.type = "button";
    this._btn.innerHTML = "▼"; // Initial state, button to open
    Object.assign(this._btn.style, {
      background: "transparent",
      border: "none",
      cursor: "pointer",
      padding: "2px 6px",
      fontSize: "12px",
      zIndex: 1001, // Ensure it's clickable and above other content
      pointerEvents: "auto", // Ensure the button is clickable
    });

    header.appendChild(title);
    header.appendChild(this._btn);

    // Create icon list container
    this._iconList = document.createElement("div");
    Object.assign(this._iconList.style, {
      display: "none", // Make sure the legend is initially closed
      marginTop: "5px",
      transition: "all 0.3s ease",
      pointerEvents: "auto", // Allow interactions
      zIndex: 999,
    });

    // Add icons to legend list
    this._layers.forEach((layer) => {
      const layerItem = document.createElement("div");
      Object.assign(layerItem.style, {
        margin: "8px 0",
        display: "flex",
        alignItems: "center",
        gap: "8px",
        cursor: "default", // Disable the click interaction on items
      });

      const iconContainer = document.createElement("div");
      Object.assign(iconContainer.style, {
        width: "20px",
        height: "24px",
        backgroundImage: `url('/pinInfra/${layer.iconmap}')`,
        backgroundSize: "contain",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center",
      });

      const iconLabel = document.createElement("span");
      const readableName =
        layer.name ||
        layer.iconmap
          .replace(/\.[^/.]+$/, "")
          .replace(/[_-]/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase());

      iconLabel.textContent = readableName;
      Object.assign(iconLabel.style, {
        fontSize: "14px",
        color: "#333",
        flex: "1",
      });

      layerItem.appendChild(iconContainer);
      layerItem.appendChild(iconLabel);
      this._iconList.appendChild(layerItem);

      // Remove the click event listener from items (so they can't be clicked)
      layerItem.addEventListener("click", (e) => {
        e.preventDefault(); // Prevent the default behavior of clicking
      });
    });

    // Append header and icon list to container
    this._container.appendChild(header);
    this._container.appendChild(this._iconList);

    // Toggle visibility of icon list on button click
    this._btn.addEventListener("click", () => {
      this._isOpen = !this._isOpen;
      this._iconList.style.display = this._isOpen ? "block" : "none";
      this._btn.innerHTML = this._isOpen ? "▼" : "▶";
    });

    return this._container;
  }

  onRemove() {
    this._container.parentNode.removeChild(this._container);
    this._map = undefined;
  }
}

const UnifiedMap = ({ lat, lon }) => {
  const mapContainer = useRef(null);
  const [mapHeight, setMapHeight] = useState("590px");
  const mapInstanceRef = useRef(null);
  const [geoAccessToken, setGeoAccessToken] = useState(null);
  const { mapCenter, mapZoom, lists, selectedItems, selectedProvinsi } =
    useMapStore();
  const [API_KEY] = useState("npXcGxdghNnPva1tMhQW");
  const initialStyle = "streets";
  const rtrw = lists.rtrw;
  const [previousLayerId, setPreviousLayerId] = useState(null);
  // Validation helper
  const isValidCoordinate = (lon, lat) => {
    const parsedLon = parseFloat(lon);
    const parsedLat = parseFloat(lat);
    return (
      !isNaN(parsedLon) &&
      !isNaN(parsedLat) &&
      parsedLon >= -180 &&
      parsedLon <= 180 &&
      parsedLat >= -90 &&
      parsedLat <= 90
    );
  };

  useEffect(() => {
    const sidebarMap = document.querySelector(".sidebar-map"); // Pastikan SidebarMap memiliki class ini

    if (sidebarMap) {
      // Fungsi untuk mengupdate tinggi
      const updateMapHeight = () => {
        const sidebarHeight = sidebarMap.clientHeight;
        setMapHeight(`${sidebarHeight}px`);
      };

      // Update height awal
      updateMapHeight();

      // Gunakan MutationObserver untuk mendeteksi perubahan tinggi
      const observer = new MutationObserver(updateMapHeight);
      observer.observe(sidebarMap, {
        childList: true,
        subtree: true,
        attributes: true,
      });

      // Tambahkan event listener untuk resize
      window.addEventListener("resize", updateMapHeight);

      // Cleanup
      return () => {
        observer.disconnect();
        window.removeEventListener("resize", updateMapHeight);
      };
    }
  }, []);

  useEffect(() => {
    const zoomToBounds = async () => {
    // Validate environment variables
    const username = process.env.NEXT_PUBLIC_GEO_USERNAME;
    const password = process.env.NEXT_PUBLIC_GEO_PASSWORD;
    
    if (!username || !password) {
      console.error('Missing geo credentials in environment variables');
      return;
    }

      const selectedRTRW = selectedItems?.rtrw;
      const selectedRDTR = selectedItems?.rdtr;
      const selectedItem = selectedRTRW || selectedRDTR;

      if (selectedItem && mapInstanceRef.current) {
        await handleRTRWLayer(selectedItem, mapInstanceRef.current);
      }
    };

    zoomToBounds();
  }, [selectedItems?.rtrw, selectedItems?.rdtr]);

  // Function to add RTRW layer for a specific selected item
  const addRTRWLayer = async (mapInstance, rtrwItem) => {
    try {
      const proxyUrl = "https://gistaru.atrbpn.go.id/proxy_bkpmsipd/run.ashx?";

      // Fetch metadata for the RTRW layer
      const metadata = await request(`${proxyUrl}${rtrwItem.url}?f=json`);

      // Query features for the RTRW layer
      const featureCollection = await queryFeatures({
        url: `${proxyUrl}${rtrwItem.url}`,
        where: "1=1",
        outFields: ["*"],
        returnGeometry: true,
        f: "geojson",
      });

      // Add source for this RTRW layer
      const sourceId = `selected-arcgis-layer`;

      // Remove existing source and layers if they exist
      if (mapInstance.getSource(sourceId)) {
        // Remove any existing layers using this source
        const existingLayers = mapInstance
          .getStyle()
          .layers.filter((layer) => layer.source === sourceId)
          .map((layer) => layer.id);

        existingLayers.forEach((layerId) => {
          if (mapInstance.getLayer(layerId)) {
            mapInstance.removeLayer(layerId);
          }
        });

        mapInstance.removeSource(sourceId);
      }

      // Add new source
      mapInstance.addSource(sourceId, {
        type: "geojson",
        data: featureCollection,
      });

      // Add layers based on the metadata renderer
      if (metadata.drawingInfo && metadata.drawingInfo.renderer) {
        if (metadata.drawingInfo.renderer.uniqueValueInfos) {
          // Handle unique value renderer
          metadata.drawingInfo.renderer.uniqueValueInfos.forEach(
            (info, valueIndex) => {
              const { value, symbol } = info;
              const fillColor = `rgba(${symbol.color.slice(0, 3).join(",")}, ${
                symbol.color[3] / 255
              })`;

              mapInstance.addLayer({
                id: `${sourceId}-value-${valueIndex}`,
                type: "fill",
                source: sourceId,
                paint: {
                  "fill-color": fillColor,
                  "fill-opacity": 0.8,
                },
                filter: ["==", ["get", "NAMOBJ"], value],
              });
            }
          );
        } else if (metadata.drawingInfo.renderer.symbol) {
          // Handle simple renderer
          const symbol = metadata.drawingInfo.renderer.symbol;
          const fillColor = `rgba(${symbol.color.slice(0, 3).join(",")}, ${
            symbol.color[3] / 255
          })`;

          mapInstance.addLayer({
            id: `${sourceId}-simple`,
            type: "fill",
            source: sourceId,
            paint: {
              "fill-color": fillColor,
              "fill-opacity": 0.8,
            },
          });
        }
      }
    } catch (error) {
      console.error("Error adding selected RTRW layer to map:", error);
    }
  };

  // Add layer to map with auto-zoom based on bbox
  const addLayerToMap = async (mapInstance, layerUid, index, token) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/cms/layer/${layerUid}/info`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) throw new Error(`Failed to fetch layer: ${layerUid}`);

      const layerInfo = await response.json();
      const layerName = layerInfo.data[0].layerName;
      const layerBbox = layerInfo.data[0].layerBbox; // Bounding box

      // const response = await getInfoGeoportal(layerUid);
      // if (!response?.data)
      //   throw new Error(`Failed to fetch layer: ${layerUid}`);
      // const layerName = response.data[0].layerName;
      // const layerBbox = response.data[0].layerBbox;

      const sourceId = `geoserverSource-${index}`;
      const layerId = `geoserver-layer-${index}`;

      // Add source
      mapInstance.addSource(sourceId, {
        type: "raster",
        tiles: [
          `${process.env.NEXT_PUBLIC_BASE_URL2}/geoserver/wms?service=WMS&version=1.1.0&request=GetMap&layers=${layerName}&bbox={bbox-epsg-3857}&width=512&height=512&srs=EPSG:3857&styles=&format=image%2Fpng&transparent=true&token=${token}`,
        ],
        tileSize: 256,
      });

      // Add layer
      mapInstance.addLayer({
        id: layerId,
        type: "raster",
        source: sourceId,
        minzoom: 0,
        maxzoom: 19,
        paint: { "raster-opacity": 0.8 },
      });

      // Auto-zoom based on bbox
      if (layerBbox && layerBbox.length === 4) {
        const bounds = new maplibregl.LngLatBounds(
          [layerBbox[0], layerBbox[1]], // [minLng, minLat]
          [layerBbox[2], layerBbox[3]] // [maxLng, maxLat]
        );

        mapInstance.fitBounds(bounds, {
          padding: { top: 50, bottom: 50, left: 50, right: 50 },
          duration: 1000, // Animation duration in ms
        });
      }
    } catch (error) {
      console.error(`Error adding layer ${layerUid}:`, error);
      throw error;
    }
  };

  // const handleRTRWLayer = async (selectedItem, mapInstance, previousLayerId) => {
  //   if (!selectedItem || !mapInstance) return;

  //   try {
  //     // Hapus layer sebelumnya jika ada
  //     if (previousLayerId && mapInstance.getLayer(previousLayerId)) {
  //       mapInstance.removeLayer(previousLayerId);
  //     }
  //     if (previousLayerId && mapInstance.getSource(previousLayerId)) {
  //       mapInstance.removeSource(previousLayerId);
  //     }

  //     // Periksa apakah URL adalah UID atau URL penuh
  //     if (selectedItem.url.startsWith("http")) {
  //       // URL berbasis gistaru
  //       const proxyUrl = "https://gistaru.atrbpn.go.id/proxy_bkpmsipd/run.ashx?";
  //       const featureCollection = await queryFeatures({
  //         url: `${proxyUrl}${selectedItem.url}`,
  //         where: "1=1",
  //         outFields: ["*"],
  //         returnGeometry: true,
  //         f: "geojson",
  //       });

  //       if (featureCollection && featureCollection.features.length > 0) {
  //         const collection = turf.featureCollection(featureCollection.features);
  //         const bounds = turf.bbox(collection);

  //         const maplibreBounds = new maplibregl.LngLatBounds(
  //           [bounds[0], bounds[1]],
  //           [bounds[2], bounds[3]]
  //         );

  //         mapInstance.fitBounds(maplibreBounds, {
  //           padding: 50,
  //           duration: 2000,
  //         });

  //         await addRTRWLayer(mapInstance, selectedItem);
  //       }
  //     } else {
  //       // URL berbasis UID
  //       const token = geoAccessToken; // Ganti dengan token Anda
  //       const newLayerId = `geoserver-layer-${Date.now()}`; // ID unik untuk layer baru
  //       await addLayerToMap(mapInstance, selectedItem.url, newLayerId, token);

  //       // Simpan ID layer baru untuk penghapusan selanjutnya
  //       return newLayerId;
  //     }
  //   } catch (error) {
  //     console.error("Error handling RTRW layer:", error);
  //   }
  // };
  // Modified RTRW layer handler to work with or without token
  const handleRTRWLayer = async (
    selectedItem,
    mapInstance,
    previousLayerId
  ) => {
    if (!selectedItem || !mapInstance) return;

    try {
      // Remove previous layer if exists
      if (previousLayerId && mapInstance.getLayer(previousLayerId)) {
        mapInstance.removeLayer(previousLayerId);
      }
      if (previousLayerId && mapInstance.getSource(previousLayerId)) {
        mapInstance.removeSource(previousLayerId);
      }

      if (selectedItem.url.startsWith("http")) {
        // Handle gistaru URL case
        const proxyUrl =
          "https://gistaru.atrbpn.go.id/proxy_bkpmsipd/run.ashx?";
        const featureCollection = await queryFeatures({
          url: `${proxyUrl}${selectedItem.url}`,
          where: "1=1",
          outFields: ["*"],
          returnGeometry: true,
          f: "geojson",
        });

        if (featureCollection && featureCollection.features.length > 0) {
          const collection = turf.featureCollection(featureCollection.features);
          const bounds = turf.bbox(collection);

          const maplibreBounds = new maplibregl.LngLatBounds(
            [bounds[0], bounds[1]],
            [bounds[2], bounds[3]]
          );

          mapInstance.fitBounds(maplibreBounds, {
            padding: 50,
            duration: 2000,
          });

          await addRTRWLayer(mapInstance, selectedItem);
        }
      } else if (geoAccessToken) {
        // Only try to add UID-based layer if token exists
        const newLayerId = `geoserver-layer-${Date.now()}`;
        await addLayerToMap(
          mapInstance,
          selectedItem.url,
          newLayerId,
          geoAccessToken
        );
        return newLayerId;
      }
    } catch (error) {
      console.error("Error handling RTRW layer:", error);
    }
  };

  useEffect(() => {
    const updateLayer = async () => {
      const newLayerId = await handleRTRWLayer(
        selectedItems?.rtrw,
        mapInstanceRef.current,
        previousLayerId
      );
      if (newLayerId) setPreviousLayerId(newLayerId);
    };

    updateLayer();
  }, [selectedItems?.rtrw]);

  // Fetch GeoServer access token
  useEffect(() => {
    const fetchToken = async () => {
      try {
        // // const response = await ApiGeo.post("/iam/login", {
        // //   username: username,
        // //   password: password,
        // // });
        // const response = await getTokenGeoportal();
        // const decryptedToken = await decryptToken(response.token);
        // // return decryptedToken;
        // setGeoAccessToken(decryptedToken);
        const response = await ApiGeo.post("/iam/login", {
          username: username,
          password: password,
        });
        setGeoAccessToken(response.accessToken);
      } catch (error) {
        console.error("Failed to login and retrieve token:", error);
      }
    };

    fetchToken();
  }, []);

  // Handle map position updates
  useEffect(() => {
    if (mapInstanceRef.current) {
      mapInstanceRef.current.flyTo({
        center: mapCenter,
        zoom: mapZoom,
        duration: 2000,
        essential: true,
      });
    }
  }, [mapCenter, mapZoom]);

  // Initialize and manage map
  // useEffect(() => {
  //   if (!geoAccessToken || mapInstanceRef.current) return;

  //   const mapInstance = new maplibregl.Map({
  //     container: mapContainer.current,
  //     style: `https://api.maptiler.com/maps/${initialStyle}/style.json?key=${API_KEY}`,
  //     center: mapCenter,
  //     zoom: mapZoom,
  //   });

  //   mapInstanceRef.current = mapInstance;

  //   mapInstance.on("load", async () => {
  //     const layers = [
  //       { name: "Pendidikan", iconmap: "icon pendidikan.svg" },
  //       { name: "Hotel", iconmap: "icon hotel.svg" },
  //       { name: "Bandara", iconmap: "icon bandara.svg" },
  //       { name: "Rumah Sakit", iconmap: "icon rumah sakit.svg" },
  //       { name: "Pelabuhan", iconmap: "icon pelabuhan.svg" },
  //     ];

  //     const layerControl = new LayerControl({ layers });
  //     mapInstance.addControl(layerControl, "top-right");
  //     // Add GeoServer WMS Layers
  //     try {
  //       if (lists) {
  //         ["bandara", "hotel", "pelabuhan", "pendidikan", "rumahSakit"].forEach(
  //           (category) => {
  //             if (lists[category]) {
  //               addMarkersToMap(mapInstance, lists[category], category);
  //             }
  //           }
  //         );

  //         // Fetch and add komoditi points
  //         const komoditiPoints = await fetchAdditionalPoints();
  //         if (komoditiPoints.length > 0) {
  //           addMarkersToMap(mapInstance, komoditiPoints);
  //         }
  //       }
  //     } catch (error) {
  //       console.error("Error adding layers to map:", error);
  //     }
  //   });

  //   return () => {
  //     if (mapInstanceRef.current) {
  //       mapInstanceRef.current.remove();
  //       mapInstanceRef.current = null;
  //     }
  //   };
  // }, [geoAccessToken, lists]);

  // Modified map initialization
  useEffect(() => {
    // Initialize map immediately without waiting for token
    if (mapInstanceRef.current) return;

    // Validasi lat dan lon
    const isLatLonValid =
      typeof lat === "number" &&
      typeof lon === "number" &&
      lat >= -90 &&
      lat <= 90 &&
      lon >= -180 &&
      lon <= 180;

    if (!isLatLonValid) {
      console.error(
        "Invalid lat/lon values. Falling back to default coordinates."
      );
      return;
    }

    const initializeMap = async () => {
      const mapInstance = new maplibregl.Map({
        container: mapContainer.current,
        style: `https://api.maptiler.com/maps/${initialStyle}/style.json?key=${API_KEY}`,
        center: [lon, lat],
        zoom: 7,
      });

      mapInstanceRef.current = mapInstance;

      mapInstance.on("load", async () => {
        const layers = [
          { name: "Pendidikan", iconmap: "icon pendidikan.svg" },
          { name: "Hotel", iconmap: "icon hotel.svg" },
          { name: "Bandara", iconmap: "icon bandara.svg" },
          { name: "Rumah Sakit", iconmap: "icon rumah sakit.svg" },
          { name: "Pelabuhan", iconmap: "icon pelabuhan.svg" },
        ];

        const layerControl = new LayerControl({ layers });
        mapInstance.addControl(layerControl, "top-right");

        // Fly to the coordinates dynamically
        mapInstance.flyTo({
          center: [lon, lat], // Fly to props coordinates
          zoom: 7, // Adjust zoom if necessary
          essential: true, // Ensures smooth animation
        });

        // Add markers regardless of token status
        try {
          if (lists) {
            [
              "bandara",
              "hotel",
              "pelabuhan",
              "pendidikan",
              "rumahSakit",
            ].forEach((category) => {
              if (lists[category]) {
                addMarkersToMap(mapInstance, lists[category], category);
              }
            });

            // Fetch and add komoditi points
            const komoditiPoints = await fetchAdditionalPoints();
            if (komoditiPoints && komoditiPoints.length > 0) {
              addMarkersToMap(mapInstance, komoditiPoints);
            }
          }
        } catch (error) {
          console.error("Error adding markers to map:", error);
        }
      });
    };

    initializeMap();

    // Cleanup function remains the same
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [lat, lon, lists]); // Remove geoAccessToken dependency

  // Helper function to get marker icon based on layer type
  const getMarkerIcon = (layerType, komoditi = null) => {
    // First, check if it's a komoditi
    if (komoditi) {
      const normalizedKomoditi = komoditi.toLowerCase();
      const komoditiIcons = {
        padi: "/pinKomoditi/agro industri.svg",
        jagung: "/pinKomoditi/agro industri.svg",
        kedelai: "/pinKomoditi/agro industri.svg",
        tebu: "/pinKomoditi/agro industri.svg",
        cabai: "/pinKomoditi/agro industri.svg",
        bawang: "/pinKomoditi/agro industri.svg",
        default: "/pinKomoditi/agro industri.svg",
      };

      // Find the best match or use default
      for (const [key, icon] of Object.entries(komoditiIcons)) {
        if (normalizedKomoditi.includes(key)) {
          return icon;
        }
      }
      return komoditiIcons["default"];
    }

    // If not komoditi, use existing infrastructure icons
    switch (layerType) {
      case "pendidikan":
        return "/pinInfra/icon pendidikan.svg";
      case "hotel":
        return "/pinInfra/icon hotel.svg";
      case "bandara":
        return "/pinInfra/icon bandara.svg";
      case "rumah_sakit":
        return "/pinInfra/icon rumah sakit.svg";
      case "pelabuhan":
        return "/pinInfra/icon pelabuhan.svg";
      default:
        return "/pinInfra/Default.svg";
    }
  };

  // Function to create and add markers
  const addMarkersToMap = (mapInstance, points, layerType = null) => {
    points.forEach((point) => {
      const { lon, lat } = point;

      // Validate coordinate
      if (!isValidCoordinate(lon, lat)) {
        console.warn(`Invalid coordinate skipped: ${lon}, ${lat}`);
        return;
      }

      // Determine icon - check for komoditi first
      const icon = point.nama_komoditi
        ? getMarkerIcon(null, point.nama_komoditi)
        : getMarkerIcon(layerType);

      if (icon) {
        const marker = document.createElement("div");
        marker.style.backgroundImage = `url('${icon}')`;
        marker.style.width = "20px";
        marker.style.height = "24px";
        marker.style.backgroundSize = "contain";

        new maplibregl.Marker({
          element: marker,
        })
          .setLngLat([lon, lat])
          .addTo(mapInstance);
      }
    });
  };

  return (
    <div className="flex flex-1 border rounded-lg shadow-lg">
      <div
        ref={mapContainer}
        style={{ height: mapHeight }}
        className="w-full rounded-lg"
      />
    </div>
  );
};

export default UnifiedMap;
