"use client";

import ChartMap from "./ChartMap";
import MiniC<PERSON> from "./MiniCard";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getMapsService } from "@/services/dataMapsService";
import useProvinceStore from "@/store/ProvinsiStore";
import React, { useState, useEffect } from "react";

const ProvinsiIndonesia = () => {
  const {
    selectedProvinceId,
    setSelectedProvinceId,
    selectedProvinceName,
    setSelectedProvinceName,
  } = useProvinceStore();
  const [provinces, setProvinces] = useState([]);
  const [selectedProvinceDetails, setSelectedProvinceDetails] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [maps, setMaps] = useState({});

  const fetchProvincesRef = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/home/<USER>/id`
      );
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const data = await response.json();
      setProvinces(data.data.data);
    } catch (error) {
      console.error("Error fetching provinces:", error);
    }
  };

  const fetchProvincesDetails = async (id) => {
    if (!id) return;
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/home/<USER>/${id}`
      );
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const data = await response.json();
      setSelectedProvinceDetails(data);
    } catch (error) {
      console.error("Error fetching provinces:", error);
    }
  };

  useEffect(() => {
    fetchProvincesRef();
  }, []);

  useEffect(() => {
    if (selectedProvinceId) {
      fetchProvincesDetails(selectedProvinceId);
    }
  }, [selectedProvinceId]);

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true);
      try {
        const response = await getMapsService(selectedProvinceId);
        setMaps(response.data);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, [selectedProvinceId]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  const data_provinsi = [
    {
      title: "Peluang",
      icon: "icons2/Peluang 56 putih.svg",
      value: "1 Peluang",
    },
    {
      title: "Umr 2024",
      icon: "icons2/UMR 2024 56 putih.svg",
      value: "1 Peluang",
    },
    {
      title: "Jumlah Penduduk",
      icon: "icons2/Jumlah Penduduk 56 putih.svg",
      value: "1 Peluang",
    },
    {
      title: "PDRB 2024",
      icon: "icons2/PDRB 56 putih.svg",
      value: "1 Peluang",
    },
    {
      title: "Realisasi Investasi 2023",
      icon: "icons2/Realisasi Investasi 2023.svg",
      value: "1 Peluang",
    },
    {
      title: "Kawasan Industri",
      icon: "icons2/Kawasan Industri 56 putih.svg",
      value: "1 Peluang",
    },
  ];

  console.log(maps);

  return (
    <div className="flex flex-col pt-10">
      <div className="flex flex-row gap-[35px] justify-center">
        <div className="pt-10 pl-10 pr-10 border shadow-2xl rounded-xl">
          <Select
            onValueChange={(value) => {
              setSelectedProvinceId(value);
              const selectedProvince = provinces.find(
                (p) => p.id_adm_provinsi === value
              );
              if (selectedProvince) {
                setSelectedProvinceName(selectedProvince.nama);
              }
            }}
            value={selectedProvinceId}
          >
            <SelectTrigger className="w-[240px] text-2xl font-montserrat border-0 mr-10 text-[#2D3A96]">
              <SelectValue placeholder="Pilih Provinsi" />
            </SelectTrigger>
            <SelectContent className="text-xl font-bold font-montserrat">
              {provinces.map((province) => (
                <SelectItem
                  key={province.id_adm_provinsi}
                  value={province.id_adm_provinsi}
                >
                  {province.nama}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <div className="flex flex-col items-start pt-[10px] pb-10 ">
            <MiniCard
              icon={data_provinsi[0].icon}
              cardName="Peluang"
              description={maps.peluang || "Data tidak tersedia"}
              className="border-0"
            />
            <MiniCard
              icon={data_provinsi[1].icon}
              cardName="UMR 2024"
              description={
                maps.umr?.nilai ? maps.umr.nilai : "Data tidak tersedia"
              }
              className="border-0"
            />
            <MiniCard
              icon={data_provinsi[2].icon}
              cardName="Jumlah Penduduk"
              description={
                maps.jumlah_penduduk?.nilai
                  ? maps.jumlah_penduduk.nilai
                  : "Data tidak tersedia"
              }
              className="border-0"
            />
            <MiniCard
              icon={data_provinsi[3].icon}
              cardName="PDRB 2024"
              description={
                maps.pdrb?.nilai ? maps.pdrb.nilai : "Data tidak tersedia"
              }
              className="border-0"
            />
            <MiniCard
              icon={data_provinsi[4].icon}
              cardName="Realisasi Investasi 2023"
              description={
                maps.realisasi_investasi?.nilai
                  ? maps.realisasi_investasi.nilai
                  : "Data tidak tersedia"
              }
              className="border-0"
            />
            <MiniCard
              icon={data_provinsi[5].icon}
              cardName="Kawasan Industri"
              description={
                maps.kawasan?.nilai ? maps.kawasan.nilai : "Data tidak tersedia"
              }
              className="border-0"
            />
          </div>
        </div>

        <div
          className="flex w-[900px] h-[600px] border-black justify-center items-center pt-[30px]"
          style={{
            width: "700px",
            height: "700px",
            backgroundImage: 'url("/peta.svg")',
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }}
        >
          <ChartMap />
        </div>
      </div>
    </div>
  );
};

export default ProvinsiIndonesia;
