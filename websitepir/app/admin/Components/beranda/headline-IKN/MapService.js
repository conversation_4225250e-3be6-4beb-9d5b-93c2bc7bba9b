"use client";

import { getInfoGeoportal, getTokenGeoportal } from "@/services/GetInfoGeoportal";
import { useMapServiceStore } from "@/store/MapServiceStore";
import ApiGeo from "@/utils/ApiGeo";
import { decryptToken } from "@/utils/decryptToken";
import maplibregl from "maplibre-gl";
import React, { useEffect, useRef, useState } from "react";

const MapService = ({}) => {
  const { previewLayer } = useMapServiceStore();
  const mapContainerRef = useRef(null);
  const mapInstanceRef = useRef(null); // Store map instance in useRef
  const [geoAccessToken, setGeoAccessToken] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const API_KEY = "npXcGxdghNnPva1tMhQW";
  const initialStyle = "streets";

  // Fetch access token
  const fetchGeoToken = async () => {
    // Validate environment variables
    const username = process.env.NEXT_PUBLIC_GEO_USERNAME;
    const password = process.env.NEXT_PUBLIC_GEO_PASSWORD;
    
    if (!username || !password) {
      console.error('Missing geo credentials in environment variables');
      return;
    }

    try {
      // const response = await getTokenGeoportal();
      // const decryptedToken = await decryptToken(response.token);
      // return decryptedToken;
      const response = await ApiGeo.post("/iam/login", {
        username: username,
        password: password,
      });
      return response.accessToken;
    } catch (error) {
      console.error("Failed to login:", error);
      throw new Error("Failed to get access token");
    }
  };

  // Add layer to map
  // Add layer to map with auto-zoom based on bbox
  const addLayerToMap = async (mapInstance, layerUid, index, token) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/cms/layer/${layerUid}/info`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) throw new Error(`Failed to fetch layer: ${layerUid}`);

      const layerInfo = await response.json();
      const layerName = layerInfo.data[0].layerName;
      const layerBbox = layerInfo.data[0].layerBbox; // Bounding box
      // const response = await getInfoGeoportal(layerUid);
      // if (!response?.data)
      //   throw new Error(`Failed to fetch layer: ${layerUid}`);
      // const layerName = response.data[0].layerName;
      // const layerBbox = response.data[0].layerBbox;

      const sourceId = `geoserverSource-${index}`;
      const layerId = `geoserver-layer-${index}`;

      // Add source
      mapInstance.addSource(sourceId, {
        type: "raster",
        tiles: [
          `${process.env.NEXT_PUBLIC_BASE_URL2}/geoserver/wms?service=WMS&version=1.1.0&request=GetMap&layers=${layerName}&bbox={bbox-epsg-3857}&width=512&height=512&srs=EPSG:3857&styles=&format=image%2Fpng&transparent=true&token=${token}`,
        ],
        tileSize: 256,
      });

      // Add layer
      mapInstance.addLayer({
        id: layerId,
        type: "raster",
        source: sourceId,
        minzoom: 0,
        maxzoom: 19,
        paint: { "raster-opacity": 0.8 },
      });

      // Auto-zoom based on bbox
      if (layerBbox && layerBbox.length === 4) {
        const bounds = new maplibregl.LngLatBounds(
          [layerBbox[0], layerBbox[1]], // [minLng, minLat]
          [layerBbox[2], layerBbox[3]] // [maxLng, maxLat]
        );

        mapInstance.fitBounds(bounds, {
          padding: { top: 50, bottom: 50, left: 50, right: 50 },
          duration: 1000, // Animation duration in ms
        });
      }
    } catch (error) {
      console.error(`Error adding layer ${layerUid}:`, error);
      throw error;
    }
  };

  // Initialize map
  useEffect(() => {
    const initializeMap = async () => {
      try {
        setIsLoading(true);
        const token = await fetchGeoToken();
        setGeoAccessToken(token);

        if (!mapInstanceRef.current) {
          const mapInstance = new maplibregl.Map({
            container: mapContainerRef.current,
            style: `https://api.maptiler.com/maps/${initialStyle}/style.json?key=${API_KEY}`,
            center: [118.0149, -2.5489],
            zoom: 3,
          });

          mapInstanceRef.current = mapInstance;

          mapInstance.setMaxBounds([
            [90, -15],
            [145, 10],
          ]);

          await new Promise((resolve) => mapInstance.on("load", resolve));

          if (previewLayer) {
            // Fetch and add layer only if previewLayer is set
            await addLayerToMap(mapInstanceRef.current, previewLayer, 0, token);
          }
        }
      } catch (err) {
        setError(err.message);
        console.error("Map initialization error:", err);
      } finally {
        setIsLoading(false);
      }
    };

    initializeMap();

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [previewLayer]); // Only re-run effect when layers, mapCenter, or mapZoom changes

  if (error) {
    return <div className="text-red-500">Error loading map: {error}</div>;
  }

  return (
    <div className="flex flex-1 border rounded-lg shadow-lg relative">
      <div ref={mapContainerRef} className="h-[320px] w-full rounded-lg" />
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50">
          Loading map...
        </div>
      )}
    </div>
  );
};

export default MapService;
