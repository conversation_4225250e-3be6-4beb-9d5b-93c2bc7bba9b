'use client';
import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Edit, Trash } from 'lucide-react';
import DataTable from '../../DataTable';
import SweetAlert from '../../SweetAlert';
import { deleteFAQService } from '@/services/DataReferensiService';
import { deleteArtikelService, statusArtikelService } from '@/services/InformasiService';
import StatusButton from '../../daerah/provinsi/status-button';



const ActionsComponent = ({ row, onEdit, onDelete }) => (
  <div className="flex space-x-2">
    <Button variant="secondary" size="sm" style={{ backgroundColor: '#E2B93B', color: 'white' }} className="text-xs" onClick={() => onEdit(row)}> <Edit className="mr-2 h-4 w-4" /> Edit</Button>
    <Button variant="destructive" size="sm" className="bg-red-500 hover:bg-red-600 text-xs" onClick={() => onDelete(row)}> <Trash className="mr-2 h-4 w-4" /> Delete</Button>
  </div>
);

// const fetchDataService = async (page, pageSize, filter) => {
//   try {
//     const response = await fetch(
//       `https://pirdev.webgis.app/be/admin/artikel_input_data/lists?page=${page}&per_page=${pageSize}&filter=${encodeURIComponent(
//         filter || ""
//       )}`
//     );
//     const result = await response.json();
//     console.log(result);

//     if (response.ok && result.success) {
//       return {
//         success: true,
//         data: result.data,
//         totalPage: result.pagination.total_pages,
//         totalRecords: result.pagination.total_count,
//       };
//     }

//     throw new Error(result.message || "Failed to fetch data");
//   } catch (error) {
//     return {
//       success: false,
//       data: [],
//       totalPage: 0,
//       totalRecords: 0,
//       message: error.message,
//     };
//   }
// };
const fetchDataService = async (page, pageSize, filter) => {
  try {

    let url = `${process.env.NEXT_PUBLIC_BASE_URL}/admin/artikel_input_data/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(filter || "")}&kategori=artikel`;


    const response = await fetch(url);
    const result = await response.json();
    console.log("result api", result);

    if (response.ok && result.success) {
      return {
        success: true,
        data: result.data || [],
        totalPage: result.pagination.total_pages,
        totalRecords: result.pagination.total_count,
      };
    }

    throw new Error(result.message || "Failed to fetch data");
  } catch (error) {
    return {
      success: false,
      data: [],
      totalPage: 0,
      totalRecords: 0,
      message: error.message,
    };
  }
};


const ArtikelTable = ({ onEditArtikel, refreshTrigger }) => {
  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const [combinedTrigger, setCombinedTrigger] = useState(refreshTrigger);

  useEffect(() => {
    setCombinedTrigger(refreshTrigger + localRefreshTrigger);
  }, [refreshTrigger, localRefreshTrigger]);

  
const columns = [
  { accessorKey: 'status', header: 'Status',
    cell: ({ row }) => (
      <div className="flex h-full">
        <StatusButton
          status={row.original.status}
          dataEdit={row.original}
          id={row.original.id}
          onStatusChange={() => setLocalRefreshTrigger((prev) => prev + 1)} // Trigger refresh
          service={statusArtikelService}
        />
      </div>
    )
   },
  { accessorKey: 'judul', header: 'Judul' },
  { accessorKey: 'deskripsi_singkat', header: 'Deskripsi Singkat' },
];

  const handleDelete = async (row) => {
    SweetAlert.confirm(
      "Konfirmasi",
      "Apakah Anda yakin ingin menghapus data ini?",
      async () => {
        try {
          await deleteArtikelService(row.id);
          SweetAlert.success("Success", "Berhasil menghapus data", () => {
            setLocalRefreshTrigger((prev) => prev + 1);
          });
        } catch (error) {
          SweetAlert.error("Error", "Gagal menghapus data", () => {
            console.error("Error deleting data:", error);
          });
        }
      }
    );
  };

  return (
    <DataTable
      columns={columns}
      fetchDataService={fetchDataService}
      pageSize={10}
      actionsComponent={({ row }) => (
        <ActionsComponent
          row={row}
          onEdit={onEditArtikel}
          onDelete={handleDelete}
        />
      )}
      filterPlaceholder="Cari Artikel..."
      refreshTrigger={combinedTrigger}
    />
  );
};

export default ArtikelTable;
