"use client";

import <PERSON><PERSON><PERSON><PERSON> from "../../SweetAlert";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { getReferensiService } from "@/services/AllService";
import { createArtikelService } from "@/services/BerandaService";
import {
  editArtikelService,
  getByIdArtikelService,
  statusArtikelService,
} from "@/services/InformasiService";
import { APICORS } from "@/utils/Api";
import dynamic from "next/dynamic";
// Dynamically import Quill

// Make sure to import Quill styles
import "react-quill/dist/quill.snow.css";
import { getTokenUserFromLocalStorage } from "@/utils/TokenManager";
import { getCookie } from "cookies-next";
// You can use other themes as well

import { Formik, ErrorMessage, Form, FieldArray } from "formik";
import { Check, ChevronsUpDown, Trash } from "lucide-react";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import * as Yup from "yup";

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });

const CreateArtikelForm = ({ ArtikelDataEdit, onClose, onDataChange }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [showTranslateForm, setShowTranslateForm] = useState(false);
  // const [sliderData, setSliderData] = useState(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false); // State for dialog open/close

  const [openRelasiPeluang, setOpenRelasiPeluang] = useState(false);
  const [openKategoriNews, setOpenKategoriNews] = useState(false);
  const [selectedRelasiPeluang, setSelectedRelasiPeluang] = useState("");
  const [selectedKategoriNews, setSelectedKategoriNews] = useState("");
  const [relasiPeluangOptions, setRelasiPeluangOptions] = useState([]);
  const [kategoriNewsOptions, setKategoriNewsOptions] = useState([]);

  const [imagePreview, setImagePreview] = useState(null);
  const [dokumenPreview, setDokumenPreview] = useState(null);
  const isEditMode = !!ArtikelDataEdit;
  const [dataEdit, setDataEdit] = useState(null);

  const containerRef = useRef(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true); // Menandakan data sedang dimuat

      try {
        // Fetch data dari API
        const [res_tb_news_kategori] = await Promise.all([
          getReferensiService("tb_news_kategori"),
        ]);

        // Fallback jika data tidak ditemukan
        const kategoriNewsOptions = res_tb_news_kategori?.data || [];

        // Filter kategori dengan ID tertentu
        const filteredKategoriNewsOptions = kategoriNewsOptions.filter((item) =>
          ![1, 5].includes(item.id_news_kategori)
        );

        // Update state dengan data yang difilter
        setKategoriNewsOptions(filteredKategoriNewsOptions);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setIsLoading(false); // Menandakan data selesai dimuat
      }
    };

    fetchData();
  }, []); // Tidak ada dependensi tambahan, karena hanya memanggil fetch di awal.

  useEffect(() => {
    const fetchData = async () => {
      if (ArtikelDataEdit) {
        try {
          const response = await getByIdArtikelService(ArtikelDataEdit.id);
          const data = response.data;

          setDataEdit(data);

          const dokumen = data?.pathDokumen?.[0];
          setDokumenPreview(dokumen);

          const foto = data?.pathFoto;
          setImagePreview(foto);

          setSelectedKategoriNews(
            kategoriNewsOptions.find(
              (option) => option.id_news_kategori === data.id_news_kategori
            )
          );

          setIsDialogOpen(true);
        } catch (error) {
          console.error("Error fetching data:", error);
        }
      }
    };
    if (ArtikelDataEdit) {
      fetchData();
    }
  }, [ArtikelDataEdit]);

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setSelectedKategoriNews("");
    setImagePreview(null);
    setDokumenPreview(null);
    setDataEdit("");
    onClose();
  };

  console.log("data edit ", dataEdit);
  return (
    <Formik
      enableReinitialize={true}
      initialValues={{
        jenis: dataEdit?.jenis || 1,
        judul: dataEdit?.judul || "",
        deskripsi_singkat: dataEdit?.deskripsi_singkat || "",
        deskripsi: dataEdit?.deskripsi || "",
        status: 0,
        id_news_kategori: dataEdit?.id_news_kategori || null,
        id_adm_kabkot: dataEdit?.id_adm_kabkot || null,
        id_adm_provinsi: dataEdit?.id_adm_provinsi || null,
        id_komoditi: dataEdit?.id_komoditi || null,
        file_foto: dataEdit?.file_foto || null,
        file_dokumen: dataEdit?.file_dokumen || null,
        tipe: dataEdit?.tipe || 1,
        file_judul: dataEdit?.file_judul || "",
        kd_bahasa: dataEdit?.kd_bahasa || "en",
        tr_judul: dataEdit?.translations?.[0]?.judul || "",
        tr_deskripsi_singkat:
          dataEdit?.translations?.[0]?.deskripsi_singkat || "",
        tr_deskripsi: dataEdit?.translations?.[0]?.deskripsi || "",
      }}
      onSubmit={async (values, { setSubmitting, resetForm }) => {
        console.log("values kirim", values);
        console.log("file nama", values.file_dokumen);

        try {
          const transformedValues = {
            jenis: parseInt(values.jenis),
            judul: values.judul,
            deskripsi_singkat: values.deskripsi_singkat,
            deskripsi: values.deskripsi,
            status: parseInt(values.status),
            id_news_kategori: values.id_news_kategori || null,
            id_adm_kabkot: values.id_adm_kabkot || null,
            id_adm_provinsi: values.id_adm_provinsi || null,
            id_komoditi: values.id_komoditi || null,
            file_foto: values.file_foto,
            tipe: parseInt(values.tipe),
            file_judul: values.file_judul,
            file_dokumen: values.file_dokumen,
            kd_bahasa: values.kd_bahasa,
            tr_judul: values.tr_judul,
            tr_deskripsi_singkat: values.tr_deskripsi_singkat,
            tr_deskripsi: values.tr_deskripsi,
          };

          const config = (token) => ({
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: `Bearer ${token}`,
            },
          });

          let response;
          let id;
          const token = getTokenUserFromLocalStorage();
          if (isEditMode) {
            // Membuat FormData baru
            const formData = new FormData();

            // Tambahkan data ke FormData sesuai format yang diminta
            formData.append("jenis", values.jenis?.toString() || "");
            formData.append("judul", values.judul?.toString() || "");
            formData.append(
              "deskripsi_singkat",
              values.deskripsi_singkat?.toString() || ""
            );
            formData.append("deskripsi", values.deskripsi?.toString() || "");
            formData.append("status", values.status?.toString() || "");
            formData.append(
              "id_news_kategori",
              values.id_news_kategori?.toString() || ""
            );
            formData.append(
              "id_adm_kabkot",
              values.id_adm_kabkot?.toString() || ""
            );
            formData.append(
              "id_adm_provinsi",
              values.id_adm_provinsi?.toString() || ""
            );
            formData.append(
              "id_komoditi",
              values.id_komoditi?.toString() || ""
            );
            formData.append("file_judul", values.file_judul?.toString() || "");
            formData.append("kd_bahasa", values.kd_bahasa?.toString() || "");
            formData.append("tr_judul", values.tr_judul?.toString() || "");
            formData.append(
              "tr_deskripsi_singkat",
              values.tr_deskripsi_singkat?.toString() || ""
            );
            formData.append(
              "tr_deskripsi",
              values.tr_deskripsi?.toString() || ""
            );

            // Handle file uploads
            if (values.file_dokumen instanceof File) {
              formData.append("file_dokumen", values.file_dokumen);
            }

            if (values.file_foto instanceof File) {
              formData.append("file_foto", values.file_foto);
            }
            response = await APICORS.patch(
              `/admin/artikel_input_data/update/${dataEdit.id}`,
              transformedValues,
              config(token)
            );
            id = ArtikelDataEdit.id;
            SweetAlert.success("Success", "Data berhasil diedit.", () => {
              // // window.location.reload();
            });
          } else {
            response = await APICORS.post(
              "/admin/artikel_input_data/create",
              transformedValues,
              config(token)
            );
           
            id = response.data?.id;
            SweetAlert.success("Success", "Data berhasil dibuat.", () => {
              // // window.location.reload();
            });
          }

          // console.log("Response:", response);
          if (id) {
            const statusBody = {
              id: id,
              status: 0,
              keterangan: isEditMode ? "Dokumen Ada Perubahan" : "Dokumen Baru",
            };

            try {
              await statusArtikelService(statusBody, id);
              console.log("Status berhasil diperbarui.");
            } catch (statusError) {
              console.error("Gagal memperbarui status:", statusError);
            }
          }
          onDataChange();
          handleDialogClose();
        } catch (error) {
          console.error("Full Error Object:", error);
          console.error("Error Response:", error.response?.data);
          console.error("Error Status:", error.response?.status);

          const errorMessage =
            error.response?.data?.message ||
            error.message ||
            "Gagal menyimpan data";

          SweetAlert.error("Error", errorMessage, () => {});

          setIsDialogOpen(false);
        } finally {
          setSubmitting(false);
          resetForm();
        }
      }}
    >
      {({ setFieldValue, values, isSubmitting }) => (
        <Form>
          <Dialog
            open={isDialogOpen}
            onOpenChange={(open) => {
              if (!open) {
                handleDialogClose();
              }
              setIsDialogOpen(open);
            }}
          >
            {/* <Dialog open={isDialogOpen} onOpenChange={(open) => {
                        if (!open) {
                            // Reset the slider data when the dialog is closed
                            onClose();
                        }
                        setIsDialogOpen(open);
                    }}> */}
            <DialogTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                className="text-xs text-white"
              >
                Tambah Artikel
              </Button>
            </DialogTrigger>

            <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Form Artikel </DialogTitle>
              </DialogHeader>

              <form>
                {/* Sektor Nasional Field */}
                <div
                  ref={containerRef}
                  className="grid grid-cols-4 items-center gap-4"
                >
                  <Label className="block text-xs font-medium">
                    Kategori Artikel
                  </Label>
                  <Popover
                    modal={true}
                    open={openKategoriNews}
                    onOpenChange={setOpenKategoriNews}
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="text-xs justify-between col-span-3 w-full"
                      >
                        {selectedKategoriNews?.nama || "PIlih Kategori"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent
                      className="w-full p-0"
                      container={containerRef.current}
                    >
                      <Command>
                        <CommandInput
                          placeholder="Cari kategori..."
                          onChange={(e) => setSearchTerm(e.target.value)} // Update search term on input change
                        />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {kategoriNewsOptions?.length > 0 &&
                              kategoriNewsOptions.map((kategori) => (
                                <CommandItem
                                  key={kategori.id_news_kategori}
                                  value={kategori.nama}
                                  onSelect={(currentValue) => {
                                    setFieldValue(
                                      "id_news_kategori",
                                      kategori.id_news_kategori
                                    );
                                    setSelectedKategoriNews(kategori);
                                    setOpenKategoriNews(false);
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      selectedKategoriNews?.nama ===
                                        kategori.nama
                                        ? "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                  {kategori.nama}
                                </CommandItem>
                              ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>

                  <ErrorMessage
                    name="id_news_kategori"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Judul Berita
                  </Label>
                  <Input
                    type="text"
                    placeholder="Masukkan judul"
                    name="judul"
                    value={values.judul}
                    onChange={(e) => setFieldValue("judul", e.target.value)}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="judul"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Deskripsi Singkat
                  </Label>
                  <Input
                    type="text"
                    placeholder="Masukkan deskripsi singkat"
                    name="deskripsi_singkat"
                    value={values.deskripsi_singkat}
                    onChange={(e) =>
                      setFieldValue("deskripsi_singkat", e.target.value)
                    }
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="deskripsi_singkat"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">Deskripsi</Label>
                  <div className="justify-between col-span-3 w-full text-xs">
                    <ReactQuill
                      value={values.deskripsi} // Menetapkan nilai saat ini dari form
                      onChange={(content) =>
                        setFieldValue("deskripsi", content)
                      } // Mengupdate nilai form
                      placeholder="Keterangan..."
                      className="w-full" // Mengatur lebar Quill editor
                    />
                  </div>
                  <ErrorMessage
                    name="deskripsi"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Upload dokumen{" "}
                    <span className="text-[10px] text-red-600">Max 20Mb</span>
                  </Label>
                  <Input
                    name="file_dokumen"
                    type="file"
                    accept="application/pdf"
                    onChange={(event) => {
                      const file = event.currentTarget.files[0];

                      if (file) {
                        const tempUrl = URL.createObjectURL(file);

                        // Set file_dokumen file
                        setFieldValue("file_dokumen", file);

                        // Update pratinjau dokumen
                        setDokumenPreview(tempUrl);

                        console.log("Selected file:", file.name); // Debugging
                      }
                    }}
                    className="justify-between col-span-1 w-full text-xs"
                  />

                  <div className="relative col-span-2 w-80 mr-auto mt-2">
                    {dokumenPreview ? (
                      <div className="p-2 bg-gray-100 rounded text-xs flex items-center">
                        <span className="truncate">
                          {dokumenPreview.startsWith("blob:")
                            ? "Pratinjau Dokumen"
                            : dokumenPreview}
                        </span>
                        <a
                          href={dokumenPreview}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-2 text-blue-600 underline text-xs"
                        >
                          Lihat
                        </a>
                      </div>
                    ) : (
                      <div className="w-full h-full bg-gray-100 rounded flex items-center justify-center">
                        <span className="text-gray-500 text-xs">
                          Belum ada dokumen
                        </span>
                      </div>
                    )}
                  </div>

                  <ErrorMessage
                    name="file_dokumen"
                    component="div"
                    className="text-red-500"
                  />
                  <Label className="block text-xs font-medium">
                    Upload Cover{" "}
                    <span className="text-[10px] text-red-600">Max 2Mb</span>
                  </Label>
                  <Input
                    type="file"
                    accept="image/jpeg, image/jpg, image/png, image/webp, image/svg+xml"
                    name="file_foto"
                    onChange={(event) => {
                      const file = event.currentTarget.files[0];
                      setFieldValue("file_foto", file);

                      const reader = new FileReader();
                      reader.onloadend = () => {
                        setImagePreview(reader.result);
                      };
                      if (file) {
                        reader.readAsDataURL(file);
                      }
                    }}
                    className="col-span-1 text-xs"
                  />
                  <div className=" relative col-span-2 w-80 ml-auto">
                    {imagePreview ? (
                      <Image
                        src={imagePreview}
                        alt="Image Preview"
                        width={320}
                        height={0}
                        layout="responsive"
                        objectFit="cover"
                        className="rounded"
                      />
                    ) : (
                      // Placeholder for empty state to maintain layout
                      <div className="w-full h-full bg-white rounded flex items-center justify-center"></div>
                    )}
                  </div>
                  <ErrorMessage
                    name="file_foto"
                    component="div"
                    className="text-red-500"
                  />

                  <div className="col-span-4">
                    <Button
                      type="button"
                      onClick={() => setShowTranslateForm((prev) => !prev)}
                      className="text-xs text-white hover:bg-blue-800"
                    >
                      {showTranslateForm ? "Hide Translate" : "Translate EN"}
                    </Button>

                    {showTranslateForm && (
                      <div
                        ref={containerRef}
                        className="grid grid-cols-4 items-center gap-4"
                      >
                        <Label className="block text-xs font-medium">
                          Judul Berita
                        </Label>
                        <Input
                          type="text"
                          placeholder="Judul [English]"
                          name="tr_judul"
                          value={values.tr_judul}
                          onChange={(e) =>
                            setFieldValue("tr_judul", e.target.value)
                          }
                          className="justify-between col-span-3 w-full text-xs"
                        />
                        <ErrorMessage
                          name="tr_judul"
                          component="div"
                          className="text-red-500"
                        />

                        <Label className="block text-xs font-medium">
                          Deskripsi Singkat
                        </Label>
                        <Input
                          type="text"
                          placeholder="Deskripsi singkat [English]"
                          name="tr_deskripsi_singkat"
                          value={values.tr_deskripsi_singkat}
                          onChange={(e) =>
                            setFieldValue(
                              "tr_deskripsi_singkat",
                              e.target.value
                            )
                          }
                          className="justify-between col-span-3 w-full text-xs"
                        />
                        <ErrorMessage
                          name="tr_deskripsi_singkat"
                          component="div"
                          className="text-red-500"
                        />

                        <Label className="block text-xs font-medium">
                          Deskripsi
                        </Label>
                        <div className="justify-between col-span-3 w-full text-xs">
                          <ReactQuill
                            value={values.tr_deskripsi} // Menetapkan nilai saat ini dari form
                            onChange={(content) =>
                              setFieldValue("tr_deskripsi", content)
                            } // Mengupdate nilai form
                            placeholder="Keterangan en..."
                            className="w-full" // Mengatur lebar Quill editor
                          />
                        </div>
                        <ErrorMessage
                          name="tr_deskripsi"
                          component="div"
                          className="text-red-500"
                        />
                      </div>
                    )}
                  </div>
                </div>

                <DialogFooter>
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={isSubmitting}
                    className="bg-blue-500 hover:bg-blue-700 text-white text-xs py-2 px-4 "
                  >
                    Simpan
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </Form>
      )}
    </Formik>
  );
};

export default CreateArtikelForm;
