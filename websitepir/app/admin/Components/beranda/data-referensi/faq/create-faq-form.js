"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { getReferensiService } from "@/services/AllService";
import { createArtikelService } from "@/services/BerandaService";
import {
  createFAQService,
  editFAQService,
  getByIdFAQService,
} from "@/services/DataReferensiService";
import dynamic from "next/dynamic";
// Dynamically import Quill

// Make sure to import Quill styles
import "react-quill/dist/quill.snow.css";
import SweetAlert from "../../../SweetAlert";
// You can use other themes as well

import { Formik, ErrorMessage, Form, FieldArray } from "formik";
import { Check, ChevronsUpDown, Trash } from "lucide-react";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import * as Yup from "yup";

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });

const CreateFaqForm = ({ FaqDataEdit, onClose, onDataChange }) => {
  // Validasi schema dengan Yup
  const validationSchema = Yup.object().shape({
    title: Yup.string().required("Title wajib diisi"),
    description: Yup.string().required("Description wajib diisi"),
    title_tr: Yup.string().required("Title (Translation) wajib diisi"),
    description_tr: Yup.string().required(
      "Description (Translation) wajib diisi"
    ),
  });

  const tipeOptions = [
    { id: 1, nama: "Glossary" },
    { id: 2, nama: "Faq" },
  ];
  const isEditMode = !!FaqDataEdit;
  const [dataEdit, setDataEdit] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showTranslateForm, setShowTranslateForm] = useState(true);
  // const [sliderData, setSliderData] = useState(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false); // State for dialog open/close

  const [openTipe, setOpenTipe] = useState(false);
  const [selectedTipe, setSelectedTipe] = useState("");
  // const [tipeOptions, setTipeOptions] = useState([]);

  const containerRef = useRef(null);

  useEffect(() => {
    const fetchData = async () => {
      if (FaqDataEdit) {
        try {
          const response = await getByIdFAQService(FaqDataEdit.id_glosary);
          const data = response.data;
          setDataEdit(data);

          setIsDialogOpen(true);
        } catch (error) {
          console.error("Error fetching data:", error);
        }
      }
    };
    if (FaqDataEdit) {
      fetchData();
    }
  }, [FaqDataEdit]);

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    onClose();
  };

  console.log("data edit", dataEdit);

  const initialValues = isEditMode
    ? {
        description: dataEdit?.description || "",
        title: dataEdit?.title || "",

        description_tr: dataEdit?.tb_glossary_tr?.[0]?.description || "",
        title_tr: dataEdit?.tb_glossary_tr?.[0]?.title || "",
        kode_bahasa: "en",
      }
    : {
        description: "",
        title: "",

        description_tr: "",
        title_tr: "",
        kode_bahasa: "en",
      };
  return (
    <Formik
      enableReinitialize={true}
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={async (values, { setSubmitting, resetForm }) => {
        try {
          if (isEditMode) {
            await editFAQService(values, dataEdit.id_glossary);
            SweetAlert.success("Success", "Data berhasil diedit.", () => {
              // // window.location.reload();
            });
          } else {
            await createFAQService(values);
            SweetAlert.success("Success", "Data berhasil dibuat.", () => {
              // // window.location.reload();
            });
          }
          onDataChange();
          handleDialogClose();
        } catch (error) {
          setIsDialogOpen(false);
          // SweetAlert.error("Error", "Gagal menyimpan data.", () => {
          //   console.error("Error:", error);
          // });
          console.error("Full Error Object:", error);
          console.error("Error Response:", error.response?.data);
          console.error("Error Status:", error.response?.status);

          const errorMessage = error.errors
            ? error.errors.length > 1
              ? error.errors.map((err) => `${err.message},`).join(" ")
              : error.errors[0].message
            : error.message || "Gagal Menyimpan.";

          SweetAlert.error("Error", errorMessage);
        } finally {
          setSubmitting(false);
          resetForm();
        }
      }}
    >
      {({ setFieldValue, values, isSubmitting }) => (
        <Form>
          <Dialog
            open={isDialogOpen}
            onOpenChange={(open) => {
              if (!open) {
                handleDialogClose();
              }
              setIsDialogOpen(open);
            }}
          >
            <DialogTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                className="text-xs text-white"
              >
                Tambah Faq
              </Button>
            </DialogTrigger>

            <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Form Faq </DialogTitle>
              </DialogHeader>

              <form>
                {/* Sektor Nasional Field */}
                <div
                  ref={containerRef}
                  className="grid grid-cols-4 items-center gap-4"
                >
                  <Label className="block text-xs font-medium">
                    Judul <span className="text-[10px] text-red-600">*</span>
                  </Label>
                  <div className="col-span-3 w-full flex flex-col">
                    <Input
                      type="text"
                      placeholder="Masukkan judul"
                      name="title"
                      value={values.title}
                      onChange={(e) => setFieldValue("title", e.target.value)}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="title"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />
                  </div>

                  <Label className="block text-xs font-medium">
                    Deskripsi{" "}
                    <span className="text-[10px] text-red-600">*</span>
                  </Label>
                  <div className="justify-between col-span-3 w-full flex flex-col text-xs">
                    <ReactQuill
                      value={values.description} // Menetapkan nilai saat ini dari form
                      onChange={(content) =>
                        setFieldValue("description", content)
                      } // Mengupdate nilai form
                      placeholder="Keterangan..."
                      className="w-full" // Mengatur lebar Quill editor
                    />
                    <ErrorMessage
                      name="description"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />
                  </div>

                  <div className="col-span-4">
                    <Button
                      type="button"
                      onClick={() => setShowTranslateForm((prev) => !prev)}
                      className="text-xs text-white hover:bg-blue-800"
                    >
                      {showTranslateForm ? "Hide Translate" : "Translate EN"}
                    </Button>

                    {showTranslateForm && (
                      <div
                        ref={containerRef}
                        className="grid grid-cols-4 items-center gap-4"
                      >
                        <Label className="block text-xs font-medium">
                          Judul En{" "}
                          <span className="text-[10px] text-red-600">*</span>
                        </Label>
                        <div className="col-span-3 w-full flex flex-col">
                          <Input
                            type="text"
                            placeholder="Judul [English]"
                            name="title_tr"
                            value={values.title_tr}
                            onChange={(e) =>
                              setFieldValue("title_tr", e.target.value)
                            }
                            className="justify-between col-span-3 w-full text-xs"
                          />
                          <ErrorMessage
                            name="title_tr"
                            component="div"
                            className="text-xs text-red-500 mt-1"
                          />
                        </div>

                        <Label className="block text-xs font-medium">
                          Deskripsi En{" "}
                          <span className="text-[10px] text-red-600">*</span>
                        </Label>
                        <div className="justify-between col-span-3 w-full flex flex-col text-xs">
                          <ReactQuill
                            value={values.description_tr} // Menetapkan nilai saat ini dari form
                            onChange={(content) =>
                              setFieldValue("description_tr", content)
                            } // Mengupdate nilai form
                            placeholder="Keterangan..."
                            className="w-full" // Mengatur lebar Quill editor
                          />
                          <ErrorMessage
                            name="description_tr"
                            component="div"
                            className="text-xs text-red-500 mt-1"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <DialogFooter>
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={isSubmitting}
                    className="bg-blue-500 hover:bg-blue-700 text-white text-xs py-2 px-4 mt-4"
                  >
                    Simpan
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </Form>
      )}
    </Formik>
  );
};

export default CreateFaqForm;
