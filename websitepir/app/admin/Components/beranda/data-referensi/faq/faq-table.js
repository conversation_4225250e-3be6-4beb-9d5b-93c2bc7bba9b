'use client';
import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Edit, Trash } from 'lucide-react';
import { getReferensiService } from '@/services/AllService';
import SweetAlert from '../../../SweetAlert';
import DataTable from '../../../DataTable';
import { deleteFAQService, getDataTableFAQService } from '@/services/DataReferensiService';

const columns = [
  { accessorKey: 'judul', header: 'Judul' },
  { accessorKey: 'deskripsi', header: 'Deskripsi',
    cell: ({ getValue }) => {
      const value = getValue();
      const stripHtml = (text) => text.replace(/<\/?[^>]+(>|$)/g, ""); // Remove HTML tags
      const truncate = (text, maxLength) =>
        text.length > maxLength ? `${text.substring(0, maxLength)}...` : text; // Truncate text
      const cleanText = value ? truncate(stripHtml(value), 100) : ""; // Adjust maxLength as needed
      return cleanText;
    }
  },
];



const ActionsComponent = ({ row, onEdit,onDelete }) => (
  <div className="flex space-x-2">
     <Button variant="secondary" size="sm" style={{ backgroundColor: '#E2B93B', color: 'white' }} className="text-xs"  onClick={() => onEdit(row)}> <Edit className="mr-2 h-4 w-4" /> Edit</Button>
     <Button variant="destructive" size="sm" className="bg-red-500 hover:bg-red-600 text-xs" onClick={() => onDelete(row)}> <Trash className="mr-2 h-4 w-4" /> Delete</Button>
  </div>
);

const FaqTable = ({onEditFaq, refreshTrigger}) => {
  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const [combinedTrigger, setCombinedTrigger] = useState(refreshTrigger);

  useEffect(() => {
    setCombinedTrigger(refreshTrigger + localRefreshTrigger);
  }, [refreshTrigger, localRefreshTrigger]);

  
const handleDelete = async (row) => {
  SweetAlert.confirm(
    "Konfirmasi",
    "Apakah Anda yakin ingin menghapus data ini?",
    async () => {
      try {
        await deleteFAQService(row.id_glosary); 
        SweetAlert.success("Success", "Berhasil menghapus data", () => {
          setLocalRefreshTrigger((prev) => prev + 1);
        });
      } catch (error) {
        SweetAlert.error("Error", "Gagal menghapus data", () => {
          console.error("Error deleting data:", error);
        });
      }
    }
  );
};
  return (
    <DataTable
      columns={columns}
      fetchDataService={getDataTableFAQService}
      pageSize={10}
        actionsComponent={({ row }) => (
          <ActionsComponent
            row={row}
            onEdit={onEditFaq}
            onDelete={handleDelete}
          />
        )}
        filterPlaceholder="Cari..."
        refreshTrigger={combinedTrigger}
    />
  );
};

export default FaqTable;
