"use client";

import <PERSON><PERSON>lert from "../../../SweetAlert";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import {
  createKomoditiNasionalRefService,
  editKomoditiNasionalRefService,
  getByIdKomoditiNasionalRefService,
} from "@/services/DataReferensiService";
import useReferensiStore2 from "@/store/cms/ReferensiStore2";
import useReferensiStore from "@/store/ReferensiStore";
import { ErrorMessage, Form, Formik } from "formik";
import { Check, ChevronsUpDown } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";

function CreateKomoditiNasionalRef({ KomoditiNasionalRefDataEdit, onClose, onDataChange, subSektorNasionalRefOptions, sektorNasionalRefOptions }) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const isEditMode = !!KomoditiNasionalRefDataEdit;
  const [dataEdit, setDataEdit] = useState(null);

  const [selectedSektorNasional, setSelectedSektorNasional] = useState("");
  const [openSektorNasional, setOpenSektorNasional] = useState(false);

  const [selectedSubSektorNasional, setSelectedSubSektorNasional] =
    useState(null);
  const [openSubSektorNasional, setOpenSubSektorNasional] = useState(false);

  const [filteredSubSektorOptions, setFilteredSubSektorOptions] = useState([]);

  
  useEffect(() => {
    if (selectedSektorNasional) {
      const filteredSubSektor = subSektorNasionalRefOptions.filter(
        (subSektor) => subSektor.id_sektor === selectedSektorNasional.id_sektor
      );
      setFilteredSubSektorOptions(filteredSubSektor);
    } else {
      setFilteredSubSektorOptions([]);
    }
  }, [selectedSektorNasional, subSektorNasionalRefOptions]);
  console.log("seknas ref",sektorNasionalRefOptions)
  useEffect(() => {
    const fetchData = async () => {
      if (KomoditiNasionalRefDataEdit) {
        try {
          const response = await getByIdKomoditiNasionalRefService(
            KomoditiNasionalRefDataEdit.id_komoditi
          );
          const data = response.data;
          setDataEdit(data);

          setSelectedSektorNasional(
            (() => {
              const selectedSektorNasional = sektorNasionalRefOptions.find(
              (option) => option.id_sektor === data.tb_sub_sektor_nasional_ref.sektor_nasional_ref.id_sektor
              );
              return selectedSektorNasional || null;
            })()
          );

          setSelectedSubSektorNasional(
            subSektorNasionalRefOptions.find(
              (option) => option.id_sub_sektor === data.id_sub_sektor
            ) || null
          );
          setIsDialogOpen(true);
        } catch (error) {
          console.error("Error fetching data:", error);
        }
      }
    };
    if (KomoditiNasionalRefDataEdit) {
      fetchData();
    }
  }, [KomoditiNasionalRefDataEdit, subSektorNasionalRefOptions]);

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setSelectedSubSektorNasional("");
    setSelectedSektorNasional("");
    onClose();
  };
  console.log("data  edit", dataEdit);

  const initialValues = isEditMode
    ? {
        nama: dataEdit?.nama || "",
        tr: [
          {
            id_komoditi: dataEdit?.id_komoditi || "",
            nama: dataEdit?.tb_komoditi_nasional_ref_tr?.nama || "",
            kd_bahasa: "en",
          },
        ],
        id_sub_sektor: dataEdit?.id_sub_sektor || null,
      }
    : {
        nama: "",
        nama_tr: "",
        id_sub_sektor: null,
        kd_bahasa: "en",
      };

      const containerRef = useRef(null);
  return (
    <Formik
      enableReinitialize={true}
      initialValues={initialValues}
      onSubmit={async (values, { setSubmitting, resetForm }) => {
        console.log("val", values);
        
        try {
          if (isEditMode) {
            await editKomoditiNasionalRefService(values, dataEdit.id_komoditi);
            SweetAlert.success("Success", "Data berhasil diedit.", () => {
              // // window.location.reload();
            });
          } else {
            await createKomoditiNasionalRefService(values);
            SweetAlert.success("Success", "Data berhasil dibuat.", () => {
              // // window.location.reload();
            });
          }
          onDataChange();
          handleDialogClose();
        } catch (error) {
          setIsDialogOpen(false);
          // SweetAlert.error("Error", "Gagal menyimpan data.", () => {
          //   console.error("Error:", error);
          // });
          console.error("Full Error Object:", error);
          console.error("Error Response:", error.response?.data);
          console.error("Error Status:", error.response?.status);

          const errorMessage = error.errors
            ? error.errors.length > 1
              ? error.errors.map((err) => `${err.message},`).join(" ")
              : error.errors[0].message
            : error.message || "Gagal Menyimpan.";

          SweetAlert.error("Error", errorMessage);
        } finally {
          setSubmitting(false);
          resetForm();
        }
      }}
    >
      {({ setFieldValue, values, handleChange, handleBlur, isSubmitting }) => (
        <Form>
          <Dialog
            open={isDialogOpen}
            onOpenChange={(open) => {
              if (!open) {
                handleDialogClose();
              }
              setIsDialogOpen(open);
            }}
          >
            <DialogTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                className="text-xs text-white"
              >
                {isEditMode
                  ? "Edit Komoditi Nasional Ref"
                  : "Tambah Komoditi Nasional Ref"}
              </Button>
            </DialogTrigger>

            <DialogContent className="sm:max-w-[700px] max-h-[80vh]  overflow-visible">
              <DialogHeader>
                <DialogTitle>
                  {isEditMode
                    ? "Edit Komoditi Nasional Ref"
                    : "Tambah Komoditi Nasional Ref"}
                </DialogTitle>
              </DialogHeader>
              <form>
                <div ref={containerRef} className="grid grid-cols-4 items-center gap-4">
                  <Label className="block text-xs font-medium">
                    Sektor Nasional <span className="text-[10px] text-red-600">*</span>
                  </Label>
                  <Popover
                    modal={true}
                    open={openSektorNasional}
                    onOpenChange={setOpenSektorNasional}
                    className="col-span-3 w-full"
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={openSektorNasional}
                        className="justify-between col-span-3 w-full text-xs"
                      >
                        {selectedSektorNasional?.nama ||
                          "Pilih Sektor Nasional"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0" container={containerRef.current}>
                      <Command>
                        <CommandInput placeholder="Cari sektor nasional..." />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {sektorNasionalRefOptions.map((sektor) => (
                              <CommandItem
                                key={sektor.id_sektor}
                                value={sektor.nama}
                                onSelect={() => {
                                  setFieldValue(
                                    "id_sektor_nasional",
                                    sektor.id_sektor
                                  );
                                  setSelectedSektorNasional(sektor);
                                  setOpenSektorNasional(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedSektorNasional?.nama === sektor.nama
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {sektor.nama}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <ErrorMessage
                    name="id_sektor_nasional"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Sub Sektor Nasional <span className="text-[10px] text-red-600">*</span>
                  </Label>
                  <Popover
                    modal={true}
                    open={openSubSektorNasional}
                    onOpenChange={setOpenSubSektorNasional}
                    className="col-span-3 w-full"
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={openSubSektorNasional}
                        className="justify-between col-span-3 w-full text-xs"
                      >
                        {selectedSubSektorNasional?.nama || "Pilih Sub Sektor"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0" container={containerRef.current}>
                      <Command>
                        <CommandInput placeholder="Cari sub sektor..." />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {filteredSubSektorOptions?.length > 0 &&
                              filteredSubSektorOptions.map((subSektor) => (
                                <CommandItem
                                  key={subSektor.id_sub_sektor}
                                  value={subSektor.nama}
                                  onSelect={() => {
                                    setFieldValue(
                                      "id_sub_sektor",
                                      subSektor.id_sub_sektor
                                    );
                                    setSelectedSubSektorNasional(subSektor);
                                    setOpenSubSektorNasional(false);
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      selectedSubSektorNasional?.nama ===
                                        subSektor.nama
                                        ? "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                  {subSektor.nama}
                                </CommandItem>
                              ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>

                  <Label className="block text-xs font-medium">Nama <span className="text-[10px] text-red-600">*</span></Label>
                  <Input
                    type="text"
                    placeholder="Masukkan nama"
                    name="nama"
                    value={values.nama}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className="col-span-3 text-xs"
                  />
                  <ErrorMessage
                    name="nama"
                    component="div"
                    className="text-red-500"
                  />

                  {isEditMode ? (
                    <>
                      <Label className="block text-xs font-medium">
                        Nama [English] <span className="text-[10px] text-red-600">*</span>
                      </Label>
                      <Input
                        type="text"
                        placeholder="Masukkan nama [English]"
                        name="tr[0].nama"
                        value={values.tr?.[0]?.nama || ""}
                        // onChange={(e) => {
                        //   const updatedTr = [...(values.tr || [{ nama: "" }])];
                        //   updatedTr[0].nama = e.target.value;
                        //   setFieldValue("tr", updatedTr);
                        // }}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        className="col-span-3 text-xs"
                      />
                      <ErrorMessage
                        name="tr[0].nama"
                        component="div"
                        className="text-red-500"
                      />
                    </>
                  ) : (
                    <>
                      <Label className="block text-xs font-medium">
                        Nama [English] <span className="text-[10px] text-red-600">*</span>
                      </Label>
                      <Input
                        type="text"
                        placeholder="Masukkan nama [English]"
                        name="nama_tr"
                        value={values.nama_tr || ""}
                        // onChange={(e) => setFieldValue("nama_tr", e.target.value)}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        className="col-span-3 text-xs"
                      />
                      <ErrorMessage
                        name="nama_tr"
                        component="div"
                        className="text-red-500"
                      />
                    </>
                  )}
                </div>

                <DialogFooter className="text-right">
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={isSubmitting}
                    className="bg-blue-500 hover:bg-blue-700 text-white text-xs py-2 px-4 mt-6"
                    onClick={() => console.log("Button clicked")}
                  >
                    Simpan
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </Form>
      )}
    </Formik>
  );
}

export default CreateKomoditiNasionalRef;
