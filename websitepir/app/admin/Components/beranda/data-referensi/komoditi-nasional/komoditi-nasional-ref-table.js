'use client';
import { But<PERSON> } from '@/components/ui/button';
import { deleteUMKMService, getDataTableUMKMService } from '@/services/BerandaService';
import { Edit, Trash } from 'lucide-react';
import React, { useEffect, useState } from 'react'
import DataTable from '../../../DataTable';
import { deleteKomoditiNasionalRefService, deleteSektorNasionalRefService, getDataTableKomoditiNasionalRefService, getDataTableSektorNasionalRefService } from '@/services/DataReferensiService';
import SweetAlert from '../../../SweetAlert';

const columns = [
    {accessorKey: 'nama_sektor', header: 'Nama Sektor' },
    {accessorKey: 'nama_sub_sektor', header: 'Nama Sub Sektor' },
    {accessorKey: 'nama_komoditi', header: '<PERSON><PERSON>' },
    {accessorKey: 'nama_komoditi_tr', header: '<PERSON>a [English]' },
    // {accessorKey: 'nama_file_image', header: 'Image',cell: ({ row }) => <img src={row.original.nama_file_image} alt={row.original.nama_sektor} width={100} /> }
]

  
const ActionsComponent = ({ row, onEdit,onDelete  }) => (
    <div className="flex space-x-2">
      <Button variant="secondary" size="sm" style={{ backgroundColor: '#E2B93B', color: 'white' }} className="text-xs"  onClick={() => onEdit(row)}> <Edit className="mr-2 h-4 w-4" /> Edit</Button>
      <Button variant="destructive" size="sm" className="bg-red-500 hover:bg-red-600 text-xs" onClick={() => onDelete(row)}> <Trash className="mr-2 h-4 w-4" /> Delete</Button>
    </div>
  );

const KomoditiNasionalRefTable =({onEditKomoditiNasionalRef, refreshTrigger}) => {
  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const [combinedTrigger, setCombinedTrigger] = useState(refreshTrigger);

  useEffect(() => {
    setCombinedTrigger(refreshTrigger + localRefreshTrigger);
  }, [refreshTrigger, localRefreshTrigger]);

  
const handleDelete = async (row) => {
  SweetAlert.confirm(
    "Konfirmasi",
    "Apakah Anda yakin ingin menghapus data ini?",
    async () => {
      try {
        await deleteKomoditiNasionalRefService(row.id_komoditi); 
        SweetAlert.success("Success", "Berhasil menghapus data", () => {
          setLocalRefreshTrigger((prev) => prev + 1);
        });
      } catch (error) {
        SweetAlert.error("Error", "Gagal menghapus data", () => {
          console.error("Error deleting data:", error);
        });
      }
    }
  );
};
  return (
    <DataTable
      columns={columns}
      fetchDataService={getDataTableKomoditiNasionalRefService}
      actionsComponent={({row}) => (
        <ActionsComponent row={row} onEdit={onEditKomoditiNasionalRef} onDelete={handleDelete}/>
      )}
      filterPlaceholder="Cari..."
      refreshTrigger={combinedTrigger}
    />
  )
}

export default KomoditiNasionalRefTable;