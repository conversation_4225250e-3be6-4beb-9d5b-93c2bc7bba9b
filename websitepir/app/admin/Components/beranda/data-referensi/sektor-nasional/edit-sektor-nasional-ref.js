"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  createSektorNasionalRefService,
  editSektorNasionalRefService,
  getByIdSektorNasionalRefService,
} from "@/services/DataReferensiService";
import { ErrorMessage, Form, Formik } from "formik";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import SweetAlert from "../../../SweetAlert";
import { Edit } from "lucide-react";
import { getReferensiService } from "@/services/AllService";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Check, ChevronsUpDown } from "lucide-react";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { cn } from "@/lib/utils";

function EditSektorNasionalRef({ id, onStatusChange, kategoriSektorOptions }) {
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false); // State for dialog open/close
  // const [kategoriSektorOptions, setKategoriSektorOptions] = useState([]);
  const [openKategoriSektor, setOpenKategoriSektor] = useState(false);
  const [selectedKategoriSektor, setSelectedKategoriSektor] = useState("");
  // const isEditMode = !!SektorNasionalRefDataEdit;
  const [dataEdit, setDataEdit] = useState("");
  const [imagePreview, setImagePreview] = useState(null);
  const [iconMapPreview, setIconMapPreview] = useState(null);


  const containerRef = useRef(null);

  const fetchData = async () => {
    try {

      const response = await getByIdSektorNasionalRefService(id);
      const dataEdit = response.data;
      setDataEdit(dataEdit);
      setImagePreview(dataEdit?.file_icon || null);
      setIconMapPreview(dataEdit?.file_iconmap || null);

      // Set selected kategori sektor setelah kategori sektor tersedia
      const selectedKategori = kategoriSektorOptions.find(
        (option) => option.id_kategori_sektor === dataEdit.id_kategori_sektor
      );
      setSelectedKategoriSektor(selectedKategori || null);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  }
  const handleDialogOpenChange = (open) => {
    setIsDialogOpen(open);
    if (open) {
      fetchData();
    }
  };


  return (
    <Formik
      enableReinitialize={true}
      initialValues={{
        nama: dataEdit?.nama || "",
        tr: [
          {
            id_sektor:
              dataEdit?.id_sektor ||
              "",
            nama: dataEdit?.sektor_nasional_tr?.nama || "",
            kd_bahasa: "en",
          },
        ],
        icon: dataEdit?.file_icon || null,
        iconmap: dataEdit?.file_iconmap || null,
        id_kategori_sektor: dataEdit?.id_kategori_sektor || null,

      }}
      onSubmit={async (values, { setSubmitting, resetForm }) => {
        console.log(values);
        try {
          await editSektorNasionalRefService(values, dataEdit?.id_sektor);
          setIsDialogOpen(false);
          SweetAlert.success("Success", "Data berhasil di edit", () => {
            onStatusChange();
          });
        } catch (error) {
          setIsDialogOpen(false);
          // SweetAlert.error("Error", "Gagal mengedit sektor nasional ref.", () => {
          //   console.error("Error creating:", error);
          // });
          const errorMessage = Array.isArray(error.messages) && error.messages.length
            ? error.messages.map(err => err.message).join(', ')
            : error.messages || "Gagal Menyimpan.";

          SweetAlert.error("Error", errorMessage);
        } finally {
          setSubmitting(false);
          resetForm();
        }
      }}
    >
      {({ setFieldValue, values, isSubmitting }) => (
        <Form>
          <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
            <DialogTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                className="text-xs text-white"
                style={{ backgroundColor: "#E2B93B", color: "white" }}
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </DialogTrigger>

            <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Edit Sektor Nasional Ref</DialogTitle>
              </DialogHeader>

              <form>
                {/* Sektor Nasional Field */}
                <div ref={containerRef} className="grid grid-cols-4 items-center gap-4">
                  <Label className="block text-xs font-medium">Nama <span className="text-[10px] text-red-600">*</span></Label>
                  <Input
                    type="text"
                    placeholder="Masukkan nama usaha"
                    name="nama"
                    value={values.nama}
                    onChange={(e) => setFieldValue("nama", e.target.value)}
                    className="col-span-3 text-xs"
                  />
                  <ErrorMessage
                    name="nama"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Nama [English] <span className="text-[10px] text-red-600">*</span>
                  </Label>
                  <Input
                    type="text"
                    placeholder="Masukkan nama usaha"
                    name="tr[0].nama" // Gunakan tr[0].nama untuk mengakses objek pertama di array
                    value={values.tr[0]?.nama || ""}
                    onChange={(e) =>
                      setFieldValue("tr[0].nama", e.target.value)
                    }
                    className="col-span-3 text-xs"
                  />
                  <ErrorMessage
                    name="tr[0].nama"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">Kategori Sektor <span className="text-[10px] text-red-600">*</span></Label>
                  <Popover
                    modal={true}
                    open={openKategoriSektor}
                    onOpenChange={setOpenKategoriSektor}
                    className="col-span-3 w-full"
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={openKategoriSektor}
                        className="justify-between col-span-3 w-full text-xs"
                      >
                        {selectedKategoriSektor?.nama || "Pilih Kategori Sektor"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput placeholder="Cari kategori sektor..." />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {kategoriSektorOptions?.length > 0 &&
                              kategoriSektorOptions.map((kategori) => (
                                <CommandItem
                                  key={kategori.id_kategori_sektor}
                                  value={kategori.nama}
                                  onSelect={() => {
                                    // setTouched({ ...values, nama: true });
                                    setFieldValue("id_kategori_sektor", kategori.id_kategori_sektor);
                                    setSelectedKategoriSektor(kategori);
                                    setOpenKategoriSektor(false);
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      selectedKategoriSektor?.nama === kategori.nama ? "opacity-100" : "opacity-0"
                                    )}
                                  />
                                  {kategori.nama}
                                </CommandItem>
                              ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>

                  <Label className="block text-xs font-medium">Image <span className="text-[10px] text-red-600">Max 2Mb</span></Label>
                  <Input
                    type="file"
                    accept="image/jpeg, image/jpg, image/png, image/webp, image/svg+xml"
                    name="icon"
                    onChange={(event) => {
                      const file = event.currentTarget.files[0];
                      setFieldValue("icon", file);

                      const reader = new FileReader();
                      reader.onloadend = () => {
                        setImagePreview(reader.result);
                      };
                      if (file) {
                        reader.readAsDataURL(file);
                      }
                    }}
                    className="col-span-1 text-xs"
                  />
                  <div className=" relative col-span-2 w-80 ml-auto">
                    {imagePreview ? (
                      <Image
                        src={imagePreview}
                        alt="Image Preview"
                        width={320}
                        height={0}
                        layout="responsive"
                        objectFit="cover"
                        className="rounded"
                      />
                    ) : (
                      // Placeholder for empty state to maintain layout
                      <div className="w-full h-full bg-white rounded flex items-center justify-center"></div>
                    )}
                  </div>
                  <ErrorMessage
                    name="icon"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">Icon Map <span className="text-[10px] text-red-600">Max 2Mb</span></Label>
                  <Input
                    type="file"
                    accept="image/jpeg, image/jpg, image/png, image/webp, image/svg+xml"
                    name="iconmap"
                    onChange={(event) => {
                      const file = event.currentTarget.files[0];
                      setFieldValue("iconmap", file);

                      const reader = new FileReader();
                      reader.onloadend = () => {
                        setIconMapPreview(reader.result);
                      };
                      if (file) {
                        reader.readAsDataURL(file);
                      }
                    }}
                    className="col-span-1 text-xs"
                  />
                  <div className=" relative col-span-2 w-80 ml-auto">
                    {iconMapPreview ? (
                      <Image
                        src={iconMapPreview}
                        alt="iconMapPreview"
                        width={320}
                        height={0}
                        layout="responsive"
                        objectFit="cover"
                        className="rounded"
                      />
                    ) : (
                      // Placeholder for empty state to maintain layout
                      <div className="w-full h-full bg-white rounded flex items-center justify-center">

                      </div>
                    )}
                  </div>
                  <ErrorMessage name="iconmap" component="div" className="text-red-500" />
                </div>

                <DialogFooter className="text-right">
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={isSubmitting}
                    className="bg-blue-500 hover:bg-blue-700 text-white text-xs py-2 px-4 mt-6"
                  >
                    Simpan
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </Form>
      )}
    </Formik>
  );
}

export default EditSektorNasionalRef;
