'use client';
import { But<PERSON> } from '@/components/ui/button';
import { deleteUMKMService, getDataTableUMKMService } from '@/services/BerandaService';
import { Edit, Trash } from 'lucide-react';
import React, { useEffect, useState } from 'react'
import DataTable from '../../../DataTable';
import { deleteSektorNasionalRefService, getDataTableSektorNasionalRefService } from '@/services/DataReferensiService';
import EditSektorNasionalRef from './edit-sektor-nasional-ref';
import SweetAlert from '../../../SweetAlert';
import Image from 'next/image';

const columns = [
  { accessorKey: 'nama_sektor', header: 'Nama' },
  { accessorKey: 'nama_sektor_tr', header: 'Nama [English]' },
  { 
      accessorKey: 'nama_file_image', 
      header: 'Image',
      cell: ({ row }) => (
          <Image 
              unoptimized
              src={row.original.nama_file_image} 
              alt={row.original.nama_sektor} 
              width={100} 
              height={100} // Menambahkan height sesuai dengan lebar gambar untuk menjaga rasio
              layout="intrinsic" // Layout intrinsic menjaga rasio gambar saat dibutuhkan
          />
      )
  }
];

  

const SektorNasionalRefTable =({onEditSektorNasionalRef, refreshTrigger, kategoriSektorOptions}) => {
  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const [combinedTrigger, setCombinedTrigger] = useState(refreshTrigger);

  useEffect(() => {
    setCombinedTrigger(refreshTrigger + localRefreshTrigger);
  }, [refreshTrigger, localRefreshTrigger]);

  const handleDelete = async (row) => {
    console.log(row);
    
      SweetAlert.confirm(
        "Konfirmasi",
        "Apakah Anda yakin ingin menghapus data ini?",
        async () => {
          try {
            await deleteSektorNasionalRefService(row.id_sektor_nasional_ref); 
            SweetAlert.success("Success", "Berhasil menghapus data", () => {
              setLocalRefreshTrigger((prev) => prev + 1);
            });
          } catch (error) {
            SweetAlert.error("Error", "Gagal menghapus data", () => {
              console.error("Error deleting data:", error);
            });
          }
        }
      );
    };

    
const ActionsComponent = ({ row, onEdit,onDelete  }) => (
  <div className="flex space-x-2">
    <EditSektorNasionalRef id={row.id_sektor_nasional_ref} onStatusChange={() => setLocalRefreshTrigger((prev) => prev + 1)} kategoriSektorOptions={kategoriSektorOptions}/>
    <Button variant="destructive" size="sm" className="text-xs bg-red-500 hover:bg-red-600" onClick={() => onDelete(row)}> <Trash className="w-4 h-4 mr-2" /> Delete</Button>
  </div>
);
  return (
    <DataTable
      columns={columns}
      fetchDataService={getDataTableSektorNasionalRefService}
      actionsComponent={({row}) => (
        <ActionsComponent row={row} onEdit={onEditSektorNasionalRef} onDelete={handleDelete}/>
      )}
      filterPlaceholder="Cari..."
      refreshTrigger={combinedTrigger} 
    />
  )
}

export default SektorNasionalRefTable;