"use client";

import <PERSON><PERSON><PERSON>t from "../../../SweetAlert";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import {
  createJenisInsentifRefService,
  createKomoditiNasionalRefService,
  editJenisInsentifRefService,
  editKomoditiNasionalRefService,
  getByIdJenisInsentifRefService,
  getByIdKomoditiNasionalRefService,
} from "@/services/DataReferensiService";
import useReferensiStore from "@/store/ReferensiStore";
import dynamic from 'next/dynamic';
const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });
import 'react-quill/dist/quill.snow.css';

import { ErrorMessage, FieldArray, Form, Formik } from "formik";
import { Check, ChevronsUpDown, Edit, PlusCircle, XCircle } from "lucide-react";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import { datetimeRegex } from "zod";

function EditJenisInsentifRef({ jenisInsentifId, onStatusChange, kategoriInsentifOptions }) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const isEditMode = !!jenisInsentifId;
  const [dataEdit, setDataEdit] = useState(null);
  const [showTranslateForm, setShowTranslateForm] = useState(false);
  const [selectedPrioritas, setSelectedPrioritas] = useState("");
  const [openPrioritas, setOpenPrioritas] = useState(false);
  const [selectedKategoriInsentif, setSelectedKategoriInsentif] = useState("");
  const [openKategoriInsentif, setOpenKategoriInsentif] = useState(false);

  const prioritasOptions = [
    { id: 1, nama: "Prioritas" },
    { id: 2, nama: "Non Prioritas" },
  ];

  const [gambarImagePreview, setGambarImagePreview] = useState([]);
  const [gambarImageTrPreview, setGambarImageTrPreview] = useState([]);

  const containerRef = useRef(null);

  const fetchJenisInsentifData = async () => {
    try {
      const response = await getByIdJenisInsentifRefService(jenisInsentifId);
      const data = response.data;
      setDataEdit(data);

      setSelectedKategoriInsentif(
        kategoriInsentifOptions.find(
          (option) => option.id_kategori_insentif === data.id_kategori_insentif
        ) || null
      );
      setSelectedPrioritas(
        prioritasOptions.find((option) => option.id === data.tipe) || null
      );

      const image = data.tb_jenis_insentif_file?.map((file) => file.nama);
      const imageTr = data.tb_jenis_insentif_file_tr?.map((file) => file.nama);
      setGambarImagePreview(image);
      setGambarImageTrPreview(imageTr);
    } catch (error) { }
  };

  const handleDialogOpenChange = (open) => {
    setIsDialogOpen(open);
    if (open) {
      fetchJenisInsentifData();
    }
  };
  console.log("data  edit", dataEdit);

  return (
    <Formik
      enableReinitialize={true}
      initialValues={{
        nama: dataEdit?.nama || "",
        nama_tr: dataEdit?.tb_jenis_insentif_tr?.[0]?.nama || "",
        keterangan: dataEdit?.keterangan || "",
        keterangan_tr: dataEdit?.tb_jenis_insentif_tr?.[0]?.keterangan || "",
        urutan: dataEdit?.urutan || null,
        id_kategori_insentif: dataEdit?.id_kategori_insentif || 0,
        tipe: dataEdit?.tipe || null,

        image:
          dataEdit?.tb_jenis_insentif_file?.length > 0
            ? dataEdit.tb_jenis_insentif_file.image
            : [{ file: null }],
        image_detail:
          dataEdit?.tb_jenis_insentif_file?.length > 0
            ? dataEdit?.tb_jenis_insentif_file?.map((file) => ({
              id_jenis_insentif_file: file.id_jenis_insentif_file || null,
              fileName: file.nama || "",
              judul: file.judul || "",
            }))
            : [
              {
                id_jenis_insentif_file: null, // Tambahkan ID kosong jika tidak ada data
                fileName: "",
                judul: "",
              },
            ],

        image_tr:
          dataEdit?.tb_jenis_insentif_file_tr?.length > 0
            ? dataEdit.tb_jenis_insentif_file_tr.image_tr
            : [{ file: null }],
        image_detail_tr:
          dataEdit?.tb_jenis_insentif_file_tr?.length > 0
            ? dataEdit?.tb_jenis_insentif_file_tr?.map((file) => ({
              id_jenis_insentif_file_tr:
                file.id_jenis_insentif_file_tr || null,
              fileName: file.nama || "",
              judul: file.judul || "",
            }))
            : [
              {
                id_jenis_insentif_file_tr: null, // Tambahkan ID kosong jika tidak ada data
                fileName: "",
                judul: "",
              },
            ],

        tr: {
          id_jenis_insentif_tr:
            dataEdit?.tb_jenis_insentif_tr?.[0]?.id_jenis_insentif_tr || null,
          nama: dataEdit?.tb_jenis_insentif_tr?.[0]?.nama || "",
          keterangan: dataEdit?.tb_jenis_insentif_tr?.[0]?.keterangan || "",
        },
      }}
      onSubmit={async (values, { setSubmitting, resetForm }) => {
        console.log(values);

        try {
          if (isEditMode) {
            await editJenisInsentifRefService(values, jenisInsentifId);
            SweetAlert.success("Success", "Data berhasil diedit.", () => {
              // // window.location.reload();
            });
          } else {
            await createJenisInsentifRefService(values);
            SweetAlert.success("Success", "Data berhasil dibuat.", () => {
              // // window.location.reload();
            });
          }
          onStatusChange();
          setIsDialogOpen(false);
        } catch (error) {
          setIsDialogOpen(false);
          // SweetAlert.error("Error", "Gagal menyimpan data.", () => {
          //   console.error("Error:", error);
          // });
          console.error("Full Error Object:", error);
          console.error("Error Response:", error.response?.data);
          console.error("Error Status:", error.response?.status);

          const errorMessage = error.errors
            ? error.errors.length > 1
              ? error.errors.map((err) => `${err.message},`).join(" ")
              : error.errors[0].message
            : error.message || "Gagal Menyimpan.";

          SweetAlert.error("Error", errorMessage);
        } finally {
          setSubmitting(false);
          resetForm();
        }
      }}
    >
      {({ setFieldValue, values, handleChange, handleBlur, isSubmitting }) => (
        <Form>
          <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
            <DialogTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                style={{ backgroundColor: "#E2B93B", color: "white" }}
                className="flex items-center text-xs"
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </DialogTrigger>

            <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {isEditMode ? "Edit Jenis Insentif" : "Tambah Jenis Insentif"}
                </DialogTitle>
              </DialogHeader>
              <form>
                <div ref={containerRef} className="grid grid-cols-4 items-center gap-4">
                  <Label className="block text-xs font-medium">
                    Nama Insentif
                  </Label>
                  <Input
                    type="text"
                    placeholder="Masukkan nama"
                    name="nama"
                    value={values.nama}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className="col-span-3 text-xs"
                  />
                  <ErrorMessage
                    name="nama"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Keterangan
                  </Label>
                  <div className="justify-between col-span-3 w-full text-xs">
                    <ReactQuill
                      value={values.keterangan} // Menetapkan nilai saat ini dari form
                      onChange={(content) => setFieldValue('keterangan', content)} // Mengupdate nilai form
                      placeholder="Keterangan..."
                      className="w-full" // Mengatur lebar Quill editor
                    />
                  </div>
                  <ErrorMessage
                    name="keterangan"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">Tipe</Label>
                  <Popover open={openPrioritas} onOpenChange={setOpenPrioritas}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="text-xs justify-between col-span-3 w-full"
                      >
                        {selectedPrioritas?.nama || "Pilih Tipe"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0" container={containerRef.current}>
                      <Command>
                        <CommandInput placeholder="Cari Prioritas..." />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {prioritasOptions?.map((prioritas) => (
                              <CommandItem
                                key={prioritas.id}
                                value={prioritas.nama}
                                onSelect={(currentValue) => {
                                  setFieldValue("tipe", prioritas.id);
                                  setSelectedPrioritas(prioritas);
                                  setOpenPrioritas(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedPrioritas?.nama === prioritas.nama
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {prioritas.nama}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <ErrorMessage
                    name="tipe"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Kategori Insentif
                  </Label>
                  <Popover
                    open={openKategoriInsentif}
                    onOpenChange={setOpenKategoriInsentif}
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="text-xs justify-between col-span-3 w-full"
                      >
                        {selectedKategoriInsentif?.nama || "Pilih Tipe"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0" container={containerRef.current}>
                      <Command>
                        <CommandInput placeholder="Cari Prioritas..." />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {kategoriInsentifOptions?.map((kategori) => (
                              <CommandItem
                                key={kategori.id}
                                value={kategori.nama}
                                onSelect={(currentValue) => {
                                  setFieldValue(
                                    "id_kategori_insentif",
                                    kategori.id_kategori_insentif
                                  );
                                  setSelectedKategoriInsentif(kategori);
                                  setOpenKategoriInsentif(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedKategoriInsentif?.nama ===
                                      kategori.nama
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {kategori.nama}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <ErrorMessage
                    name="id_kategori_insentif"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">Urutan</Label>
                  <Input
                    type="text"
                    placeholder="Masukkan urutan"
                    name="urutan"
                    value={values.urutan}
                    onChange={(e) => setFieldValue("urutan", Number(e.target.value))}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage name="urutan" component="div" className="text-red-500" />


                  <span className="text-base bg-blue-600 text-white rounded-lg p-2 mt-4 justify-between col-span-4 w-full">
                    Upload File
                  </span>

                  <Label className="block text-xs font-medium">file <span className="text-[10px] text-red-600">Max 5Mb</span></Label>
                  <FieldArray name="image">
                    {({ push, remove }) => (
                      <div className="justify-between col-span-3 w-full">
                        {values?.image_detail?.map((input, index) => (
                          <div
                            key={index}
                            className="mb-4 flex flex-col space-y-2"
                          >
                            <div className="flex items-center space-x-2">
                              <div className="w-full flex flex-col space-y-2">
                                <Input
                                  name={`image_detail[${index}].judul`}
                                  type="text"
                                  placeholder="Judul Foto"
                                  value={
                                    values.image_detail[index]?.judul || ""
                                  }
                                  onChange={(event) =>
                                    setFieldValue(
                                      `image_detail[${index}].judul`,
                                      event.currentTarget.value
                                    )
                                  }
                                  className="justify-between col-span-3 w-full text-xs"
                                />
                                <ErrorMessage
                                  name={`image_detail[${index}].judul`}
                                  component="div"
                                  className="text-red-500"
                                />

                                <Input
                                  name={`image[${index}]`}
                                  type="file"
                                  accept="image/jpeg, image/jpg, image/png, image/webp, image/svg+xml"
                                  onChange={(event) => {
                                    const file = event.currentTarget.files[0];

                                    // Check file size - 5MB = 5 * 1024 * 1024 bytes
                                    const maxSize = 5 * 1024 * 1024; // 5MB in bytes

                                    if (file && file.size > maxSize) {
                                      alert('File size must be less than 5MB');
                                      event.target.value = ''; // Reset input
                                      return;
                                    }

                                    setFieldValue(`image[${index}]`, file);

                                    const reader = new FileReader();
                                    reader.onloadend = () => {
                                      const updatedPreviews = [...gambarImagePreview];
                                      updatedPreviews[index] = reader.result;
                                      setGambarImagePreview(updatedPreviews);
                                      console.log("Updated previews:", updatedPreviews);
                                    };

                                    if (file) {
                                      reader.readAsDataURL(file);
                                      setFieldValue(
                                        `image_detail[${index}].fileName`,
                                        file.name
                                      );
                                    }
                                  }}
                                  className="justify-between col-span-3 w-full text-xs"
                                />
                                <div className="relative col-span-2 w-80 mr-auto">
                                  {gambarImagePreview[index] ? (
                                    <Image
                                      src={gambarImagePreview[index]}
                                      alt="Image Preview"
                                      width={320}
                                      height={0}
                                      layout="responsive"
                                      objectFit="cover"
                                      className="rounded"
                                    />
                                  ) : (
                                    <div className="w-full h-full bg-white rounded flex items-center justify-center">
                                      {/* Placeholder text */}
                                    </div>
                                  )}
                                </div>
                                <ErrorMessage
                                  name={`image[${index}]`}
                                  component="div"
                                  className="text-red-500"
                                />
                              </div>

                              <div className="flex space-x-2">
                                {index === 0 && (
                                  <Button
                                    type="button"
                                    // onClick={() => push({ judul: '', file: null, keterangan: '' })}
                                    // onClick={() => {
                                    //     push(''); // Push new file input for file
                                    //     setFieldValue(`image_detail[${index + 1}]`, { judul: '', keterangan: '' }); // Push new image_detail with empty fields
                                    // }}
                                    onClick={() => {
                                      push(""); // Push new file input for file
                                      setFieldValue(`image_detail`, [
                                        ...values.image_detail,
                                        { fileName: "", judul: "" },
                                      ]); // Tambahkan elemen baru ke image_detail
                                      setFieldValue(
                                        `image_detail_tr`,
                                        [
                                          ...values.image_detail_tr,
                                          { fileName: "", judul: "" },
                                        ]
                                      );
                                    }}
                                    className="text-xs hover:bg-blue-800"
                                    variant="ghost"
                                    size="icon"
                                  >
                                    <PlusCircle className="h-6 w-6" />
                                  </Button>
                                )}
                                {index !== 0 && (
                                  <Button
                                    type="button"
                                    onClick={() => {
                                      remove(index); // Remove the file input
                                      setFieldValue(
                                        `image_detail`,
                                        values.image_detail.filter(
                                          (_, i) => i !== index
                                        )
                                      );
                                      setFieldValue(
                                        `image_detail_tr`,
                                        values.image_detail_tr.filter(
                                          (_, i) => i !== index
                                        )
                                      );
                                    }}
                                    className="text-red-500"
                                    variant="ghost"
                                    size="icon"
                                  >
                                    <XCircle className="h-6 w-6" />
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </FieldArray>

                  <ErrorMessage
                    name="image_detail"
                    component="div"
                    className="text-red-500"
                  />

                  <div className="col-span-4">
                    <Button
                      type="button"
                      onClick={() => setShowTranslateForm((prev) => !prev)}
                      className="text-xs text-white hover:bg-blue-800"
                    >
                      {showTranslateForm ? "Hide Translate" : "Translate EN"}
                    </Button>

                    {showTranslateForm && (
                      <div ref={containerRef} className="grid grid-cols-4 items-center gap-4">
                        <Label className="block text-xs font-medium">
                          Nama En
                        </Label>
                        <Input
                          type="text"
                          placeholder="Nama [English]"
                          name="tr.nama"
                          value={values?.tr?.nama}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          className="justify-between col-span-3 w-full text-xs"
                        />
                        <ErrorMessage
                          name="nama_tr"
                          component="div"
                          className="text-red-500"
                        />

                        <Label className="block text-xs font-medium">
                          Keterangan En
                        </Label>
                        <div className="justify-between col-span-3 w-full text-xs">
                          <ReactQuill
                            value={values?.tr?.keterangan} // Menetapkan nilai saat ini dari form
                            onChange={(content) => setFieldValue('tr.keterangan', content)} // Mengupdate nilai form
                            placeholder="Keterangan [English]"
                            className="w-full" // Mengatur lebar Quill editor
                          />
                        </div>
                        <ErrorMessage
                          name="keterangan_tr"
                          component="div"
                          className="text-red-500"
                        />

                        <span className="text-base bg-blue-600 text-white rounded-lg p-2 mt-4 justify-between col-span-4 w-full">
                          Upload File En
                        </span>

                        <Label className="block text-xs font-medium">
                          file En <span className="text-[10px] text-red-600">Max 5Mb</span>
                        </Label>
                        <FieldArray name="image_tr">
                          {({ push, remove }) => (
                            <div className="justify-between col-span-3 w-full">
                              {values?.image_detail_tr?.map((input, index) => (
                                <div
                                  key={index}
                                  className="mb-4 flex flex-col space-y-2"
                                >
                                  <div className="flex items-center space-x-2">
                                    <div className="w-full flex flex-col space-y-2">
                                      <Input
                                        name={`image_detail_tr[${index}].judul`}
                                        type="text"
                                        placeholder="Judul Foto [En]"
                                        value={
                                          values.image_detail_tr[index]?.judul || ""
                                        }
                                        onChange={(event) =>
                                          setFieldValue(
                                            `image_detail_tr[${index}].judul`,
                                            event.currentTarget.value
                                          )
                                        }
                                        className="justify-between col-span-3 w-full text-xs"
                                      />
                                      <ErrorMessage
                                        name={`image_detail_tr[${index}].judul`}
                                        component="div"
                                        className="text-red-500"
                                      />

                                      <Input
                                        name={`image_tr[${index}]`}
                                        type="file"
                                        accept="image/jpeg, image/jpg, image/png, image/webp, image/svg+xml"
                                        onChange={(event) => {
                                          const file = event.currentTarget.files[0];

                                          // Check file size - 5MB = 5 * 1024 * 1024 bytes
                                          const maxSize = 5 * 1024 * 1024; // 5MB in bytes

                                          if (file && file.size > maxSize) {
                                            alert('File size must be less than 5MB');
                                            event.target.value = ''; // Reset input
                                            return;
                                          }

                                          setFieldValue(`image_tr[${index}]`, file);

                                          const reader = new FileReader();
                                          reader.onloadend = () => {
                                            const updatedPreviews = [...gambarImageTrPreview];
                                            updatedPreviews[index] = reader.result;
                                            setGambarImageTrPreview(updatedPreviews);
                                            console.log("Updated previews:", updatedPreviews);
                                          };

                                          if (file) {
                                            reader.readAsDataURL(file);
                                            setFieldValue(
                                              `image_detail_tr[${index}].fileName`,
                                              file.name
                                            );
                                          }
                                        }}
                                        className="justify-between col-span-3 w-full text-xs"
                                      />
                                      <div className="relative col-span-2 w-80 mr-auto">
                                        {gambarImageTrPreview[index] ? (
                                          <Image
                                            src={
                                              gambarImageTrPreview[index]
                                            }
                                            alt="Image Preview"
                                            width={320}
                                            height={0}
                                            layout="responsive"
                                            objectFit="cover"
                                            className="rounded"
                                          />
                                        ) : (
                                          <div className="w-full h-full bg-white rounded flex items-center justify-center">
                                            {/* Placeholder text */}
                                          </div>
                                        )}
                                      </div>
                                      <ErrorMessage
                                        name={`image_tr[${index}]`}
                                        component="div"
                                        className="text-red-500"
                                      />
                                    </div>

                                    {/* <div className="flex space-x-2">
                                      {index === 0 && (
                                        <Button
                                          type="button"
                                          // onClick={() => push({ judul: '', file: null, keterangan: '' })}
                                          // onClick={() => {
                                          //     push(''); // Push new file input for file
                                          //     setFieldValue(`image_detail_tr[${index + 1}]`, { judul: '', keterangan: '' }); // Push new image_detail_tr with empty fields
                                          // }}
                                          onClick={() => {
                                            push(""); // Push new file input for file
                                            setFieldValue(
                                              `image_detail_tr`,
                                              [
                                                ...values.image_detail_tr,
                                                { fileName: "", judul: "" },
                                              ]
                                            ); // Tambahkan elemen baru ke image_detail_tr
                                          }}
                                          className="text-xs hover:bg-blue-800"
                                          variant="ghost"
                                          size="icon"
                                        >
                                          <PlusCircle className="h-6 w-6" />
                                        </Button>
                                      )}
                                      {index !== 0 && (
                                        <Button
                                          type="button"
                                          onClick={() => {
                                            remove(index); // Remove the file input
                                            setFieldValue(
                                              `image_detail_tr`,
                                              values.image_detail_tr.filter(
                                                (_, i) => i !== index
                                              )
                                            );
                                          }}
                                          className="text-red-500"
                                          variant="ghost"
                                          size="icon"
                                        >
                                          <XCircle className="h-6 w-6" />
                                        </Button>
                                      )}
                                    </div> */}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </FieldArray>

                        <ErrorMessage
                          name="image_detail_tr"
                          component="div"
                          className="text-red-500"
                        />
                      </div>
                    )}
                  </div>

                  <ErrorMessage
                    name="dokumen"
                    component="div"
                    className="text-red-500"
                  />
                </div>

                <DialogFooter className="text-right">
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={isSubmitting}
                    className="bg-blue-500 hover:bg-blue-700 text-white text-xs py-2 px-4 mt-6"
                    onClick={() => console.log("Button clicked")}
                  >
                    Simpan
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </Form>
      )}
    </Formik>
  );
}

export default EditJenisInsentifRef;
