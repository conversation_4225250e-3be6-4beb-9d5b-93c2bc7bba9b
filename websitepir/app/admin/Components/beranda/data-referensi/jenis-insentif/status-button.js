import { useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button"; // Button dari ShadCN UI
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"; // Modal components dari <PERSON>hadCN <PERSON>I
import { Textarea } from "@/components/ui/textarea";
import { Form, Formik } from "formik";
import useAuthStore from "@/store/AuthStore";
import { editStatusUMRProvinsiService } from "@/services/ProvinsiCMSService";
import SweetAlert from "../../../SweetAlert";
import { editStatusJenisInsentifService } from "@/services/DataReferensiService";

const StatusButton = ({ status, dataEdit, onStatusChange }) => {
  const [showDialog, setShowDialog] = useState(false);
  const roleId = useAuthStore((state) => state.roleId);
  const isDisabled = useMemo(() => roleId !== 1, [roleId]);
  
 
  const getStatusColor = (status) => {
    switch (status) {
      case 99:
        return "bg-[#4ca95e] text-white hover:bg-[#45984f]";
      case -1:
        return "bg-red-500 text-white hover:bg-red-600";
      case 0:
        return "bg-yellow-500 text-white hover:bg-yellow-600";
      default:
        return "bg-gray-500 text-white hover:bg-gray-600";
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 99:
        return "Approved";
      case -1:
        return "Rejected";
      case 0:
        return "Pending";
      default:
        return "Change Status";
    }
  };

  return (
    <Formik
    enableReinitialize={true}
    initialValues={{
      id:
        dataEdit?.id_jenis_insetntif,
      status: dataEdit?.status || 0,
      keterangan: "-",
    }}
    onSubmit={async (values, { setSubmitting, resetForm }) => {
      try {
        const response = await editStatusJenisInsentifService(
          values,
          dataEdit.id_jenis_insetntif
        );
        if (response.success) {
          resetForm();
          setShowDialog(false);
          SweetAlert.success("Success", "Status berhasil di ubah", () => {
            if (onStatusChange) {
              onStatusChange(); // Trigger refresh
            }
          });
        } else {
          SweetAlert.error("Error", "Gagal mengubah status.", () => {
            console.error("Error updating status:", response.message);
          });
        }
      } catch (error) {
        SweetAlert.error("Error", "Gagal mengubah status.", () => {
          console.error("Error updating status:", error);
        });
      }
    }}
  >
    {({ setFieldValue, handleSubmit }) => (
      <Form>
        <Dialog open={showDialog} onOpenChange={setShowDialog}>
          <DialogTrigger asChild>
            <Button
              className={`${getStatusColor(
                dataEdit?.status
              )} text-xs flex items-center`}
              variant="outline"
              disabled={isDisabled} 
            >
              {getStatusText(dataEdit?.status)}
            </Button>
          </DialogTrigger>

          {/* Modal untuk mengubah status */}
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Ubah Status</DialogTitle>
              <DialogDescription>
                {dataEdit?.status === 0
                  ? "Status saat ini pending. Silahkan pilih untuk menyetujui atau menolak."
                  : dataEdit?.status === 99
                  ? "Status saat ini sudah disetujui. Anda hanya dapat menolak status."
                  : "Status saat ini belum disetujui atau ditolak. Anda dapat memilih untuk menyetujui."}
              </DialogDescription>
            </DialogHeader>

              <Textarea
                onChange={(e) => {
                  setFieldValue("keterangan", e.target.value);
                }}
                placeholder="Tulis alasan jika Reject"
              />

            <DialogFooter className="space-x-2">
              <Button variant="outline" onClick={() => setShowDialog(false)}>
                Cancel
              </Button>

              {/* Tampilkan kedua tombol Approve dan Reject jika status Pending */}
              {dataEdit?.status === 0 && (
                <>
                  <Button
                    variant="danger"
                    type="button"
                    className="bg-red-500 hover:bg-red-600 text-white"
                    onClick={() => {
                      setFieldValue("status", -1);
                      handleSubmit();
                    }}
                  >
                    Reject
                  </Button>
                  <Button
                    variant="success"
                    type="button"
                    className="bg-green-500 hover:bg-green-600 text-white"
                    onClick={() => {
                      setFieldValue("status", 99);
                      handleSubmit();
                    }}
                  >
                    Approve
                  </Button>
                </>
              )}

              {/* Tampilkan tombol Reject hanya jika status sudah Approved */}
              {dataEdit?.status === 99 && (
                <Button
                  variant="danger"
                  type="button"
                  className="bg-red-500 hover:bg-red-600 text-white"
                  onClick={() => {
                    setFieldValue("status", -1);
                    handleSubmit();
                  }}
                >
                  Reject
                </Button>
              )}

              {/* Tampilkan tombol Approve hanya jika status Rejected */}
              {dataEdit?.status === -1 && (
                <Button
                  variant="success"
                  type="button"
                  className="bg-green-500 hover:bg-green-600 text-white"
                  onClick={() => {
                    setFieldValue("status", 99);
                    handleSubmit();
                  }}
                >
                  Approve
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </Form>
    )}
  </Formik>
  );
};

export default StatusButton;
