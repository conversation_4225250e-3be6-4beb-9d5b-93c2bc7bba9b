'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Edit, Trash } from 'lucide-react';
import React, { useEffect, useState } from 'react'
import DataTable from '../../../DataTable';
import { deleteJenisInsentifRefService, getDataTableJenisInsentifRefService } from '@/services/DataReferensiService';
import SweetAlert from '../../../SweetAlert';
import EditJenisInsentifRef from './edit-jenis-insentif-ref';
import StatusButton from './status-button';



const JenisInsentifRefTable =({onEditJenisInsentifRef, refreshTrigger, kategoriInsentifOptions}) => {
  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const [combinedTrigger, setCombinedTrigger] = useState(refreshTrigger);

  useEffect(() => {
    setCombinedTrigger(refreshTrigger + localRefreshTrigger);
  }, [refreshTrigger, localRefreshTrigger]);

  
const handleDelete = async (row) => {
  SweetAlert.confirm(
    "Konfirmasi",
    "Apakah Anda yakin ingin menghapus data ini?",
    async () => {
      try {
        await deleteJenisInsentifRefService(row.id_jenis_insetntif); 
        SweetAlert.success("Success", "Berhasil menghapus data", () => {
          setLocalRefreshTrigger((prev) => prev + 1);
        });
      } catch (error) {
        SweetAlert.error("Error", "Gagal menghapus data", () => {
          console.error("Error deleting data:", error);
        });
      }
    }
  );
};


const columns = [
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => (
      <StatusButton
        status={row.original.status}
        dataEdit={row.original}
        onStatusChange={() => setLocalRefreshTrigger((prev) => prev + 1)} // Trigger refresh
      />
    ),
  },
  {accessorKey: 'nama', header: 'nama' },
  {
    accessorKey: 'keterangan',
    header: 'Keterangan',
    cell: ({ getValue }) => {
      const value = getValue();
      const stripHtml = (text) => text.replace(/<\/?[^>]+(>|$)/g, ""); // Remove HTML tags
      const truncate = (text, maxLength) =>
        text.length > maxLength ? `${text.substring(0, maxLength)}...` : text; // Truncate text
      const cleanText = value ? truncate(stripHtml(value), 100) : ""; // Adjust maxLength as needed
      return cleanText;
    }
  },
  
  // {accessorKey: 'nama_file_image', header: 'Image',cell: ({ row }) => <img src={row.original.nama_file_image} alt={row.original.nama_sektor} width={100} /> }
]

const ActionsComponent = ({ row, onEdit,onDelete  }) => (
  <div className="flex space-x-2">
   <EditJenisInsentifRef jenisInsentifId = {row.id_jenis_insetntif} onStatusChange={() => setLocalRefreshTrigger((prev) => prev + 1)} kategoriInsentifOptions={kategoriInsentifOptions}/>
    <Button variant="destructive" size="sm" className="bg-red-500 hover:bg-red-600 text-xs" onClick={() => onDelete(row)}> <Trash className="mr-2 h-4 w-4" /> Delete</Button>
  </div>
);

  return (
    <DataTable
      columns={columns}
      fetchDataService={getDataTableJenisInsentifRefService}
      actionsComponent={({row}) => (
        <ActionsComponent row={row} onEdit={onEditJenisInsentifRef} onDelete={handleDelete}/>
      )}
      filterPlaceholder="Cari..."
      refreshTrigger={combinedTrigger} 
      exportOptions={{
        orientation: "portrait",
        columnStyles: {
          0: { cellWidth: 40 },
        },
      }}
    />
  )
}

export default JenisInsentifRefTable;