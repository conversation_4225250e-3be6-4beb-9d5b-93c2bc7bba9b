"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { getReferensiService } from "@/services/AllService";
import { createArtikelService } from "@/services/BerandaService";
import dynamic from 'next/dynamic';
const ReactQuill = dynamic(() => import('react-quill'), { ssr: false }); // Dynamically import Quill

// Make sure to import Quill styles
import 'react-quill/dist/quill.snow.css'; // You can use other themes as well



import { Formik, ErrorMessage, Form, FieldArray } from "formik";
import { Check, ChevronsUpDown, Trash } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import * as Yup from "yup";

const ArtikelForm = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [showTranslateForm, setShowTranslateForm] = useState(false);
  const [openPrioritas, setOpenPrioritas] = useState(false);
  // const [sliderData, setSliderData] = useState(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false); // State for dialog open/close

  const [selectedPrioritas, setSelectedPrioritas] = useState("");
  const [openRelasiPeluang, setOpenRelasiPeluang] = useState(false);
  const [selectedRelasiPeluang, setSelectedRelasiPeluang] = useState("");
  const [relasiPeluangOptions, setRelasiPeluangOptions] = useState([]);
  const prioritasOptions = [
    { id: 1, nama: "Prioritas" },
    { id: 2, nama: "Daerah" },
    { id: 3, nama: "Prioritas & IKN" },
  ];
  const containerRef = useRef(null);

  useEffect(() => {
    async function fetchData() {
      const cachedData = localStorage.getItem("dropdownDataArtikel");
      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        setRelasiPeluangOptions(parsedData.relasiPeluangOptions);
        setIsLoading(false);
      } else {
        try {
          const [res_tb_peluang_kabkot] = await Promise.all([
            getReferensiService("tb_peluang_kabkot"),
          ]);

          const fetchedData = {
            relasiPeluangOptions: res_tb_peluang_kabkot.data,
          };

          localStorage.setItem("dropdownData", JSON.stringify(fetchedData));

          setRelasiPeluangOptions(fetchedData.relasiPeluangOptions);
        } catch (error) {
          console.error("Error fetching data:", error);
        } finally {
          setIsLoading(false);
        }
      }
    }

    fetchData();
  }, []);

  const handleFormSubmit = async (values) => {
    try {
      console.log("Data Artikel submitted:", values);
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  return (
    <Formik
      enableReinitialize={true}
      initialValues={{
        judul: "",
        deskripsi_singkat: "",
        deskripsi: "",
        judul_tr: "",
        deskripsi_singkat_tr: "",
        deskripsi_tr: "",

        // id_peluang: [],
        id_peluang: null,

        dokumen: "",
        cover: null,
      }}
      onSubmit={async (values, { setSubmitting, resetForm }) => {
        console.log(values);
        try {
          await createArtikelService(values);
          // await createSliderBerandaService(formData); // Call API to save data
          // // window.location.reload(); // Reload page after successful save (or handle success feedback)
          // if (isEditMode) {
          //     // Update existing slider
          //     await editVideoService(values, videoDataEdit.id_vidio); // Use the ID of the slider being edited
          //     console.log("berhasil edit")
          // } else {
          //     // Create new slider
          //     await createVideoService(values); // Call API to save data
          // } handleDialogClose();
        } catch (error) {
          console.error("Error creating News:", error);
        } finally {
          setSubmitting(false);
          resetForm(); // Reset form after submission
        }
      }}
    >
      {({ setFieldValue, values, isSubmitting }) => (
        <Form>
          <Dialog>
            {/* <Dialog open={isDialogOpen} onOpenChange={(open) => {
                        if (!open) {
                            // Reset the slider data when the dialog is closed
                            onClose();
                        }
                        setIsDialogOpen(open);
                    }}> */}
            <DialogTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                className="text-xs text-white"
              >
                Tambah Artikel
              </Button>
            </DialogTrigger>

            <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Form Artikel </DialogTitle>
              </DialogHeader>

              <form>
                {/* Sektor Nasional Field */}
                <div ref={containerRef} className="grid grid-cols-4 items-center gap-4">
                  <Label className="block text-xs font-medium">
                    Judul Berita
                  </Label>
                  <Input
                    type="text"
                    placeholder="Masukkan judul"
                    name="judul"
                    value={values.judul}
                    onChange={(e) => setFieldValue("judul", e.target.value)}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="judul"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Deskripsi Singkat
                  </Label>
                  <Input
                    type="text"
                    placeholder="Masukkan deskripsi singkat"
                    name="deskripsi_singkat"
                    value={values.deskripsi_singkat}
                    onChange={(e) =>
                      setFieldValue("deskripsi_singkat", e.target.value)
                    }
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="deskripsi_singkat"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">Deskripsi</Label>
                   <div className="justify-between col-span-3 w-full text-xs">
                    <ReactQuill
                      value={values.deskripsi} // Menetapkan nilai saat ini dari form
                      onChange={(content) => setFieldValue('deskripsi', content)} // Mengupdate nilai form
                      placeholder="Keterangan..."
                      className="w-full" // Mengatur lebar Quill editor
                    />
                  </div>
                  <ErrorMessage
                    name="deskripsi"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">Kategori</Label>
                  <Popover open={openPrioritas} onOpenChange={setOpenPrioritas}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="text-xs justify-between col-span-3 w-full"
                      >
                        {selectedPrioritas?.nama || "Pilih Kategori"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0" container={containerRef.current}>
                      <Command>
                        <CommandInput placeholder="Cari Prioritas..." />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {prioritasOptions?.map((prioritas) => (
                              <CommandItem
                                key={prioritas.id}
                                value={prioritas.nama}
                                onSelect={(currentValue) => {
                                  setFieldValue("id_prioritas", prioritas.id);
                                  setSelectedPrioritas(prioritas);
                                  setOpenPrioritas(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedPrioritas?.nama === prioritas.nama
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {prioritas.nama}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <ErrorMessage
                    name="prioritas"
                    component="div"
                    className="text-red-500"
                  />

                  <DialogHeader className="inline-block bg-orange-500 text-white py-2 px-4 rounded font-semibold">
                    <DialogTitle className="inline">Relasi Peluang</DialogTitle>
                  </DialogHeader>
                </div>
                <div ref={containerRef} className="grid grid-cols-4 items-center gap-4">
                  <Label className="block text-xs font-medium">
                    Relasi Peluang
                  </Label>
                  <Popover
                    open={openRelasiPeluang}
                    onOpenChange={setOpenRelasiPeluang}
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="text-xs justify-between col-span-3 w-full"
                      >
                        {selectedRelasiPeluang.nama || "Pilih Sumber Data"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0" container={containerRef.current}>
                      <Command>
                        <CommandInput
                          placeholder="Cari sumber data..."
                          onChange={(e) => setSearchTerm(e.target.value)} // Update search term on input change
                        />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {relasiPeluangOptions?.length > 0 &&
                              relasiPeluangOptions.map((relasi) => (
                                <CommandItem
                                  key={relasi.id_peluang_kabkot}
                                  value={relasi.nama}
                                  onSelect={(currentValue) => {
                                    setFieldValue(
                                      "id_peluang",
                                      relasi.id_peluang_kabkot
                                    );
                                    setSelectedRelasiPeluang(relasi);
                                    setOpenRelasiPeluang(false);
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      selectedRelasiPeluang?.nama ===
                                        relasi.nama
                                        ? "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                  {relasi.nama}
                                </CommandItem>
                              ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  {/* 
                                    <FieldArray name="id_peluang">
                                        {({ push, remove }) => (
                                            <Popover open={openRelasiPeluang} onOpenChange={setOpenRelasiPeluang}>
                                                <PopoverTrigger asChild>
                                                    <Button variant="outline" className="justify-between col-span-3 w-full text-xs">

                                                        Pilih Relasi Peluang
                                                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                                                    </Button>
                                                </PopoverTrigger>
                                                <PopoverContent className="w-full max-w-[400px] p-0">
                                                    <Command>
                                                        <CommandInput placeholder="Cari relasi peluang..." />
                                                        <CommandList>
                                                            <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                                                            {relasiPeluangOptions.map(peluang => (
                                                                <CommandItem
                                                                    key={peluang.id_peluang_kabkot}
                                                                    onSelect={() => {
                                                                        const selectedIndex = values.id_peluang.indexOf(peluang.id_peluang_kabkot);
                                                                        if (selectedIndex === -1) {
                                                                            // Menambahkan insentif yang dipilih
                                                                            push(peluang.id_peluang_kabkot);
                                                                        } else {
                                                                            // Menghapus insentif yang sudah dipilih
                                                                            remove(selectedIndex);
                                                                        }
                                                                    }}
                                                                >
                                                                    <Check
                                                                        className={cn("mr-2 h-4 w-4", values.id_peluang.includes(peluang.id_peluang_kabkot) ? "opacity-100" : "opacity-0")}
                                                                    />
                                                                    {peluang.nama}
                                                                </CommandItem>
                                                            ))}
                                                        </CommandList>
                                                    </Command>
                                                </PopoverContent>

                                                {values.id_peluang.length > 0 && (
                                                    <div className="justify-between col-span-3 w-full text-xs">
                                                        <h4 className="font-semibold">Relasi Peluang Terpilih:</h4>
                                                        <ul>
                                                            {values.id_peluang.map(id => {
                                                                const peluang = relasiPeluangOptions.find(option => option.id_peluang_kabkot === id);
                                                                return peluang ? (
                                                                    <li key={peluang.id_peluang_kabkot} className="flex justify-between items-center py-1">
                                                                        <span>{peluang.nama}</span>
                                                                        <Button
                                                                            variant="ghost"
                                                                            onClick={() => remove(values.id_peluang.indexOf(id))}
                                                                            className="text-red-500"
                                                                        >
                                                                            <Trash className="h-4 w-4" />
                                                                        </Button>
                                                                    </li>
                                                                ) : null;
                                                            })}
                                                        </ul>
                                                    </div>
                                                )}

                                            </Popover>
                                        )}
                                    </FieldArray> */}
                  <ErrorMessage
                    name="sektor_peluang"
                    component="div"
                    className="text-red-500"
                  />

                  <hr className="my-4 border-t border-gray-300 col-span-4" />

                  <Label className="block text-xs font-medium">
                    Upload dokumen
                  </Label>
                  <Input
                    type="file"
                    accept="application/dokumen"
                    name="dokumen"
                    onChange={(event) => {
                      const file = event.currentTarget.files[0];
                      setFieldValue("dokumen", file);
                    }}
                    className="col-span-3 text-xs"
                  />
                  <ErrorMessage
                    name="dokumen"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Upload Cover
                  </Label>
                  <Input
                    type="file"
                    accept="image/jpeg"
                    name="cover"
                    onChange={(event) => {
                      const file = event.currentTarget.files[0];
                      setFieldValue("cover", file);
                    }}
                    className="col-span-3 text-xs"
                  />
                  <ErrorMessage
                    name="cover"
                    component="div"
                    className="text-red-500"
                  />

                  <div className="col-span-4">
                    <Button
                      type="button"
                      onClick={() => setShowTranslateForm((prev) => !prev)}
                      className="text-xs text-white hover:bg-blue-800"
                    >
                      {showTranslateForm ? "Hide Translate" : "Translate EN"}
                    </Button>

                    {showTranslateForm && (
                      <div ref={containerRef} className="grid grid-cols-4 items-center gap-4">
                        <Label className="block text-xs font-medium">
                          Judul Berita
                        </Label>
                        <Input
                          type="text"
                          placeholder="Judul [English]"
                          name="judul_tr"
                          value={values.judul_tr}
                          onChange={(e) =>
                            setFieldValue("judul_tr", e.target.value)
                          }
                          className="justify-between col-span-3 w-full text-xs"
                        />
                        <ErrorMessage
                          name="judul_tr"
                          component="div"
                          className="text-red-500"
                        />

                        <Label className="block text-xs font-medium">
                          Deskripsi Singkat
                        </Label>
                        <Input
                          type="text"
                          placeholder="Deskripsi singkat [English]"
                          name="deskripsi_singkat_tr"
                          value={values.deskripsi_singkat_tr}
                          onChange={(e) =>
                            setFieldValue(
                              "deskripsi_singkat_tr",
                              e.target.value
                            )
                          }
                          className="justify-between col-span-3 w-full text-xs"
                        />
                        <ErrorMessage
                          name="deskripsi_singkat_tr"
                          component="div"
                          className="text-red-500"
                        />

                        <Label className="block text-xs font-medium">
                          Deskripsi
                        </Label>
                          <div className="justify-between col-span-3 w-full text-xs">
                          <ReactQuill
                            value={values.deskripsi_tr} // Menetapkan nilai saat ini dari form
                            onChange={(content) => setFieldValue('deskripsi_tr', content)} // Mengupdate nilai form
                            placeholder="Keterangan en..."
                            className="w-full" // Mengatur lebar Quill editor
                          />
                        </div>
                        <ErrorMessage
                          name="deskripsi_tr"
                          component="div"
                          className="text-red-500"
                        />
                      </div>
                    )}
                  </div>
                </div>

                <DialogFooter>
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={isSubmitting}
                    className="bg-blue-500 hover:bg-blue-700 text-white text-xs py-2 px-4 "
                  >
                    Simpan
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </Form>
      )}
    </Formik>
  );
};

export default ArtikelForm;
