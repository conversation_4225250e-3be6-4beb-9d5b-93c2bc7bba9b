'use client';
import React from 'react';
import { Button } from '@/components/ui/button';
import DataTable2 from '@/app/admin/Components/DataTable2';

// Dummy data for Artikel
const dummyArtikelData = [
  {
    judul: 'Pengembangan Infrastruktur Hijau',
    deskripsi: 'Artikel mengenai pentingnya infrastruktur hijau dalam pembangunan berkelanjutan.',
    kategori: 'Renstra',
    status: 'Published',
  },
  {
    judul: 'Kebijakan Energi Terbarukan',
    deskripsi: 'Kebijakan pemerintah tentang energi terbarukan di Indonesia.',
    kategori: 'Kebijakan',
    status: 'Draft',
  },
  {
    judul: 'Realisasi Anggaran Ke<PERSON>hatan',
    deskripsi: 'Laporan mengenai realisasi anggaran di sektor kesehatan tahun ini.',
    kategori: 'Realisasi',
    status: 'Published',
  },
  {
    judul: 'Rencana Umum Pengelolaan Minyak',
    deskripsi: 'Strategi pengelolaan minyak dalam menghadapi tantangan global.',
    kategori: 'RUPM',
    status: 'Published',
  },
  {
    judul: 'Strategi Nasional Ketahanan Pangan',
    deskripsi: 'Renstra mengenai ketahanan pangan di Indonesia.',
    kategori: 'Renstra',
    status: 'Draft',
  },
  {
    judul: 'Kebijakan Perlindungan Lingkungan',
    deskripsi: 'Kebijakan terkait perlindungan lingkungan dan pemulihan ekosistem.',
    kategori: 'Kebijakan',
    status: 'Published',
  },
  {
    judul: 'Realisasi Pembangunan Infrastruktur',
    deskripsi: 'Laporan realisasi proyek infrastruktur.',
    kategori: 'Realisasi',
    status: 'Published',
  },
  {
    judul: 'RUPM Sumber Daya Alam',
    deskripsi: 'Rencana pengelolaan sumber daya alam untuk lima tahun ke depan.',
    kategori: 'RUPM',
    status: 'Draft',
  },
  {
    judul: 'Peningkatan Kualitas Pendidikan',
    deskripsi: 'Renstra untuk meningkatkan kualitas pendidikan di Indonesia.',
    kategori: 'Renstra',
    status: 'Published',
  },
  {
    judul: 'Kebijakan Pengembangan Industri',
    deskripsi: 'Kebijakan pemerintah untuk mendukung pertumbuhan industri nasional.',
    kategori: 'Kebijakan',
    status: 'Draft',
  },
];

// Define columns for Artikel table
const artikelColumns = [
  { accessorKey: 'judul', header: 'Judul' },
  { accessorKey: 'deskripsi', header: 'Deskripsi' },
  { accessorKey: 'kategori', header: 'Kategori' },
  { accessorKey: 'status', header: 'Status' },
];

// Actions component for edit and delete buttons
const ArtikelActionsComponent = ({ row }) => (
  <div className="flex space-x-2">
    <Button variant="outline" size="sm">Edit</Button>
    <Button variant="destructive" size="sm">Delete</Button>
  </div>
);

// Fetch service to simulate data retrieval
const fetchArtikelDataService = async () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(dummyArtikelData);
    }, 500); // Simulate API call delay
  });
};

const ArtikelTable = () => {
  return (
    <>
      <DataTable2
        columns={artikelColumns}
        fetchDataService={fetchArtikelDataService}
        actionsComponent={ArtikelActionsComponent}
        filterPlaceholder="Cari Artikel..."
      />
    </>
  );
};

export default ArtikelTable;
