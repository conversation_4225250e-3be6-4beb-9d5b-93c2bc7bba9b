// import React, { useEffect, useState } from "react";
// import { getByIdStatusDataService } from "@/services/Dashboard";
// import useSelectedDataStore from "@/store/cms/SelectedDataStore";
// import { BarChart, Bar, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from "recharts";

// const StatusDataProv = () => {
//   const { selectedUserId } = useSelectedDataStore();
//   const [statusData, setStatusData] = useState([]);
//   const [loading, setLoading] = useState(false);

//   useEffect(() => {
//     const fetchStatusData = async () => {
//       setLoading(true);
//       try {
//         const id = selectedUserId || "";
//         const response = await getByIdStatusDataService(id);
//         setStatusData(response || []);
//       } catch (error) {
//         console.error("Error fetching status data:", error);
//         setStatusData([]);
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchStatusData();
//   }, [selectedUserId]);

//   // Get all unique years from the data
//   const getUniqueYears = () => {
//     const years = new Set();
    
//     statusData.forEach(category => {
//       const categoryName = Object.keys(category)[0];
//       category[categoryName].forEach(item => {
//         years.add(item.tahun);
//       });
//     });
    
//     return Array.from(years).sort((a, b) => b - a); // Sort years in descending order
//   };

//   if (loading) return <div>Loading status data...</div>;
//   if (!statusData.length) return <div>No status data available.</div>;

//   const uniqueYears = getUniqueYears();

//   return (
//     <div className="p-4 bg-gray-100 rounded-md">
//       {/* Bar Charts Section */}
//       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
//         {statusData.map((kategori, index) => {
//           const kategoriNama = Object.keys(kategori)[0]; // Nama kategori (DEMOGRAFI, EKSPOR/IMPOR, dll.)
//           const dataChart = kategori[kategoriNama];

//           return (
//             <div key={index} className="bg-white p-2 shadow-md rounded-lg">
//               <h4 className="text-sm font-semibold text-gray-700 mb-1">{kategoriNama}</h4>
//               <ResponsiveContainer width="100%" height={200}>
//                 <BarChart data={dataChart} barCategoryGap="20%">
//                   <XAxis dataKey="tahun" tick={{ fontSize: 10 }} />
//                   <YAxis tick={{ fontSize: 10 }} />
//                   <Tooltip wrapperStyle={{ fontSize: "10px" }} />
//                   <Legend wrapperStyle={{ fontSize: "10px" }} />
//                   <Bar dataKey="ada" stackId="a" fill="#28A745" name="Update" barSize={15} />
//                   <Bar dataKey="tidak_ada" stackId="a" fill="#DC3545" name="Belum Update" barSize={15} />
//                 </BarChart>
//               </ResponsiveContainer>
//             </div>
//           );
//         })}
//       </div>

//       {/* Summary Table Section */}
//       <div className="overflow-x-auto bg-white rounded-lg shadow">
//         <table className="min-w-full divide-y divide-gray-200">
//           <thead className="bg-gray-50">
//             <tr>
//               {/* Using rowSpan=2 to merge the cells vertically */}
//               <th scope="col" rowSpan="2" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-r align-middle">
//                 Nama Kategori
//               </th>
//               <th scope="col" rowSpan="2" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-r align-middle">
//                 Total Provinsi
//               </th>
//               <th scope="col" colSpan={uniqueYears.length} className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b ">
//                 Data Update
//               </th>
//               <th scope="col" colSpan={uniqueYears.length} className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
//                 Data Belum Update
//               </th>
//             </tr>
//             <tr>
//               {uniqueYears.map(year => (
//                 <th key={`update-${year}`} scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b ">
//                   {year}
//                 </th>
//               ))}
//               {uniqueYears.map(year => (
//                 <th key={`notupdate-${year}`} scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b ">
//                   {year}
//                 </th>
//               ))}
//             </tr>
//           </thead>
//           <tbody className="bg-white divide-y divide-gray-200 text-xs">
//             {statusData.map((kategori, index) => {
//               const kategoriNama = Object.keys(kategori)[0];
//               const dataByYear = {};
              
//               // Organize data by year for easier access
//               kategori[kategoriNama].forEach(item => {
//                 dataByYear[item.tahun] = item;
//               });
              
//               return (
//                 <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
//                   <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r">
//                     {kategoriNama}
//                   </td>
//                   <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center border-r">
//                     {/* Display a single Total Provinsi value based on the first year's data */}
//                     {uniqueYears.length > 0 && dataByYear[uniqueYears[0]] ? 
//                       dataByYear[uniqueYears[0]].ada + dataByYear[uniqueYears[0]].tidak_ada : 
//                       '-'}
//                   </td>
                  
//                   {/* Data Update columns */}
//                   {uniqueYears.map(year => (
//                     <td key={`update-${year}`} className="px-4 py-4 whitespace-nowrap text-sm text-center border-r">
//                       {dataByYear[year] ? (
//                         <span>
//                           {dataByYear[year].ada} 
//                         </span>
//                       ) : '-'}
//                     </td>
//                   ))}
                  
//                   {/* Data Belum Update columns */}
//                   {uniqueYears.map(year => (
//                     <td key={`notupdate-${year}`} className="px-4 py-4 whitespace-nowrap text-sm text-center border-r">
//                       {dataByYear[year] ? (
//                         <span>
//                           {dataByYear[year].tidak_ada}
//                         </span>
//                       ) : '-'}
//                     </td>
//                   ))}
//                 </tr>
//               );
//             })}
//           </tbody>
//         </table>
//       </div>
//     </div>
//   );
// };

// export default StatusDataProv;


import React, { useEffect, useState, useRef } from "react";
import { getByIdStatusDataService } from "@/services/Dashboard";
import useSelectedDataStore from "@/store/cms/SelectedDataStore";
import { BarChart, Bar, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from "recharts";
import { Printer } from "lucide-react";

const StatusDataProv = () => {
  const { selectedUserId } = useSelectedDataStore();
  const [statusData, setStatusData] = useState([]);
  const [loading, setLoading] = useState(false);
  const printableContentRef = useRef(null);

  useEffect(() => {
    const fetchStatusData = async () => {
      setLoading(true);
      try {
        const id = selectedUserId || "";
        const response = await getByIdStatusDataService(id);
        setStatusData(response || []);
      } catch (error) {
        console.error("Error fetching status data:", error);
        setStatusData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchStatusData();
  }, [selectedUserId]);

  // Get all unique years from the data
  const getUniqueYears = () => {
    const years = new Set();
    
    statusData.forEach(category => {
      const categoryName = Object.keys(category)[0];
      category[categoryName].forEach(item => {
        years.add(item.tahun);
      });
    });
    
    return Array.from(years).sort((a, b) => b - a); // Sort years in descending order
  };

  // Handle print functionality
  const handlePrint = () => {
    const printWindow = window.open('', '_blank', 'height=600,width=800');
    
    if (!printWindow) {
      alert('Popup blocker might be preventing the print window. Please allow popups for this site.');
      return;
    }

    // Get the current date for the report header
    const currentDate = new Date().toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });

    // Create print content with styles
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Laporan Status Data Provinsi</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              padding: 20px;
              color: #333;
            }
            .header {
              text-align: center;
              margin-bottom: 20px;
              padding-bottom: 10px;
              border-bottom: 1px solid #ddd;
            }
            .page-break {
              page-break-before: always;
            }
            .charts-container {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 15px;
              margin-bottom: 30px;
            }
            .chart-item {
              background: #f9f9f9;
              border: 1px solid #ddd;
              border-radius: 5px;
              padding: 10px;
              box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            .chart-title {
              font-weight: bold;
              margin-bottom: 10px;
              font-size: 14px;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 20px;
              font-size: 12px;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: center;
            }
            th {
              background-color: #f2f2f2;
            }
            tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            .text-left {
              text-align: left;
            }
            .footer {
              margin-top: 30px;
              text-align: center;
              font-size: 12px;
              color: #666;
            }
            @media print {
              .charts-container {
                page-break-inside: avoid;
              }
              table { 
                page-break-inside: auto;
              }
              tr { 
                page-break-inside: avoid; 
                page-break-after: auto;
              }
              thead { 
                display: table-header-group; 
              }
              tfoot { 
                display: table-footer-group; 
              }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Laporan Status Data Provinsi</h1>
            <p>Tanggal Cetak: ${currentDate}</p>
          </div>
          <h2>Grafik Statistik Status Data</h2>
          <div class="charts-container">
            ${statusData.map((kategori, index) => {
              const kategoriNama = Object.keys(kategori)[0];
              const dataChart = kategori[kategoriNama];
              
              // Create an SVG representation for printing
              return `
                <div class="chart-item">
                  <div class="chart-title">${kategoriNama}</div>
                  <svg width="350" height="200" viewBox="0 0 350 200">
                    <!-- Simple representation of the bar chart -->
                    ${dataChart.map((item, i) => {
                      const total = item.ada + item.tidak_ada;
                      const adaHeight = (item.ada / total) * 150;
                      const tidakAdaHeight = (item.tidak_ada / total) * 150;
                      const barWidth = 40;
                      const spacing = 60;
                      const startX = 50 + (i * spacing);
                      
                      return `
                        <!-- Year label -->
                        <text x="${startX + barWidth/2}" y="190" text-anchor="middle" font-size="10">${item.tahun}</text>
                        
                        <!-- Data Updated bar -->
                        <rect x="${startX}" y="${180 - adaHeight}" width="${barWidth}" height="${adaHeight}" fill="#28A745" />
                        
                        <!-- Data Not Updated bar -->
                        <rect x="${startX}" y="${180 - adaHeight - tidakAdaHeight}" width="${barWidth}" height="${tidakAdaHeight}" fill="#DC3545" />
                        
                        <!-- Value labels -->
                        <text x="${startX + barWidth/2}" y="${180 - adaHeight/2}" text-anchor="middle" font-size="8" fill="white">${item.ada}</text>
                        <text x="${startX + barWidth/2}" y="${180 - adaHeight - tidakAdaHeight/2}" text-anchor="middle" font-size="8" fill="white">${item.tidak_ada}</text>
                      `;
                    }).join('')}
                    
                    <!-- Legend -->
                    <rect x="10" y="10" width="10" height="10" fill="#28A745" />
                    <text x="25" y="18" font-size="10">Update (${dataChart.reduce((sum, item) => sum + item.ada, 0)})</text>
                    
                    <rect x="10" y="30" width="10" height="10" fill="#DC3545" />
                    <text x="25" y="38" font-size="10">Belum Update (${dataChart.reduce((sum, item) => sum + item.tidak_ada, 0)})</text>
                  </svg>
                </div>
              `;
            }).join('')}
          </div>
          
          <div class="page-break"></div>
          
          <h2>Tabel Status Data Provinsi</h2>
          <table>
            <thead>
              <tr>
                <th rowspan="2" class="text-left">Nama Kategori</th>
                <th rowspan="2">Total Provinsi</th>
                <th colspan="${getUniqueYears().length}">Data Update</th>
                <th colspan="${getUniqueYears().length}">Data Belum Update</th>
              </tr>
              <tr>
                ${getUniqueYears().map(year => `<th>${year}</th>`).join('')}
                ${getUniqueYears().map(year => `<th>${year}</th>`).join('')}
              </tr>
            </thead>
            <tbody>
              ${statusData.map((kategori, index) => {
                const kategoriNama = Object.keys(kategori)[0];
                const dataByYear = {};
                
                // Organize data by year for easier access
                kategori[kategoriNama].forEach(item => {
                  dataByYear[item.tahun] = item;
                });
                
                const uniqueYears = getUniqueYears();
                const totalProvinsi = uniqueYears.length > 0 && dataByYear[uniqueYears[0]] ? 
                  dataByYear[uniqueYears[0]].ada + dataByYear[uniqueYears[0]].tidak_ada : '-';
                
                return `
                  <tr>
                    <td class="text-left">${kategoriNama}</td>
                    <td>${totalProvinsi}</td>
                    ${uniqueYears.map(year => `
                      <td>${dataByYear[year] ? dataByYear[year].ada : '-'}</td>
                    `).join('')}
                    ${uniqueYears.map(year => `
                      <td>${dataByYear[year] ? dataByYear[year].tidak_ada : '-'}</td>
                    `).join('')}
                  </tr>
                `;
              }).join('')}
            </tbody>
          </table>
          
          <div class="footer">
            <p>Laporan ini dicetak dari Sistem Manajemen Data</p>
          </div>
        </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    
    // Add a slight delay to ensure the content is fully loaded before printing
    setTimeout(() => {
      printWindow.print();
      // Uncomment the line below if you want the print window to close after printing
      // printWindow.close();
    }, 500);
  };

  if (loading) return <div>Loading status data...</div>;
  if (!statusData.length) return <div>No status data available.</div>;

  const uniqueYears = getUniqueYears();

  return (
    <div className="p-4 bg-gray-100 rounded-md">
      {/* Print Button */}
      <div className="flex justify-end mb-4">
        <button 
          onClick={handlePrint}
          className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md shadow transition-colors"
        >
          <Printer size={16} />
          <span>Cetak Laporan</span>
        </button>
      </div>

      {/* Content to be printed - We'll use this reference for direct printing if needed */}
      <div ref={printableContentRef}>
        {/* Bar Charts Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {statusData.map((kategori, index) => {
            const kategoriNama = Object.keys(kategori)[0]; // Nama kategori (DEMOGRAFI, EKSPOR/IMPOR, dll.)
            const dataChart = kategori[kategoriNama];

            return (
              <div key={index} className="bg-white p-2 shadow-md rounded-lg">
                <h4 className="text-sm font-semibold text-gray-700 mb-1">{kategoriNama}</h4>
                <ResponsiveContainer width="100%" height={200}>
                  <BarChart data={dataChart} barCategoryGap="20%">
                    <XAxis dataKey="tahun" tick={{ fontSize: 10 }} />
                    <YAxis tick={{ fontSize: 10 }} />
                    <Tooltip wrapperStyle={{ fontSize: "10px" }} />
                    <Legend wrapperStyle={{ fontSize: "10px" }} />
                    <Bar dataKey="ada" stackId="a" fill="#28A745" name="Update" barSize={15} />
                    <Bar dataKey="tidak_ada" stackId="a" fill="#DC3545" name="Belum Update" barSize={15} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            );
          })}
        </div>

        {/* Summary Table Section */}
        <div className="overflow-x-auto bg-white rounded-lg shadow">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {/* Using rowSpan=2 to merge the cells vertically */}
                <th scope="col" rowSpan="2" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-r align-middle">
                  Nama Kategori
                </th>
                <th scope="col" rowSpan="2" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-r align-middle">
                  Total Provinsi
                </th>
                <th scope="col" colSpan={uniqueYears.length} className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b ">
                  Data Update
                </th>
                <th scope="col" colSpan={uniqueYears.length} className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                  Data Belum Update
                </th>
              </tr>
              <tr>
                {uniqueYears.map(year => (
                  <th key={`update-${year}`} scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b ">
                    {year}
                  </th>
                ))}
                {uniqueYears.map(year => (
                  <th key={`notupdate-${year}`} scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b ">
                    {year}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200 text-xs">
              {statusData.map((kategori, index) => {
                const kategoriNama = Object.keys(kategori)[0];
                const dataByYear = {};
                
                // Organize data by year for easier access
                kategori[kategoriNama].forEach(item => {
                  dataByYear[item.tahun] = item;
                });
                
                return (
                  <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r">
                      {kategoriNama}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center border-r">
                      {/* Display a single Total Provinsi value based on the first year's data */}
                      {uniqueYears.length > 0 && dataByYear[uniqueYears[0]] ? 
                        dataByYear[uniqueYears[0]].ada + dataByYear[uniqueYears[0]].tidak_ada : 
                        '-'}
                    </td>
                    
                    {/* Data Update columns */}
                    {uniqueYears.map(year => (
                      <td key={`update-${year}`} className="px-4 py-4 whitespace-nowrap text-sm text-center border-r">
                        {dataByYear[year] ? (
                          <span>
                            {dataByYear[year].ada} 
                          </span>
                        ) : '-'}
                      </td>
                    ))}
                    
                    {/* Data Belum Update columns */}
                    {uniqueYears.map(year => (
                      <td key={`notupdate-${year}`} className="px-4 py-4 whitespace-nowrap text-sm text-center border-r">
                        {dataByYear[year] ? (
                          <span>
                            {dataByYear[year].tidak_ada}
                          </span>
                        ) : '-'}
                      </td>
                    ))}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default StatusDataProv;