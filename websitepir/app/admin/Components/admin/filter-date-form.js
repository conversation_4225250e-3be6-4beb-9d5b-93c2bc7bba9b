'use client'
import React, { useRef } from "react";
import { Formik, Form, ErrorMessage } from "formik";
import * as Yup from "yup";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
// import { getFilteredDataService } from "@/services/DataService";
import SweetAlert from "../SweetAlert";

// const FilterDateForm = ({ onDataChange }) => {

//     const containerRef = useRef(null);
//     return (
//         <Formik
//             enableReinitialize={true}
//             initialValues={{
//                 startDate: "",
//                 endDate: "",
//             }}
//             onSubmit={async (values, { setSubmitting }) => {
//                 try {
//                   // Kirimkan nilai startDate dan endDate ke parent (Admin)
//                   onDataChange(values.startDate, values.endDate);
//                 //   SweetAlert.success("Success", "Data berhasil diambil");
//                 } catch (error) {
//                 //   SweetAlert.error("Error", "Gagal mengambil data.");
//                 //   console.error("Error:", error);
//                 } finally {
//                   setSubmitting(false);
//                 }
//               }}
//         >
//             {({ setFieldValue, values, isSubmitting }) => (
//                 <Form>
//                     <div className="flex flex-row space-x-2 items-center">
//                         <label className="text-sm font-medium text-gray-700">Filter:    Dari</label>
//                         <Input
//                             type="date"
//                             name="fromDate"
//                             value={values.fromDate}
//                             onChange={(e) => setFieldValue("fromDate", e.target.value)}
//                             className="w-36 text-sm"
//                         />
//                         <ErrorMessage name="fromDate" component="div" className="text-red-500 text-xs" />

//                         {/* Label Sampai Tanggal */}
//                         <label className="text-sm font-medium text-gray-700">Sampai Tanggal</label>
//                         <Input
//                             type="date"
//                             name="toDate"
//                             value={values.toDate}
//                             onChange={(e) => setFieldValue("toDate", e.target.value)}
//                             className="w-36 text-sm"
//                         />
//                         <ErrorMessage name="toDate" component="div" className="text-red-500 text-xs" />

//                         {/* Tombol Tampilkan */}
//                         <Button
//                             type="submit"
//                             variant="primary"
//                             disabled={isSubmitting}
//                             className="bg-blue-500 hover:bg-blue-700 text-white text-sm px-4 py-2"
//                         >
//                             Tampilkan
//                         </Button>
//                     </div>
//                 </Form>
//             )}
//         </Formik>
//     );
// };

// export default FilterDateForm;
const FilterDateForm = ({ onDataChange }) => {

    const containerRef = useRef(null);

    // Fungsi untuk mengonversi tanggal menjadi format yyyy-mm-dd
    const formatDate = (date) => {
        if (!date) return "";
        const d = new Date(date);
        return d.toISOString().split('T')[0]; // Mengambil bagian yyyy-mm-dd
    }

    return (
        <Formik
            enableReinitialize={true}
            initialValues={{
                startDate: "",
                endDate: "",
            }}
            onSubmit={async (values, { setSubmitting }) => {
                try {
                    // Kirimkan nilai startDate dan endDate dengan format yyyy-mm-dd
                    const formattedStartDate = formatDate(values.startDate);
                    const formattedEndDate = formatDate(values.endDate);

                    onDataChange(formattedStartDate, formattedEndDate); // Kirim data ke parent
                } catch (error) {
                    // Tangani error jika perlu
                } finally {
                    setSubmitting(false);
                }
            }}
        >
            {({ setFieldValue, values, isSubmitting }) => (
                <Form>
                    <div className="flex flex-row space-x-2 items-center">
                        <label className="text-sm font-medium text-gray-700">Filter: Dari</label>
                        <Input
                            type="date"
                            name="startDate" // Perbaiki nama input menjadi startDate
                            value={values.startDate}
                            onChange={(e) => setFieldValue("startDate", e.target.value)}
                            className="w-36 text-sm"
                        />
                        <ErrorMessage name="startDate" component="div" className="text-red-500 text-xs" />

                        {/* Label Sampai Tanggal */}
                        <label className="text-sm font-medium text-gray-700">Sampai Tanggal</label>
                        <Input
                            type="date"
                            name="endDate" // Perbaiki nama input menjadi endDate
                            value={values.endDate}
                            onChange={(e) => setFieldValue("endDate", e.target.value)}
                            className="w-36 text-sm"
                        />
                        <ErrorMessage name="endDate" component="div" className="text-red-500 text-xs" />

                        {/* Tombol Tampilkan */}
                        <Button
                            type="submit"
                            variant="primary"
                            disabled={isSubmitting}
                            className="bg-blue-500 hover:bg-blue-700 text-white text-sm px-4 py-2"
                        >
                            Tampilkan
                        </Button>
                    </div>
                </Form>
            )}
        </Formik>
    );
};

export default FilterDateForm;

