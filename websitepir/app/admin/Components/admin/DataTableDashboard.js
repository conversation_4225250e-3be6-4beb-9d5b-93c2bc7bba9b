"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { flexRender } from "@tanstack/react-table";
import {
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { ChevronDown } from "lucide-react";
import React, { useCallback, useEffect, useRef, useState } from "react";

function DataTableDashboard({
    columns,
    fetchDataService,
    actionsComponent: ActionsComponent,
    filterPlaceholder = "Filter...",
    pageSize = 200,
    refreshTrigger,
}) {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchValue, setSearchValue] = useState("");
    const [globalFilter, setGlobalFilter] = useState("");
    const [sorting, setSorting] = useState([]);
    const [columnVisibility, setColumnVisibility] = useState({});
    const [rowSelection, setRowSelection] = useState([]);
    const [pageIndex, setPageIndex] = useState(0);
    const [totalPages, setTotalPages] = useState(0);
    const [totalRecords, setTotalRecords] = useState(0);

    const fetchData = useCallback(async () => {
        try {
            setLoading(true);
            const response = await fetchDataService(
            );
            setData(response.data);
            setTotalPages(response.totalPage || 1);
            setTotalRecords(response.totalRecords || response.data.length);
            if (response && response.success && Array.isArray(response.data)) {
            } else {
                throw new Error("Data format error or request unsuccessful.");
            }
        } catch (err) {
            setError(err.message || "Failed to fetch data.");
        } finally {
            setLoading(false);
        }
    }, [fetchDataService]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const table = useReactTable({
        data,
        columns,
        pageCount: totalPages,
        state: {
            globalFilter,
            sorting,
            columnVisibility,
            rowSelection,
            pagination: {
                pageIndex,
                pageSize,
            },
        },
        onGlobalFilterChange: setGlobalFilter,
        onSortingChange: setSorting,
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        manualPagination: true,
        globalFilterFn: (row, columnId, filterValue) => {
            return row.original[columnId]
                ?.toString()
                ?.toLowerCase()
                ?.includes(filterValue.toLowerCase());
        },
    });

    return (
        <div className="w-full">
            <div className="flex items-center py-4">

                <DropdownMenu>
                  
                    <DropdownMenuContent align="end">
                        {table
                            .getAllColumns()
                            .filter((column) => column.getCanHide())
                            .map((column) => (
                                <DropdownMenuCheckboxItem
                                    key={column.id}
                                    className="capitalize"
                                    checked={column.getIsVisible()}
                                    onCheckedChange={(value) => column.toggleVisibility(!!value)}
                                >
                                    {column.id}
                                </DropdownMenuCheckboxItem>
                            ))}
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
            <div className="rounded-md border">
                {/* Set a fixed height for the scrollable container */}
                <div className="max-h-[400px] overflow-y-auto">
                    <Table className="w-full text-xs">
                        {/* Add background and border styling to header */}
                        <TableHeader className="sticky top-0 bg-white z-10 after:absolute after:left-0 after:right-0 after:bottom-0 after:h-px after:bg-border">
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead key={header.id} className="whitespace-nowrap p-2 bg-white">
                                            {header.isPlaceholder ? null : (
                                                <>
                                                    {flexRender(
                                                        header.column.columnDef.header,
                                                        header.getContext()
                                                    )}
                                                    {header.column.getCanSort() && (
                                                        <Button
                                                            variant="ghost"
                                                            onClick={() => {
                                                                const isSortedAsc =
                                                                    header.column.getIsSorted() === "asc";
                                                                header.column.getToggleSortingHandler()(
                                                                    isSortedAsc ? "desc" : "asc"
                                                                );
                                                            }}
                                                            className="ml-2"
                                                        >
                                                            {header.column.getIsSorted() === "asc"
                                                                ? "↑"
                                                                : header.column.getIsSorted() === "desc"
                                                                    ? "↓"
                                                                    : "↕️"}
                                                        </Button>
                                                    )}
                                                </>
                                            )}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody>
                            {table.getRowModel().rows.length ? (
                                table.getRowModel().rows.map((row) => (
                                    <TableRow key={row.id} className="h-12"> {/* Set fixed height for each row */}
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell
                                                key={cell.id}
                                                className="text-left align-middle p-2 max-w-max overflow-hidden"
                                                style={{
                                                    minWidth: `${cell.getValue()?.length * 8 || 100}px`,
                                                    maxWidth: "500px",
                                                    height: "48px", // Set consistent height for each cell
                                                }}
                                            >
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell
                                        colSpan={columns.length}
                                        className="h-24 text-center"
                                    >
                                        No results.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>
            </div>
            <div className="flex items-center justify-end space-x-2 py-4">
             
            </div>
        </div>
    );
}

export default DataTableDashboard;
