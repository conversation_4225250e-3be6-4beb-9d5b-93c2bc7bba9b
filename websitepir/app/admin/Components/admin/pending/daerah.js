import { useEffect, useState } from 'react';
import { getPendingDaerah } from '@/services/PendingDashboard';
import { Layers, LayoutList, Leaf, BarChart, BadgeDollarSign } from 'lucide-react';

const Daerah = () => {
  const [daerahData, setDaerahData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getPendingDaerah();
        if (response.success) {
          setDaerahData(response.data);
        }
      } catch (error) {
        console.error("Error fetching daerah data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleViewDetail = (name) => {
    const paths = {
      "Sektor Daerah": "/admin/daerah/sektor-unggulan-daerah/sektor-daerah",
      "Sub Sektor Daerah": "/admin/daerah/sektor-unggulan-daerah/sub-sektor-daerah",
      "Komoditi Daerah": "/admin/daerah/sektor-unggulan-daerah/komoditi-daerah",
      "PDRB Sektor Daerah": "/admin/daerah/sektor-unggulan-daerah/pdrb-sektor-daerah",
      "Insentif Sektor Daerah": "/admin/daerah/sektor-unggulan-daerah/insentif-sektor-daerah"
    };

    const path = paths[name] || "/admin";
    const pathWithStatus = `${path}?status=0`;
    window.location.href = pathWithStatus;
  };

  const categoryInfo = {
    "Sektor Daerah": {
      icon: <Layers size={48} />,
      color: "bg-sky-600"
    },
    "Sub Sektor Daerah": {
      icon: <LayoutList size={48} />,
      color: "bg-indigo-600"
    },
    "Komoditi Daerah": {
      icon: <Leaf size={48} />,
      color: "bg-lime-600"
    },
    "PDRB Sektor Daerah": {
      icon: <BarChart size={48} />,
      color: "bg-yellow-600"
    },
    "Insentif Sektor Daerah": {
      icon: <BadgeDollarSign size={48} />,
      color: "bg-cyan-600"
    }
  };

  const getCategory = (name) => {
    return daerahData.find(item => item.nama === name) || { nama: name, jumlah: 0 };
  };

  if (loading) {
    return <div className="text-center p-8">Loading...</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {Object.keys(categoryInfo).map((name) => (
        <div key={name} className={`${categoryInfo[name].color} text-white rounded-lg shadow-md`}>
          <div className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-6xl font-bold">{getCategory(name).jumlah}</div>
                <div className="mt-2">{name}</div>
              </div>
              <div>
                {categoryInfo[name].icon}
              </div>
            </div>
            <div className="flex justify-end mt-4">
              <button
                onClick={() => handleViewDetail(name)}
                className="text-white"
              >
                Lihat Detail ➤
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Daerah;
