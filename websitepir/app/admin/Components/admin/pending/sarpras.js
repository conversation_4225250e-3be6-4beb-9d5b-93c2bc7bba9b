import { getPendingSarpras } from "@/services/PendingDashboard";
import { Hotel, Ship, BookOpen, Hospital, Plane } from "lucide-react";
import { useEffect, useState } from "react";

const categoryInfo = {
  Hotel: {
    icon: <Hotel size={48} />,
    color: "bg-green-500",
    path: "/admin/daerah/sarana-dan-prasarana/hotel",
  },
  Pelabuhan: {
    icon: <Ship size={48} />,
    color: "bg-red-500",
    path: "/admin/daerah/sarana-dan-prasarana/pelabuhan",
  },
  Pendidikan: {
    icon: <BookOpen size={48} />,
    color: "bg-yellow-500",
    path: "/admin/daerah/sarana-dan-prasarana/pendidikan",
  },
  "Rumah Sakit": {
    icon: <Hospital size={48} />,
    color: "bg-blue-400",
    path: "/admin/daerah/sarana-dan-prasarana/rumah-sakit",
  },
  Bandara: {
    icon: <Plane size={48} />,
    color: "bg-indigo-500",
    path: "/admin/daerah/sarana-dan-prasarana/bandara",
  },
};

const Sarpras = () => {
  const [sarprasData, setSarprasData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getPendingSarpras();
        if (response.success) {
          setSarprasData(response.data);
        }
      } catch (error) {
        console.error("Error fetching sarpras data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleViewDetail = (name) => {
    const basePath = categoryInfo[name]?.path || "/admin";
    const pathWithStatus = `${basePath}?status=0`;
    window.location.href = pathWithStatus;
  };

  const getCategory = (name) => {
    return (
      sarprasData.find((item) => item.nama === name) || {
        nama: name,
        jumlah: 0,
      }
    );
  };

  if (loading) {
    return <div className="text-center p-8">Loading...</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {Object.keys(categoryInfo).map((name) => (
        <div
          key={name}
          className={`${categoryInfo[name].color} text-white rounded-lg shadow-md`}
        >
          <div className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-6xl font-bold">
                  {getCategory(name).jumlah}
                </div>
                <div className="mt-2">{name}</div>
              </div>
              <div>{categoryInfo[name].icon}</div>
            </div>
            <div className="flex justify-end mt-4">
              <button
                onClick={() => handleViewDetail(name)}
                className="text-white"
              >
                Lihat Detail ➤
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Sarpras;
