import { useEffect, useState } from 'react';
import { getPendingKabupaten } from '@/services/PendingDashboard';
import { Users, Briefcase, Coins } from 'lucide-react';

const Kabupaten = () => {
  const [provinsiData, setProvinsiData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getPendingKabupaten();
        if (response.success) {
          setProvinsiData(response.data);
        }
      } catch (error) {
        console.error("Error fetching Provinsi data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const categoryInfo = {
    "Jumlah Penduduk": {
      icon: <Users size={48} />,
      color: "bg-purple-500",
      path: "/admin/daerah/kabupaten/demografi"
    },
    "Angkatan Kerja": {
      icon: <Briefcase size={48} />,
      color: "bg-orange-500",
      path: "/admin/daerah/kabupaten/demografi"
    },
    "UMR": {
      icon: <Coins size={48} />,
      color: "bg-teal-500",
      path: "/admin/daerah/kabupaten/umr"
    }
  };

  const getCategory = (name) => {
    return provinsiData.find(item => item.nama === name) || { nama: name, jumlah: 0 };
  };

  const handleViewDetail = (name) => {
    const path = categoryInfo[name]?.path || '/admin';
    const pathWithStatus = `${path}?status=0`;
    window.location.href = pathWithStatus;
  };

  if (loading) return <div className="text-center p-8">Loading...</div>;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {Object.keys(categoryInfo).map((key) => {
        const info = categoryInfo[key];
        const { jumlah } = getCategory(key);
        return (
          <div key={key} className={`${info.color} text-white rounded-lg shadow-md`}>
            <div className="p-4">
              <div className="flex justify-between items-center">
                {info.icon}
                <div className="text-6xl font-bold">{jumlah}</div>
              </div>
              <div className="mt-2">{key}</div>
              <div className="flex justify-end mt-4">
                <button onClick={() => handleViewDetail(key)} className="text-white">
                  Lihat Detail ➤
                </button>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default Kabupaten;
