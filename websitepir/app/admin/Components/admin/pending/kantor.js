import { useEffect, useState } from 'react';
import { getPendingKantor } from '@/services/PendingDashboard';
import { MapPin, Landmark, ClipboardList, FileText, Info } from 'lucide-react';

const Kantor = () => {
  const [kantorData, setKantorData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getPendingKantor();
        if (response.success) {
          setKantorData(response.data);
        }
      } catch (error) {
        console.error("Error fetching kantor data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleViewDetail = (name) => {
    const paths = {
      "Provinsi": "/admin/daerah/provinsi/kantor",
      "Kabupaten": "/admin/daerah/kabupaten/kantor",
      "Profil Komoditi Provinsi": "/admin/daerah/komoditi/komoditi-provinsi",
      "Profil Komoditi Kab/Kota": "/admin/daerah/komoditi/komoditi-kabkot",
      "Info Peluang": "/admin/peluang-investasi/daftar-peluang-investasi"
    };

    const path = paths[name] || "/admin";
    const pathWithStatus = `${path}?status=0`;
    window.location.href = pathWithStatus;
  };

  const categoryInfo = {
    "Provinsi": {
      icon: <MapPin size={48} />,
      color: "bg-blue-500"
    },
    "Kabupaten": {
      icon: <Landmark size={48} />,
      color: "bg-green-500"
    },
    "Profil Komoditi Provinsi": {
      icon: <ClipboardList size={48} />,
      color: "bg-yellow-500"
    },
    "Profil Komoditi Kab/Kota": {
      icon: <FileText size={48} />,
      color: "bg-indigo-500"
    },
    "Info Peluang": {
      icon: <Info size={48} />,
      color: "bg-red-500"
    }
  };

  const getCategory = (name) => {
    return kantorData.find(item => item.nama === name) || { nama: name, jumlah: 0 };
  };

  if (loading) {
    return <div className="text-center p-8">Loading...</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {Object.keys(categoryInfo).map((name) => (
        <div key={name} className={`${categoryInfo[name].color} text-white rounded-lg shadow-md`}>
          <div className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-6xl font-bold">{getCategory(name).jumlah}</div>
                <div className="mt-2">{name}</div>
              </div>
              <div>
                {categoryInfo[name].icon}
              </div>
            </div>
            <div className="flex justify-end mt-4">
              <button
                onClick={() => handleViewDetail(name)}
                className="text-white"
              >
                Lihat Detail ➤
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Kantor;
