import { useEffect, useState } from 'react';
import { getPendingNasional } from '@/services/PendingDashboard';
import { Layers, LayoutList, <PERSON>, Bar<PERSON><PERSON>, BadgeDollarSign } from 'lucide-react';

const Nasional = () => {
  const [nasionalData, setNasionalData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getPendingNasional();
        if (response.success) {
          setNasionalData(response.data);
        }
      } catch (error) {
        console.error("Error fetching nasional data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleViewDetail = (name) => {
    const paths = {
      "Sektor Nasional": "/admin/beranda/sektor-unggulan-nasional/sektor-nasional",
      "Sub Sektor Nasional": "/admin//beranda/sektor-unggulan-nasional/sub-sektor-nasional",
      "Komoditi Nasional": "/admin//beranda/sektor-unggulan-nasional/komoditi-nasional",
      "PDB Sektor Nasional": "/admin//beranda/sektor-unggulan-nasional/pdb-sektor-nasional",
      "Insentif Sektor Nasional": "/admin//beranda/sektor-unggulan-nasional/insentif-sektor-nasional"
    };

    const path = paths[name] || "/admin";
    const pathWithStatus = `${path}?status=0`;
    window.location.href = pathWithStatus;
  };

  const categoryInfo = {
    "Sektor Nasional": {
      icon: <Layers size={48} />,
      color: "bg-purple-600"
    },
    "Sub Sektor Nasional": {
      icon: <LayoutList size={48} />,
      color: "bg-pink-600"
    },
    "Komoditi Nasional": {
      icon: <Leaf size={48} />,
      color: "bg-emerald-600"
    },
    "PDB Sektor Nasional": {
      icon: <BarChart size={48} />,
      color: "bg-orange-600"
    },
    "Insentif Sektor Nasional": {
      icon: <BadgeDollarSign size={48} />,
      color: "bg-teal-600"
    }
  };

  const getCategory = (name) => {
    return nasionalData.find(item => item.nama === name) || { nama: name, jumlah: 0 };
  };

  if (loading) {
    return <div className="text-center p-8">Loading...</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {Object.keys(categoryInfo).map((name) => (
        <div key={name} className={`${categoryInfo[name].color} text-white rounded-lg shadow-md`}>
          <div className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-6xl font-bold">{getCategory(name).jumlah}</div>
                <div className="mt-2">{name}</div>
              </div>
              <div>
                {categoryInfo[name].icon}
              </div>
            </div>
            <div className="flex justify-end mt-4">
              <button
                onClick={() => handleViewDetail(name)}
                className="text-white"
              >
                Lihat Detail ➤
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Nasional;
