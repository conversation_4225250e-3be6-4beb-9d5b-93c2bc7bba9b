import axios from "axios";
import { 
  getTokenUserFromLocalStorage, 
  removeExpiresTokenFromLocalStorage, 
  removeTokenUserFromLocalStorage 
} from "./TokenManager";

const BASEURL = process.env.NEXT_PUBLIC_BASE_URL;
let csrfToken = null;

// Function to get CSRF token with double submit cookie pattern
const getCsrfToken = async () => {
  try {
    const response = await axios.get(`${BASEURL}/csrf`, {
      withCredentials: true,  // Required for cross-origin cookie handling
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });
    
    // Store CSRF token in memory and cookie
    if (response.data.csrfToken) {
      // Set CSRF cookie with secure attributes
      document.cookie = `XSRF-TOKEN=${response.data.csrfToken}; Secure; SameSite=Strict; Path=/`;
      return response.data.csrfToken;
    }
    return null;
  } catch (error) {
    console.error('Error getting CSRF token:', error);
    return null;
  }
};

// Function to validate CSRF token format
const isValidCsrfToken = (token) => {
  return typeof token === 'string' && /^[a-zA-Z0-9_-]{32,128}$/.test(token);
};

const Api = axios.create({
  baseURL: BASEURL,
  timeout: 600 * 1000,
  withCredentials: true, // Required for CSRF cookie handling
  headers: {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "X-Requested-With": "XMLHttpRequest" // Helps prevent CSRF
  },
  xsrfCookieName: 'XSRF-TOKEN',
  xsrfHeaderName: 'X-CSRF-Token',
});

export const APICORS = axios.create({
  baseURL: BASEURL,
});

// Sanitize headers by removing potentially dangerous headers
const sanitizeHeaders = (headers) => {
  const sanitized = { ...headers };
  delete sanitized['Content-Length'];
  delete sanitized['Transfer-Encoding'];
  return sanitized;
};

// Main request interceptor
const onRequestSuccess = async (config) => {
  // Get CSRF token if not already obtained or invalid
  if (!csrfToken || !isValidCsrfToken(csrfToken)) {
    csrfToken = await getCsrfToken();
  }

  // Add CSRF token to headers if available and valid
  if (csrfToken && isValidCsrfToken(csrfToken)) {
    config.headers['X-CSRF-Token'] = csrfToken;
  } else {
    throw new Error('Invalid or missing CSRF token');
  }

  // Add request timestamp for replay protection
  config.headers['X-Request-Time'] = Date.now().toString();
  const token = getTokenUserFromLocalStorage(); 
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  // Used to prevent RSC conflicts or internal middleware conflicts
  config.headers['x-middleware-subrequest'] = '';

  // Sanitize headers before sending request
  config.headers = sanitizeHeaders(config.headers);
  
  // Add WAF-friendly headers
  config.headers['X-Requested-With'] = 'XMLHttpRequest';
  config.headers['X-Client-Version'] = '1.0.0';
  config.headers['User-Agent'] = 'Mozilla/5.0 PIR-Website';
  config.headers['Origin'] = typeof window !== 'undefined' ? window.location.origin : '';
  config.headers['Referer'] = typeof window !== 'undefined' ? window.location.origin : '';
  config.headers['X-Forwarded-Host'] = typeof window !== 'undefined' ? window.location.host : '';
  config.headers['Accept'] = 'application/json, text/plain, /*';
  config.headers['Cache-Control'] = 'no-cache';
  config.headers['Pragma'] = 'no-cache';

  // Tambahkan timestamp untuk menghindari cache
  if (config.method === 'get') {
    config.params = config.params || {};
    config.params['_t'] = Date.now();
  }

  return config;
};

const onRequestError = (error) => Promise.reject(error);

const onResponseSuccess = (response) => {
  // Optional: Reset token expiry
  const newExpirationDate = new Date(Date.now() + 10 * 60 * 1000);
  return response.data;
};

const onResponseError = async (error) => {
  // Handle CSRF token mismatch or expired
  if (error.response?.status === 419 || 
      error.response?.status === 401 || 
      error.response?.data?.message?.includes('CSRF')) {
    // Reset CSRF token and cookie
    csrfToken = null;
    document.cookie = 'XSRF-TOKEN=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; Secure; SameSite=Strict';
    
    // Retry the failed request with new CSRF token
    try {
      const newToken = await getCsrfToken();
      if (newToken && isValidCsrfToken(newToken)) {
        error.config.headers['X-CSRF-Token'] = newToken;
        error.config.headers['X-Request-Time'] = Date.now().toString();
        return axios.request(error.config);
      }
    } catch (retryError) {
      console.error('Error refreshing CSRF token:', retryError);
      // Clear auth tokens on persistent CSRF issues
      removeTokenUserFromLocalStorage();
      removeExpiresTokenFromLocalStorage();
      window.location.href = '/auth/login';
    }
  }

  // Handle unauthorized access
  if (error.response?.status === 401) {
    removeTokenUserFromLocalStorage();
    removeExpiresTokenFromLocalStorage();
    window.location.href = "/admin/auth/login";
  }
  return Promise.reject(error.response ? error.response.data : error);
};

// Register interceptors
Api.interceptors.request.use(onRequestSuccess, onRequestError);
Api.interceptors.response.use(onResponseSuccess, onResponseError);

APICORS.interceptors.request.use(onRequestSuccess, onRequestError);
APICORS.interceptors.response.use(onResponseSuccess, onResponseError);

export default Api;