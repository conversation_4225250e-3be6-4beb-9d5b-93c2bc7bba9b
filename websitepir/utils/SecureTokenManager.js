/**
 * Secure Token Manager with Encryption
 * Provides encrypted token storage and validation
 */

import CryptoJS from 'crypto-js';

// Get encryption key from environment
const ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'default-fallback-key-change-in-production';

// Token validation patterns
const TOKEN_PATTERNS = {
  JWT: /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/,
  BEARER: /^[A-Za-z0-9-_]{20,}$/,
  SESSION: /^[A-Za-z0-9-_]{32,}$/
};

/**
 * Encrypt sensitive data before storage
 */
const encryptData = (data) => {
  try {
    return CryptoJS.AES.encrypt(JSON.stringify(data), ENCRYPTION_KEY).toString();
  } catch (error) {
    console.error('Encryption failed:', error);
    return null;
  }
};

/**
 * Decrypt sensitive data after retrieval
 */
const decryptData = (encryptedData) => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
    const decryptedData = bytes.toString(CryptoJS.enc.Utf8);
    return JSON.parse(decryptedData);
  } catch (error) {
    console.error('Decryption failed:', error);
    return null;
  }
};

/**
 * Validate token format and structure
 */
const validateTokenFormat = (token) => {
  if (!token || typeof token !== 'string') return false;
  
  // Check against known token patterns
  return Object.values(TOKEN_PATTERNS).some(pattern => pattern.test(token));
};

/**
 * Check if token is expired
 */
const isTokenExpired = (expiresAt) => {
  if (!expiresAt) return true;
  return new Date() > new Date(expiresAt);
};

/**
 * Securely store authentication token with encryption
 */
export const setSecureToken = (token, expiresAt, refreshToken = null) => {
  if (typeof window === 'undefined') return false;
  
  // Validate token format
  if (!validateTokenFormat(token)) {
    console.error('Invalid token format');
    return false;
  }
  
  // Validate expiration
  if (isTokenExpired(expiresAt)) {
    console.error('Token already expired');
    return false;
  }
  
  try {
    const tokenData = {
      token: token,
      expiresAt: expiresAt,
      refreshToken: refreshToken,
      timestamp: Date.now(),
      userAgent: navigator.userAgent.substring(0, 100), // Fingerprinting
      origin: window.location.origin
    };
    
    const encryptedData = encryptData(tokenData);
    if (!encryptedData) return false;
    
    localStorage.setItem('secure_auth_data', encryptedData);
    
    // Set expiration cleanup
    const timeUntilExpiry = new Date(expiresAt).getTime() - Date.now();
    if (timeUntilExpiry > 0) {
      setTimeout(() => {
        clearSecureToken();
      }, timeUntilExpiry);
    }
    
    return true;
  } catch (error) {
    console.error('Failed to store secure token:', error);
    return false;
  }
};

/**
 * Retrieve and validate authentication token
 */
export const getSecureToken = () => {
  if (typeof window === 'undefined') return null;
  
  try {
    const encryptedData = localStorage.getItem('secure_auth_data');
    if (!encryptedData) return null;
    
    const tokenData = decryptData(encryptedData);
    if (!tokenData) {
      clearSecureToken();
      return null;
    }
    
    // Validate token structure
    if (!tokenData.token || !tokenData.expiresAt || !tokenData.timestamp) {
      clearSecureToken();
      return null;
    }
    
    // Check expiration
    if (isTokenExpired(tokenData.expiresAt)) {
      clearSecureToken();
      return null;
    }
    
    // Validate user agent (basic fingerprinting)
    const currentUserAgent = navigator.userAgent.substring(0, 100);
    if (tokenData.userAgent && tokenData.userAgent !== currentUserAgent) {
      console.warn('User agent mismatch detected');
      clearSecureToken();
      return null;
    }
    
    // Validate origin
    if (tokenData.origin && tokenData.origin !== window.location.origin) {
      console.warn('Origin mismatch detected');
      clearSecureToken();
      return null;
    }
    
    // Check token age (max 24 hours regardless of expiration)
    const tokenAge = Date.now() - tokenData.timestamp;
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    if (tokenAge > maxAge) {
      clearSecureToken();
      return null;
    }
    
    return {
      token: tokenData.token,
      expiresAt: tokenData.expiresAt,
      refreshToken: tokenData.refreshToken
    };
  } catch (error) {
    console.error('Failed to retrieve secure token:', error);
    clearSecureToken();
    return null;
  }
};

/**
 * Clear all authentication data
 */
export const clearSecureToken = () => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem('secure_auth_data');
    localStorage.removeItem('token'); // Legacy cleanup
    localStorage.removeItem('expires-token'); // Legacy cleanup
    localStorage.removeItem('refresh-token'); // Legacy cleanup
    localStorage.removeItem('name-user'); // Legacy cleanup
    localStorage.removeItem('geoAccessToken'); // Legacy cleanup
    localStorage.removeItem('geoAccessTokenExp'); // Legacy cleanup
  } catch (error) {
    console.error('Failed to clear secure token:', error);
  }
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = () => {
  const tokenData = getSecureToken();
  return tokenData && tokenData.token && !isTokenExpired(tokenData.expiresAt);
};

/**
 * Get token for API requests
 */
export const getAuthToken = () => {
  const tokenData = getSecureToken();
  return tokenData ? tokenData.token : null;
};

/**
 * Get refresh token
 */
export const getRefreshToken = () => {
  const tokenData = getSecureToken();
  return tokenData ? tokenData.refreshToken : null;
};

/**
 * Update token expiration
 */
export const updateTokenExpiration = (newExpiresAt) => {
  const tokenData = getSecureToken();
  if (!tokenData) return false;
  
  return setSecureToken(tokenData.token, newExpiresAt, tokenData.refreshToken);
};

/**
 * Validate token before API requests
 */
export const validateTokenForRequest = () => {
  const tokenData = getSecureToken();
  
  if (!tokenData) {
    throw new Error('No authentication token available');
  }
  
  if (isTokenExpired(tokenData.expiresAt)) {
    clearSecureToken();
    throw new Error('Authentication token expired');
  }
  
  // Check if token expires soon (within 5 minutes)
  const fiveMinutes = 5 * 60 * 1000;
  const timeUntilExpiry = new Date(tokenData.expiresAt).getTime() - Date.now();
  
  if (timeUntilExpiry < fiveMinutes) {
    console.warn('Token expires soon, consider refreshing');
  }
  
  return tokenData.token;
};

// Legacy compatibility functions (deprecated)
export const setTokenUserInLocalStorage = (token) => {
  console.warn('setTokenUserInLocalStorage is deprecated, use setSecureToken instead');
  return setSecureToken(token, new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString());
};

export const getTokenUserFromLocalStorage = () => {
  console.warn('getTokenUserFromLocalStorage is deprecated, use getAuthToken instead');
  return getAuthToken();
};

export const removeTokenUserFromLocalStorage = () => {
  console.warn('removeTokenUserFromLocalStorage is deprecated, use clearSecureToken instead');
  clearSecureToken();
};

export default {
  setSecureToken,
  getSecureToken,
  clearSecureToken,
  isAuthenticated,
  getAuthToken,
  getRefreshToken,
  updateTokenExpiration,
  validateTokenForRequest
};
