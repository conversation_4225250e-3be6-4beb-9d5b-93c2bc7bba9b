<<<<<<< HEAD
FROM node:18-alpine

WORKDIR /
=======
##new
# Dockerfile
FROM node:20-alpine AS base
>>>>>>> origin/main

RUN apk add --no-cache git

COPY package.json ./

RUN npm install --legacy-peer-deps

COPY ./app .

CMD ["npm", "dev"]


<<<<<<< HEAD
=======
# Runner
FROM base AS runner
WORKDIR /app
ENV NODE_ENV production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
USER nextjs
EXPOSE 3000
CMD ["node", "server.js"]
>>>>>>> origin/main
