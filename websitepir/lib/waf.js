/**
 * Web Application Firewall (WAF) module for websitepir
 * Provides protection against client-side desync attacks and other web vulnerabilities
 */

// IP reputation store to track suspicious clients
const ipReputationStore = new Map();
const MAX_REPUTATION_ENTRIES = 10000;
const REPUTATION_CLEANUP_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours
const BLOCK_THRESHOLD = 90; // Higher threshold to prevent false positives (was 80)

// Threat signatures for detecting various attacks
const threatSignatures = [
  // SQL Injection signatures
  {
    name: 'SQL_INJECTION_UNION',
    pattern: /(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)/i,
    severity: 'critical',
    action: 'block',
    description: 'SQL injection attempt using UNION SELECT'
  },
  
  // XSS signatures
  {
    name: 'XSS_SCRIPT_TAG',
    pattern: /<script[\s\S]*?>[\s\S]*?<\/script>/i,
    severity: 'high',
    action: 'block',
    description: 'Cross-site scripting attempt using script tags'
  },
  
  // Path Traversal signatures
  {
    name: 'PATH_TRAVERSAL',
    pattern: /(?:\.\.|%2e%2e|%252e%252e)/i,
    severity: 'high',
    action: 'block',
    description: 'Path traversal attempt using dot-dot-slash'
  },

  // Client-Side Desync signatures
  {
    name: 'CLIENT_DESYNC_REQUEST_SPLITTING',
    pattern: /[\r\n](?:GET|POST|PUT|DELETE|HEAD) /i,
    severity: 'critical',
    action: 'block',
    description: 'HTTP request splitting attempt'
  },
  {
    name: 'CLIENT_DESYNC_CONTENT_LENGTH',
    pattern: /^(?:0|[1-9]\d*)\s*(?:,|$)/,
    severity: 'high',
    action: 'block',
    description: 'Invalid content length header format'
  },
  {
    name: 'CLIENT_DESYNC_CHUNKED_ENCODING',
    pattern: /^(?:[0-9a-fA-F]+\r\n|0\r\n\r\n)$/,
    severity: 'high',
    action: 'block',
    description: 'Suspicious chunked encoding format'
  },
  {
    name: 'CLIENT_DESYNC_HEADER_INJECTION',
    pattern: /[\r\n][\s\S]*?:/,
    severity: 'critical',
    action: 'block',
    description: 'HTTP header injection attempt'
  },
  {
    name: 'CLIENT_DESYNC_TRANSFER_ENCODING',
    pattern: /^chunked(?:\s*,\s*)+(?:chunked|identity)$/i,
    severity: 'critical',
    action: 'block',
    description: 'Suspicious transfer encoding header'
  },
  {
    name: 'CLIENT_DESYNC_PIPELINE',
    pattern: /[\r\n](?:GET|POST|PUT|DELETE) [^\r\n]*HTTP\/[12]/i,
    severity: 'critical',
    action: 'block',
    description: 'HTTP request pipelining attempt'
  },
  {
    name: 'CLIENT_DESYNC_CONTENT_LENGTH',
    pattern: /^(?:0|[1-9]\d*)\s*(?:,|$)/,
    severity: 'high',
    action: 'block',
    description: 'Invalid content length header format'
  },
  {
    name: 'CLIENT_DESYNC_CHUNKED_ENCODING',
    pattern: /^(?:[0-9a-fA-F]+\r\n|0\r\n\r\n)$/,
    severity: 'high',
    action: 'block',
    description: 'Suspicious chunked encoding format'
  },
  {
    name: 'CLIENT_DESYNC_HEADER_INJECTION',
    pattern: /[\r\n][\s\S]*?:/,
    severity: 'critical',
    action: 'block',
    description: 'HTTP header injection attempt'
  },
  {
    name: 'CLIENT_DESYNC_HEADER_SMUGGLING',
    pattern: /\r\n(GET|POST|PUT|DELETE|HEAD|OPTIONS|PATCH|CONNECT|TRACE)\s+\//i,
    severity: 'critical',
    action: 'block',
    description: 'Client-side desync attempt with smuggled HTTP request in headers'
  },
  {
    name: 'CLIENT_DESYNC_PATH_TRAVERSAL',
    pattern: /\/(\.\.\/)|(\%2e\%2e\%2f)/i,
    severity: 'high',
    action: 'block',
    description: 'Client-side desync attempt with path traversal'
  },
  {
    name: 'CLIENT_DESYNC_CRLF_INJECTION',
    pattern: /(\%0d\%0a)|(\r\n)/i,
    severity: 'high',
    action: 'block',
    description: 'Client-side desync attempt with CRLF injection'
  }
];

// CSD detection patterns
const csdPatterns = {
  requestSplitting: /[\r\n](?:GET|POST|PUT|DELETE|HEAD) /i,
  contentLengthMismatch: /^(?:0|[1-9]\d*)\s*(?:,|$)/,
  chunkedEncoding: /^(?:[0-9a-fA-F]+\r\n|0\r\n\r\n)$/,
  headerInjection: /[\r\n][\s\S]*?:/
};

// Helper function to check for client-side desync attempts
const checkForCSD = (headers, body) => {
  // Check for request splitting
  if (csdPatterns.requestSplitting.test(body)) {
    return true;
  }

  // Check for content-length manipulation
  const contentLength = headers['content-length'];
  if (contentLength && csdPatterns.contentLengthMismatch.test(contentLength)) {
    return true;
  }

  // Check for chunked encoding abuse
  const transferEncoding = headers['transfer-encoding'];
  if (transferEncoding && csdPatterns.chunkedEncoding.test(body)) {
    return true;
  }

  // Check for header injection
  if (csdPatterns.headerInjection.test(body)) {
    return true;
  }

  return false;
};

// Additional threat signatures
const additionalSignatures = [
  {
    name: 'CLIENT_DESYNC_BODY_SMUGGLING',
    // This pattern detects a complete HTTP request smuggled in the body
    pattern: /^(GET|POST|PUT|DELETE|HEAD|OPTIONS|PATCH|CONNECT|TRACE)\s+\/[^\s]*\s+HTTP\/[0-9.]+\r?\n/im,
    severity: 'critical',
    action: 'block',
    description: 'Client-side desync attempt with smuggled HTTP request in body'
  },
  {
    name: 'CLIENT_DESYNC_BODY_HOST_HEADER',
    // This pattern detects a Host header in the body, which is a strong indicator of a smuggled request
    pattern: /\r?\nHost:\s+[^\r\n]+\r?\n/im,
    severity: 'critical',
    action: 'block',
    description: 'Client-side desync attempt with smuggled Host header in body'
  }
];

// Helper function to identify routes that should be exempted from strict checks
const isAuthRoute = (path) => {
  // Robustly match all authentication-related routes for exemption
  return (
    /\/auth(\/|$)/.test(path) ||
    /\/login(\/|$)/.test(path) ||
    /\/register(\/|$)/.test(path) ||
    /\/admin(\/|$)/.test(path) ||
    /\/reset-password(\/|$)/.test(path) ||
    /\/be(\/|$)/.test(path) ||
    /\/forgot-password(\/|$)/.test(path)
  );
}
  // // Main navigation paths
  // if (path === '/user' || 
  //     path === '/user/' || 
  //     path.match(/^\/user\/(peluang_investasi|hilirisasi|daerah|kebijakan|insentif|artikel)\/?$/) !== null) {
  //   return true;
  // }
  
// Helper function to get severity score for reputation tracking
const getSeverityScore = (severity) => {
  switch (severity) {
    case 'critical': return 25;
    case 'high': return 15;
    case 'medium': return 10;
    case 'low': return 5;
    default: return 0;
  }
};

// Update IP reputation based on detected threats
const updateIPReputation = (ip, violation, severityScore) => {
  let reputation = ipReputationStore.get(ip);
  
  if (!reputation) {
    reputation = {
      ip,
      score: 0,
      lastSeen: Date.now(),
      violations: []
    };
  }
  
  reputation.score = Math.min(100, reputation.score + severityScore);
  reputation.lastSeen = Date.now();
  reputation.violations.push(`${new Date().toISOString()}: ${violation}`);
  
  // Keep only last 10 violations
  if (reputation.violations.length > 10) {
    reputation.violations = reputation.violations.slice(-10);
  }
  
  ipReputationStore.set(ip, reputation);
  
  // Cleanup old entries if store is getting too large
  if (ipReputationStore.size > MAX_REPUTATION_ENTRIES) {
    cleanupOldReputationEntries();
  }
};

// Cleanup old reputation entries
const cleanupOldReputationEntries = () => {
  const now = Date.now();
  const cutoff = now - REPUTATION_CLEANUP_INTERVAL;
  
  for (const [ip, reputation] of ipReputationStore.entries()) {
    if (reputation.lastSeen < cutoff) {
      ipReputationStore.delete(ip);
    }
  }
};

// Log threat detection for monitoring
const logThreatDetection = (ip, url, method, threats, riskScore, userAgent) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event_type: 'waf_threat_detection',
    ip_address: ip,
    url: url,
    method: method,
    user_agent: userAgent,
    threats_detected: threats.map(t => ({
      name: t.name,
      severity: t.severity,
      description: t.description,
      action: t.action
    })),
    risk_score: riskScore,
    ip_reputation: ipReputationStore.get(ip)
  };
  
  console.warn('[WAF] Threat Detection:', JSON.stringify(logEntry, null, 2));
};

// Helper: Detect harmful SQLi characters/patterns
function detectSQLiHarmfulPatterns(str) {
  if (!str || typeof str !== 'string') return false;
  // Patterns: quotes, semicolon, comment, logic, whitespace, parens, comp, backtick, brackets
  const patterns = [
    /'/, // single quote
    /"/, // double quote
    /`/, // backtick
    /;/, // semicolon
    /--/, // double dash
    /#/, // hash
    /\/\//, // double slash
    /\bAND\b/i, // logical operator
    /\bOR\b/i, // logical operator
    /[\s\t\n\r]/, // whitespace
    /\(/, // open paren
    /\)/, // close paren
    /=|!=|<|>/, // comparison
    /\[/, // open bracket
    /\]/ // close bracket
  ];
  return patterns.some((pat) => pat.test(str));
}

// Helper: Scan all values in an object for SQLi patterns
function scanObjectForSQLi(obj) {
  if (!obj) return false;
  if (typeof obj === 'string') return detectSQLiHarmfulPatterns(obj);
  if (typeof obj === 'object') {
    for (const k in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, k)) {
        if (scanObjectForSQLi(obj[k])) return true;
      }
    }
  }
  return false;
}

// Analyze request for threats
const analyzeRequest = async (request, url) => {
  const threats = [];
  let riskScore = 0;
  
  // Get request data to analyze
  const userAgent = request.headers.get('user-agent') || '';
  const referer = request.headers.get('referer') || '';
  const host = request.headers.get('host') || '';

  // SQLi protection: scan harmful patterns in query, body, cookies, headers for critical paths
  const urlObj = new URL(url);
  const pathname = urlObj.pathname;
  const method = request.method;
  // Only check /be/, /admin/, /api/ and skip /user and auth routes
  const isCritical = (
    (pathname.startsWith('/be/') || pathname.startsWith('/admin/') || pathname.startsWith('/api/')) &&
    !pathname.startsWith('/user') &&
    !isAuthRoute(pathname)
  );
  if (isCritical) {
    // Query params
    const paramsObj = Object.fromEntries(urlObj.searchParams.entries());
    if (scanObjectForSQLi(paramsObj)) {
      threats.push({
        name: 'SQLI_HARMFUL_CHAR_QUERY',
        severity: 'critical',
        action: 'block',
        description: 'Harmful SQLi character/pattern detected in query params'
      });
      riskScore += getSeverityScore('critical');
    }
    // Cookies
    const cookieHeader = request.headers.get('cookie') || '';
    if (cookieHeader && detectSQLiHarmfulPatterns(cookieHeader)) {
      threats.push({
        name: 'SQLI_HARMFUL_CHAR_COOKIE',
        severity: 'critical',
        action: 'block',
        description: 'Harmful SQLi character/pattern detected in cookies'
      });
      riskScore += getSeverityScore('critical');
    }
    // Headers
    for (const [header, value] of request.headers.entries()) {
      if (header === 'cookie') continue; // already checked
      if (detectSQLiHarmfulPatterns(value)) {
        threats.push({
          name: 'SQLI_HARMFUL_CHAR_HEADER',
          severity: 'high',
          action: 'block',
          description: `Harmful SQLi character/pattern detected in header: ${header}`
        });
        riskScore += getSeverityScore('high');
        break;
      }
    }
    // Body (only for methods with body)
    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
      try {
        const contentType = request.headers.get('content-type') || '';
        if (contentType.includes('application/json')) {
          const bodyText = await request.text();
          try {
            const bodyObj = JSON.parse(bodyText);
            if (scanObjectForSQLi(bodyObj)) {
              threats.push({
                name: 'SQLI_HARMFUL_CHAR_BODY_JSON',
                severity: 'critical',
                action: 'block',
                description: 'Harmful SQLi character/pattern detected in JSON body'
              });
              riskScore += getSeverityScore('critical');
            }
          } catch (err) {
            // fallback: scan as string
            if (detectSQLiHarmfulPatterns(bodyText)) {
              threats.push({
                name: 'SQLI_HARMFUL_CHAR_BODY_RAW',
                severity: 'critical',
                action: 'block',
                description: 'Harmful SQLi character/pattern detected in raw body'
              });
              riskScore += getSeverityScore('critical');
            }
          }
        } else {
          // For non-JSON, scan as string
          const bodyText = await request.text();
          if (detectSQLiHarmfulPatterns(bodyText)) {
            threats.push({
              name: 'SQLI_HARMFUL_CHAR_BODY_RAW',
              severity: 'critical',
              action: 'block',
              description: 'Harmful SQLi character/pattern detected in raw body'
            });
            riskScore += getSeverityScore('critical');
          }
        }
      } catch (e) {
        // Ignore body parse errors
      }
    }
  }
  
  // For sensitive paths, check for smuggled HTTP requests in the body
  // Only check paths under /be/ and /admin/ - /user/ paths are completely excluded
  // const urlObj = new URL(url);
  // const pathname = urlObj.pathname;
  
  // Check only /be/ and /admin/ paths, skip auth routes
  if ((pathname.startsWith('/be/') || pathname.startsWith('/admin/')) && 
      !isAuthRoute(pathname) && 
      ['POST', 'PUT', 'PATCH', 'DELETE'].includes(request.method)) {
    
    try {
      // Read the request body
      const body = await request.text();
      
      // Check for HTTP request patterns in the body
      const smugglingPatterns = [
        /^(GET|POST|PUT|DELETE|HEAD|OPTIONS|PATCH)\s+\/[^\s]*\s+HTTP\/[0-9.]+/im,  // HTTP method pattern
        /\r?\nHost:\s+[^\r\n]+\r?\n/im,  // Host header
        /\r?\nUser-Agent:\s+[^\r\n]+\r?\n/im,  // User-Agent header
        /\r?\nConnection:\s+[^\r\n]+\r?\n/im,  // Connection header
        /\r?\nAccept[\-\w]*:\s*[^\r\n]+\r?\n/im,  // Accept headers
        /\r?\nContent-Type:\s*[^\r\n]+\r?\n/im,  // Content-Type header
      ];
      
      for (const pattern of smugglingPatterns) {
        if (pattern.test(body)) {
          threats.push({
            name: 'DIRECT_BODY_SMUGGLING',
            severity: 'critical',
            action: 'block',
            description: 'Direct detection of smuggled HTTP request in body',
            pattern: pattern
          });
          riskScore += getSeverityScore('critical');
          break;
        }
      }
      
      // Check for a complete HTTP request pattern (method, path, HTTP version, host header)
      if (/GET\s+\/[^\s]+\s+HTTP\/[\d\.]+[\s\S]*Host:\s*[^\r\n]+/i.test(body)) {
        threats.push({
          name: 'COMPLETE_HTTP_REQUEST_IN_BODY',
          severity: 'critical',
          action: 'block',
          description: 'Complete HTTP request detected in request body',
          pattern: /GET\s+\/[^\s]+\s+HTTP\/[\d\.]+[\s\S]*Host:\s*[^\r\n]+/i
        });
        riskScore += getSeverityScore('critical');
      }
    } catch (error) {
      console.error('Error reading request body for WAF analysis:', error);
    }
  }
  
  // Check for client-side desync patterns in URL path segments
  if ((url.includes('/be/') || url.includes('/admin/')) && !isAuthRoute(url)) {
    const urlObj = new URL(url);
    const pathSegments = urlObj.pathname.split('/').filter(Boolean);
    
    for (const segment of pathSegments) {
      // Check for suspicious characters that could be used in desync attacks
      if (/[:@&=+$,;%]/.test(segment)) {
        threats.push({
          name: 'SUSPICIOUS_PATH_CHARACTERS',
          severity: 'medium',
          action: 'block',
          description: 'Suspicious characters detected in URL path segment',
          pattern: /[:@&=+$,;%]/
        });
        riskScore += getSeverityScore('medium');
      }
      
      // Check for CR/LF injection attempts
      if (/%0[dD]|%0[aA]|\\r|\\n/.test(segment)) {
        threats.push({
          name: 'CRLF_INJECTION',
          severity: 'high',
          action: 'block',
          description: 'CR/LF injection attempt detected in URL path',
          pattern: /%0[dD]|%0[aA]|\\r|\\n/
        });
        riskScore += getSeverityScore('high');
      }
      
      // Check for path traversal attempts
      if (/\.\.|%2e%2e|\.\/|\/\./.test(segment)) {
        threats.push({
          name: 'PATH_TRAVERSAL',
          severity: 'high',
          action: 'block',
          description: 'Path traversal attempt detected in URL path',
          pattern: /\.\.|%2e%2e|\.\/|\/\./
        });
        riskScore += getSeverityScore('high');
      }
      
      // Check for overlong UTF-8 encodings that could be used to bypass filters
      if (/%[cC]0%[aA]|%[cC]1%[89]|%[eE]0/.test(segment)) {
        threats.push({
          name: 'OVERLONG_UTF8',
          severity: 'high',
          action: 'block',
          description: 'Overlong UTF-8 encoding detected in URL path',
          pattern: /%[cC]0%[aA]|%[cC]1%[89]|%[eE]0/
        });
        riskScore += getSeverityScore('high');
      }
    }
  }
  
  // Check for suspicious headers that could be used in desync attacks
  const contentLength = request.headers.get('content-length');
  const transferEncoding = request.headers.get('transfer-encoding');
  const connection = request.headers.get('connection');
  const te = request.headers.get('te');
  
  // Check for conflicting headers (CL.TE vulnerability)
  if (contentLength && transferEncoding) {
    threats.push({
      name: 'CONFLICTING_TRANSFER_HEADERS',
      severity: 'critical',
      action: 'block',
      description: 'Conflicting Content-Length and Transfer-Encoding headers',
      pattern: /Content-Length.*Transfer-Encoding|Transfer-Encoding.*Content-Length/i
    });
    riskScore += getSeverityScore('critical');
  }
  
  // Check for suspicious Transfer-Encoding values
  if (transferEncoding) {
    const teValue = transferEncoding.toLowerCase();
    if (!teValue.includes('chunked') || 
        teValue.includes('identity') || 
        teValue.includes(',') || 
        teValue.includes(';') || 
        teValue.includes('\n') || 
        teValue.includes('\r')) {
      threats.push({
        name: 'MALFORMED_TRANSFER_ENCODING',
        severity: 'critical',
        action: 'block',
        description: 'Malformed Transfer-Encoding header value',
        pattern: /identity|,|;|\n|\r/i
      });
      riskScore += getSeverityScore('critical');
    }
  }
  
  // Check for TE header which can be used in request smuggling
  if (te && (te.includes('chunked') || te.includes('trailers'))) {
    threats.push({
      name: 'SUSPICIOUS_TE_HEADER',
      severity: 'high',
      action: 'block',
      description: 'Suspicious TE header value detected',
      pattern: /chunked|trailers/i
    });
    riskScore += getSeverityScore('high');
  }
  
  // Connection header checks are now disabled to prevent false positives
  // This prevents the SUSPICIOUS_CONNECTION_HEADER error when navigating
  
  return { threats, riskScore };
};

// Reset the IP reputation store - helps recover from false positives
const resetIPReputationStore = () => {
  ipReputationStore.clear();
  console.log('[WAF] IP reputation store has been reset at', new Date().toISOString());
};

// Reset reputation store on module load to clear any previous false positives
resetIPReputationStore();

// Helper function to check if a path is a navigation page that should be exempted from WAF
// Note: /user paths are now completely excluded from middleware processing
const isNavigationPath = (pathname) => {
  return false; // /user paths are now completely excluded from middleware
};

// Header normalization and deduplication
function normalizeHeaders(request) {
  // Allow x-method-override ONLY for authentication routes
  const isAuth = isAuthRoute(request.url || request.path || '');
  const dangerousHeaders = isAuth
    ? [
        'proxy-connection', 'keep-alive', 'x-http-method-override',
        'transfer-encoding', 'te', 'connection',
        'content-length', 'upgrade', 'via'
      ]
    : [
        'proxy-connection', 'keep-alive', 'x-http-method-override', 'x-method-override',
        'transfer-encoding', 'te', 'connection',
        'content-length', 'upgrade', 'via'
      ];
  const newHeaders = new Headers();
  for (const [key, value] of request.headers.entries()) {
    const lowerKey = key.toLowerCase();
    if (!dangerousHeaders.includes(lowerKey)) {
      if (!newHeaders.has(lowerKey)) {
        newHeaders.set(lowerKey, value);
      }
    }
  }
  return newHeaders;
}

// Block legacy HTTP/1.0 requests and suspicious method/path
function isLegacyOrSuspicious(request) {
  const method = request.method;
  const url = request.url;
  const allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'];
  if (!allowedMethods.includes(method)) return true;
  try {
    const { pathname } = new URL(url);
    // Allow root path, empty path, and admin paths for standard methods
    if (pathname === '/' || pathname === '' || pathname === '/admin' || pathname === '/admin/' || pathname.startsWith('/admin/')) return false;
    // Block encoded newlines, non-printable ASCII
    if (/%0d|%0a|[\r\n]/i.test(pathname)) return true;
    if (/[^\x20-\x7E]/.test(pathname)) return true;
  } catch (e) {
    return true;
  }
  return false;
}

// Main WAF handler function
const wafHandler = async (request) => {
  // Normalize headers
  const normalizedHeaders = normalizeHeaders(request);
  const securedRequest = new Request(request, { headers: normalizedHeaders });
  // Block legacy/suspicious requests
  if (isLegacyOrSuspicious(securedRequest)) {
    const ip = securedRequest.headers.get('x-forwarded-for')?.split(',')[0].trim() || 
               securedRequest.headers.get('x-real-ip') || 
               '0.0.0.0';
    const url = securedRequest.url;
    const method = securedRequest.method;
    const userAgent = securedRequest.headers.get('user-agent') || '';
    logThreatDetection(ip, url, method, [{
      name: 'LEGACY_OR_SUSPICIOUS_REQUEST',
      severity: 'critical',
      description: 'Blocked legacy or suspicious HTTP request',
      action: 'block'
    }], 25, userAgent);
    return {
      blocked: true,
      status: 400,
      message: 'Blocked: legacy or suspicious HTTP request',
      timestamp: new Date().toISOString()
    };
  }

  const ip = request.headers.get('x-forwarded-for')?.split(',')[0].trim() || 
             request.headers.get('x-real-ip') || 
             '0.0.0.0';
  const url = request.url;
  const urlObj = new URL(url);
  const pathname = urlObj.pathname;
  const method = request.method;
  const userAgent = request.headers.get('user-agent') || '';
  
  // Immediately skip WAF for navigation pages and admin paths
  if (isNavigationPath(pathname) || pathname === '/admin' || pathname === '/admin/' || pathname.startsWith('/admin/')) {
    return { blocked: false };
  }
  
  // Check IP reputation first - use the higher BLOCK_THRESHOLD
  const reputation = ipReputationStore.get(ip);
  if (reputation && reputation.score > BLOCK_THRESHOLD) {
    // Only block if there are multiple violations (at least 3)
    if (reputation.violations.length >= 3) {
      logThreatDetection(ip, url, method, [], reputation.score, userAgent);
      
      // Special handling for admin paths - check if it's a legitimate admin access
      const pathname = new URL(url).pathname;
      if (pathname.includes('/admin/')) {
        // For admin paths, we'll reset the reputation score to allow access
        // This helps legitimate admin users who might have triggered false positives
        console.warn(`[WAF] Resetting reputation score for admin access: ${pathname}`);
        ipReputationStore.delete(ip); // Remove the IP from reputation store
        return { blocked: false };
      }
      
      return {
        blocked: true,
        status: 403,
        message: 'Access denied - Security violation detected',
        timestamp: new Date().toISOString()
      };
    }
  }
  
  // Skip WAF checks for authentication routes
  if (isAuthRoute(new URL(url).pathname)) {
    return { blocked: false };
  }
  
  // Analyze request for threats
  const analysis = await analyzeRequest(request, url);
  
  if (analysis.threats.length > 0) {
    // Update IP reputation
    for (const threat of analysis.threats) {
      updateIPReputation(ip, threat.name, getSeverityScore(threat.severity));
    }
    
    // Log the detection
    logThreatDetection(ip, url, method, analysis.threats, analysis.riskScore, userAgent);
    
    // Determine action based on highest severity threat
    const highestSeverityThreat = analysis.threats.reduce((prev, current) => {
      const severityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 };
      return severityOrder[current.severity] > severityOrder[prev.severity] ? current : prev;
    });
    
    // Take action based on threat
    if (highestSeverityThreat.action === 'block') {
      return {
        blocked: true,
        status: 403,
        message: 'Request blocked by WAF',
        timestamp: new Date().toISOString(),
        threat_id: highestSeverityThreat.name
      };
    }
  }
  
  return { blocked: false };
};

export { wafHandler, isAuthRoute };
