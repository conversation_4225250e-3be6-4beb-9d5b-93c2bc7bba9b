import fs from 'fs';
import path from 'path';

// Load the schema.prisma file
const schemaPath = path.join('prisma', 'schema.prisma');
const schemaContent = fs.readFileSync(schemaPath, 'utf-8');

// Directory to store split model files
const modelsDir = path.join('prisma', 'models3');

// Ensure the models directory exists
if (!fs.existsSync(modelsDir)) {
  fs.mkdirSync(modelsDir, { recursive: true });
}

// Regex to extract models, generator, and datasource
const modelRegex = /model\s+\w+\s+{[^}]+}/g;
const models = schemaContent.match(modelRegex);
const generatorRegex = /generator\s+\w+\s+{[^}]+}/g;
const generator = schemaContent.match(generatorRegex);
const datasourceRegex = /datasource\s+\w+\s+{[^}]+}/g;
const datasource = schemaContent.match(datasourceRegex);

// Path for the _config.prisma file
const configPath = path.join(modelsDir, '_config.prisma');

// Group models by prefix
const groupedModels = {};

models.forEach((model) => {
  const modelNameMatch = model.match(/model\s+(\w+)/);
  if (modelNameMatch) {
    const modelName = modelNameMatch[1];
    const nameParts = modelName.split('_');
    let groupName;

    if (nameParts.length >= 3) {
      groupName = `${nameParts[0]}_${nameParts[1]}`;
    } else {
      groupName = modelName;
    }

    if (!groupedModels[groupName]) {
      groupedModels[groupName] = [];
    }

    groupedModels[groupName].push(model);
  }
});

// Write the grouped models to individual files
Object.keys(groupedModels).forEach((groupName) => {
  const filePath = path.join(modelsDir, `${groupName}.prisma`);
  const fileContent = groupedModels[groupName].join('\n\n');
  fs.writeFileSync(filePath, fileContent, 'utf-8');
});

// Create the _config.prisma file content
let configContent = '';

if (generator && generator.length > 0) {
  configContent += generator[0] + '\n\n';
}

if (datasource && datasource.length > 0) {
  configContent += datasource[0] + '\n';
}

// Write the _config.prisma file
fs.writeFileSync(configPath, configContent, 'utf-8');

console.log('Schema has been split into multiple files under prisma/models2, and _config.prisma has been created.');
