
model menu {
  id        Int      @id @default(autoincrement())
  menu_name     String
  url       String?  
  parent_id  Int?     
  icon String?
  parent    menu?    @relation("MenuToParent", fields: [parent_id], references: [id])
  children  menu[]   @relation("MenuToParent")
  order     Int      @default(0)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  role_access role_menu_access[]

  @@schema("public")
}