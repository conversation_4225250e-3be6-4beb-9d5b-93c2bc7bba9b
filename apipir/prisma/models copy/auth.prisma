
model users {
  id         Int      @id @default(autoincrement())
  email      String?  @unique 
  full_name  String?  
  login_name    String?  @unique 
  password   String   
  role_id    Int      @default(0)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  roles      role     @relation(fields: [role_id], references: [id])
  @@schema("public")
}


model auth_access_tokens {
  id    Int    @id @default(autoincrement())
  tokenable_id String
  hash  String
  type String 
  name  String?
  abilities  String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  last_used_at DateTime? @updatedAt
  expires_at DateTime? @updatedAt
  @@schema("public")
}

model password_reset {
  id         Int      @id @default(autoincrement())
  email      String
  token      String   @unique
  expires_at  DateTime
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
  @@schema("public")
}
