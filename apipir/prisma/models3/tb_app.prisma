model tb_app_slider {
  id_app_slider   Int
  judul           String? @db.<PERSON>ar<PERSON><PERSON>(255)
  deskripsi       String?
  nama_file_image String  @db.VarChar(255)
  url_link        String? @db.VarChar(255)
  ordering        Int?

  @@ignore
  @@schema("public")
}

model tb_app_slider_peluang {
  id_app_slider   Int
  judul           String? @db.VarChar(255)
  deskripsi       String?
  nama_file_image String  @db.VarChar(255)
  url_link        String? @db.VarChar(255)
  ordering        Int?

  @@ignore
  @@schema("public")
}

model tb_app_slider_peluang_tr {
  id_app_slider_tr Int
  id_app_slider    Int
  kd_bahasa        String  @db.Var<PERSON>har(2)
  judul            String? @db.VarChar(255)
  deskripsi        String?
  ordering         Int?
  nama_file_image  String? @db.Var<PERSON>har(255)

  @@ignore
  @@schema("public")
}

model tb_app_slider_tr {
  id_app_slider_tr Int
  id_app_slider    Int
  kd_bahasa        String  @db.Var<PERSON>har(2)
  judul            String? @db.Var<PERSON>har(255)
  deskripsi        String?
  ordering         Int?
  nama_file_image  String? @db.VarChar(255)

  @@ignore
  @@schema("public")
}