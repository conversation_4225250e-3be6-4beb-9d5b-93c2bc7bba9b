model tb_user {
  id_user       Int       @default(autoincrement())
  id_role       Int
  username      String    @db.VarChar(100)
  password      String    @db.VarChar(128)
  email         String    @db.VarChar(100)
  first_name    String    @db.VarChar(30)
  middle_name   String?   @db.Var<PERSON>har(30)
  last_name     String?   @db.VarChar(30)
  address       String?
  phone_number  String?   @db.VarChar(30)
  mobile_number String?   @db.VarChar(30)
  file_image    String?   @db.VarChar(50)
  status        Int       @db.SmallInt
  last_login    DateTime? @db.Timestamp(6)
  created_date  DateTime  @db.Timestamp(6)
  updated_by    Int?
  updated_date  DateTime? @db.Timestamp(6)
  code_forgot   String?   @db.VarChar(255)
  code_expired  DateTime? @db.Timestamp(6)
  keterangan    String?
  user_token    String?   @db.VarChar(255)
  jabatan       String?   @db.VarChar(255)
  instansi      String?
  user_api_key  String?   @db.Var<PERSON>har(36)

  @@ignore
  @@schema("public")
}

model tb_user_hapus {
  id_user       Int?
  id_role       Int?
  username      String?   @db.VarChar(32)
  password      String?   @db.VarChar(128)
  email         String?   @db.VarChar(64)
  first_name    String?   @db.VarChar(30)
  middle_name   String?   @db.VarChar(30)
  last_name     String?   @db.VarChar(30)
  address       String?
  phone_number  String?   @db.VarChar(30)
  mobile_number String?   @db.VarChar(30)
  file_image    String?   @db.VarChar(50)
  status        Int?      @db.SmallInt
  last_login    DateTime? @db.Timestamp(6)
  created_date  DateTime? @db.Timestamp(6)
  updated_by    Int?
  updated_date  DateTime? @db.Timestamp(6)
  code_forgot   String?   @db.VarChar(255)
  code_expired  DateTime? @db.Timestamp(6)
  keterangan    String?
  user_token    String?   @db.VarChar(255)
  jabatan       String?   @db.VarChar(255)
  instansi      String?
  user_api_key  String?   @db.VarChar(36)

  @@ignore
  @@schema("public")
}

model tb_user_internal {
  id_user    Int
  id_jabatan Int

  @@ignore
  @@schema("public")
}

model tb_user_internal_kawasan_industri {
  id_user             Int
  id_kawasan_industri Int

  @@ignore
  @@schema("public")
}

model tb_user_internal_provinsi {
  id_user         Int
  id_adm_provinsi Int

  @@ignore
  @@schema("public")
}