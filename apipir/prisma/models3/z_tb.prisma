model z_tb_komoditi {
  id_komoditi Int?
  id_sektor   Int?
  nama        String?  @db.VarChar(50)
  keterangan  String?
  warna       String?  @db.VarChar(7)
  is_internal Boolean?
  url_service String?  @db.Var<PERSON>har(255)

  @@ignore
  @@schema("public")
}

model z_tb_komoditi_kabkot {
  id_komoditi_kabkot Int?
  id_komoditi        Int?

  @@ignore
  @@schema("public")
}

model z_tb_komoditi_provinsi {
  id_komoditi_provinsi Int?
  id_komoditi          Int?

  @@ignore
  @@schema("public")
}

model z_tb_komoditi_sektor {
  id_komoditi_sektor Int?
  nama               String? @db.VarChar(50)
  keterangan         String?

  @@ignore
  @@schema("public")
}

model z_tb_komoditi_sektor_tr {
  id_komoditi_sektor_tr Int?
  id_komoditi_sektor    Int?
  kd_bahasa             String? @db.VarChar(2)
  nama                  String? @db.Var<PERSON>har(50)
  keterangan            String?

  @@ignore
  @@schema("public")
}

model z_tb_komoditi_tr {
  id_komoditi_tr Int?
  id_komoditi    Int?
  kd_bahasa      String? @db.VarChar(2)
  nama           String? @db.VarChar(50)
  keterangan     String?
  warna          String? @db.VarChar(7)

  @@ignore
  @@schema("public")
}