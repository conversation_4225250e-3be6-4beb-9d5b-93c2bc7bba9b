model tb_sektor_daerah {
  id_sektor_daerah   Int
  tipe               Int
  id_sektor_nasional Int
  id_adm_kabkot      Int?
  id_adm_provinsi    Int?
  deskripsi_singkat  String
  deskripsi          String
  potensi_pasar      String
  no_dokumen         String? @db.Var<PERSON>har(255)
  perihal            String? @db.VarChar(255)
  file_dokumen       String? @db.VarChar(255)
  status             Int

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_file {
  id_sektor_daerah_file Int
  id_sektor_daerah      Int
  tipe                  Int
  jenis                 Int
  nama                  String @db.VarChar(255)
  judul                 String @db.VarChar(255)
  keterangan            String

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_file_tr {
  id_sektor_daerah_file Int
  kd_bahasa             String @db.VarChar(2)
  nama                  String @db.VarChar(255)
  judul                 String @db.<PERSON>ar<PERSON>har(255)
  keterangan            String

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_insentif {
  id_sektor_daerah_insentif Int
  id_sektor_daerah          Int
  nama                      String @db.VarChar(255)
  deskripsi                 String
  status                    Int

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_insentif_file {
  id_sektor_daerah_insentif_file Int
  id_sektor_daerah_insentif      Int
  tipe                           Int
  jenis                          Int
  nama                           String @db.VarChar(255)
  judul                          String @db.VarChar(255)
  keterangan                     String

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_insentif_file_tr {
  id_sektor_daerah_insentif_file Int
  kd_bahasa                      String @db.VarChar(2)
  nama                           String @db.VarChar(255)
  judul                          String @db.VarChar(255)
  keterangan                     String

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_insentif_status {
  id_sektor_daerah_insentif Int
  status                    Int
  status_proses             Int
  keterangan                String?
  created_by                Int
  created_date              DateTime  @db.Timestamp(6)
  updated_by                Int?
  updated_date              DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_insentif_tr {
  id_sektor_daerah_insentif Int
  kd_bahasa                 String @db.VarChar(2)
  nama                      String @db.VarChar(255)
  deskripsi                 String

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_lq {
  id_sektor_daerah Int
  tahun_pdrb       Int
  pdrb_sektor      Float
  pdrb_total       Float
  tahun_pdb        Int
  pdb_sektor       Float
  pdb_total        Float
  nilai_lq         Float

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_pdrb {
  id_sektor_daerah_pdrb Int
  id_adm_provinsi       Int?
  id_adm_kabkot         Int?
  id_sektor_nasional    Int
  tahun_pdrb            Int
  id_sumber_data        Int
  jumlah_pdrb           Decimal @db.Decimal(19, 0)
  status                Int

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_pdrb_status {
  id_sektor_daerah_pdrb Int
  tahun_pdrb            Int
  status                Int
  status_proses         Int
  keterangan            String?
  created_by            Int
  created_date          DateTime  @db.Timestamp(6)
  updated_by            Int?
  updated_date          DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_status {
  id_sektor_daerah Int
  status           Int
  status_proses    Int
  keterangan       String?
  created_by       Int
  created_date     DateTime  @db.Timestamp(6)
  updated_by       Int?
  updated_date     DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_sumber_data {
  id_sektor_daerah Int
  id_sumber_data   Int

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_tr {
  id_sektor_daerah  Int
  kd_bahasa         String  @db.VarChar(2)
  deskripsi_singkat String
  deskripsi         String
  potensi_pasar     String
  perihal           String? @db.VarChar(255)
  file_dokumen      String? @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_value_detail {
  id_sektor_daerah         Int
  id_sektor_nasional_value Int
  tahun                    Int
  numeric_value            Float?    @db.Real
  string_value             String?   @db.VarChar(255)
  date_value               DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional {
  id_sektor_nasional Int
  deskripsi_singkat  String
  deskripsi          String
  potensi_pasar      String
  file_icon          String @db.VarChar(255)
  file_image         String @db.VarChar(255)
  status             Int

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_file {
  id_sektor_nasional_file Int
  id_sektor_nasional      Int
  tipe                    Int
  jenis                   Int
  nama                    String @db.VarChar(255)
  judul                   String @db.VarChar(255)
  keterangan              String

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_file_tr {
  id_sektor_nasional_file Int
  kd_bahasa               String @db.VarChar(2)
  nama                    String @db.VarChar(255)
  judul                   String @db.VarChar(255)
  keterangan              String

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_insentif {
  id_sektor_nasional_insentif Int
  id_sektor_nasional          Int
  nama                        String @db.VarChar(255)
  deskripsi                   String
  status                      Int

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_insentif_file {
  id_sektor_nasional_insentif_file Int
  id_sektor_nasional_insentif      Int
  tipe                             Int
  jenis                            Int
  nama                             String @db.VarChar(255)
  judul                            String @db.VarChar(255)
  keterangan                       String

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_insentif_file_tr {
  id_sektor_nasional_insentif_file Int
  kd_bahasa                        String @db.VarChar(2)
  nama                             String @db.VarChar(255)
  judul                            String @db.VarChar(255)
  keterangan                       String

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_insentif_status {
  id_sektor_nasional_insentif Int
  status                      Int
  status_proses               Int
  keterangan                  String?
  created_by                  Int
  created_date                DateTime  @db.Timestamp(6)
  updated_by                  Int?
  updated_date                DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_insentif_tr {
  id_sektor_nasional_insentif Int
  kd_bahasa                   String @db.VarChar(2)
  nama                        String @db.VarChar(255)
  deskripsi                   String

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_pdb {
  id_sektor_nasional Int
  tahun_pdb          Int
  id_sumber_data     Int
  jumlah_pdb         Decimal @db.Decimal(19, 0)
  status             Int

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_pdb_status {
  id_sektor_nasional Int
  tahun_pdb          Int
  status             Int
  status_proses      Int
  keterangan         String?
  created_by         Int
  created_date       DateTime  @db.Timestamp(6)
  updated_by         Int?
  updated_date       DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_ref {
  id_sektor Int
  nama      String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_ref_tr {
  id_sektor Int
  kd_bahasa String @db.VarChar(2)
  nama      String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_status {
  id_sektor_nasional Int
  status             Int
  status_proses      Int
  keterangan         String?
  created_by         Int
  created_date       DateTime  @db.Timestamp(6)
  updated_by         Int?
  updated_date       DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_sumber_data {
  id_sektor_nasional Int
  id_sumber_data     Int

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_tr {
  id_sektor_nasional Int
  kd_bahasa          String @db.VarChar(2)
  deskripsi_singkat  String
  deskripsi          String
  potensi_pasar      String
  file_icon          String @db.VarChar(255)
  file_image         String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_value {
  id_sektor_nasional_value Int
  id_sektor_nasional       Int
  nama                     String @db.VarChar(255)
  tipe                     Int    @db.SmallInt
  satuan                   String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_value_detail {
  id_sektor_nasional_value Int
  tahun                    Int
  numeric_value            Float?    @db.Real
  string_value             String?   @db.VarChar(255)
  date_value               DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_value_tr {
  id_sektor_nasional_value Int
  kd_bahasa                String @db.VarChar(2)
  nama                     String @db.VarChar(255)
  satuan                   String @db.VarChar(255)

  @@ignore
  @@schema("public")
}