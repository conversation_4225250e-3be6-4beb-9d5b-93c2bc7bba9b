model tb_glossary {
  id_glossary   Int
  glossary_type String    @db.Var<PERSON>har(20)
  title         String    @db.VarChar(150)
  description   String    @db.VarChar(2000)
  modified_time DateTime? @default(dbgenerated("CURRENT_DATE")) @db.Date

  @@ignore
  @@schema("public")
}

model tb_glossary_tr {
  id_glossary_tr Int
  id_glossary    Int
  kode_bahasa    String    @db.VarChar(2)
  title          String    @db.VarChar(150)
  description    String    @db.VarChar(2000)
  modified_time  DateTime? @default(dbgenerated("CURRENT_DATE")) @db.Date

  @@ignore
  @@schema("public")
}