model tb_pdrb_kabkot {
  id_pdrb_kabkot   Int
  id_adm_kabkot    Int
  tahun            Int
  id_sektor        Int
  id_sumber_data   Int?
  pct              Float @db.Real
  jumlah           Float
  laju_pertumbuhan Float
  status           Int

  @@ignore
  @@schema("public")
}

model tb_pdrb_kabkot_status {
  id_pdrb_kabkot Int
  status         Int
  status_proses  Int
  keterangan     String
  created_by     Int
  created_date   DateTime  @db.Timestamp(6)
  updated_by     Int?
  updated_date   DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_pdrb_provinsi {
  id_pdrb_provinsi Int
  id_adm_provinsi  Int
  tahun            Int
  id_sektor        Int
  id_sumber_data   Int?
  pct              Float @db.Real
  jumlah           Float
  laju_pertumbuhan Float
  status           Int

  @@ignore
  @@schema("public")
}

model tb_pdrb_provinsi_status {
  id_pdrb_provinsi Int
  status           Int
  status_proses    Int
  keterangan       String
  created_by       Int
  created_date     DateTime  @db.Timestamp(6)
  updated_by       Int?
  updated_date     DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_pdrb_sektor {
  id_pdrb_sektor Int
  nama           String  @db.VarChar(255)
  warna          String? @db.VarChar(7)

  @@ignore
  @@schema("public")
}

model tb_pdrb_sektor_tr {
  id_pdrb_sektor_tr Int
  id_pdrb_sektor    Int
  kd_bahasa         String  @db.VarChar(2)
  nama              String  @db.VarChar(255)
  warna             String? @db.VarChar(7)

  @@ignore
  @@schema("public")
}