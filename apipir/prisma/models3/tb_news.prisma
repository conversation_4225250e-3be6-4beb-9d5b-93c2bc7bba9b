model tb_news {
  id                Int     @default(autoincrement())
  jenis             Int
  judul             String  @db.VarChar(255)
  deskripsi_singkat String  @db.VarChar(500)
  deskripsi         String
  status            Int
  file_cover        String? @db.<PERSON>ar<PERSON>har(255)

  @@ignore
  @@schema("public")
}

model tb_news_file {
  id_news_file Int    @default(autoincrement())
  id_news      Int
  tipe         Int
  nama         String @db.VarChar(255)
  judul        String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_news_status {
  id_news       Int
  status        Int
  created_by    Int
  created_date  DateTime  @db.Timestamp(6)
  updated_by    Int?
  updated_date  DateTime? @db.Timestamp(6)
  status_proses Int
  keterangan    String?

  @@ignore
  @@schema("public")
}

model tb_news_tr {
  id_news           Int
  kd_bahasa         String @db.VarChar(2)
  judul             String @db.VarChar(255)
  deskripsi_singkat String @db.VarChar(500)
  deskripsi         String

  @@ignore
  @@schema("public")
}