model tb_sub_sektor_daerah {
  id_sub_sektor_daerah   Int
  id_sektor_daerah       Int
  id_sub_sektor_nasional Int
  deskripsi_singkat      String
  deskripsi              String
  status                 Int

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_daerah_file {
  id_sub_sektor_daerah_file Int
  id_sub_sektor_daerah      Int
  tipe                      Int
  jenis                     Int
  nama                      String @db.VarChar(255)
  judul                     String @db.VarChar(255)
  keterangan                String

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_daerah_file_tr {
  id_sub_sektor_daerah_file Int
  kd_bahasa                 String @db.VarChar(2)
  nama                      String @db.VarChar(255)
  judul                     String @db.VarChar(255)
  keterangan                String

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_daerah_status {
  id_sub_sektor_daerah Int
  status               Int
  status_proses        Int
  keterangan           String?
  created_by           Int
  created_date         DateTime  @db.Timestamp(6)
  updated_by           Int?
  updated_date         DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_daerah_sumber_data {
  id_sub_sektor_daerah Int
  id_sumber_data       Int

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_daerah_tr {
  id_sub_sektor_daerah Int
  kd_bahasa            String @db.VarChar(2)
  deskripsi_singkat    String
  deskripsi            String

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_daerah_value_detail {
  id_sub_sektor_daerah         Int
  id_sub_sektor_nasional_value Int
  tahun                        Int
  numeric_value                Float?    @db.Real
  string_value                 String?   @db.VarChar(255)
  date_value                   DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_nasional {
  id_sub_sektor_nasional Int
  id_sektor_nasional     Int
  id_sub_sektor          Int
  deskripsi_singkat      String?
  deskripsi              String
  file_icon              String  @db.VarChar(255)
  file_image             String? @db.VarChar(255)
  status                 Int

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_nasional_file {
  id_sub_sektor_nasional_file Int
  id_sub_sektor_nasional      Int
  tipe                        Int
  jenis                       Int
  nama                        String @db.VarChar(255)
  judul                       String @db.VarChar(255)
  keterangan                  String

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_nasional_file_tr {
  id_sub_sektor_nasional_file Int
  kd_bahasa                   String @db.VarChar(2)
  nama                        String @db.VarChar(255)
  judul                       String @db.VarChar(255)
  keterangan                  String

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_nasional_ref {
  id_sub_sektor Int
  id_sektor     Int
  nama          String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_nasional_ref_tr {
  id_sub_sektor Int
  kd_bahasa     String @db.VarChar(2)
  nama          String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_nasional_status {
  id_sub_sektor_nasional Int
  status                 Int
  status_proses          Int
  keterangan             String?
  created_by             Int
  created_date           DateTime  @db.Timestamp(6)
  updated_by             Int?
  updated_date           DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_nasional_sumber_data {
  id_sub_sektor_nasional Int
  id_sumber_data         Int

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_nasional_tr {
  id_sub_sektor_nasional Int
  kd_bahasa              String  @db.VarChar(2)
  deskripsi_singkat      String?
  deskripsi              String
  file_icon              String  @db.VarChar(255)
  file_image             String? @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_nasional_value {
  id_sub_sektor_nasional_value Int
  id_sub_sektor_nasional       Int
  nama                         String @db.VarChar(255)
  tipe                         Int    @db.SmallInt
  satuan                       String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_nasional_value_detail {
  id_sub_sektor_nasional_value Int
  tahun                        Int
  numeric_value                Float?    @db.Real
  string_value                 String?   @db.VarChar(255)
  date_value                   DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sub_sektor_nasional_value_tr {
  id_sub_sektor_nasional_value Int
  kd_bahasa                    String @db.VarChar(2)
  nama                         String @db.VarChar(255)
  satuan                       String @db.VarChar(255)

  @@ignore
  @@schema("public")
}