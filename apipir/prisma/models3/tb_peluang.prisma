model tb_peluang_daerah {
  id_peluang_daerah    Int      @id(map: "pk_peluang_daerah") @default(autoincrement())
  id_adm_provinsi      Int?
  id_adm_kabkot        Int?
  id_sub_sektor_daerah Int
  status_peluang       Int      @db.SmallInt
  status               Int      @db.SmallInt
  id_bandara           Int?
  jarak_bandara        Int      @default(0) @db.SmallInt
  id_pelabuhan         Int?
  jarak_pelabuhan      Int      @default(0) @db.SmallInt
  jarak_ibukota        Int      @default(0) @db.SmallInt
  kode_kbli            String?  @db.VarChar(150)
  judul                String?  @db.VarChar(255)
  lokasi               String?  @db.VarChar(255)
  tahun                Int      @default(2022) @db.SmallInt
  keterangan           String?
  aspek_pasar          String?
  aspek_teknis         String?
  lon                  Float?
  lat                  Float?
  zoom_peta_default    Int?
  is_ikn               Boolean?

  @@schema("public")
}

model tb_peluang_daerah_file {
  id_peluang_daerah_file Int
  id_peluang_daerah      Int
  tipe                   Int
  jenis                  Int
  nama                   String @db.VarChar(255)
  judul                  String @db.VarChar(255)
  keterangan             String

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_file_tr {
  id_peluang_daerah_file Int
  kd_bahasa              String @db.VarChar(2)
  nama                   String @db.VarChar(255)
  judul                  String @db.VarChar(255)
  keterangan             String

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_insentif {
  id_peluang_daerah_insentif  Int
  id_peluang_daerah           Int
  id_sektor_nasional_insentif Int?
  id_sektor_daerah_insentif   Int?

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_kelayakan {
  id_peluang_daerah  Int
  pct_cost_capital   Int   @db.SmallInt
  year_period        Int   @db.SmallInt
  base_revenue       Float
  pct_revenue_growth Int   @db.SmallInt
  base_opex          Float
  pct_inflation      Int   @db.SmallInt
  initial_invesment  Float
  pct_salvage_value  Int   @db.SmallInt
  irr                Float
  npv                Float
  pp                 Int   @db.SmallInt

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_kelayakan_detail {
  id_peluang_daerah Int
  jenis             Int
  tahun             Int
  nilai             Float

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_komoditi {
  id_peluang_daerah  Int
  id_komoditi_daerah Int
  jenis              String @db.VarChar(255)
  manfaat            String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_kontak {
  id_peluang_daerah Int
  id_peluang_kontak Int

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_layer_spasial {
  id_peluang_daerah_layer_spasial Int
  id_peluang_daerah               Int
  nama_layer                      String @db.VarChar(500)
  tipe                            Int
  url_service                     String @db.VarChar(500)
  status                          Int

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_status {
  id_peluang_daerah Int
  status            Int       @db.SmallInt
  status_proses     Int       @db.SmallInt
  keterangan        String?
  created_by        Int
  created_date      DateTime  @db.Timestamp(6)
  updated_by        Int?
  updated_date      DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_tr {
  id_peluang_daerah Int
  kd_bahasa         String  @db.VarChar(2)
  judul             String? @db.VarChar(255)
  lokasi            String? @db.VarChar(255)
  keterangan        String?
  aspek_pasar       String?
  aspek_teknis      String?

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_berita_mapping {
  id_peluang_kabkot Int
  id_berita         Int
  created_time      DateTime? @default(now()) @db.Timestamptz(6)

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_file {
  id_peluang_kabkot_file Int
  id_peluang_kabkot      Int
  tipe                   Int
  nama                   String? @db.VarChar(255)
  url_rest               String? @db.VarChar(255)
  status                 Int?    @default(0)

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_file_tr {
  id_peluang_kabkot_file_tr Int
  id_peluang_kabkot         Int
  kd_bahasa                 String  @db.VarChar(2)
  tipe                      Int
  nama                      String? @db.VarChar(255)
  url_rest                  String? @db.VarChar(255)
  status                    Int?    @default(0)

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_insentif {
  id_peluang_kabkot Int
  id_jenis_insentif Int

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_kontak {
  id_peluang_kabkot Int
  id_peluang_kontak Int

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_likes_counter {
  id_likes          Int
  id_peluang_kabkot Int
  ip_pengunjung     String    @db.VarChar(15)
  created_time      DateTime? @default(now()) @db.Timestamptz(6)

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_status {
  id_peluang_kabkot Int
  status            Int
  status_proses     Int
  keterangan        String?
  created_by        Int
  created_date      DateTime  @db.Timestamp(6)
  updated_by        Int?
  updated_date      DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_tr {
  id_peluang_kabkot_tr Int
  id_peluang_kabkot    Int
  kd_bahasa            String  @db.VarChar(2)
  nama                 String  @db.VarChar(255)
  keterangan           String
  nama_singkat         String? @db.VarChar(36)
  deskripsi_singkat    String? @db.VarChar(120)
  deskripsi            String? @db.VarChar(600)
  lokasi_kawasan       String? @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_peluang_kontak {
  id_peluang_kontak Int
  nama              String? @db.VarChar(255)
  alamat            String? @db.VarChar(255)
  url_web           String? @db.VarChar(255)
  cp                String? @db.VarChar(255)
  jabatan_cp        String? @db.VarChar(255)
  email             String? @db.VarChar(255)
  no_telp           String? @db.VarChar(255)
  no_fax            String? @db.VarChar(255)
  lon               Float?
  lat               Float?
  id_adm_provinsi   Int?
  id_adm_kabkot     Int?

  @@ignore
  @@schema("public")
}

model tb_peluang_prioritas {
  id_peluang_prioritas Int
  nama                 String @db.VarChar(50)
  keterangan           String

  @@ignore
  @@schema("public")
}

model tb_peluang_prioritas_tr {
  id_peluang_prioritas_tr Int
  id_peluang_prioritas    Int
  kd_bahasa               String  @db.VarChar(2)
  nama                    String? @db.VarChar(50)
  keterangan              String

  @@ignore
  @@schema("public")
}

model tb_peluang_provinsi_file {
  id_peluang_provinsi_file Int
  id_peluang_provinsi      Int
  tipe                     Int
  nama                     String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_peluang_provinsi_status {
  id_peluang_provinsi Int
  status              Int
  status_proses       Int
  keterangan          String
  created_by          Int
  created_date        DateTime  @db.Timestamp(6)
  updated_by          Int?
  updated_date        DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_peluang_provinsi_tr {
  id_peluang_provinsi_tr Int
  id_peluang_provinsi    Int
  kd_bahasa              String @db.VarChar(2)
  nama                   String @db.VarChar(255)
  keterangan             String

  @@ignore
  @@schema("public")
}

model tb_peluang_sektor {
  id_peluang_sektor Int
  nama              String  @db.VarChar(50)
  keterangan        String
  icon              String? @db.VarChar(255)
  iconmap           String? @db.VarChar(255)
  image             String? @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_peluang_sektor_tr {
  id_peluang_sektor_tr Int
  id_peluang_sektor    Int
  kd_bahasa            String @db.VarChar(2)
  nama                 String @db.VarChar(50)
  keterangan           String

  @@ignore
  @@schema("public")
}