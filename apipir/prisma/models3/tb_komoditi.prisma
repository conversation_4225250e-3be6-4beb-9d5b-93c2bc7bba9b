model tb_komoditi {
  id_komoditi Int
  id_sektor   Int
  nama        String?  @db.VarChar(50)
  keterangan  String
  warna       String?  @db.VarChar(7)
  is_internal Boolean?
  url_service String?  @db.Var<PERSON>har(255)

  @@ignore
  @@schema("public")
}

model tb_komoditi_daerah {
  id_komoditi_daerah   Int
  id_komoditi          Int
  id_sub_sektor_daerah Int
  deskripsi_singkat    String
  deskripsi            String
  file_icon            String  @db.VarChar(255)
  sentra_produksi      String? @db.VarChar(500)
  status               Int

  @@ignore
  @@schema("public")
}

model tb_komoditi_daerah_file {
  id_komoditi_daerah_file Int
  id_komoditi_daerah      Int
  tipe                    Int
  jenis                   Int
  nama                    String @db.VarChar(255)
  judul                   String @db.VarChar(255)
  keterangan              String

  @@ignore
  @@schema("public")
}

model tb_komoditi_daerah_file_tr {
  id_komoditi_daerah_file Int
  kd_bahasa               String @db.Var<PERSON>har(2)
  nama                    String @db.VarChar(255)
  judul                   String @db.VarChar(255)
  keterangan              String

  @@ignore
  @@schema("public")
}

model tb_komoditi_daerah_status {
  id_komoditi_daerah Int
  status             Int
  status_proses      Int
  keterangan         String?
  created_by         Int
  created_date       DateTime  @db.Timestamp(6)
  updated_by         Int?
  updated_date       DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_komoditi_daerah_tr {
  id_komoditi_daerah Int
  kd_bahasa          String  @db.VarChar(2)
  deskripsi_singkat  String
  deskripsi          String
  file_icon          String  @db.VarChar(255)
  sentra_produksi    String? @db.VarChar(500)

  @@ignore
  @@schema("public")
}

model tb_komoditi_daerah_value {
  id_komoditi_daerah_value Int
  id_komoditi_daerah       Int
  nama                     String @db.VarChar(255)
  satuan                   String @db.VarChar(255)
  tipe                     Int    @db.SmallInt

  @@ignore
  @@schema("public")
}

model tb_komoditi_daerah_value_detail {
  id_komoditi_daerah_value Int
  tahun                    Int
  numeric_value            Float?    @db.Real
  string_value             String?   @db.VarChar(255)
  date_value               DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_komoditi_daerah_value_tr {
  id_komoditi_daerah_value Int
  kd_bahasa                String @db.VarChar(2)
  nama                     String @db.VarChar(255)
  satuan                   String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_komoditi_kabkot_status {
  id_komoditi_kabkot Int
  status             Int
  status_proses      Int
  keterangan         String
  created_by         Int
  created_date       DateTime  @db.Timestamp(6)
  updated_by         Int?
  updated_date       DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_komoditi_nasional {
  id_komoditi_nasional   Int
  id_sub_sektor_nasional Int
  id_komoditi            Int
  deskripsi_singkat      String
  deskripsi              String
  file_icon              String @db.VarChar(255)
  status                 Int

  @@ignore
  @@schema("public")
}

model tb_komoditi_nasional_file {
  id_komoditi_nasional_file Int
  id_komoditi_nasional      Int
  tipe                      Int
  jenis                     Int
  nama                      String @db.VarChar(255)
  judul                     String @db.VarChar(255)
  keterangan                String

  @@ignore
  @@schema("public")
}

model tb_komoditi_nasional_file_tr {
  id_komoditi_nasional_file Int
  kd_bahasa                 String @db.VarChar(2)
  nama                      String @db.VarChar(255)
  judul                     String @db.VarChar(255)
  keterangan                String

  @@ignore
  @@schema("public")
}

model tb_komoditi_nasional_ref {
  id_komoditi   Int
  id_sub_sektor Int
  nama          String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_komoditi_nasional_ref_tr {
  id_komoditi Int
  kd_bahasa   String @db.VarChar(2)
  nama        String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_komoditi_nasional_status {
  id_komoditi_nasional Int
  status               Int
  status_proses        Int
  keterangan           String?
  created_by           Int
  created_date         DateTime  @db.Timestamp(6)
  updated_by           Int?
  updated_date         DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_komoditi_nasional_tr {
  id_komoditi_nasional Int
  kd_bahasa            String @db.VarChar(2)
  deskripsi_singkat    String
  deskripsi            String
  file_icon            String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_komoditi_nasional_value {
  id_komoditi_nasional_value Int
  id_komoditi_nasional       Int
  nama                       String @db.VarChar(255)
  satuan                     String @db.VarChar(255)
  tipe                       Int    @db.SmallInt

  @@ignore
  @@schema("public")
}

model tb_komoditi_nasional_value_detail {
  id_komoditi_nasional_value Int
  tahun                      Int
  numeric_value              Float?    @db.Real
  string_value               String?   @db.VarChar(255)
  date_value                 DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_komoditi_nasional_value_tr {
  id_komoditi_nasional_value Int
  kd_bahasa                  String @db.VarChar(2)
  nama                       String @db.VarChar(255)
  satuan                     String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_komoditi_provinsi_status {
  id_komoditi_provinsi Int
  status               Int
  status_proses        Int
  keterangan           String
  created_by           Int
  created_date         DateTime  @db.Timestamp(6)
  updated_by           Int?
  updated_date         DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_komoditi_satuan {
  id_komoditi_satuan Int
  nama               String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_komoditi_sektor {
  id_komoditi_sektor Int
  nama               String? @db.VarChar(50)
  keterangan         String

  @@ignore
  @@schema("public")
}

model tb_komoditi_sektor_tr {
  id_komoditi_sektor_tr Int
  id_komoditi_sektor    Int
  kd_bahasa             String  @db.VarChar(2)
  nama                  String? @db.VarChar(50)
  keterangan            String

  @@ignore
  @@schema("public")
}

model tb_komoditi_tr {
  id_komoditi_tr Int
  id_komoditi    Int
  kd_bahasa      String  @db.VarChar(2)
  nama           String? @db.VarChar(50)
  keterangan     String
  warna          String? @db.VarChar(7)

  @@ignore
  @@schema("public")
}

model tb_komoditi_kabkot {
  id_komoditi_kabkot  Int      @id @default(autoincrement())
  id_adm_kabkot       Int
  id_komoditi         Int?
  tahun               Int
  id_sumber_data      Int?
  status              Int
  lon                 Float?
  lat                 Float?
  shape               Bytes?   @db.VarBinary(255) // Assuming sde.st_geometry is stored as binary
  id_satuan           Int
  luas_lahan          Float?
  nama                String?  @db.VarChar(255)
  nilai_produksi      Float?
  bijih_hipotik       Float?
  logam_hipotik       Float?
  bijih_tereka        Float?
  logam_tereka        Float?
  bijih_tertunjuk     Float?
  logam_tertunjuk     Float?
  bijih_terukur       Float?
  logam_terukur       Float?
  bijih_terkira       Float?
  logam_terkira       Float?
  bijih_terbukti      Float?
  logam_terbukti      Float?
  logam_status        String?  @db.VarChar(255)
  non_logam_hipotik   Float?
  non_logam_tereka    Float?
  non_logam_tertunjuk Float?
  non_logam_terukur   Float?
  non_logam_terkira   Float?
  non_logam_terbukti  Float?
  non_logam_status    String?  @db.VarChar(255)
  panas_spekulasi     Float?
  panas_hipotik       Float?
  panas_terduga       Float?
  panas_mungkin       Float?
  panas_terbukti      Float?
  panas_terpasang     Float?
  panas_temperatur    Float?
  panas_klasifikasi   String?  @db.VarChar(255)
}
