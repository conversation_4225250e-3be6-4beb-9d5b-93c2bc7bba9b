model tb_unduh_data {
  id_unduh_data        Int
  id_unduh_data_tujuan Int
  nama                 String   @db.VarChar(255)
  email                String   @db.VarChar(255)
  no_telp              String   @db.VarChar(255)
  asal_negara          String   @db.VarChar(255)
  id_konten            Int?
  ip_pengunjung        String   @db.VarChar(255)
  created_date         DateTime @db.Timestamp(6)
  id_jenis_konten      String?  @db.VarChar(255)
  status_minat         Boolean?

  @@ignore
  @@schema("public")
}

model tb_unduh_data_keperluan {
  id_unduh_data_keperluan Int
  nama                    String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_unduh_data_keperluan_tr {
  id_unduh_data_keperluan_tr Int
  id_unduh_data_keperluan    Int
  kd_bahasa                  String @db.VarChar(2)
  nama                       String @db.Var<PERSON>har(255)

  @@ignore
  @@schema("public")
}