model tb_nswi_jenis {
  id_nswi_jenis Int
  nama          String? @db.VarChar(50)
  keterangan    String

  @@ignore
  @@schema("public")
}

model tb_nswi_jenis_data {
  id_nswi_jenis_data Int
  nama               String? @db.Var<PERSON>har(50)
  keterangan         String

  @@ignore
  @@schema("public")
}

model tb_nswi_jenis_data_tr {
  id_nswi_jenis_data_tr Int
  id_nswi_jenis_data    Int
  kd_bahasa             String  @db.VarChar(2)
  nama                  String? @db.VarChar(50)
  keterangan            String

  @@ignore
  @@schema("public")
}

model tb_nswi_jenis_tr {
  id_nswi_jenis_tr Int
  id_nswi_jenis    Int
  kd_bahasa        String  @db.VarChar(2)
  nama             String? @db.VarChar(50)
  keterangan       String

  @@ignore
  @@schema("public")
}

model tb_nswi_kabkot {
  id_nswi_kabkot   Int
  id_adm_kabkot    Int
  id_sumber_data   Int
  id_sektor        Int
  id_jenis         Int
  id_jenis_data    Int
  tahun            Int
  jumlah_proyek    Int
  jumlah_investasi Float
  status           Int

  @@ignore
  @@schema("public")
}

model tb_nswi_kabkot_status {
  id_nswi_kabkot Int
  status         Int
  status_proses  Int
  keterangan     String
  created_by     Int
  created_date   DateTime  @db.Timestamp(6)
  updated_by     Int?
  updated_date   DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_nswi_provinsi {
  id_nswi_provinsi Int
  id_adm_provinsi  Int
  id_sumber_data   Int
  id_sektor        Int
  id_jenis         Int
  id_jenis_data    Int
  tahun            Int
  jumlah_proyek    Int
  jumlah_investasi Float
  status           Int

  @@ignore
  @@schema("public")
}

model tb_nswi_provinsi_status {
  id_nswi_provinsi Int
  status           Int
  status_proses    Int
  keterangan       String
  created_by       Int
  created_date     DateTime  @db.Timestamp(6)
  updated_by       Int?
  updated_date     DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_nswi_sektor {
  id_nswi_sektor Int
  nama           String? @db.VarChar(255)
  keterangan     String

  @@ignore
  @@schema("public")
}

model tb_nswi_sektor_tr {
  id_nswi_sektor_tr Int
  id_nswi_sektor    Int
  kd_bahasa         String  @db.VarChar(2)
  nama              String? @db.VarChar(255)
  keterangan        String

  @@ignore
  @@schema("public")
}