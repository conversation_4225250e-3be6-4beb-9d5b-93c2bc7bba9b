model tb_utilitas_kabkot {
  id_utilitas_kabkot  Int     @default(autoincrement())
  id_adm_kabkot       Int
  id_sumber_data      Int?
  tahun               Int?
  keterangan          String?
  produksi_air_bersih Float?
  daya_terpasang      Float?
  jumlah_bts          Float?
  panjang_jalan       Float?
  status              Int?

  @@ignore
  @@schema("public")
}

model tb_utilitas_kabkot_status {
  id_utilitas_kabkot Int
  status             Int
  status_proses      Int
  keterangan         String?
  created_by         Int
  created_date       DateTime  @db.Timestamp(6)
  updated_by         Int?
  updated_date       DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_utilitas_kabkot_tr {
  id_utilitas_kabkot_tr Int     @default(autoincrement())
  id_utilitas_kabkot    Int
  kd_bahasa             String  @db.VarChar(2)
  keterangan            String?

  @@ignore
  @@schema("public")
}

model tb_utilitas_provinsi {
  id_utilitas_provinsi Int     @default(autoincrement())
  id_adm_provinsi      Int
  id_sumber_data       Int?
  tahun                Int?
  keterangan           String?
  produksi_air_bersih  Float?
  daya_terpasang       Float?
  jumlah_bts           Float?
  panjang_jalan        Float?
  status               Int?

  @@ignore
  @@schema("public")
}

model tb_utilitas_provinsi_status {
  id_utilitas_provinsi Int
  status               Int
  status_proses        Int
  keterangan           String?
  created_by           Int
  created_date         DateTime  @db.Timestamp(6)
  updated_by           Int?
  updated_date         DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_utilitas_provinsi_tr {
  id_utilitas_provinsi_tr Int     @default(autoincrement())
  id_utilitas_provinsi    Int
  kd_bahasa               String  @db.VarChar(2)
  keterangan              String?

  @@ignore
  @@schema("public")
}