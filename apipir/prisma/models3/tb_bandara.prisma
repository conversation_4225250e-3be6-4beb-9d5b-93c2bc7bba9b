model tb_bandara_kategori {
  id_bandara_kategori Int
  nama                String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_bandara_kategori_tr {
  id_bandara_kategori_tr Int
  id_bandara_kategori    Int
  kd_bahasa              String @db.VarChar(2)
  nama                   String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_bandara_kelas {
  id_bandara_kelas Int
  nama             String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_bandara_kelas_tr {
  id_bandara_kelas_tr Int
  id_bandara_kelas    Int
  kd_bahasa           String @db.VarChar(2)
  nama                String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_bandara_status {
  id_bandara    Int
  status        Int
  status_proses Int
  keterangan    String
  created_by    Int
  created_date  DateTime  @db.Timestamp(6)
  updated_by    Int?
  updated_date  DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_bandara_tr {
  id_bandara_tr Int
  id_bandara    Int
  kd_bahasa     String @db.VarChar(2)
  nama          String @db.VarChar(50)
  keterangan    String

  @@ignore
  @@schema("public")
}