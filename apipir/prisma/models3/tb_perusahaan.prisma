model tb_perusahaan {
  id_perusahaan     Int
  id_tipe           Int
  id_adm_kabkot     Int
  nib               String @db.VarChar(15)
  kd_pa             String @db.VarChar(255)
  kd_kmk            String @db.VarChar(255)
  nama              String @db.VarChar(255)
  status_verifikasi Int
  status            Int

  @@ignore
  @@schema("public")
}

model tb_perusahaan_cabang_tr {
  id_perusahaan_cabang_tr Int
  id_perusahaan_cabang    Int
  kd_bahasa               String @db.VarChar(2)
  nama                    String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_perusahaan_file {
  id_perusahaan_file Int
  id_perusahaan      Int
  tipe               Int
  nama               String @db.VarChar(255)
  judul              String @db.VarChar(255)
  keterangan         String

  @@ignore
  @@schema("public")
}

model tb_perusahaan_kbli {
  id_perusahaan_kbli Int
  id_perusahaan      Int
  id_kbli            Int

  @@ignore
  @@schema("public")
}

model tb_perusahaan_kbli_produk {
  id_perusahaan_kbli_produk Int
  id_perusahaan_kbli        Int
  nama                      String    @db.VarChar(255)
  keterangan                String
  created_by                Int
  created_date              DateTime  @db.Timestamp(6)
  updated_by                Int?
  updated_date              DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_perusahaan_kbli_produk_file {
  id_perusahaan_kbli_produk_file Int
  tb_perusahaan_kbli_produk      Int
  tipe                           Int
  nama                           String @db.VarChar(255)
  judul                          String @db.VarChar(255)
  keterangan                     String

  @@ignore
  @@schema("public")
}

model tb_perusahaan_kbli_produk_tr {
  id_perusahaan_kbli_produk_tr Int
  pk_perusahaan_kbli_produk    Int
  kd_bahasa                    String @db.VarChar(2)
  nama                         String @db.VarChar(255)
  keterangan                   String

  @@ignore
  @@schema("public")
}

model tb_perusahaan_kebutuhan_kbli {
  id_perusahaan_kebutuhan_kbli Int
  id_perusahaan                Int
  id_kbli                      Int

  @@ignore
  @@schema("public")
}

model tb_perusahaan_kebutuhan_produk {
  id_perusahaan_kebutuhan_produk Int
  id_perusahaan_lokasi           Int
  nama                           String    @db.VarChar(255)
  keterangan                     String
  created_by                     Int
  created_date                   DateTime  @db.Timestamp(6)
  updated_by                     Int?
  updated_date                   DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_perusahaan_kebutuhan_produk_tr {
  id_perusahaan_kebutuhan_produk_tr Int
  id_perusahaan_kebutuhan_produk    Int
  kd_bahasa                         String @db.VarChar(2)
  nama                              String @db.VarChar(255)
  keterangan                        String

  @@ignore
  @@schema("public")
}

model tb_perusahaan_status {
  id_perusahaan Int
  status        Int
  status_proses Int
  keterangan    String
  created_by    Int
  created_date  DateTime  @db.Timestamp(6)
  updated_by    Int?
  updated_date  DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_perusahaan_tipe {
  id_perusahaan_tipe Int
  kode               String @db.VarChar(10)
  nama               String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_perusahaan_tipe_tr {
  id_perusahaan_tipe_tr Int
  id_perusahaan_tipe    Int
  kd_bahasa             String @db.VarChar(2)
  nama                  String @db.VarChar(255)

  @@ignore
  @@schema("public")
}