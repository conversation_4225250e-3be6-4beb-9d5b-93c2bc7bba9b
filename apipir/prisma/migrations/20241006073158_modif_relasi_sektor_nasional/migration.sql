-- DropForeignKey
ALTER TABLE "public"."tb_sektor_nasional_value" DROP CONSTRAINT "tb_sektor_nasional_value_id_sektor_nasional_fkey";

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_sumber_data" ADD CONSTRAINT "tb_sektor_nasional_sumber_data_id_sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional"("id_sektor_nasional") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_tr" ADD CONSTRAINT "tb_sektor_nasional_tr_id_sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional"("id_sektor_nasional") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_value" ADD CONSTRAINT "tb_sektor_nasional_value_id_sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional_ref"("id_sektor") ON DELETE RESTRICT ON UPDATE CASCADE;
