/*
  Warnings:

  - A unique constraint covering the columns `[id_sub_sektor_daerah]` on the table `tb_sub_sektor_daerah_tr` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "public"."tb_sektor_nasional_pdb" DROP CONSTRAINT "tb_sektor_nasional_pdb_id_sumber_data_fkey";

-- CreateIndex
CREATE UNIQUE INDEX "tb_sub_sektor_daerah_tr_id_sub_sektor_daerah_key" ON "public"."tb_sub_sektor_daerah_tr"("id_sub_sektor_daerah");

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_pdb" ADD CONSTRAINT "tb_sektor_nasional_pdb_id_sumber_data_fkey" FOREIGN KEY ("id_sumber_data") REFERENCES "public"."tb_sumber_data"("id_sumber_data") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_komoditi_daerah_value_tr" ADD CONSTRAINT "tb_komoditi_daerah_value_tr_id_komoditi_daerah_value_fkey" FOREIGN KEY ("id_komoditi_daerah_value") REFERENCES "public"."tb_komoditi_daerah_value"("id_komoditi_daerah_value") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_daerah_tr" ADD CONSTRAINT "tb_sub_sektor_daerah_tr_id_sub_sektor_daerah_fkey" FOREIGN KEY ("id_sub_sektor_daerah") REFERENCES "public"."tb_sub_sektor_daerah"("id_sub_sektor_daerah") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_daerah_value_detail" ADD CONSTRAINT "tb_sub_sektor_daerah_value_detail_id_sub_sektor_daerah_fkey" FOREIGN KEY ("id_sub_sektor_daerah") REFERENCES "public"."tb_sub_sektor_daerah"("id_sub_sektor_daerah") ON DELETE CASCADE ON UPDATE CASCADE;
