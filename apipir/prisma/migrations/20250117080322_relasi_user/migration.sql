-- AddForeignKey
ALTER TABLE "public"."tb_user_internal" ADD CONSTRAINT "tb_user_internal_id_jabatan_fkey" FOREIGN KEY ("id_jabatan") REFERENCES "public"."tb_ref_user_jabatan"("id_jabatan") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_user_internal_provinsi" ADD CONSTRAINT "tb_user_internal_provinsi_id_adm_provinsi_fkey" FOREIGN KEY ("id_adm_provinsi") REFERENCES "public"."tb_adm_provinsi"("id_adm_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;
