/*
  Warnings:

  - The primary key for the `tb_peluang_daerah_komoditi` table will be changed. If it partially fails, the table could be left without primary key constraint.

*/
-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_komoditi" DROP CONSTRAINT "tb_peluang_daerah_komoditi_pkey",
ADD COLUMN     "id_peluang_daerah_komoditi" SERIAL NOT NULL,
ADD CONSTRAINT "tb_peluang_daerah_komoditi_pkey" PRIMARY KEY ("id_peluang_daerah_komoditi");

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_daerah_komoditi" ADD CONSTRAINT "tb_peluang_daerah_komoditi_id_peluang_daerah_fkey" FOREIGN KEY ("id_peluang_daerah") REFERENCES "public"."tb_peluang_daerah"("id_peluang_daerah") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_daerah_komoditi" ADD CONSTRAINT "tb_peluang_daerah_komoditi_id_komoditi_daerah_fkey" FOREIGN KEY ("id_komoditi_daerah") REFERENCES "public"."tb_komoditi_daerah"("id_komoditi_daerah") ON DELETE CASCADE ON UPDATE CASCADE;
