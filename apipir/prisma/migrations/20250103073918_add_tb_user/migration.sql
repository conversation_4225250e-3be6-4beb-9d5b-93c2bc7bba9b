-- DropForeignKey
ALTER TABLE "public"."tb_user_internal" DROP CONSTRAINT "tb_user_internal_id_user_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_user_internal_kawasan_industri" DROP CONSTRAINT "tb_user_internal_kawasan_industri_id_user_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_user_internal_provinsi" DROP CONSTRAINT "tb_user_internal_provinsi_id_user_fkey";

-- CreateTable
CREATE TABLE "public"."tb_user" (
    "id_user" SERIAL NOT NULL,
    "id_role" INTEGER NOT NULL,
    "username" VARCHAR(100) NOT NULL,
    "password" VARCHAR(128) NOT NULL,
    "email" VARCHAR(100) NOT NULL,
    "first_name" VARCHAR(30) NOT NULL,
    "middle_name" VARCHAR(30),
    "last_name" VARCHAR(30),
    "address" TEXT,
    "phone_number" VARCHAR(30),
    "mobile_number" VARCHAR(30),
    "file_image" VARCHAR(50),
    "status" SMALLINT NOT NULL,
    "last_login" TIMESTAMP(6),
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),
    "code_forgot" VARCHAR(255),
    "code_expired" TIMESTAMP(6),
    "keterangan" TEXT,
    "user_token" VARCHAR(255),
    "jabatan" VARCHAR(255),
    "instansi" TEXT,
    "user_api_key" VARCHAR(36),

    CONSTRAINT "tb_user_pkey" PRIMARY KEY ("id_user")
);
