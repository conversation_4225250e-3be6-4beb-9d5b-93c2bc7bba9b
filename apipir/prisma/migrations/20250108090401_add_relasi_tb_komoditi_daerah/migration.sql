-- AddForeignKey
ALTER TABLE "public"."tb_komoditi_daerah_file_tr" ADD CONSTRAINT "tb_komoditi_daerah_file_tr_id_komoditi_daerah_file_fkey" FOREIGN KEY ("id_komoditi_daerah_file") REFERENCES "public"."tb_komoditi_daerah_file"("id_komoditi_daerah_file") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_komoditi_daerah_tr" ADD CONSTRAINT "tb_komoditi_daerah_tr_id_komoditi_daerah_fkey" FOREIGN KEY ("id_komoditi_daerah") REFERENCES "public"."tb_komoditi_daerah"("id_komoditi_daerah") ON DELETE CASCADE ON UPDATE CASCADE;
