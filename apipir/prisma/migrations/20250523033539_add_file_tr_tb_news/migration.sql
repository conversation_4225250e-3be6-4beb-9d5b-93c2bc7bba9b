-- CreateTable
CREATE TABLE "public"."tb_news_file_tr" (
    "id_news_file_tr" SERIAL NOT NULL,
    "id_news" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(6),
    "updated_at" TIMESTAMP(3),

    CONSTRAINT "tb_news_file_tr_pkey" PRIMARY KEY ("id_news_file_tr")
);

-- AddForeignKey
ALTER TABLE "public"."tb_news_file_tr" ADD CONSTRAINT "tb_news_file_tr_id_news_fkey" FOREIGN KEY ("id_news") REFERENCES "public"."tb_news"("id") ON DELETE CASCADE ON UPDATE CASCADE;
