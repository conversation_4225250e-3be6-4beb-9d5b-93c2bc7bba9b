-- CreateTable
CREATE TABLE "public"."tb_air" (
    "id_air" SERIAL NOT NULL,
    "id_jenis" INTEGER NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "file_logo" VARCHAR(255),
    "alamat" TEXT NOT NULL,
    "no_telp" VARCHAR(30) NOT NULL,
    "no_fax" VARCHAR(30) NOT NULL,
    "email" VARCHAR(50) NOT NULL,
    "status" INTEGER NOT NULL,
    "url_web" VARCHAR(255) NOT NULL,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "is_ikn" BOOLEAN,
    "id_kategori_infrastruktur" INTEGER,

    CONSTRAINT "tb_air_pkey" PRIMARY KEY ("id_air")
);

-- CreateTable
CREATE TABLE "public"."tb_air_jenis" (
    "id_jenis" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_air_jenis_pkey" PRIMARY KEY ("id_jenis")
);

-- CreateTable
CREATE TABLE "public"."tb_air_jenis_tr" (
    "id_air_jenis_tr" SERIAL NOT NULL,
    "id_air_jenis" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_air_jenis_tr_pkey" PRIMARY KEY ("id_air_jenis_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_air_status" (
    "id_air" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),
    "id_air_status" SERIAL NOT NULL,

    CONSTRAINT "tb_air_status_pkey" PRIMARY KEY ("id_air_status")
);

-- CreateTable
CREATE TABLE "public"."tb_air_tr" (
    "id_air_tr" SERIAL NOT NULL,
    "id_air" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "file_logo" VARCHAR(255),

    CONSTRAINT "tb_air_tr_pkey" PRIMARY KEY ("id_air_tr")
);

-- AddForeignKey
ALTER TABLE "public"."tb_air" ADD CONSTRAINT "tb_air_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_air" ADD CONSTRAINT "tb_air_id_jenis_fkey" FOREIGN KEY ("id_jenis") REFERENCES "public"."tb_air_jenis"("id_jenis") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_air" ADD CONSTRAINT "tb_air_id_sumber_data_fkey" FOREIGN KEY ("id_sumber_data") REFERENCES "public"."tb_sumber_data"("id_sumber_data") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_air" ADD CONSTRAINT "tb_air_id_kategori_infrastruktur_fkey" FOREIGN KEY ("id_kategori_infrastruktur") REFERENCES "public"."tb_kategori_infrastruktur"("id_kategori_infrastruktur") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_air_status" ADD CONSTRAINT "tb_air_status_id_air_fkey" FOREIGN KEY ("id_air") REFERENCES "public"."tb_air"("id_air") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_air_tr" ADD CONSTRAINT "tb_air_tr_id_air_fkey" FOREIGN KEY ("id_air") REFERENCES "public"."tb_air"("id_air") ON DELETE CASCADE ON UPDATE CASCADE;
