-- CreateTable
CREATE TABLE "public"."tb_vidio_beranda" (
    "id_vidio" SERIAL NOT NULL,
    "url" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_vidio_beranda_pkey" PRIMARY KEY ("id_vidio")
);

-- CreateTable
CREATE TABLE "public"."tb_ikn" (
    "id_ikn" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "file_image" VARCHAR(255) NOT NULL,
    "file_logo" VARCHAR(255) NOT NULL,
    "lon" DOUBLE PRECISION NOT NULL,
    "lat" DOUBLE PRECISION NOT NULL,
    "view_count" INTEGER NOT NULL,
    "last_view" TIMESTAMP(6) NOT NULL,
    "file_video" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_ikn_pkey" PRIMARY KEY ("id_ikn")
);

-- CreateTable
CREATE TABLE "public"."tb_ikn_data" (
    "id_ikn_data" SERIAL NOT NULL,
    "id_ikn" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "file_image" VARCHAR(255),
    "status" INTEGER NOT NULL,
    "is_fe" BOOLEAN,
    "fe_posisi" INTEGER NOT NULL,

    CONSTRAINT "tb_ikn_data_pkey" PRIMARY KEY ("id_ikn_data")
);

-- CreateTable
CREATE TABLE "public"."tb_ikn_data_file" (
    "id_ikn_data_file" SERIAL NOT NULL,
    "id_ikn_data" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_ikn_data_file_pkey" PRIMARY KEY ("id_ikn_data_file")
);

-- CreateTable
CREATE TABLE "public"."tb_ikn_data_file_tr" (
    "id_ikn_data_file_tr" SERIAL NOT NULL,
    "id_ikn_data_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_ikn_data_file_tr_pkey" PRIMARY KEY ("id_ikn_data_file_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_ikn_data_tr" (
    "id_ikn_data_tr" SERIAL NOT NULL,
    "id_ikn_data" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "deskripsi" TEXT NOT NULL,

    CONSTRAINT "tb_ikn_data_tr_pkey" PRIMARY KEY ("id_ikn_data_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_ikn_file" (
    "id_ikn_file" SERIAL NOT NULL,
    "id_ikn" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "status" INTEGER NOT NULL,

    CONSTRAINT "tb_ikn_file_pkey" PRIMARY KEY ("id_ikn_file")
);

-- CreateTable
CREATE TABLE "public"."tb_ikn_file_tr" (
    "id_ikn_file_tr" SERIAL NOT NULL,
    "id_ikn_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_ikn_file_tr_pkey" PRIMARY KEY ("id_ikn_file_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_ikn_kabkot" (
    "id_ikn_kabkot" SERIAL NOT NULL,
    "id_ikn" INTEGER NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,

    CONSTRAINT "tb_ikn_kabkot_pkey" PRIMARY KEY ("id_ikn_kabkot")
);

-- CreateTable
CREATE TABLE "public"."tb_ikn_layer" (
    "id_ikn_layer" SERIAL NOT NULL,
    "id_ikn" INTEGER NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "nama_layer" VARCHAR(255) NOT NULL,
    "tipe" INTEGER NOT NULL,
    "url_service" VARCHAR(255) NOT NULL,
    "status" INTEGER NOT NULL,

    CONSTRAINT "tb_ikn_layer_pkey" PRIMARY KEY ("id_ikn_layer")
);

-- CreateTable
CREATE TABLE "public"."tb_ikn_peluang" (
    "id_ikn_peluang" SERIAL NOT NULL,
    "id_ikn" INTEGER NOT NULL,
    "id_peluang_kabkot" INTEGER,
    "id_peluang_daerah" INTEGER,
    "status" INTEGER NOT NULL,

    CONSTRAINT "tb_ikn_peluang_pkey" PRIMARY KEY ("id_ikn_peluang")
);

-- CreateTable
CREATE TABLE "public"."tb_ikn_tr" (
    "id_ikn_tr" SERIAL NOT NULL,
    "id_ikn" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "deskripsi" TEXT NOT NULL,

    CONSTRAINT "tb_ikn_tr_pkey" PRIMARY KEY ("id_ikn_tr")
);
