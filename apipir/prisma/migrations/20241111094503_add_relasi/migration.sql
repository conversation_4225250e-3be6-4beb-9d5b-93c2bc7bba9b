-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_nasional_ref" ADD CONSTRAINT "tb_sub_sektor_nasional_ref_id_sektor_fkey" FOREIGN KEY ("id_sektor") REFERENCES "public"."tb_sektor_nasional_ref"("id_sektor") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_nasional_ref_tr" ADD CONSTRAINT "tb_sub_sektor_nasional_ref_tr_id_sub_sektor_fkey" FOREIGN KEY ("id_sub_sektor") REFERENCES "public"."tb_sub_sektor_nasional_ref"("id_sub_sektor") ON DELETE CASCADE ON UPDATE CASCADE;
