-- DropForeignKey
ALTER TABLE "public"."tb_sektor_daerah" DROP CONSTRAINT "sektor_nasional_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_daerah" DROP CONSTRAINT "sektor_nasional_ref_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_nasional_value" DROP CONSTRAINT "unique_constraint_name_tb_sektor_nasional";

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_daerah" ADD CONSTRAINT "sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional"("id_sektor_nasional") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_daerah" ADD CONSTRAINT "sektor_nasional_ref_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional_ref"("id_sektor") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_value" ADD CONSTRAINT "unique_constraint_name_tb_sektor_nasional" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional"("id_sektor_nasional") ON DELETE CASCADE ON UPDATE CASCADE;
