-- CreateTable
CREATE TABLE "public"."tb_roadmap_tr" (
    "id_roadmap_tr" SERIAL NOT NULL,
    "id_roadmap" INTEGER NOT NULL,
    "judul" VARCHAR(100) NOT NULL,
    "deskripsi" VARCHAR(1000) NOT NULL,

    CONSTRAINT "tb_roadmap_tr_pkey" PRIMARY KEY ("id_roadmap_tr")
);

-- AddForeignKey
ALTER TABLE "public"."tb_roadmap_tr" ADD CONSTRAINT "tb_roadmap_tr_id_roadmap_fkey" FOREIGN KEY ("id_roadmap") REFERENCES "public"."tb_roadmap"("id_roadmap") ON DELETE CASCADE ON UPDATE CASCADE;
