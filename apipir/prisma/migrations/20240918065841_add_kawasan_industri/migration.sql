-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri" (
    "id_kawasan_industri" SERIAL NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL,
    "id_ka<PERSON><PERSON>i" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT,
    "alamat" VARCHAR(255),
    "luas" DOUBLE PRECISION,
    "luas_satuan" VARCHAR(50),
    "id_bandara_terdekat" INTEGER,
    "jarak_bandara_terdekat" DOUBLE PRECISION,
    "id_pelabuhan_terdekat" INTEGER,
    "jarak_pelabuhan_terdekat" DOUBLE PRECISION,
    "jarak_ibukota" DOUBLE PRECISION,
    "url_web" VARCHAR(255),
    "no_telp" VARCHAR(50),
    "no_fax" VARCHAR(50),
    "email" VARCHAR(255),
    "cp" VARCHAR(255),
    "ketersediaan" VARCHAR(50),
    "status" VARCHAR(50),
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "shape" TEXT,
    "is_ikn" BOOLEAN,
    "id_kawasan_industri_ref_range" INTEGER,
    "major_tenants" TEXT,
    "id_kawasan_industri_occupancy" INTEGER,

    CONSTRAINT "tb_kawasan_industri_pkey" PRIMARY KEY ("id_kawasan_industri")
);
