-- CreateTable
CREATE TABLE "public"."tb_user_internal_kabkot" (
    "id_user_internal_provinsi" SERIAL NOT NULL,
    "id_user" INTEGER NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,

    CONSTRAINT "tb_user_internal_kabkot_pkey" PRIMARY KEY ("id_user_internal_provinsi")
);

-- AddForeignKey
ALTER TABLE "public"."tb_user_internal_kabkot" ADD CONSTRAINT "tb_user_internal_kabkot_id_user_fkey" FOREIGN KEY ("id_user") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_kawasan_user" ADD CONSTRAINT "tb_kawasan_user_id_user_fkey" FOREIGN KEY ("id_user") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
