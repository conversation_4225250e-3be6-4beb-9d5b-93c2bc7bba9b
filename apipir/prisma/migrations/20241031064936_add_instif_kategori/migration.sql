-- AlterTable
ALTER TABLE "public"."tb_jenis_insentif" ADD COLUMN     "id_kategori_insentif" INTEGER;

-- CreateTable
CREATE TABLE "public"."tb_ketegori_insentif" (
    "id_kategori_insentif" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_ketegori_insentif_pkey" PRIMARY KEY ("id_kategori_insentif")
);

-- AddForeignKey
ALTER TABLE "public"."tb_jenis_insentif" ADD CONSTRAINT "tb_jenis_insentif_id_kategori_insentif_fkey" FOREIGN KEY ("id_kategori_insentif") REFERENCES "public"."tb_ketegori_insentif"("id_kategori_insentif") ON DELETE CASCADE ON UPDATE CASCADE;
