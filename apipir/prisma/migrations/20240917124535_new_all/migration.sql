-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "pu";

-- CreateTable
CREATE TABLE "public"."users" (
    "id" SERIAL NOT NULL,
    "email" TEXT,
    "full_name" TEXT,
    "login_name" TEXT,
    "password" TEXT NOT NULL,
    "role_id" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."auth_access_tokens" (
    "id" SERIAL NOT NULL,
    "tokenable_id" TEXT NOT NULL,
    "hash" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "name" TEXT,
    "abilities" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "last_used_at" TIMESTAMP(3),
    "expires_at" TIMESTAMP(3),

    CONSTRAINT "auth_access_tokens_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."password_reset" (
    "id" SERIAL NOT NULL,
    "email" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires_at" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "password_reset_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."config_upload" (
    "id" SERIAL NOT NULL,
    "jenis" TEXT NOT NULL,
    "size" TEXT NOT NULL,
    "extnames" TEXT NOT NULL,

    CONSTRAINT "config_upload_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."menu" (
    "id" SERIAL NOT NULL,
    "menu_name" TEXT NOT NULL,
    "url" TEXT,
    "parent_id" INTEGER,
    "icon" TEXT,
    "order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "menu_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."role" (
    "id" SERIAL NOT NULL,
    "role_name" TEXT NOT NULL,

    CONSTRAINT "role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."role_menu_access" (
    "id" SERIAL NOT NULL,
    "role_id" INTEGER NOT NULL,
    "menu_id" INTEGER NOT NULL,

    CONSTRAINT "role_menu_access_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."tb_app_slider" (
    "id_app_slider" SERIAL NOT NULL,
    "judul" VARCHAR(255),
    "deskripsi" TEXT,
    "nama_file_image" VARCHAR(255) NOT NULL,
    "url_link" VARCHAR(255),
    "ordering" INTEGER,

    CONSTRAINT "tb_app_slider_pkey" PRIMARY KEY ("id_app_slider")
);

-- CreateTable
CREATE TABLE "public"."tb_app_slider_peluang" (
    "id_app_slider" SERIAL NOT NULL,
    "judul" VARCHAR(255),
    "deskripsi" TEXT,
    "nama_file_image" VARCHAR(255) NOT NULL,
    "url_link" VARCHAR(255),
    "ordering" INTEGER,

    CONSTRAINT "tb_app_slider_peluang_pkey" PRIMARY KEY ("id_app_slider")
);

-- CreateTable
CREATE TABLE "public"."tb_app_slider_peluang_tr" (
    "id_app_slider_tr" SERIAL NOT NULL,
    "id_app_slider" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "judul" VARCHAR(255),
    "deskripsi" TEXT,
    "ordering" INTEGER,
    "nama_file_image" VARCHAR(255),

    CONSTRAINT "tb_app_slider_peluang_tr_pkey" PRIMARY KEY ("id_app_slider_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_app_slider_tr" (
    "id_app_slider_tr" SERIAL NOT NULL,
    "id_app_slider" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "judul" VARCHAR(255),
    "deskripsi" TEXT,
    "ordering" INTEGER,
    "nama_file_image" VARCHAR(255),

    CONSTRAINT "tb_app_slider_tr_pkey" PRIMARY KEY ("id_app_slider_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah" (
    "id_sektor_daerah" SERIAL NOT NULL,
    "tipe" INTEGER NOT NULL,
    "id_sektor_nasional" INTEGER NOT NULL,
    "id_adm_kabkot" INTEGER,
    "id_adm_provinsi" INTEGER,
    "deskripsi_singkat" TEXT NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "potensi_pasar" TEXT NOT NULL,
    "no_dokumen" VARCHAR(255),
    "perihal" VARCHAR(255),
    "file_dokumen" VARCHAR(255),
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_file" (
    "id_sektor_daerah_file" SERIAL NOT NULL,
    "id_sektor_daerah" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_file_tr" (
    "id_sektor_daerah_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_insentif" (
    "id_sektor_daerah_insentif" SERIAL NOT NULL,
    "id_sektor_daerah" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_insentif_file" (
    "id_sektor_daerah_insentif_file" SERIAL NOT NULL,
    "id_sektor_daerah_insentif" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_insentif_file_tr" (
    "id_sektor_daerah_insentif_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_insentif_status" (
    "id_sektor_daerah_insentif" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_insentif_tr" (
    "id_sektor_daerah_insentif" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "deskripsi" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_lq" (
    "id_sektor_daerah" INTEGER NOT NULL,
    "tahun_pdrb" INTEGER NOT NULL,
    "pdrb_sektor" DOUBLE PRECISION NOT NULL,
    "pdrb_total" DOUBLE PRECISION NOT NULL,
    "tahun_pdb" INTEGER NOT NULL,
    "pdb_sektor" DOUBLE PRECISION NOT NULL,
    "pdb_total" DOUBLE PRECISION NOT NULL,
    "nilai_lq" DOUBLE PRECISION NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_pdrb" (
    "id_sektor_daerah_pdrb" SERIAL NOT NULL,
    "id_adm_provinsi" INTEGER,
    "id_adm_kabkot" INTEGER,
    "id_sektor_nasional" INTEGER NOT NULL,
    "tahun_pdrb" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL,
    "jumlah_pdrb" DECIMAL(19,0) NOT NULL,
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_pdrb_status" (
    "id_sektor_daerah_pdrb" INTEGER NOT NULL,
    "tahun_pdrb" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_status" (
    "id_sektor_daerah" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_sumber_data" (
    "id_sektor_daerah" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_tr" (
    "id_sektor_daerah" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "deskripsi_singkat" TEXT NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "potensi_pasar" TEXT NOT NULL,
    "perihal" VARCHAR(255),
    "file_dokumen" VARCHAR(255)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_value_detail" (
    "id_sektor_daerah" INTEGER NOT NULL,
    "id_sektor_nasional_value" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "numeric_value" REAL,
    "string_value" VARCHAR(255),
    "date_value" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional" (
    "id_sektor_nasional" INTEGER NOT NULL,
    "deskripsi_singkat" TEXT NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "potensi_pasar" TEXT NOT NULL,
    "file_icon" VARCHAR(255) NOT NULL,
    "file_image" VARCHAR(255) NOT NULL,
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_file" (
    "id_sektor_nasional_file" SERIAL NOT NULL,
    "id_sektor_nasional" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_file_tr" (
    "id_sektor_nasional_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_insentif" (
    "id_sektor_nasional_insentif" SERIAL NOT NULL,
    "id_sektor_nasional" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_insentif_file" (
    "id_sektor_nasional_insentif_file" SERIAL NOT NULL,
    "id_sektor_nasional_insentif" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_insentif_file_tr" (
    "id_sektor_nasional_insentif_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_insentif_status" (
    "id_sektor_nasional_insentif" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_insentif_tr" (
    "id_sektor_nasional_insentif" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "deskripsi" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_pdb" (
    "id_sektor_nasional" INTEGER NOT NULL,
    "tahun_pdb" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL,
    "jumlah_pdb" DECIMAL(19,0) NOT NULL,
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_pdb_status" (
    "id_sektor_nasional" INTEGER NOT NULL,
    "tahun_pdb" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_ref" (
    "id_sektor" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_ref_tr" (
    "id_sektor" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_status" (
    "id_sektor_nasional" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_sumber_data" (
    "id_sektor_nasional" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_tr" (
    "id_sektor_nasional" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "deskripsi_singkat" TEXT NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "potensi_pasar" TEXT NOT NULL,
    "file_icon" VARCHAR(255) NOT NULL,
    "file_image" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_value" (
    "id_sektor_nasional_value" SERIAL NOT NULL,
    "id_sektor_nasional" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "tipe" SMALLINT NOT NULL,
    "satuan" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_value_detail" (
    "id_sektor_nasional_value" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "numeric_value" REAL,
    "string_value" VARCHAR(255),
    "date_value" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_value_tr" (
    "id_sektor_nasional_value" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "satuan" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah" (
    "id_peluang_daerah" SERIAL NOT NULL,
    "id_adm_provinsi" INTEGER,
    "id_adm_kabkot" INTEGER,
    "id_sub_sektor_daerah" INTEGER NOT NULL,
    "status_peluang" SMALLINT NOT NULL,
    "status" SMALLINT NOT NULL,
    "id_bandara" INTEGER,
    "jarak_bandara" SMALLINT NOT NULL DEFAULT 0,
    "id_pelabuhan" INTEGER,
    "jarak_pelabuhan" SMALLINT NOT NULL DEFAULT 0,
    "jarak_ibukota" SMALLINT NOT NULL DEFAULT 0,
    "kode_kbli" VARCHAR(150),
    "judul" VARCHAR(255),
    "lokasi" VARCHAR(255),
    "tahun" SMALLINT NOT NULL DEFAULT 2022,
    "keterangan" TEXT,
    "aspek_pasar" TEXT,
    "aspek_teknis" TEXT,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "zoom_peta_default" INTEGER,
    "is_ikn" BOOLEAN,

    CONSTRAINT "pk_peluang_daerah" PRIMARY KEY ("id_peluang_daerah")
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_file" (
    "id_peluang_daerah_file" SERIAL NOT NULL,
    "id_peluang_daerah" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_file_tr" (
    "id_peluang_daerah_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_insentif" (
    "id_peluang_daerah_insentif" SERIAL NOT NULL,
    "id_peluang_daerah" INTEGER NOT NULL,
    "id_sektor_nasional_insentif" INTEGER,
    "id_sektor_daerah_insentif" INTEGER
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_kelayakan" (
    "id_peluang_daerah" INTEGER NOT NULL,
    "pct_cost_capital" SMALLINT NOT NULL,
    "year_period" SMALLINT NOT NULL,
    "base_revenue" DOUBLE PRECISION NOT NULL,
    "pct_revenue_growth" SMALLINT NOT NULL,
    "base_opex" DOUBLE PRECISION NOT NULL,
    "pct_inflation" SMALLINT NOT NULL,
    "initial_invesment" DOUBLE PRECISION NOT NULL,
    "pct_salvage_value" SMALLINT NOT NULL,
    "irr" DOUBLE PRECISION NOT NULL,
    "npv" DOUBLE PRECISION NOT NULL,
    "pp" SMALLINT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_kelayakan_detail" (
    "id_peluang_daerah" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "nilai" DOUBLE PRECISION NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_komoditi" (
    "id_peluang_daerah" INTEGER NOT NULL,
    "id_komoditi_daerah" INTEGER NOT NULL,
    "jenis" VARCHAR(255) NOT NULL,
    "manfaat" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_kontak" (
    "id_peluang_daerah" INTEGER NOT NULL,
    "id_peluang_kontak" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_layer_spasial" (
    "id_peluang_daerah_layer_spasial" SERIAL NOT NULL,
    "id_peluang_daerah" INTEGER NOT NULL,
    "nama_layer" VARCHAR(500) NOT NULL,
    "tipe" INTEGER NOT NULL,
    "url_service" VARCHAR(500) NOT NULL,
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_status" (
    "id_peluang_daerah" INTEGER NOT NULL,
    "status" SMALLINT NOT NULL,
    "status_proses" SMALLINT NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_tr" (
    "id_peluang_daerah" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "judul" VARCHAR(255),
    "lokasi" VARCHAR(255),
    "keterangan" TEXT,
    "aspek_pasar" TEXT,
    "aspek_teknis" TEXT
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot" (
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_sektor" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL,
    "id_prioritas" INTEGER NOT NULL,
    "id_adm_kabkot_kantor" INTEGER NOT NULL,
    "nama" TEXT NOT NULL,
    "keterangan" TEXT,
    "nilai_investasi" DOUBLE PRECISION,
    "nilai_irr" DOUBLE PRECISION,
    "nilai_npv" DOUBLE PRECISION,
    "nilai_pp" DOUBLE PRECISION,
    "status" TEXT,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "shape" TEXT,
    "id_kontak" INTEGER,
    "nama_singkat" TEXT,
    "deskripsi_singkat" TEXT,
    "deskripsi" TEXT,
    "lokasi_kawasan" TEXT,
    "modified_time" TIMESTAMP(3),
    "kode_kbli" TEXT,
    "zoom_peta_default" INTEGER,
    "project_status_enum" TEXT,
    "is_ikn" BOOLEAN NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_berita_mapping" (
    "id_peluang_kabkot" INTEGER NOT NULL,
    "id_berita" INTEGER NOT NULL,
    "created_time" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_file" (
    "id_peluang_kabkot_file" SERIAL NOT NULL,
    "id_peluang_kabkot" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "nama" VARCHAR(255),
    "url_rest" VARCHAR(255),
    "status" INTEGER DEFAULT 0
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_file_tr" (
    "id_peluang_kabkot_file_tr" INTEGER NOT NULL,
    "id_peluang_kabkot" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "tipe" INTEGER NOT NULL,
    "nama" VARCHAR(255),
    "url_rest" VARCHAR(255),
    "status" INTEGER DEFAULT 0
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_insentif" (
    "id_peluang_kabkot" INTEGER NOT NULL,
    "id_jenis_insentif" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_kontak" (
    "id_peluang_kabkot" INTEGER NOT NULL,
    "id_peluang_kontak" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_likes_counter" (
    "id_likes" INTEGER NOT NULL,
    "id_peluang_kabkot" INTEGER NOT NULL,
    "ip_pengunjung" VARCHAR(15) NOT NULL,
    "created_time" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_status" (
    "id_peluang_kabkot" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_tr" (
    "id_peluang_kabkot_tr" SERIAL NOT NULL,
    "id_peluang_kabkot" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "nama_singkat" VARCHAR(36),
    "deskripsi_singkat" VARCHAR(120),
    "deskripsi" VARCHAR(600),
    "lokasi_kawasan" VARCHAR(255)
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kontak" (
    "id_peluang_kontak" SERIAL NOT NULL,
    "nama" VARCHAR(255),
    "alamat" VARCHAR(255),
    "url_web" VARCHAR(255),
    "cp" VARCHAR(255),
    "jabatan_cp" VARCHAR(255),
    "email" VARCHAR(255),
    "no_telp" VARCHAR(255),
    "no_fax" VARCHAR(255),
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "id_adm_provinsi" INTEGER,
    "id_adm_kabkot" INTEGER
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_prioritas" (
    "id_peluang_prioritas" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_prioritas_tr" (
    "id_peluang_prioritas_tr" SERIAL NOT NULL,
    "id_peluang_prioritas" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50),
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_provinsi_file" (
    "id_peluang_provinsi_file" SERIAL NOT NULL,
    "id_peluang_provinsi" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_provinsi_status" (
    "id_peluang_provinsi" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_provinsi_tr" (
    "id_peluang_provinsi_tr" SERIAL NOT NULL,
    "id_peluang_provinsi" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_sektor" (
    "id_peluang_sektor" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "icon" VARCHAR(255),
    "iconmap" VARCHAR(255),
    "image" VARCHAR(255)
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_sektor_tr" (
    "id_peluang_sektor_tr" SERIAL NOT NULL,
    "id_peluang_sektor" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_adm_provinsi" (
    "id_adm_provinsi" INTEGER NOT NULL,
    "id_adm_wilayah" INTEGER NOT NULL,
    "kd_adm" TEXT NOT NULL,
    "nama" TEXT NOT NULL,
    "nama_ibukota" TEXT NOT NULL,
    "file_logo" TEXT,
    "file_image" TEXT,
    "deskripsi" TEXT,
    "luas_wilayah" DOUBLE PRECISION,
    "jumlah_penduduk" INTEGER,
    "alamat" TEXT,
    "no_telp" TEXT,
    "no_fax" TEXT,
    "url_web" TEXT,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "shape" TEXT
);

-- CreateTable
CREATE TABLE "public"."tb_adm_provinsi_tr" (
    "id_adm_provinsi_tr" SERIAL NOT NULL,
    "id_adm_provinsi" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,
    "nama_ibukota" VARCHAR(50) NOT NULL,
    "file_logo" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_adm_provinsi_layer_spasial" (
    "id_adm_provinsi_layer_spasial" SERIAL NOT NULL,
    "id_adm_provinsi" INTEGER NOT NULL,
    "nama_layer" VARCHAR(500) NOT NULL,
    "tipe" INTEGER NOT NULL,
    "url_service" VARCHAR(500) NOT NULL,
    "status" INTEGER NOT NULL,
    "is_active" BOOLEAN
);

-- CreateTable
CREATE TABLE "public"."tb_adm_kabkot" (
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_adm_provinsi" INTEGER NOT NULL,
    "kd_adm" TEXT NOT NULL,
    "jenis" TEXT NOT NULL,
    "nama" TEXT NOT NULL,
    "nama_ibukota" TEXT NOT NULL,
    "file_logo" TEXT,
    "file_image" TEXT,
    "deskripsi" TEXT,
    "luas_wilayah" DOUBLE PRECISION,
    "jumlah_penduduk" INTEGER,
    "alamat" TEXT,
    "no_telp" TEXT,
    "no_fax" TEXT,
    "url_web" TEXT,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "shape" TEXT,
    "is_daerah_tertinggal" BOOLEAN NOT NULL DEFAULT false
);

-- CreateTable
CREATE TABLE "public"."tb_adm_kabkot_alias" (
    "id_adm_kabkot" INTEGER NOT NULL,
    "nama_mineral_logam" VARCHAR(50),
    "nama_mineral_non_logam" VARCHAR(50),
    "nama_batubara" VARCHAR(50),
    "nama_panas_bumi" VARCHAR(50)
);

-- CreateTable
CREATE TABLE "public"."tb_adm_kabkot_layer_spasial" (
    "id_adm_kabkot_layer_spasial" SERIAL NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "nama_layer" VARCHAR(500) NOT NULL,
    "tipe" INTEGER NOT NULL,
    "url_service" VARCHAR(500) NOT NULL,
    "status" INTEGER NOT NULL,
    "is_active" BOOLEAN
);

-- CreateTable
CREATE TABLE "public"."tb_adm_kabkot_tr" (
    "id_adm_kabkot_tr" SERIAL,
    "id_adm_kabkot" INTEGER,
    "kd_bahasa" VARCHAR(2),
    "nama" VARCHAR(50),
    "nama_ibukota" VARCHAR(50),
    "file_logo" VARCHAR(255)
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "public"."users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "users_login_name_key" ON "public"."users"("login_name");

-- CreateIndex
CREATE UNIQUE INDEX "password_reset_token_key" ON "public"."password_reset"("token");

-- CreateIndex
CREATE UNIQUE INDEX "role_menu_access_role_id_menu_id_key" ON "public"."role_menu_access"("role_id", "menu_id");

-- AddForeignKey
ALTER TABLE "public"."users" ADD CONSTRAINT "users_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."role"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."menu" ADD CONSTRAINT "menu_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "public"."menu"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."role_menu_access" ADD CONSTRAINT "role_menu_access_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."role"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."role_menu_access" ADD CONSTRAINT "role_menu_access_menu_id_fkey" FOREIGN KEY ("menu_id") REFERENCES "public"."menu"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_app_slider_peluang_tr" ADD CONSTRAINT "tb_app_slider_peluang_tr_id_app_slider_fkey" FOREIGN KEY ("id_app_slider") REFERENCES "public"."tb_app_slider_peluang"("id_app_slider") ON DELETE RESTRICT ON UPDATE CASCADE;
