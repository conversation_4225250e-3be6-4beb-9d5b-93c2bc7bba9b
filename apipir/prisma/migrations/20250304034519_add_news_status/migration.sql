-- CreateTable
CREATE TABLE "public"."tb_kebijakan_status" (
    "tb_kebijakan_status" SERIAL NOT NULL,
    "id_kebijakan" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,

    CONSTRAINT "tb_kebijakan_status_pkey" PRIMARY KEY ("tb_kebijakan_status")
);

-- AddForeignKey
ALTER TABLE "public"."tb_kebijakan_status" ADD CONSTRAINT "tb_kebijakan_status_id_kebijakan_fkey" FOREIGN KEY ("id_kebijakan") REFERENCES "public"."tb_kebijakan"("id_kebijakan") ON DELETE CASCADE ON UPDATE CASCADE;
