-- AlterTable
ALTER TABLE "public"."tb_halaman_pengunjung_det" ADD COLUMN     "id_peluang_daerah" INTEGER,
ADD COLUMN     "id_peluang_kabkot" INTEGER;

-- AddForeignKey
ALTER TABLE "public"."tb_halaman_pengunjung_det" ADD CONSTRAINT "tb_halaman_pengunjung_det_id_peluang_kabkot_fkey" FOREIGN KEY ("id_peluang_kabkot") REFERENCES "public"."tb_peluang_kabkot"("id_peluang_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_halaman_pengunjung_det" ADD CONSTRAINT "tb_halaman_pengunjung_det_id_peluang_daerah_fkey" FOREIGN KEY ("id_peluang_daerah") REFERENCES "public"."tb_peluang_daerah"("id_peluang_daerah") ON DELETE CASCADE ON UPDATE CASCADE;
