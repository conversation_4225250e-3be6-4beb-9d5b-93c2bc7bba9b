/*
  Warnings:

  - You are about to drop the `tes_table` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `tes_table_again` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
DROP TABLE "public"."tes_table";

-- DropTable
DROP TABLE "public"."tes_table_again";

-- CreateTable
CREATE TABLE "public"."tb_demografi_kabkot" (
    "id_demografi_kabkot" SERIAL NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "id_kategori" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "jumlah_pria" INTEGER NOT NULL,
    "jumlah_wanita" INTEGER NOT NULL,
    "kepadatan_penduduk" DOUBLE PRECISION NOT NULL,
    "pertumbuhan_penduduk" DOUBLE PRECISION NOT NULL,
    "status" INTEGER NOT NULL,

    CONSTRAINT "tb_demografi_kabkot_pkey" PRIMARY KEY ("id_demografi_kabkot")
);

-- CreateTable
CREATE TABLE "public"."tb_demografi_kabkot_status" (
    "id_demografi_kabkot_status" SERIAL NOT NULL,
    "id_demografi_kabkot" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_demografi_kabkot_status_pkey" PRIMARY KEY ("id_demografi_kabkot_status")
);

-- CreateTable
CREATE TABLE "public"."tb_demografi_kategori" (
    "id_demografi_kategori" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "warna" VARCHAR(7),

    CONSTRAINT "tb_demografi_kategori_pkey" PRIMARY KEY ("id_demografi_kategori")
);

-- CreateTable
CREATE TABLE "public"."tb_demografi_kategori_tr" (
    "id_demografi_kategori_tr" SERIAL NOT NULL,
    "id_demografi_kategori" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_demografi_kategori_tr_pkey" PRIMARY KEY ("id_demografi_kategori_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_demografi_provinsi" (
    "id_demografi_provinsi" SERIAL NOT NULL,
    "id_adm_provinsi" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "id_kategori" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "jumlah_pria" INTEGER NOT NULL,
    "jumlah_wanita" INTEGER NOT NULL,
    "kepadatan_penduduk" DOUBLE PRECISION NOT NULL,
    "pertumbuhan_penduduk" DOUBLE PRECISION NOT NULL,
    "status" INTEGER NOT NULL,

    CONSTRAINT "tb_demografi_provinsi_pkey" PRIMARY KEY ("id_demografi_provinsi")
);

-- CreateTable
CREATE TABLE "public"."tb_demografi_provinsi_status" (
    "id_demografi_provinsi_status" SERIAL NOT NULL,
    "id_demografi_provinsi" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_demografi_provinsi_status_pkey" PRIMARY KEY ("id_demografi_provinsi_status")
);
