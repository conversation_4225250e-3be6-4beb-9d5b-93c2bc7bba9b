-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_daerah" (
    "id_sub_sektor_daerah" SERIAL NOT NULL,
    "id_sektor_daerah" INTEGER NOT NULL,
    "id_sub_sektor_nasional" INTEGER NOT NULL,
    "deskripsi_singkat" TEXT NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "status" INTEGER NOT NULL,

    CONSTRAINT "tb_sub_sektor_daerah_pkey" PRIMARY KEY ("id_sub_sektor_daerah")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_daerah_file" (
    "id_sub_sektor_daerah_file" SERIAL NOT NULL,
    "id_sub_sektor_daerah" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_sub_sektor_daerah_file_pkey" PRIMARY KEY ("id_sub_sektor_daerah_file")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_daerah_file_tr" (
    "id_sub_sektor_daerah_file_tr" SERIAL NOT NULL,
    "id_sub_sektor_daerah_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_sub_sektor_daerah_file_tr_pkey" PRIMARY KEY ("id_sub_sektor_daerah_file_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_daerah_status" (
    "id_sub_sektor_daerah_status" SERIAL NOT NULL,
    "id_sub_sektor_daerah" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_sub_sektor_daerah_status_pkey" PRIMARY KEY ("id_sub_sektor_daerah_status")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_daerah_sumber_data" (
    "id_sub_sektor_daerah_daerah_sumber_data" SERIAL NOT NULL,
    "id_sub_sektor_daerah" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL,

    CONSTRAINT "tb_sub_sektor_daerah_sumber_data_pkey" PRIMARY KEY ("id_sub_sektor_daerah_daerah_sumber_data")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_daerah_tr" (
    "id_sub_sektor_daerah_tr" SERIAL NOT NULL,
    "id_sub_sektor_daerah" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "deskripsi_singkat" TEXT NOT NULL,
    "deskripsi" TEXT NOT NULL,

    CONSTRAINT "tb_sub_sektor_daerah_tr_pkey" PRIMARY KEY ("id_sub_sektor_daerah_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_daerah_value_detail" (
    "id_sub_sektor_nasional_value_detail" SERIAL NOT NULL,
    "id_sub_sektor_daerah" INTEGER NOT NULL,
    "id_sub_sektor_nasional_value" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "numeric_value" REAL,
    "string_value" VARCHAR(255),
    "date_value" TIMESTAMP(6),

    CONSTRAINT "tb_sub_sektor_daerah_value_detail_pkey" PRIMARY KEY ("id_sub_sektor_nasional_value_detail")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_nasional" (
    "id_sub_sektor_nasional" SERIAL NOT NULL,
    "id_sektor_nasional" INTEGER NOT NULL,
    "id_sub_sektor" INTEGER NOT NULL,
    "deskripsi_singkat" TEXT,
    "deskripsi" TEXT NOT NULL,
    "file_icon" VARCHAR(255) NOT NULL,
    "file_image" VARCHAR(255),
    "status" INTEGER NOT NULL,

    CONSTRAINT "tb_sub_sektor_nasional_pkey" PRIMARY KEY ("id_sub_sektor_nasional")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_nasional_file" (
    "id_sub_sektor_nasional_file" SERIAL NOT NULL,
    "id_sub_sektor_nasional" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_sub_sektor_nasional_file_pkey" PRIMARY KEY ("id_sub_sektor_nasional_file")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_nasional_file_tr" (
    "id_sub_sektor_nasional_file_tr" SERIAL NOT NULL,
    "id_sub_sektor_nasional_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_sub_sektor_nasional_file_tr_pkey" PRIMARY KEY ("id_sub_sektor_nasional_file_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_nasional_ref" (
    "id_sub_sektor" SERIAL NOT NULL,
    "id_sektor" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_sub_sektor_nasional_ref_pkey" PRIMARY KEY ("id_sub_sektor")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_nasional_ref_tr" (
    "id_sub_sektor_tr" SERIAL NOT NULL,
    "id_sub_sektor" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_sub_sektor_nasional_ref_tr_pkey" PRIMARY KEY ("id_sub_sektor_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_nasional_status" (
    "id_sub_sektor_nasional_status" SERIAL NOT NULL,
    "id_sub_sektor_nasional" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_sub_sektor_nasional_status_pkey" PRIMARY KEY ("id_sub_sektor_nasional_status")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_nasional_sumber_data" (
    "id_sub_sektor_nasional_sumber_data" SERIAL NOT NULL,
    "id_sub_sektor_nasional" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL,

    CONSTRAINT "tb_sub_sektor_nasional_sumber_data_pkey" PRIMARY KEY ("id_sub_sektor_nasional_sumber_data")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_nasional_tr" (
    "id_sub_sektor_nasional_tr" SERIAL NOT NULL,
    "id_sub_sektor_nasional" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "deskripsi_singkat" TEXT,
    "deskripsi" TEXT NOT NULL,
    "file_icon" VARCHAR(255) NOT NULL,
    "file_image" VARCHAR(255),

    CONSTRAINT "tb_sub_sektor_nasional_tr_pkey" PRIMARY KEY ("id_sub_sektor_nasional_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_nasional_value" (
    "id_sub_sektor_nasional_value" SERIAL NOT NULL,
    "id_sub_sektor_nasional" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "tipe" SMALLINT NOT NULL,
    "satuan" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_sub_sektor_nasional_value_pkey" PRIMARY KEY ("id_sub_sektor_nasional_value")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_nasional_value_detail" (
    "id_sub_sektor_nasional_value_detail" SERIAL NOT NULL,
    "id_sub_sektor_nasional_value" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "numeric_value" REAL,
    "string_value" VARCHAR(255),
    "date_value" TIMESTAMP(6),

    CONSTRAINT "tb_sub_sektor_nasional_value_detail_pkey" PRIMARY KEY ("id_sub_sektor_nasional_value_detail")
);

-- CreateTable
CREATE TABLE "public"."tb_sub_sektor_nasional_value_tr" (
    "id_sub_sektor_nasional_value_tr" SERIAL NOT NULL,
    "id_sub_sektor_nasional_value" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "satuan" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_sub_sektor_nasional_value_tr_pkey" PRIMARY KEY ("id_sub_sektor_nasional_value_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_link_terkait" (
    "id_link_terkait" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "alamat" VARCHAR(500) NOT NULL,
    "no_telpon" VARCHAR(255) NOT NULL,
    "url_web" VARCHAR(255) NOT NULL,
    "lon" DOUBLE PRECISION NOT NULL,
    "lat" DOUBLE PRECISION NOT NULL,
    "nama_pic" VARCHAR(255) NOT NULL,
    "foto_pic" VARCHAR(255) NOT NULL,
    "file_logo" VARCHAR(255) NOT NULL,
    "status" INTEGER NOT NULL,
    "is_ikn" BOOLEAN,
    "id_kategori" INTEGER NOT NULL,
    "is_kontak" BOOLEAN,

    CONSTRAINT "tb_link_terkait_pkey" PRIMARY KEY ("id_link_terkait")
);
