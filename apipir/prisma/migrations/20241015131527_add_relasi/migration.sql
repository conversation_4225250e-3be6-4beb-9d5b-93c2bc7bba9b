-- AddForeignKey
ALTER TABLE "public"."tb_ikn_data_file" ADD CONSTRAINT "tb_ikn_data_file_id_ikn_data_fkey" FOREIGN KEY ("id_ikn_data") REFERENCES "public"."tb_ikn_data"("id_ikn_data") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_ikn_data_file_tr" ADD CONSTRAINT "tb_ikn_data_file_tr_id_ikn_data_file_fkey" FOREIGN KEY ("id_ikn_data_file") REFERENCES "public"."tb_ikn_data_file"("id_ikn_data_file") ON DELETE CASCADE ON UPDATE CASCADE;
