/*
  Warnings:

  - The primary key for the `tb_bandara_status` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `id_bandara_statu` on the `tb_bandara_status` table. All the data in the column will be lost.
  - The primary key for the `tb_hotel_status` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `id_hotel_staus` on the `tb_hotel_status` table. All the data in the column will be lost.
  - The primary key for the `tb_komoditi_daerah_status` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_komoditi_kabkot_status` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_komoditi_nasional_status` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_komoditi_provinsi_status` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_nswi_kabkot_status` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_daerah_insentif_status` table will be changed. If it partially fails, the table could be left without primary key constraint.

*/
-- AlterTable
ALTER TABLE "public"."tb_bandara_status" DROP CONSTRAINT "tb_bandara_status_pkey",
DROP COLUMN "id_bandara_statu",
ADD COLUMN     "id_bandara_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_bandara_status_pkey" PRIMARY KEY ("id_bandara_status");

-- AlterTable
ALTER TABLE "public"."tb_hotel_status" DROP CONSTRAINT "tb_hotel_status_pkey",
DROP COLUMN "id_hotel_staus",
ADD COLUMN     "id_hotel_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_hotel_status_pkey" PRIMARY KEY ("id_hotel_status");

-- AlterTable
ALTER TABLE "public"."tb_komoditi_daerah_status" DROP CONSTRAINT "tb_komoditi_daerah_status_pkey",
ADD COLUMN     "id_komoditi_daerah_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_komoditi_daerah_status_pkey" PRIMARY KEY ("id_komoditi_daerah_status");

-- AlterTable
ALTER TABLE "public"."tb_komoditi_kabkot_status" DROP CONSTRAINT "tb_komoditi_kabkot_status_pkey",
ADD COLUMN     "id_komoditi_kabkot_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_komoditi_kabkot_status_pkey" PRIMARY KEY ("id_komoditi_kabkot_status");

-- AlterTable
ALTER TABLE "public"."tb_komoditi_nasional_status" DROP CONSTRAINT "tb_komoditi_nasional_status_pkey",
ADD COLUMN     "id_komoditi_nasional_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_komoditi_nasional_status_pkey" PRIMARY KEY ("id_komoditi_nasional_status");

-- AlterTable
ALTER TABLE "public"."tb_komoditi_provinsi_status" DROP CONSTRAINT "tb_komoditi_provinsi_status_pkey",
ADD COLUMN     "id_komoditi_provinsi_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_komoditi_provinsi_status_pkey" PRIMARY KEY ("id_komoditi_provinsi_status");

-- AlterTable
ALTER TABLE "public"."tb_nswi_kabkot_status" DROP CONSTRAINT "tb_nswi_kabkot_status_pkey",
ADD COLUMN     "id_nswi_kabkot_status" SERIAL NOT NULL,
ALTER COLUMN "id_nswi_kabkot" DROP DEFAULT,
ADD CONSTRAINT "tb_nswi_kabkot_status_pkey" PRIMARY KEY ("id_nswi_kabkot_status");
DROP SEQUENCE "tb_nswi_kabkot_status_id_nswi_kabkot_seq";

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_insentif_status" DROP CONSTRAINT "tb_sektor_daerah_insentif_status_pkey",
ADD COLUMN     "id_sektor_daerah_insentif_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_daerah_insentif_status_pkey" PRIMARY KEY ("id_sektor_daerah_insentif_status");
