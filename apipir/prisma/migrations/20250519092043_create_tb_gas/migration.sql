-- CreateTable
CREATE TABLE "public"."tb_gas" (
    "id_gas" SERIAL NOT NULL,
    "id_jenis" INTEGER NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "file_logo" VARCHAR(255),
    "alamat" TEXT NOT NULL,
    "no_telp" VARCHAR(30) NOT NULL,
    "no_fax" VARCHAR(30) NOT NULL,
    "email" VARCHAR(50) NOT NULL,
    "status" INTEGER NOT NULL,
    "url_web" VARCHAR(255) NOT NULL,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "is_ikn" BOOLEAN,
    "id_kategori_infrastruktur" INTEGER,

    CONSTRAINT "tb_gas_pkey" PRIMARY KEY ("id_gas")
);

-- CreateTable
CREATE TABLE "public"."tb_gas_jenis" (
    "id_jenis" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_gas_jenis_pkey" PRIMARY KEY ("id_jenis")
);

-- CreateTable
CREATE TABLE "public"."tb_gas_jenis_tr" (
    "id_gas_jenis_tr" SERIAL NOT NULL,
    "id_gas_jenis" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_gas_jenis_tr_pkey" PRIMARY KEY ("id_gas_jenis_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_gas_status" (
    "id_gas" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),
    "id_gas_status" SERIAL NOT NULL,

    CONSTRAINT "tb_gas_status_pkey" PRIMARY KEY ("id_gas_status")
);

-- CreateTable
CREATE TABLE "public"."tb_gas_tr" (
    "id_gas_tr" SERIAL NOT NULL,
    "id_gas" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "file_logo" VARCHAR(255),

    CONSTRAINT "tb_gas_tr_pkey" PRIMARY KEY ("id_gas_tr")
);

-- AddForeignKey
ALTER TABLE "public"."tb_gas" ADD CONSTRAINT "tb_gas_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_gas" ADD CONSTRAINT "tb_gas_id_jenis_fkey" FOREIGN KEY ("id_jenis") REFERENCES "public"."tb_gas_jenis"("id_jenis") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_gas" ADD CONSTRAINT "tb_gas_id_sumber_data_fkey" FOREIGN KEY ("id_sumber_data") REFERENCES "public"."tb_sumber_data"("id_sumber_data") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_gas" ADD CONSTRAINT "tb_gas_id_kategori_infrastruktur_fkey" FOREIGN KEY ("id_kategori_infrastruktur") REFERENCES "public"."tb_kategori_infrastruktur"("id_kategori_infrastruktur") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_gas_status" ADD CONSTRAINT "tb_gas_status_id_gas_fkey" FOREIGN KEY ("id_gas") REFERENCES "public"."tb_gas"("id_gas") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_gas_tr" ADD CONSTRAINT "tb_gas_tr_id_gas_fkey" FOREIGN KEY ("id_gas") REFERENCES "public"."tb_gas"("id_gas") ON DELETE CASCADE ON UPDATE CASCADE;
