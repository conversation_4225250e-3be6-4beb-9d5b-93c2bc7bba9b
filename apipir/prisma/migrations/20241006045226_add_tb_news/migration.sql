-- CreateTable
CREATE TABLE "public"."tb_news" (
    "id" SERIAL NOT NULL,
    "jenis" INTEGER NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "deskripsi_singkat" VARCHAR(500) NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "status" INTEGER NOT NULL,
    "file_cover" VARCHAR(255),

    CONSTRAINT "tb_news_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."tb_news_file" (
    "id_news_file" SERIAL NOT NULL,
    "id_news" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_news_file_pkey" PRIMARY KEY ("id_news_file")
);

-- CreateTable
CREATE TABLE "public"."tb_news_status" (
    "id_news_status" SERIAL NOT NULL,
    "id_news" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,

    CONSTRAINT "tb_news_status_pkey" PRIMARY KEY ("id_news_status")
);

-- CreateTable
CREATE TABLE "public"."tb_news_tr" (
    "id_news_tr" SERIAL NOT NULL,
    "id_news" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "deskripsi_singkat" VARCHAR(500) NOT NULL,
    "deskripsi" TEXT NOT NULL,

    CONSTRAINT "tb_news_tr_pkey" PRIMARY KEY ("id_news_tr")
);

-- AddForeignKey
ALTER TABLE "public"."tb_news_file" ADD CONSTRAINT "tb_news_file_id_news_fkey" FOREIGN KEY ("id_news") REFERENCES "public"."tb_news"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_news_status" ADD CONSTRAINT "tb_news_status_id_news_fkey" FOREIGN KEY ("id_news") REFERENCES "public"."tb_news"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_news_tr" ADD CONSTRAINT "tb_news_tr_id_news_fkey" FOREIGN KEY ("id_news") REFERENCES "public"."tb_news"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
