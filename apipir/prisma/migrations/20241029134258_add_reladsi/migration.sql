-- AddForeignKey
ALTER TABLE "public"."tb_peluang_daerah" ADD CONSTRAINT "tb_peluang_daerah_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_daerah" ADD CONSTRAINT "tb_peluang_daerah_id_adm_provinsi_fkey" FOREIGN KEY ("id_adm_provinsi") REFERENCES "public"."tb_adm_provinsi"("id_adm_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_daerah_file" ADD CONSTRAINT "tb_peluang_daerah_file_id_peluang_daerah_fkey" FOREIGN KEY ("id_peluang_daerah") REFERENCES "public"."tb_peluang_daerah"("id_peluang_daerah") ON DELETE CASCADE ON UPDATE CASCADE;
