-- CreateTable
CREATE TABLE "public"."tb_adm_wilayah" (
    "id_adm_wilayah" INTEGER NOT NULL,
    "nama" VARCHAR(50) NOT NULL,
    "deskripsi" VARCHAR(255) NOT NULL,
    "index" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_adm_wilayah_tr" (
    "id_adm_wilayah_tr" SERIAL NOT NULL,
    "id_adm_wilayah" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,
    "deskripsi" VARCHAR(255) NOT NULL
);
