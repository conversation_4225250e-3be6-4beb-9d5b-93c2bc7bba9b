-- AddForeignKey
ALTER TABLE "public"."tb_sektor_daerah_sumber_data" ADD CONSTRAINT "tb_sektor_daerah_sumber_data_id_sektor_daerah_fkey" FOREIGN KEY ("id_sektor_daerah") REFERENCES "public"."tb_sektor_daerah"("id_sektor_daerah") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddFore<PERSON>K<PERSON>
ALTER TABLE "public"."tb_sektor_daerah_sumber_data" ADD CONSTRAINT "tb_sektor_daerah_sumber_data_id_sumber_data_fkey" FOREIGN KEY ("id_sumber_data") REFERENCES "public"."tb_sumber_data"("id_sumber_data") ON DELETE CASCADE ON UPDATE CASCADE;
