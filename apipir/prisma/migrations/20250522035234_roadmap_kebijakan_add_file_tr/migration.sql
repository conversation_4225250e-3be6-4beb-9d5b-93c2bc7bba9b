-- CreateTable
CREATE TABLE "public"."tb_kebijakan_file_tr" (
    "id_kebijakan_file_tr" SERIAL NOT NULL,
    "id_kebijakan" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_kebijakan_file_tr_pkey" PRIMARY KEY ("id_kebijakan_file_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_roadmap_file_tr" (
    "id_roadmap_file_tr" SERIAL NOT NULL,
    "id_roadmap" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_roadmap_file_tr_pkey" PRIMARY KEY ("id_roadmap_file_tr")
);

-- AddForeignKey
ALTER TABLE "public"."tb_kebijakan_file_tr" ADD CONSTRAINT "tb_kebijakan_file_tr_id_kebijakan_fkey" FOREIGN KEY ("id_kebijakan") REFERENCES "public"."tb_kebijakan"("id_kebijakan") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_roadmap_file_tr" ADD CONSTRAINT "tb_roadmap_file_tr_id_roadmap_fkey" FOREIGN KEY ("id_roadmap") REFERENCES "public"."tb_roadmap"("id_roadmap") ON DELETE CASCADE ON UPDATE CASCADE;
