-- CreateTable
CREATE TABLE "public"."tb_kebijakan" (
    "id_kebijakan" SERIAL NOT NULL,
    "id_peluang_kabkot" INTEGER NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "nama_file" VARCHAR(500) NOT NULL,

    CONSTRAINT "tb_kebijakan_pkey" PRIMARY KEY ("id_kebijakan")
);

-- AddForeignKey
ALTER TABLE "public"."tb_kebijakan" ADD CONSTRAINT "tb_kebijakan_id_peluang_kabkot_fkey" FOREIGN KEY ("id_peluang_kabkot") REFERENCES "public"."tb_peluang_kabkot"("id_peluang_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;
