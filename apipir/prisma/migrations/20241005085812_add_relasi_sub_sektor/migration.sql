-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_nasional" ADD CONSTRAINT "tb_sub_sektor_nasional_id_sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional_ref"("id_sektor") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_nasional_value" ADD CONSTRAINT "tb_sub_sektor_nasional_value_id_sub_sektor_nasional_fkey" FOREIGN KEY ("id_sub_sektor_nasional") REFERENCES "public"."tb_sub_sektor_nasional"("id_sub_sektor_nasional") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_nasional_value_detail" ADD CONSTRAINT "tb_sub_sektor_nasional_value_detail_id_sub_sektor_nasional_fkey" FOREIGN KEY ("id_sub_sektor_nasional_value") REFERENCES "public"."tb_sub_sektor_nasional_value"("id_sub_sektor_nasional_value") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_nasional_value_tr" ADD CONSTRAINT "tb_sub_sektor_nasional_value_tr_id_sub_sektor_nasional_val_fkey" FOREIGN KEY ("id_sub_sektor_nasional_value") REFERENCES "public"."tb_sub_sektor_nasional_value"("id_sub_sektor_nasional_value") ON DELETE RESTRICT ON UPDATE CASCADE;
