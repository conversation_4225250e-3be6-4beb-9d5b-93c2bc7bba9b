/*
  Warnings:

  - Made the column `id_adm_kabkot_tr` on table `tb_adm_kabkot_tr` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "public"."tb_adm_kabkot" ADD CONSTRAINT "tb_adm_kabkot_pkey" PRIMARY KEY ("id_adm_kabkot");

-- AlterTable
ALTER TABLE "public"."tb_adm_kabkot_alias" ADD CONSTRAINT "tb_adm_kabkot_alias_pkey" PRIMARY KEY ("id_adm_kabkot");

-- AlterTable
ALTER TABLE "public"."tb_adm_kabkot_layer_spasial" ADD CONSTRAINT "tb_adm_kabkot_layer_spasial_pkey" PRIMARY KEY ("id_adm_kabkot_layer_spasial");

-- AlterTable
ALTER TABLE "public"."tb_adm_kabkot_tr" ALTER COLUMN "id_adm_kabkot_tr" SET NOT NULL,
ADD CONSTRAINT "tb_adm_kabkot_tr_pkey" PRIMARY KEY ("id_adm_kabkot_tr");

-- AlterTable
ALTER TABLE "public"."tb_adm_provinsi" ADD CONSTRAINT "tb_adm_provinsi_pkey" PRIMARY KEY ("id_adm_provinsi");

-- AlterTable
ALTER TABLE "public"."tb_adm_provinsi_layer_spasial" ADD CONSTRAINT "tb_adm_provinsi_layer_spasial_pkey" PRIMARY KEY ("id_adm_provinsi_layer_spasial");

-- AlterTable
ALTER TABLE "public"."tb_adm_provinsi_tr" ADD CONSTRAINT "tb_adm_provinsi_tr_pkey" PRIMARY KEY ("id_adm_provinsi_tr");

-- AlterTable
ALTER TABLE "public"."tb_adm_wilayah" ADD CONSTRAINT "tb_adm_wilayah_pkey" PRIMARY KEY ("id_adm_wilayah");

-- AlterTable
ALTER TABLE "public"."tb_adm_wilayah_tr" ADD CONSTRAINT "tb_adm_wilayah_tr_pkey" PRIMARY KEY ("id_adm_wilayah_tr");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah" RENAME CONSTRAINT "pk_peluang_daerah" TO "tb_peluang_daerah_pkey";

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_file" ADD CONSTRAINT "tb_peluang_daerah_file_pkey" PRIMARY KEY ("id_peluang_daerah_file");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_file_tr" ADD CONSTRAINT "tb_peluang_daerah_file_tr_pkey" PRIMARY KEY ("id_peluang_daerah_file");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_insentif" ADD CONSTRAINT "tb_peluang_daerah_insentif_pkey" PRIMARY KEY ("id_peluang_daerah_insentif");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_kelayakan" ADD CONSTRAINT "tb_peluang_daerah_kelayakan_pkey" PRIMARY KEY ("id_peluang_daerah");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_kelayakan_detail" ADD CONSTRAINT "tb_peluang_daerah_kelayakan_detail_pkey" PRIMARY KEY ("id_peluang_daerah");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_komoditi" ADD CONSTRAINT "tb_peluang_daerah_komoditi_pkey" PRIMARY KEY ("id_peluang_daerah");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_kontak" ADD CONSTRAINT "tb_peluang_daerah_kontak_pkey" PRIMARY KEY ("id_peluang_daerah");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_layer_spasial" ADD CONSTRAINT "tb_peluang_daerah_layer_spasial_pkey" PRIMARY KEY ("id_peluang_daerah_layer_spasial");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_status" ADD CONSTRAINT "tb_peluang_daerah_status_pkey" PRIMARY KEY ("id_peluang_daerah");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_tr" ADD CONSTRAINT "tb_peluang_daerah_tr_pkey" PRIMARY KEY ("id_peluang_daerah");

-- AlterTable
ALTER TABLE "public"."tb_peluang_kabkot" ADD CONSTRAINT "tb_peluang_kabkot_pkey" PRIMARY KEY ("id_adm_kabkot");

-- AlterTable
ALTER TABLE "public"."tb_peluang_kabkot_berita_mapping" ADD CONSTRAINT "tb_peluang_kabkot_berita_mapping_pkey" PRIMARY KEY ("id_peluang_kabkot");

-- AlterTable
ALTER TABLE "public"."tb_peluang_kabkot_file" ADD CONSTRAINT "tb_peluang_kabkot_file_pkey" PRIMARY KEY ("id_peluang_kabkot_file");

-- AlterTable
ALTER TABLE "public"."tb_peluang_kabkot_file_tr" ADD CONSTRAINT "tb_peluang_kabkot_file_tr_pkey" PRIMARY KEY ("id_peluang_kabkot_file_tr");

-- AlterTable
ALTER TABLE "public"."tb_peluang_kabkot_insentif" ADD CONSTRAINT "tb_peluang_kabkot_insentif_pkey" PRIMARY KEY ("id_peluang_kabkot");

-- AlterTable
ALTER TABLE "public"."tb_peluang_kabkot_kontak" ADD CONSTRAINT "tb_peluang_kabkot_kontak_pkey" PRIMARY KEY ("id_peluang_kabkot");

-- AlterTable
ALTER TABLE "public"."tb_peluang_kabkot_likes_counter" ADD CONSTRAINT "tb_peluang_kabkot_likes_counter_pkey" PRIMARY KEY ("id_likes");

-- AlterTable
ALTER TABLE "public"."tb_peluang_kabkot_status" ADD CONSTRAINT "tb_peluang_kabkot_status_pkey" PRIMARY KEY ("id_peluang_kabkot");

-- AlterTable
ALTER TABLE "public"."tb_peluang_kabkot_tr" ADD CONSTRAINT "tb_peluang_kabkot_tr_pkey" PRIMARY KEY ("id_peluang_kabkot_tr");

-- AlterTable
ALTER TABLE "public"."tb_peluang_kontak" ADD CONSTRAINT "tb_peluang_kontak_pkey" PRIMARY KEY ("id_peluang_kontak");

-- AlterTable
ALTER TABLE "public"."tb_peluang_prioritas" ADD CONSTRAINT "tb_peluang_prioritas_pkey" PRIMARY KEY ("id_peluang_prioritas");

-- AlterTable
ALTER TABLE "public"."tb_peluang_prioritas_tr" ADD CONSTRAINT "tb_peluang_prioritas_tr_pkey" PRIMARY KEY ("id_peluang_prioritas_tr");

-- AlterTable
ALTER TABLE "public"."tb_peluang_provinsi_file" ADD CONSTRAINT "tb_peluang_provinsi_file_pkey" PRIMARY KEY ("id_peluang_provinsi_file");

-- AlterTable
ALTER TABLE "public"."tb_peluang_provinsi_status" ADD CONSTRAINT "tb_peluang_provinsi_status_pkey" PRIMARY KEY ("id_peluang_provinsi");

-- AlterTable
ALTER TABLE "public"."tb_peluang_provinsi_tr" ADD CONSTRAINT "tb_peluang_provinsi_tr_pkey" PRIMARY KEY ("id_peluang_provinsi_tr");

-- AlterTable
ALTER TABLE "public"."tb_peluang_sektor" ADD CONSTRAINT "tb_peluang_sektor_pkey" PRIMARY KEY ("nama");

-- AlterTable
ALTER TABLE "public"."tb_peluang_sektor_tr" ADD CONSTRAINT "tb_peluang_sektor_tr_pkey" PRIMARY KEY ("id_peluang_sektor_tr");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_file" ADD CONSTRAINT "tb_sektor_daerah_file_pkey" PRIMARY KEY ("id_sektor_daerah_file");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_file_tr" ADD CONSTRAINT "tb_sektor_daerah_file_tr_pkey" PRIMARY KEY ("id_sektor_daerah_file");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_insentif" ADD CONSTRAINT "tb_sektor_daerah_insentif_pkey" PRIMARY KEY ("id_sektor_daerah_insentif");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_insentif_file" ADD CONSTRAINT "tb_sektor_daerah_insentif_file_pkey" PRIMARY KEY ("id_sektor_daerah_insentif_file");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_insentif_file_tr" ADD CONSTRAINT "tb_sektor_daerah_insentif_file_tr_pkey" PRIMARY KEY ("id_sektor_daerah_insentif_file");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_insentif_status" ADD CONSTRAINT "tb_sektor_daerah_insentif_status_pkey" PRIMARY KEY ("id_sektor_daerah_insentif");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_insentif_tr" ADD CONSTRAINT "tb_sektor_daerah_insentif_tr_pkey" PRIMARY KEY ("id_sektor_daerah_insentif");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_lq" ADD CONSTRAINT "tb_sektor_daerah_lq_pkey" PRIMARY KEY ("id_sektor_daerah");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_pdrb" ADD CONSTRAINT "tb_sektor_daerah_pdrb_pkey" PRIMARY KEY ("id_sektor_daerah_pdrb");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_pdrb_status" ADD CONSTRAINT "tb_sektor_daerah_pdrb_status_pkey" PRIMARY KEY ("id_sektor_daerah_pdrb");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_status" ADD CONSTRAINT "tb_sektor_daerah_status_pkey" PRIMARY KEY ("id_sektor_daerah");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_sumber_data" ADD CONSTRAINT "tb_sektor_daerah_sumber_data_pkey" PRIMARY KEY ("id_sektor_daerah");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_tr" ADD CONSTRAINT "tb_sektor_daerah_tr_pkey" PRIMARY KEY ("id_sektor_daerah");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_value_detail" ADD CONSTRAINT "tb_sektor_daerah_value_detail_pkey" PRIMARY KEY ("id_sektor_daerah");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional" ADD CONSTRAINT "tb_sektor_nasional_pkey" PRIMARY KEY ("id_sektor_nasional");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_file" ADD CONSTRAINT "tb_sektor_nasional_file_pkey" PRIMARY KEY ("id_sektor_nasional_file");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_file_tr" ADD CONSTRAINT "tb_sektor_nasional_file_tr_pkey" PRIMARY KEY ("id_sektor_nasional_file");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_insentif" ADD CONSTRAINT "tb_sektor_nasional_insentif_pkey" PRIMARY KEY ("id_sektor_nasional_insentif");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_insentif_file" ADD CONSTRAINT "tb_sektor_nasional_insentif_file_pkey" PRIMARY KEY ("id_sektor_nasional_insentif_file");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_insentif_file_tr" ADD CONSTRAINT "tb_sektor_nasional_insentif_file_tr_pkey" PRIMARY KEY ("id_sektor_nasional_insentif_file");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_insentif_status" ADD CONSTRAINT "tb_sektor_nasional_insentif_status_pkey" PRIMARY KEY ("id_sektor_nasional_insentif");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_insentif_tr" ADD CONSTRAINT "tb_sektor_nasional_insentif_tr_pkey" PRIMARY KEY ("id_sektor_nasional_insentif");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_pdb" ADD CONSTRAINT "tb_sektor_nasional_pdb_pkey" PRIMARY KEY ("id_sektor_nasional");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_pdb_status" ADD CONSTRAINT "tb_sektor_nasional_pdb_status_pkey" PRIMARY KEY ("id_sektor_nasional");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_ref_tr" ADD CONSTRAINT "tb_sektor_nasional_ref_tr_pkey" PRIMARY KEY ("id_sektor");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_status" ADD CONSTRAINT "tb_sektor_nasional_status_pkey" PRIMARY KEY ("id_sektor_nasional");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_sumber_data" ADD CONSTRAINT "tb_sektor_nasional_sumber_data_pkey" PRIMARY KEY ("id_sektor_nasional");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_tr" ADD CONSTRAINT "tb_sektor_nasional_tr_pkey" PRIMARY KEY ("id_sektor_nasional");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_value" ADD CONSTRAINT "tb_sektor_nasional_value_pkey" PRIMARY KEY ("id_sektor_nasional_value");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_value_detail" ADD CONSTRAINT "tb_sektor_nasional_value_detail_pkey" PRIMARY KEY ("id_sektor_nasional_value");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_value_tr" ADD CONSTRAINT "tb_sektor_nasional_value_tr_pkey" PRIMARY KEY ("id_sektor_nasional_value");

-- AlterTable
ALTER TABLE "public"."tb_umr_kabkot" ADD CONSTRAINT "tb_umr_kabkot_pkey" PRIMARY KEY ("id_umr_kabkot");

-- AlterTable
ALTER TABLE "public"."tb_umr_kabkot_status" ADD CONSTRAINT "tb_umr_kabkot_status_pkey" PRIMARY KEY ("id_umr_kabkot");

-- AlterTable
ALTER TABLE "public"."tb_umr_provinsi" ADD CONSTRAINT "tb_umr_provinsi_pkey" PRIMARY KEY ("id_umr_provinsi");

-- AlterTable
ALTER TABLE "public"."tb_umr_provinsi_status" ADD CONSTRAINT "tb_umr_provinsi_status_pkey" PRIMARY KEY ("id_umr_provinsi");
