-- AddForeignKey
ALTER TABLE "public"."tb_peluang_kabkot_file_tr" ADD CONSTRAINT "tb_peluang_kabkot_file_tr_id_peluang_kabkot_fkey" FOREIGN KEY ("id_peluang_kabkot") REFERENCES "public"."tb_peluang_kabkot"("id_peluang_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_kabkot_tr" ADD CONSTRAINT "tb_peluang_kabkot_tr_id_peluang_kabkot_fkey" FOREIGN KEY ("id_peluang_kabkot") REFERENCES "public"."tb_peluang_kabkot"("id_peluang_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;
