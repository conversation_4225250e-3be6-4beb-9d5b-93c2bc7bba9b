-- CreateTable
CREATE TABLE "public"."tb_komoditi_layers" (
    "id_kl" SERIAL NOT NULL,
    "id_komoditi" INTEGER NOT NULL,
    "layeruid" VARCHAR(16) NOT NULL,
    "kategori" INTEGER NOT NULL,
    "keterangan" VARCHAR(255),

    CONSTRAINT "tb_komoditi_layers_pkey" PRIMARY KEY ("id_kl")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_layers" (
    "id_kl" SERIAL NOT NULL,
    "id_kawasan_industri" INTEGER NOT NULL,
    "layeruid" VARCHAR(16) NOT NULL,
    "kategori" INTEGER NOT NULL,
    "keterangan" VARCHAR(255),

    CONSTRAINT "tb_kawasan_layers_pkey" PRIMARY KEY ("id_kl")
);

-- AddForeignKey
ALTER TABLE "public"."tb_komoditi_layers" ADD CONSTRAINT "tb_komoditi_layers_id_komoditi_fkey" FOREIGN KEY ("id_komoditi") REFERENCES "public"."tb_komoditi_nasional_ref"("id_komoditi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_kawasan_layers" ADD CONSTRAINT "tb_kawasan_layers_id_kawasan_industri_fkey" FOREIGN KEY ("id_kawasan_industri") REFERENCES "public"."tb_kawasan_industri"("id_kawasan_industri") ON DELETE CASCADE ON UPDATE CASCADE;
