-- CreateTable
CREATE TABLE "public"."tb_bpn_rdtr" (
    "id_rdtr" SERIAL NOT NULL,
    "url" VARCHAR(200) NOT NULL,
    "kd_adm_kabkot" INTEGER NOT NULL,
    "kd_adm_provinsi" INTEGER NOT NULL,
    "nama_provinsi" VARCHAR(50) NOT NULL,
    "nama_kabkot" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_bpn_rdtr_pkey" PRIMARY KEY ("id_rdtr")
);

-- CreateTable
CREATE TABLE "public"."tb_bpn_rtr" (
    "id_rtr" SERIAL NOT NULL,
    "url" VARCHAR(200) NOT NULL,
    "kd_adm_kabkot" INTEGER NOT NULL,
    "kd_adm_provinsi" INTEGER NOT NULL,
    "nama_provinsi" VARCHAR(50) NOT NULL,
    "nama_kabkot" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_bpn_rtr_pkey" PRIMARY KEY ("id_rtr")
);
