-- DropFore<PERSON><PERSON>ey
ALTER TABLE "public"."menu" DROP CONSTRAINT "menu_parent_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."role_menu_access" DROP CONSTRAINT "role_menu_access_menu_id_fkey";

-- DropFore<PERSON>Key
ALTER TABLE "public"."role_menu_access" DROP CONSTRAINT "role_menu_access_role_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_adm_kabkot" DROP CONSTRAINT "tb_adm_kabkot_id_adm_provinsi_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_adm_provinsi" DROP CONSTRAINT "tb_adm_provinsi_id_adm_wilayah_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_app_slider_peluang_tr" DROP CONSTRAINT "tb_app_slider_peluang_tr_id_app_slider_fkey";

-- DropF<PERSON>ign<PERSON>ey
ALTER TABLE "public"."tb_app_slider_tr" DROP CONSTRAINT "tb_app_slider_tr_id_app_slider_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_halaman_pengunjung_det" DROP CONSTRAINT "tb_halaman_pengunjung_det_id_halaman_pengunjung_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_investasi_provinsi" DROP CONSTRAINT "tb_investasi_provinsi_id_jenis_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_jenis_insentif_file" DROP CONSTRAINT "tb_jenis_insentif_file_id_jenis_insentif_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_jenis_insentif_file_tr" DROP CONSTRAINT "tb_jenis_insentif_file_tr_id_jenis_insentif_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_jenis_insentif_kbli" DROP CONSTRAINT "tb_jenis_insentif_kbli_id_jenis_insentif_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_jenis_insentif_status" DROP CONSTRAINT "tb_jenis_insentif_status_id_jenis_insentif_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_jenis_insentif_tr" DROP CONSTRAINT "tb_jenis_insentif_tr_id_jenis_insentif_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_news_file" DROP CONSTRAINT "tb_news_file_id_news_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_news_status" DROP CONSTRAINT "tb_news_status_id_news_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_news_tr" DROP CONSTRAINT "tb_news_tr_id_news_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_peluang_kabkot" DROP CONSTRAINT "tb_peluang_kabkot_id_adm_kabkot_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_peluang_kabkot" DROP CONSTRAINT "tb_peluang_kabkot_id_sektor_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_peluang_kabkot" DROP CONSTRAINT "tb_peluang_kabkot_id_sumber_data_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_peluang_kabkot_file" DROP CONSTRAINT "tb_peluang_kabkot_file_id_peluang_kabkot_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_daerah" DROP CONSTRAINT "tb_sektor_daerah_id_adm_provinsi_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_nasional" DROP CONSTRAINT "tb_sektor_nasional_id_sektor_nasional_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_nasional_file" DROP CONSTRAINT "tb_sektor_nasional_file_id_sektor_nasional_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_nasional_file_tr" DROP CONSTRAINT "tb_sektor_nasional_file_tr_id_sektor_nasional_file_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_nasional_insentif_file" DROP CONSTRAINT "tb_sektor_nasional_insentif_file_id_sektor_nasional_insent_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_nasional_insentif_tr" DROP CONSTRAINT "tb_sektor_nasional_insentif_tr_id_sektor_nasional_insentif_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_nasional_kontak" DROP CONSTRAINT "tb_sektor_nasional_kontak_id_sektor_nasional_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_nasional_sumber_data" DROP CONSTRAINT "tb_sektor_nasional_sumber_data_id_sektor_nasional_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_nasional_tr" DROP CONSTRAINT "tb_sektor_nasional_tr_id_sektor_nasional_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_nasional_value" DROP CONSTRAINT "tb_sektor_nasional_value_id_sektor_nasional_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_nasional_value_detail" DROP CONSTRAINT "tb_sektor_nasional_value_detail_id_sektor_nasional_value_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_nasional_value_tr" DROP CONSTRAINT "tb_sektor_nasional_value_tr_id_sektor_nasional_value_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sub_sektor_nasional" DROP CONSTRAINT "tb_sub_sektor_nasional_id_sektor_nasional_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sub_sektor_nasional_value" DROP CONSTRAINT "tb_sub_sektor_nasional_value_id_sub_sektor_nasional_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sub_sektor_nasional_value_detail" DROP CONSTRAINT "tb_sub_sektor_nasional_value_detail_id_sub_sektor_nasional_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sub_sektor_nasional_value_tr" DROP CONSTRAINT "tb_sub_sektor_nasional_value_tr_id_sub_sektor_nasional_val_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sumber_data" DROP CONSTRAINT "tb_sumber_data_id_sumber_data_judul_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sumber_data_instansi_tr" DROP CONSTRAINT "tb_sumber_data_instansi_tr_id_sumber_data_instansi_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sumber_data_judul" DROP CONSTRAINT "tb_sumber_data_judul_id_sumber_data_instansi_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_user_internal" DROP CONSTRAINT "tb_user_internal_id_user_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_user_internal_kawasan_industri" DROP CONSTRAINT "tb_user_internal_kawasan_industri_id_user_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_user_internal_provinsi" DROP CONSTRAINT "tb_user_internal_provinsi_id_user_fkey";

-- DropForeignKey
ALTER TABLE "public"."users" DROP CONSTRAINT "users_role_id_fkey";

-- AddForeignKey
ALTER TABLE "public"."users" ADD CONSTRAINT "users_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."role"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."menu" ADD CONSTRAINT "menu_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "public"."menu"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."role_menu_access" ADD CONSTRAINT "role_menu_access_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."role"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."role_menu_access" ADD CONSTRAINT "role_menu_access_menu_id_fkey" FOREIGN KEY ("menu_id") REFERENCES "public"."menu"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_app_slider_peluang_tr" ADD CONSTRAINT "tb_app_slider_peluang_tr_id_app_slider_fkey" FOREIGN KEY ("id_app_slider") REFERENCES "public"."tb_app_slider_peluang"("id_app_slider") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_app_slider_tr" ADD CONSTRAINT "tb_app_slider_tr_id_app_slider_fkey" FOREIGN KEY ("id_app_slider") REFERENCES "public"."tb_app_slider"("id_app_slider") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_daerah" ADD CONSTRAINT "tb_sektor_daerah_id_adm_provinsi_fkey" FOREIGN KEY ("id_adm_provinsi") REFERENCES "public"."tb_adm_provinsi"("id_adm_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional" ADD CONSTRAINT "tb_sektor_nasional_id_sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional_ref"("id_sektor") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_file" ADD CONSTRAINT "tb_sektor_nasional_file_id_sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional"("id_sektor_nasional") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_file_tr" ADD CONSTRAINT "tb_sektor_nasional_file_tr_id_sektor_nasional_file_fkey" FOREIGN KEY ("id_sektor_nasional_file") REFERENCES "public"."tb_sektor_nasional_file"("id_sektor_nasional_file") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_insentif_file" ADD CONSTRAINT "tb_sektor_nasional_insentif_file_id_sektor_nasional_insent_fkey" FOREIGN KEY ("id_sektor_nasional_insentif") REFERENCES "public"."tb_sektor_nasional_insentif"("id_sektor_nasional_insentif") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_insentif_tr" ADD CONSTRAINT "tb_sektor_nasional_insentif_tr_id_sektor_nasional_insentif_fkey" FOREIGN KEY ("id_sektor_nasional_insentif") REFERENCES "public"."tb_sektor_nasional_insentif"("id_sektor_nasional_insentif") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_sumber_data" ADD CONSTRAINT "tb_sektor_nasional_sumber_data_id_sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional"("id_sektor_nasional") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_tr" ADD CONSTRAINT "tb_sektor_nasional_tr_id_sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional"("id_sektor_nasional") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_value" ADD CONSTRAINT "tb_sektor_nasional_value_id_sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional_ref"("id_sektor") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_value_detail" ADD CONSTRAINT "tb_sektor_nasional_value_detail_id_sektor_nasional_value_fkey" FOREIGN KEY ("id_sektor_nasional_value") REFERENCES "public"."tb_sektor_nasional_value"("id_sektor_nasional_value") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_value_tr" ADD CONSTRAINT "tb_sektor_nasional_value_tr_id_sektor_nasional_value_fkey" FOREIGN KEY ("id_sektor_nasional_value") REFERENCES "public"."tb_sektor_nasional_value"("id_sektor_nasional_value") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_kabkot" ADD CONSTRAINT "tb_peluang_kabkot_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_kabkot" ADD CONSTRAINT "tb_peluang_kabkot_id_sektor_fkey" FOREIGN KEY ("id_sektor") REFERENCES "public"."tb_peluang_sektor"("id_peluang_sektor") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_kabkot" ADD CONSTRAINT "tb_peluang_kabkot_id_sumber_data_fkey" FOREIGN KEY ("id_sumber_data") REFERENCES "public"."tb_sumber_data"("id_sumber_data") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_kabkot_file" ADD CONSTRAINT "tb_peluang_kabkot_file_id_peluang_kabkot_fkey" FOREIGN KEY ("id_peluang_kabkot") REFERENCES "public"."tb_peluang_kabkot"("id_peluang_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_adm_provinsi" ADD CONSTRAINT "tb_adm_provinsi_id_adm_wilayah_fkey" FOREIGN KEY ("id_adm_wilayah") REFERENCES "public"."tb_adm_wilayah"("id_adm_wilayah") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_adm_kabkot" ADD CONSTRAINT "tb_adm_kabkot_id_adm_provinsi_fkey" FOREIGN KEY ("id_adm_provinsi") REFERENCES "public"."tb_adm_provinsi"("id_adm_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_investasi_provinsi" ADD CONSTRAINT "tb_investasi_provinsi_id_jenis_fkey" FOREIGN KEY ("id_jenis") REFERENCES "public"."tb_investasi_jenis"("id_investasi_jenis") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sumber_data" ADD CONSTRAINT "tb_sumber_data_id_sumber_data_judul_fkey" FOREIGN KEY ("id_sumber_data_judul") REFERENCES "public"."tb_sumber_data_judul"("id_sumber_data_judul") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sumber_data_instansi_tr" ADD CONSTRAINT "tb_sumber_data_instansi_tr_id_sumber_data_instansi_fkey" FOREIGN KEY ("id_sumber_data_instansi") REFERENCES "public"."tb_sumber_data_instansi"("id_sumber_data_instansi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sumber_data_judul" ADD CONSTRAINT "tb_sumber_data_judul_id_sumber_data_instansi_fkey" FOREIGN KEY ("id_sumber_data_instansi") REFERENCES "public"."tb_sumber_data_instansi"("id_sumber_data_instansi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_halaman_pengunjung_det" ADD CONSTRAINT "tb_halaman_pengunjung_det_id_halaman_pengunjung_fkey" FOREIGN KEY ("id_halaman_pengunjung") REFERENCES "public"."tb_halaman_pengunjung"("id_halaman_pengunjung") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_user_internal" ADD CONSTRAINT "tb_user_internal_id_user_fkey" FOREIGN KEY ("id_user") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_user_internal_kawasan_industri" ADD CONSTRAINT "tb_user_internal_kawasan_industri_id_user_fkey" FOREIGN KEY ("id_user") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_user_internal_provinsi" ADD CONSTRAINT "tb_user_internal_provinsi_id_user_fkey" FOREIGN KEY ("id_user") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_jenis_insentif_file" ADD CONSTRAINT "tb_jenis_insentif_file_id_jenis_insentif_fkey" FOREIGN KEY ("id_jenis_insentif") REFERENCES "public"."tb_jenis_insentif"("id_jenis_insentif") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_jenis_insentif_file_tr" ADD CONSTRAINT "tb_jenis_insentif_file_tr_id_jenis_insentif_fkey" FOREIGN KEY ("id_jenis_insentif") REFERENCES "public"."tb_jenis_insentif"("id_jenis_insentif") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_jenis_insentif_kbli" ADD CONSTRAINT "tb_jenis_insentif_kbli_id_jenis_insentif_fkey" FOREIGN KEY ("id_jenis_insentif") REFERENCES "public"."tb_jenis_insentif"("id_jenis_insentif") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_jenis_insentif_status" ADD CONSTRAINT "tb_jenis_insentif_status_id_jenis_insentif_fkey" FOREIGN KEY ("id_jenis_insentif") REFERENCES "public"."tb_jenis_insentif"("id_jenis_insentif") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_jenis_insentif_tr" ADD CONSTRAINT "tb_jenis_insentif_tr_id_jenis_insentif_fkey" FOREIGN KEY ("id_jenis_insentif") REFERENCES "public"."tb_jenis_insentif"("id_jenis_insentif") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_nasional" ADD CONSTRAINT "tb_sub_sektor_nasional_id_sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional_ref"("id_sektor") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_nasional_value" ADD CONSTRAINT "tb_sub_sektor_nasional_value_id_sub_sektor_nasional_fkey" FOREIGN KEY ("id_sub_sektor_nasional") REFERENCES "public"."tb_sub_sektor_nasional"("id_sub_sektor_nasional") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_nasional_value_detail" ADD CONSTRAINT "tb_sub_sektor_nasional_value_detail_id_sub_sektor_nasional_fkey" FOREIGN KEY ("id_sub_sektor_nasional_value") REFERENCES "public"."tb_sub_sektor_nasional_value"("id_sub_sektor_nasional_value") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_nasional_value_tr" ADD CONSTRAINT "tb_sub_sektor_nasional_value_tr_id_sub_sektor_nasional_val_fkey" FOREIGN KEY ("id_sub_sektor_nasional_value") REFERENCES "public"."tb_sub_sektor_nasional_value"("id_sub_sektor_nasional_value") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_kontak" ADD CONSTRAINT "tb_sektor_nasional_kontak_id_sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional"("id_sektor_nasional") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_news_file" ADD CONSTRAINT "tb_news_file_id_news_fkey" FOREIGN KEY ("id_news") REFERENCES "public"."tb_news"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_news_status" ADD CONSTRAINT "tb_news_status_id_news_fkey" FOREIGN KEY ("id_news") REFERENCES "public"."tb_news"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_news_tr" ADD CONSTRAINT "tb_news_tr_id_news_fkey" FOREIGN KEY ("id_news") REFERENCES "public"."tb_news"("id") ON DELETE CASCADE ON UPDATE CASCADE;
