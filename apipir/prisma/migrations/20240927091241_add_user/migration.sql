-- CreateTable
CREATE TABLE "public"."tb_user_internal" (
    "id_user" INTEGER NOT NULL,
    "id_jabatan" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_user_internal_kawasan_industri" (
    "id_user" INTEGER NOT NULL,
    "id_kawasan_industri" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_user_internal_provinsi" (
    "id_user" INTEGER NOT NULL,
    "id_adm_provinsi" INTEGER NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "tb_user_internal_id_user_key" ON "public"."tb_user_internal"("id_user");

-- CreateIndex
CREATE UNIQUE INDEX "tb_user_internal_kawasan_industri_id_user_key" ON "public"."tb_user_internal_kawasan_industri"("id_user");

-- CreateIndex
CREATE UNIQUE INDEX "tb_user_internal_provinsi_id_user_key" ON "public"."tb_user_internal_provinsi"("id_user");

-- AddForeignKey
ALTER TABLE "public"."tb_user_internal" ADD CONSTRAINT "tb_user_internal_id_user_fkey" FOREIGN KEY ("id_user") REFERENCES "public"."users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_user_internal_kawasan_industri" ADD CONSTRAINT "tb_user_internal_kawasan_industri_id_user_fkey" FOREIGN KEY ("id_user") REFERENCES "public"."users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_user_internal_provinsi" ADD CONSTRAINT "tb_user_internal_provinsi_id_user_fkey" FOREIGN KEY ("id_user") REFERENCES "public"."users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
