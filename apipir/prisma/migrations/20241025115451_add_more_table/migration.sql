-- CreateTable
CREATE TABLE "public"."tb_bandara" (
    "id_bandara" SERIAL NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "id_kelas" INTEGER NOT NULL,
    "id_ka<PERSON><PERSON>i" INTEGER NOT NULL,
    "nama" VARCHAR(50) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "jarak_ibu_kota_provinsi" DOUBLE PRECISION NOT NULL,
    "iata" VARCHAR(10) NOT NULL,
    "alamat" VARCHAR(255) NOT NULL,
    "no_telp" VARCHAR(50) NOT NULL,
    "no_fax" VARCHAR(30) NOT NULL,
    "url_web" VARCHAR(255) NOT NULL,
    "jam_operasional_awal" TIME NOT NULL,
    "jam_operasional_akhir" TIME NOT NULL,
    "id_zona_waktu" INTEGER NOT NULL,
    "jenis_pesawat" VARCHAR(255),
    "maskapai" VARCHAR(255),
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "status" INTEGER NOT NULL,
    "is_ikn" BOOLEAN,

    CONSTRAINT "tb_bandara_pkey" PRIMARY KEY ("id_bandara")
);

-- CreateTable
CREATE TABLE "public"."tb_hotel" (
    "id_hotel" SERIAL NOT NULL,
    "id_kelas" INTEGER NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "file_logo" VARCHAR(255) NOT NULL,
    "alamat" TEXT NOT NULL,
    "no_telp" VARCHAR(30) NOT NULL,
    "no_fax" VARCHAR(30) NOT NULL,
    "email" VARCHAR(50) NOT NULL,
    "status" INTEGER NOT NULL,
    "url_web" VARCHAR(255) NOT NULL,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "is_ikn" BOOLEAN,

    CONSTRAINT "tb_hotel_pkey" PRIMARY KEY ("id_hotel")
);

-- CreateTable
CREATE TABLE "public"."tb_pelabuhan" (
    "id_pelabuhan" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "alamat" VARCHAR(255) NOT NULL,
    "no_telp" VARCHAR(50) NOT NULL,
    "no_fax" VARCHAR(50) NOT NULL,
    "url_web" VARCHAR(255) NOT NULL,
    "panjang_dermaga" DOUBLE PRECISION NOT NULL,
    "kedalaman" DOUBLE PRECISION NOT NULL,
    "id_fungsi" INTEGER NOT NULL,
    "id_kelas" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "is_ikn" BOOLEAN,

    CONSTRAINT "tb_pelabuhan_pkey" PRIMARY KEY ("id_pelabuhan")
);

-- CreateTable
CREATE TABLE "public"."tb_pendidikan" (
    "id_pendidikan" SERIAL NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "id_kategori" INTEGER NOT NULL,
    "id_jenjang" INTEGER NOT NULL,
    "alamat" VARCHAR(255) NOT NULL,
    "no_telp" VARCHAR(30) NOT NULL,
    "no_fax" VARCHAR(30) NOT NULL,
    "email" VARCHAR(255) NOT NULL,
    "url_web" VARCHAR(255) NOT NULL,
    "status" INTEGER NOT NULL,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "is_ikn" BOOLEAN,

    CONSTRAINT "tb_pendidikan_pkey" PRIMARY KEY ("id_pendidikan")
);

-- CreateTable
CREATE TABLE "public"."tb_rumah_sakit" (
    "id_rumah_sakit" SERIAL NOT NULL,
    "id_adm_kabkot" INTEGER,
    "id_sumber_data" INTEGER,
    "nama" VARCHAR(255),
    "keterangan" TEXT,
    "id_kategori" INTEGER,
    "id_jenis" INTEGER,
    "alamat" VARCHAR(255),
    "no_telp" VARCHAR(30),
    "no_fax" VARCHAR(30),
    "email" VARCHAR(255),
    "url_web" VARCHAR(255),
    "status" INTEGER,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "is_ikn" BOOLEAN,

    CONSTRAINT "tb_rumah_sakit_pkey" PRIMARY KEY ("id_rumah_sakit")
);
