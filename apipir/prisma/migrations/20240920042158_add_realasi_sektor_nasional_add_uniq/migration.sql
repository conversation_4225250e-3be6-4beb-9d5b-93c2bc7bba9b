/*
  Warnings:

  - A unique constraint covering the columns `[id_sektor]` on the table `tb_sektor_nasional_ref` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateIndex
CREATE UNIQUE INDEX "tb_sektor_nasional_ref_id_sektor_key" ON "public"."tb_sektor_nasional_ref"("id_sektor");

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_daerah" ADD CONSTRAINT "tb_sektor_daerah_id_sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional_ref"("id_sektor") ON DELETE RESTRICT ON UPDATE CASCADE;
