-- AddForeignKey
ALTER TABLE "public"."tb_sektor_daerah_file" ADD CONSTRAINT "tb_sektor_daerah_file_id_sektor_daerah_fkey" FOREIGN KEY ("id_sektor_daerah") REFERENCES "public"."tb_sektor_daerah"("id_sektor_daerah") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_daerah_insentif" ADD CONSTRAINT "tb_sektor_daerah_insentif_id_sektor_daerah_fkey" FOREIGN KEY ("id_sektor_daerah") REFERENCES "public"."tb_sektor_daerah"("id_sektor_daerah") ON DELETE CASCADE ON UPDATE CASCADE;
