-- AddForeignKey
ALTER TABLE "public"."tb_peluang_kabkot_insentif" ADD CONSTRAINT "tb_peluang_kabkot_insentif_id_peluang_kabkot_fkey" FOREIGN KEY ("id_peluang_kabkot") REFERENCES "public"."tb_peluang_kabkot"("id_peluang_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_kabkot_insentif" ADD CONSTRAINT "tb_peluang_kabkot_insentif_id_jenis_insentif_fkey" FOREIGN KEY ("id_jenis_insentif") REFERENCES "public"."tb_jenis_insentif"("id_jenis_insentif") ON DELETE CASCADE ON UPDATE CASCADE;
