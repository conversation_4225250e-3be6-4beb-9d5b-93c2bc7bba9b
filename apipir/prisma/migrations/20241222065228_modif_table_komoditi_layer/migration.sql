/*
  Warnings:

  - You are about to drop the column `id_komoditi` on the `tb_komoditi_layers` table. All the data in the column will be lost.
  - Added the required column `id_komoditi_kabkot` to the `tb_komoditi_layers` table without a default value. This is not possible if the table is not empty.
  - Added the required column `id_komoditi_provinsi` to the `tb_komoditi_layers` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "public"."tb_komoditi_layers" DROP CONSTRAINT "tb_komoditi_layers_id_komoditi_fkey";

-- DropForeignKey
ALTER TABLE "public"."tb_sektor_daerah" DROP CONSTRAINT "tb_sektor_daerah_id_sektor_nasional_fkey";

-- AlterTable
ALTER TABLE "public"."tb_komoditi_layers" DROP COLUMN "id_komoditi",
ADD COLUMN     "id_komoditi_kabkot" INTEGER NOT NULL,
ADD COLUMN     "id_komoditi_provinsi" INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE "public"."tb_komoditi_layers" ADD CONSTRAINT "tb_komoditi_layers_id_komoditi_kabkot_fkey" FOREIGN KEY ("id_komoditi_kabkot") REFERENCES "public"."tb_komoditi_kabkot"("id_komoditi_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_komoditi_layers" ADD CONSTRAINT "tb_komoditi_layers_id_komoditi_provinsi_fkey" FOREIGN KEY ("id_komoditi_provinsi") REFERENCES "public"."tb_komoditi_provinsi"("id_komoditi_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;
