-- CreateTable
CREATE TABLE "public"."tb_jenis_insentif" (
    "id_jenis_insentif" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "status" INTEGER NOT NULL,
    "tipe" INTEGER,
    "id_adm_provinsi" INTEGER,
    "id_adm_kabkot" INTEGER,

    CONSTRAINT "tb_jenis_insentif_pkey" PRIMARY KEY ("id_jenis_insentif")
);

-- CreateTable
CREATE TABLE "public"."tb_jenis_insentif_file" (
    "id_jenis_insentif_file" SERIAL NOT NULL,
    "id_jenis_insentif" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_jenis_insentif_file_pkey" PRIMARY KEY ("id_jenis_insentif_file")
);

-- CreateTable
CREATE TABLE "public"."tb_jenis_insentif_file_tr" (
    "id_jenis_insentif_file_tr" SERIAL NOT NULL,
    "id_jenis_insentif" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "tipe" INTEGER,

    CONSTRAINT "tb_jenis_insentif_file_tr_pkey" PRIMARY KEY ("id_jenis_insentif_file_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_jenis_insentif_kbli" (
    "id_jenis_insentif" INTEGER NOT NULL,
    "id_kbli" INTEGER NOT NULL,

    CONSTRAINT "tb_jenis_insentif_kbli_pkey" PRIMARY KEY ("id_jenis_insentif")
);

-- CreateTable
CREATE TABLE "public"."tb_jenis_insentif_status" (
    "id_jenis_insentif_status" SERIAL NOT NULL,
    "id_jenis_insentif" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_jenis_insentif_status_pkey" PRIMARY KEY ("id_jenis_insentif_status")
);

-- CreateTable
CREATE TABLE "public"."tb_jenis_insentif_tr" (
    "id_jenis_insentif_tr" SERIAL NOT NULL,
    "id_jenis_insentif" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_jenis_insentif_tr_pkey" PRIMARY KEY ("id_jenis_insentif_tr")
);

-- AddForeignKey
ALTER TABLE "public"."tb_jenis_insentif_file" ADD CONSTRAINT "tb_jenis_insentif_file_id_jenis_insentif_fkey" FOREIGN KEY ("id_jenis_insentif") REFERENCES "public"."tb_jenis_insentif"("id_jenis_insentif") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_jenis_insentif_file_tr" ADD CONSTRAINT "tb_jenis_insentif_file_tr_id_jenis_insentif_fkey" FOREIGN KEY ("id_jenis_insentif") REFERENCES "public"."tb_jenis_insentif"("id_jenis_insentif") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_jenis_insentif_kbli" ADD CONSTRAINT "tb_jenis_insentif_kbli_id_jenis_insentif_fkey" FOREIGN KEY ("id_jenis_insentif") REFERENCES "public"."tb_jenis_insentif"("id_jenis_insentif") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_jenis_insentif_status" ADD CONSTRAINT "tb_jenis_insentif_status_id_jenis_insentif_fkey" FOREIGN KEY ("id_jenis_insentif") REFERENCES "public"."tb_jenis_insentif"("id_jenis_insentif") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_jenis_insentif_tr" ADD CONSTRAINT "tb_jenis_insentif_tr_id_jenis_insentif_fkey" FOREIGN KEY ("id_jenis_insentif") REFERENCES "public"."tb_jenis_insentif"("id_jenis_insentif") ON DELETE RESTRICT ON UPDATE CASCADE;
