/*
  Warnings:

  - Made the column `id_zona_waktu` on table `tb_adm_provinsi` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "public"."tb_adm_provinsi" ALTER COLUMN "id_zona_waktu" SET NOT NULL;

-- AddForeignKey
ALTER TABLE "public"."tb_adm_provinsi" ADD CONSTRAINT "tb_adm_provinsi_id_zona_waktu_fkey" FOREIGN KEY ("id_zona_waktu") REFERENCES "public"."tb_zona_waktu"("id_zona_waktu") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_zona_waktu_tr" ADD CONSTRAINT "tb_zona_waktu_tr_id_zona_waktu_fkey" FOREIGN KEY ("id_zona_waktu") REFERENCES "public"."tb_zona_waktu"("id_zona_waktu") ON DELETE CASCADE ON UPDATE CASCADE;
