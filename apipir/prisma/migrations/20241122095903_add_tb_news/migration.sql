/*
  Warnings:

  - You are about to drop the `_tb_adm_provinsiTotb_adm_provinsi_kantor` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."_tb_adm_provinsiTotb_adm_provinsi_kantor" DROP CONSTRAINT "_tb_adm_provinsiTotb_adm_provinsi_kantor_A_fkey";

-- DropForeignKey
ALTER TABLE "public"."_tb_adm_provinsiTotb_adm_provinsi_kantor" DROP CONSTRAINT "_tb_adm_provinsiTotb_adm_provinsi_kantor_B_fkey";

-- AlterTable
ALTER TABLE "public"."tb_news" ADD COLUMN     "id_adm_kabkot" INTEGER,
ADD COLUMN     "id_adm_provinsi" INTEGER;

-- DropTable
DROP TABLE "public"."_tb_adm_provinsiTotb_adm_provinsi_kantor";

-- AddForeignKey
ALTER TABLE "public"."tb_adm_provinsi_kantor" ADD CONSTRAINT "tb_adm_provinsi_kantor_id_adm_provinsi_fkey" FOREIGN KEY ("id_adm_provinsi") REFERENCES "public"."tb_adm_provinsi"("id_adm_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_news" ADD CONSTRAINT "tb_news_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_news" ADD CONSTRAINT "tb_news_id_adm_provinsi_fkey" FOREIGN KEY ("id_adm_provinsi") REFERENCES "public"."tb_adm_provinsi"("id_adm_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;
