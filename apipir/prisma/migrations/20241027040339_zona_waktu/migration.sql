-- AlterTable
ALTER TABLE "public"."tb_adm_provinsi" ADD COLUMN     "id_zona_waktu" INTEGER;

-- CreateTable
CREATE TABLE "public"."tb_zona_waktu" (
    "id_zona_waktu" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_zona_waktu_pkey" PRIMARY KEY ("id_zona_waktu")
);

-- CreateTable
CREATE TABLE "public"."tb_zona_waktu_tr" (
    "id_zona_waktu_tr" SERIAL NOT NULL,
    "id_zona_waktu" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_zona_waktu_tr_pkey" PRIMARY KEY ("id_zona_waktu_tr")
);

-- AddForeignKey
ALTER TABLE "public"."tb_investasi_provinsi" ADD CONSTRAINT "tb_investasi_provinsi_id_adm_provinsi_fkey" FOREIGN KEY ("id_adm_provinsi") REFERENCES "public"."tb_adm_provinsi"("id_adm_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;
