-- AddForeignKey
ALTER TABLE "public"."tb_peluang_daerah_kontak" ADD CONSTRAINT "tb_peluang_daerah_kontak_id_peluang_daerah_fkey" FOREIGN KEY ("id_peluang_daerah") REFERENCES "public"."tb_peluang_daerah"("id_peluang_daerah") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_daerah_kontak" ADD CONSTRAINT "tb_peluang_daerah_kontak_id_peluang_kontak_fkey" FOREIGN KEY ("id_peluang_kontak") REFERENCES "public"."tb_peluang_kontak"("id_peluang_kontak") ON DELETE CASCADE ON UPDATE CASCADE;
