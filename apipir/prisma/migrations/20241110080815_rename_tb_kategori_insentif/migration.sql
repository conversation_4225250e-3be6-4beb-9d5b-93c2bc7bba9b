/*
  Warnings:

  - You are about to drop the `tb_ketegori_insentif` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."tb_jenis_insentif" DROP CONSTRAINT "tb_jenis_insentif_id_kategori_insentif_fkey";

-- DropTable
DROP TABLE "public"."tb_ketegori_insentif";

-- CreateTable
CREATE TABLE "public"."tb_kategori_insentif" (
    "id_kategori_insentif" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_kategori_insentif_pkey" PRIMARY KEY ("id_kategori_insentif")
);

-- AddForeignKey
ALTER TABLE "public"."tb_jenis_insentif" ADD CONSTRAINT "tb_jenis_insentif_id_kategori_insentif_fkey" FOREIGN KEY ("id_kategori_insentif") REFERENCES "public"."tb_kategori_insentif"("id_kategori_insentif") ON DELETE CASCADE ON UPDATE CASCADE;
