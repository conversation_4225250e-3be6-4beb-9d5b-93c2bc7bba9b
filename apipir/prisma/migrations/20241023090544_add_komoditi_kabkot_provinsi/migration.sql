-- CreateTable
CREATE TABLE "public"."tb_komoditi_kabkot" (
    "id_komoditi_kabkot" SERIAL NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_komoditi" INTEGER,
    "tahun" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "status" INTEGER NOT NULL,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "id_satuan" INTEGER NOT NULL,
    "luas_lahan" DOUBLE PRECISION,
    "nama" VARCHAR(255),
    "nilai_produksi" DOUBLE PRECISION,
    "bijih_hipotik" DOUBLE PRECISION,
    "logam_hipotik" DOUBLE PRECISION,
    "bijih_tereka" DOUBLE PRECISION,
    "logam_tereka" DOUBLE PRECISION,
    "bijih_tertunjuk" DOUBLE PRECISION,
    "logam_tertunjuk" DOUBLE PRECISION,
    "bijih_terukur" DOUBLE PRECISION,
    "logam_terukur" DOUBLE PRECISION,
    "bijih_terkira" DOUBLE PRECISION,
    "logam_terkira" DOUBLE PRECISION,
    "bijih_terbukti" DOUBLE PRECISION,
    "logam_terbukti" DOUBLE PRECISION,
    "logam_status" VARCHAR(255),
    "non_logam_hipotik" DOUBLE PRECISION,
    "non_logam_tereka" DOUBLE PRECISION,
    "non_logam_tertunjuk" DOUBLE PRECISION,
    "non_logam_terukur" DOUBLE PRECISION,
    "non_logam_terkira" DOUBLE PRECISION,
    "non_logam_terbukti" DOUBLE PRECISION,
    "non_logam_status" VARCHAR(255),
    "panas_spekulasi" DOUBLE PRECISION,
    "panas_hipotik" DOUBLE PRECISION,
    "panas_terduga" DOUBLE PRECISION,
    "panas_mungkin" DOUBLE PRECISION,
    "panas_terbukti" DOUBLE PRECISION,
    "panas_terpasang" DOUBLE PRECISION,
    "panas_temperatur" DOUBLE PRECISION,
    "panas_klasifikasi" VARCHAR(255),

    CONSTRAINT "tb_komoditi_kabkot_pkey" PRIMARY KEY ("id_komoditi_kabkot")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_provinsi" (
    "id_komoditi_provinsi" SERIAL NOT NULL,
    "id_adm_provinsi" INTEGER NOT NULL,
    "id_komoditi" INTEGER,
    "tahun" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "status" INTEGER NOT NULL,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "id_satuan" INTEGER NOT NULL,
    "luas_lahan" DOUBLE PRECISION,
    "nama" VARCHAR(255),
    "nilai_produksi" DOUBLE PRECISION,
    "bijih_hipotik" DOUBLE PRECISION,
    "logam_hipotik" DOUBLE PRECISION,
    "bijih_tereka" DOUBLE PRECISION,
    "logam_tereka" DOUBLE PRECISION,
    "bijih_tertunjuk" DOUBLE PRECISION,
    "logam_tertunjuk" DOUBLE PRECISION,
    "bijih_terukur" DOUBLE PRECISION,
    "logam_terukur" DOUBLE PRECISION,
    "bijih_terkira" DOUBLE PRECISION,
    "logam_terkira" DOUBLE PRECISION,
    "bijih_terbukti" DOUBLE PRECISION,
    "logam_terbukti" DOUBLE PRECISION,
    "logam_status" VARCHAR(255),
    "non_logam_hipotik" DOUBLE PRECISION,
    "non_logam_tereka" DOUBLE PRECISION,
    "non_logam_tertunjuk" DOUBLE PRECISION,
    "non_logam_terukur" DOUBLE PRECISION,
    "non_logam_terkira" DOUBLE PRECISION,
    "non_logam_terbukti" DOUBLE PRECISION,
    "non_logam_status" VARCHAR(255),
    "panas_spekulasi" DOUBLE PRECISION,
    "panas_hipotik" DOUBLE PRECISION,
    "panas_terduga" DOUBLE PRECISION,
    "panas_mungkin" DOUBLE PRECISION,
    "panas_terbukti" DOUBLE PRECISION,
    "panas_terpasang" DOUBLE PRECISION,
    "panas_temperatur" DOUBLE PRECISION,
    "panas_klasifikasi" VARCHAR(255),

    CONSTRAINT "tb_komoditi_provinsi_pkey" PRIMARY KEY ("id_komoditi_provinsi")
);
