/*
  Warnings:

  - A unique constraint covering the columns `[id_peluang_sektor]` on the table `tb_peluang_sektor` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateIndex
CREATE UNIQUE INDEX "tb_peluang_sektor_id_peluang_sektor_key" ON "public"."tb_peluang_sektor"("id_peluang_sektor");

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_kabkot" ADD CONSTRAINT "tb_peluang_kabkot_id_sektor_fkey" FOREIGN KEY ("id_sektor") REFERENCES "public"."tb_peluang_sektor"("id_peluang_sektor") ON DELETE RESTRICT ON UPDATE CASCADE;
