-- CreateTable
CREATE TABLE "public"."tb_bandara_kategori" (
    "id_bandara_kategori" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_bandara_kategori_pkey" PRIMARY KEY ("id_bandara_kategori")
);

-- CreateTable
CREATE TABLE "public"."tb_bandara_kategori_tr" (
    "id_bandara_kategori_tr" SERIAL NOT NULL,
    "id_bandara_kategori" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_bandara_kategori_tr_pkey" PRIMARY KEY ("id_bandara_kategori_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_bandara_kelas" (
    "id_bandara_kelas" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_bandara_kelas_pkey" PRIMARY KEY ("id_bandara_kelas")
);

-- CreateTable
CREATE TABLE "public"."tb_bandara_kelas_tr" (
    "id_bandara_kelas_tr" SERIAL NOT NULL,
    "id_bandara_kelas" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_bandara_kelas_tr_pkey" PRIMARY KEY ("id_bandara_kelas_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_bandara_status" (
    "id_bandara_statu" SERIAL NOT NULL,
    "id_bandara" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_bandara_status_pkey" PRIMARY KEY ("id_bandara_statu")
);

-- CreateTable
CREATE TABLE "public"."tb_bandara_tr" (
    "id_bandara_tr" SERIAL NOT NULL,
    "id_bandara" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_bandara_tr_pkey" PRIMARY KEY ("id_bandara_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_buku_tamu" (
    "id_buku_tamu" SERIAL NOT NULL,
    "id_adm_kabkot" INTEGER,
    "tujuan" VARCHAR(100),
    "instansi" VARCHAR(100),
    "deskripsi" TEXT,
    "file" VARCHAR(100),
    "created_date" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "tb_buku_tamu_pkey" PRIMARY KEY ("id_buku_tamu")
);

-- CreateTable
CREATE TABLE "public"."tb_buku_tamu_pengunjung" (
    "id_buku_tamu_pengunjung" SERIAL NOT NULL,
    "id_buku_tamu" INTEGER,
    "nama" VARCHAR(100),
    "nip" VARCHAR(20),
    "jabatan" VARCHAR(100),
    "file_ttd" VARCHAR(100),
    "no_hp" VARCHAR(14),

    CONSTRAINT "tb_buku_tamu_pengunjung_pkey" PRIMARY KEY ("id_buku_tamu_pengunjung")
);

-- CreateTable
CREATE TABLE "public"."tb_ekspor_kabkot" (
    "id_ekspor_kabkot" SERIAL NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "tahun" INTEGER,
    "keterangan" TEXT,
    "nilai_ekspor" DOUBLE PRECISION,
    "status" INTEGER,
    "nilai_impor" DOUBLE PRECISION,

    CONSTRAINT "tb_ekspor_kabkot_pkey" PRIMARY KEY ("id_ekspor_kabkot")
);

-- CreateTable
CREATE TABLE "public"."tb_ekspor_kabkot_status" (
    "id_ekspor_kabkot_status" SERIAL NOT NULL,
    "id_ekspor_kabkot" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_ekspor_kabkot_status_pkey" PRIMARY KEY ("id_ekspor_kabkot_status")
);

-- CreateTable
CREATE TABLE "public"."tb_ekspor_kabkot_tr" (
    "id_ekspor_kabkot_tr" SERIAL NOT NULL,
    "id_ekspor_kabkot" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "keterangan" TEXT,

    CONSTRAINT "tb_ekspor_kabkot_tr_pkey" PRIMARY KEY ("id_ekspor_kabkot_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_ekspor_provinsi" (
    "id_ekspor_provinsi" SERIAL NOT NULL,
    "id_adm_provinsi" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "tahun" INTEGER,
    "keterangan" TEXT,
    "nilai_ekspor" DOUBLE PRECISION,
    "status" INTEGER,
    "nilai_impor" DOUBLE PRECISION,

    CONSTRAINT "tb_ekspor_provinsi_pkey" PRIMARY KEY ("id_ekspor_provinsi")
);

-- CreateTable
CREATE TABLE "public"."tb_ekspor_provinsi_status" (
    "id_ekspor_provinsi_status" SERIAL NOT NULL,
    "id_ekspor_provinsi" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_ekspor_provinsi_status_pkey" PRIMARY KEY ("id_ekspor_provinsi_status")
);

-- CreateTable
CREATE TABLE "public"."tb_ekspor_provinsi_tr" (
    "id_ekspor_provinsi_tr" SERIAL NOT NULL,
    "id_ekspor_provinsi" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "keterangan" TEXT,

    CONSTRAINT "tb_ekspor_provinsi_tr_pkey" PRIMARY KEY ("id_ekspor_provinsi_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_email_subscription" (
    "id_email_subscription" SERIAL NOT NULL,
    "email" VARCHAR(255) NOT NULL,
    "lang" VARCHAR(10) NOT NULL,
    "ip_pengunjung" VARCHAR(15) NOT NULL,
    "token_exp_date" TIMESTAMPTZ(6) DEFAULT (CURRENT_TIMESTAMP + '3 days'::interval day),
    "token" VARCHAR(255) NOT NULL,
    "is_active" BOOLEAN NOT NULL,
    "created_time" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "created_by" VARCHAR(255),
    "modified_time" TIMESTAMP(6),

    CONSTRAINT "tb_email_subscription_pkey" PRIMARY KEY ("id_email_subscription")
);

-- CreateTable
CREATE TABLE "public"."tb_glossary" (
    "id_glossary" SERIAL NOT NULL,
    "glossary_type" VARCHAR(20) NOT NULL,
    "title" VARCHAR(150) NOT NULL,
    "description" VARCHAR(2000) NOT NULL,
    "modified_time" DATE DEFAULT CURRENT_DATE,

    CONSTRAINT "tb_glossary_pkey" PRIMARY KEY ("id_glossary")
);

-- CreateTable
CREATE TABLE "public"."tb_glossary_tr" (
    "id_glossary_tr" SERIAL NOT NULL,
    "id_glossary" INTEGER NOT NULL,
    "kode_bahasa" VARCHAR(2) NOT NULL,
    "title" VARCHAR(150) NOT NULL,
    "description" VARCHAR(2000) NOT NULL,
    "modified_time" DATE DEFAULT CURRENT_DATE,

    CONSTRAINT "tb_glossary_tr_pkey" PRIMARY KEY ("id_glossary_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_hotel_kelas" (
    "id_hotel_kelas" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_hotel_kelas_pkey" PRIMARY KEY ("id_hotel_kelas")
);

-- CreateTable
CREATE TABLE "public"."tb_hotel_kelas_tr" (
    "id_hotel_kelas_tr" SERIAL NOT NULL,
    "id_hotel_kelas" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_hotel_kelas_tr_pkey" PRIMARY KEY ("id_hotel_kelas_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_hotel_status" (
    "id_hotel_staus" SERIAL NOT NULL,
    "id_hotel" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_hotel_status_pkey" PRIMARY KEY ("id_hotel_staus")
);

-- CreateTable
CREATE TABLE "public"."tb_hotel_tr" (
    "id_hotel_tr" SERIAL NOT NULL,
    "id_hotel" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "file_logo" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_hotel_tr_pkey" PRIMARY KEY ("id_hotel_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_investasi_jenis_data" (
    "id_investasi_jenis_data" SERIAL NOT NULL,
    "nama" VARCHAR(50),
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_investasi_jenis_data_pkey" PRIMARY KEY ("id_investasi_jenis_data")
);

-- CreateTable
CREATE TABLE "public"."tb_investasi_jenis_data_tr" (
    "id_investasi_jenis_data_tr" SERIAL NOT NULL,
    "id_investasi_jenis_data" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50),
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_investasi_jenis_data_tr_pkey" PRIMARY KEY ("id_investasi_jenis_data_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_investasi_jenis_tr" (
    "id_investasi_jenis_tr" SERIAL NOT NULL,
    "id_investasi_jenis" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50),
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_investasi_jenis_tr_pkey" PRIMARY KEY ("id_investasi_jenis_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_investasi_kabkot" (
    "id_investasi_kabkot" SERIAL NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "id_sektor" INTEGER NOT NULL,
    "id_jenis" INTEGER NOT NULL,
    "id_jenis_data" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "jumlah_proyek" INTEGER NOT NULL,
    "jumlah_investasi" DOUBLE PRECISION NOT NULL,
    "status" INTEGER NOT NULL,

    CONSTRAINT "tb_investasi_kabkot_pkey" PRIMARY KEY ("id_investasi_kabkot")
);

-- CreateTable
CREATE TABLE "public"."tb_investasi_kabkot_status" (
    "id_investasi_kabkot_status" SERIAL NOT NULL,
    "id_investasi_kabkot" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_investasi_kabkot_status_pkey" PRIMARY KEY ("id_investasi_kabkot_status")
);

-- CreateTable
CREATE TABLE "public"."tb_investasi_provinsi_status" (
    "id_investasi_provinsi_status" SERIAL NOT NULL,
    "id_investasi_provinsi" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_investasi_provinsi_status_pkey" PRIMARY KEY ("id_investasi_provinsi_status")
);

-- CreateTable
CREATE TABLE "public"."tb_investasi_sektor" (
    "id_investasi_sektor" SERIAL NOT NULL,
    "nama" VARCHAR(255),
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_investasi_sektor_pkey" PRIMARY KEY ("id_investasi_sektor")
);

-- CreateTable
CREATE TABLE "public"."tb_investasi_sektor_tr" (
    "id_investasi_sektor_tr" SERIAL NOT NULL,
    "id_investasi_sektor" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255),
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_investasi_sektor_tr_pkey" PRIMARY KEY ("id_investasi_sektor_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_blok_fungsi" (
    "id_kawasan_industri_blok_fungsi" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_kawasan_industri_blok_fungsi_pkey" PRIMARY KEY ("id_kawasan_industri_blok_fungsi")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_blok_fungsi_tr" (
    "id_kawasan_industri_blok_fungsi_tr" SERIAL NOT NULL,
    "id_kawasan_industri_blok_fungsi" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_kawasan_industri_blok_fungsi_tr_pkey" PRIMARY KEY ("id_kawasan_industri_blok_fungsi_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_blok_kondisi" (
    "id_kawasan_industri_blok_kondisi" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_kawasan_industri_blok_kondisi_pkey" PRIMARY KEY ("id_kawasan_industri_blok_kondisi")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_blok_kondisi_tr" (
    "id_kawasan_industri_blok_kondisi_tr" SERIAL NOT NULL,
    "id_kawasan_industri_blok_kondisi" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_kawasan_industri_blok_kondisi_tr_pkey" PRIMARY KEY ("id_kawasan_industri_blok_kondisi_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_blok_staging" (
    "id_kawasan_industri_blok_staging" SERIAL NOT NULL,
    "id_kawasan_industri" INTEGER,
    "status" INTEGER,
    "kode" VARCHAR(255),
    "kondisi" VARCHAR(255),
    "fungsi" VARCHAR(255),
    "luas" VARCHAR(255),
    "harga" VARCHAR(255),
    "perusahaan" VARCHAR(255),
    "negara" VARCHAR(255),
    "lon" VARCHAR(255),
    "lat" VARCHAR(255),
    "prj" VARCHAR(255),
    "shape" TEXT,
    "log" TEXT,
    "publish_by" INTEGER,
    "publish_dt" TIMESTAMP(6),

    CONSTRAINT "tb_kawasan_industri_blok_staging_pkey" PRIMARY KEY ("id_kawasan_industri_blok_staging")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_blok_status" (
    "id_kawasan_industri_blok" SERIAL NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_kawasan_industri_blok_status_pkey" PRIMARY KEY ("id_kawasan_industri_blok")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_blok_status_tr" (
    "id_kawasan_industri_blok_status_tr" SERIAL NOT NULL,
    "id_kawasan_industri_blok_status" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_kawasan_industri_blok_status_tr_pkey" PRIMARY KEY ("id_kawasan_industri_blok_status_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_blok_tr" (
    "id_kawasan_industri_blok_tr" SERIAL NOT NULL,
    "id_kawasan_industri_blok" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "nama_perusahaan" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_kawasan_industri_blok_tr_pkey" PRIMARY KEY ("id_kawasan_industri_blok_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_file" (
    "id_kawasan_industri_file" SERIAL NOT NULL,
    "id_kawasan_industri" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_kawasan_industri_file_pkey" PRIMARY KEY ("id_kawasan_industri_file")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_file_tr" (
    "id_kawasan_industri_file_tr" SERIAL NOT NULL,
    "id_kawasan_industri_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_kawasan_industri_file_tr_pkey" PRIMARY KEY ("id_kawasan_industri_file_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_kategori" (
    "id_kawasan_industri_kategori" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_kawasan_industri_kategori_pkey" PRIMARY KEY ("id_kawasan_industri_kategori")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_kategori_tr" (
    "id_kawasan_industri_kategori_tr" SERIAL NOT NULL,
    "id_kawasan_industri_kategori" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_kawasan_industri_kategori_tr_pkey" PRIMARY KEY ("id_kawasan_industri_kategori_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_layer_spasial" (
    "id_kawasan_industri_layer_spasial" SERIAL NOT NULL,
    "id_kawasan_industri" INTEGER NOT NULL,
    "nama_layer" VARCHAR(500) NOT NULL,
    "tipe" INTEGER NOT NULL,
    "url_service" VARCHAR(500) NOT NULL,
    "status" INTEGER NOT NULL,
    "is_active" BOOLEAN,

    CONSTRAINT "tb_kawasan_industri_layer_spasial_pkey" PRIMARY KEY ("id_kawasan_industri_layer_spasial")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_occupancy" (
    "id_kawasan_industri_occupancy" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_kawasan_industri_occupancy_pkey" PRIMARY KEY ("id_kawasan_industri_occupancy")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_peluang" (
    "id_kawasan_industri_peluang" SERIAL NOT NULL,
    "id_kawasan_industri" INTEGER NOT NULL,
    "judul" VARCHAR NOT NULL,
    "nilai_investasi" DOUBLE PRECISION,
    "deskripsi" TEXT NOT NULL,
    "luas_lahan" DOUBLE PRECISION,

    CONSTRAINT "tb_kawasan_industri_peluang_pkey" PRIMARY KEY ("id_kawasan_industri_peluang")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_peluang_file" (
    "id_kawasan_industri_peluang_file" SERIAL NOT NULL,
    "id_kawasan_industri_peluang" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_kawasan_industri_peluang_file_pkey" PRIMARY KEY ("id_kawasan_industri_peluang_file")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_peluang_tr" (
    "id_kawasan_industri_peluang_tr" SERIAL NOT NULL,
    "id_kawasan_industri_peluang" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR NOT NULL,
    "judul" VARCHAR NOT NULL,
    "deskripsi" TEXT NOT NULL,

    CONSTRAINT "tb_kawasan_industri_peluang_tr_pkey" PRIMARY KEY ("id_kawasan_industri_peluang_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_ref_range" (
    "id_kawasan_industri_ref_range" SERIAL NOT NULL,
    "min" DOUBLE PRECISION NOT NULL,
    "max" DOUBLE PRECISION,

    CONSTRAINT "tb_kawasan_industri_ref_range_pkey" PRIMARY KEY ("id_kawasan_industri_ref_range")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_status" (
    "id_kawasan_industri_status" SERIAL NOT NULL,
    "id_kawasan_industri" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_kawasan_industri_status_pkey" PRIMARY KEY ("id_kawasan_industri_status")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_industri_tr" (
    "id_kawasan_industri_tr" SERIAL NOT NULL,
    "id_kawasan_industri" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_kawasan_industri_tr_pkey" PRIMARY KEY ("id_kawasan_industri_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_user" (
    "id_kawasan_user" SERIAL NOT NULL,
    "id_kawasan_industri" INTEGER NOT NULL,
    "id_user" INTEGER NOT NULL,

    CONSTRAINT "tb_kawasan_user_pkey" PRIMARY KEY ("id_kawasan_user")
);

-- CreateTable
CREATE TABLE "public"."tb_kawasan_user_hapus" (
    "id_kawasan_user_hapus" SERIAL NOT NULL,
    "id_kawasan_user" INTEGER,
    "id_kawasan_industri" INTEGER,
    "id_user" INTEGER,

    CONSTRAINT "tb_kawasan_user_hapus_pkey" PRIMARY KEY ("id_kawasan_user_hapus")
);

-- CreateTable
CREATE TABLE "public"."tb_negara" (
    "id_negara" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "ibu_kota" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_negara_pkey" PRIMARY KEY ("id_negara")
);

-- CreateTable
CREATE TABLE "public"."tb_negara_tr" (
    "id_negara_tr" SERIAL NOT NULL,
    "id_negara" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "ibu_kota" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_negara_tr_pkey" PRIMARY KEY ("id_negara_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_nswi_jenis" (
    "id_nswi_jenis" SERIAL NOT NULL,
    "nama" VARCHAR(50),
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_nswi_jenis_pkey" PRIMARY KEY ("id_nswi_jenis")
);

-- CreateTable
CREATE TABLE "public"."tb_nswi_jenis_data" (
    "id_nswi_jenis_data" SERIAL NOT NULL,
    "nama" VARCHAR(50),
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_nswi_jenis_data_pkey" PRIMARY KEY ("id_nswi_jenis_data")
);

-- CreateTable
CREATE TABLE "public"."tb_nswi_jenis_data_tr" (
    "id_nswi_jenis_data_tr" SERIAL NOT NULL,
    "id_nswi_jenis_data" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50),
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_nswi_jenis_data_tr_pkey" PRIMARY KEY ("id_nswi_jenis_data_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_nswi_jenis_tr" (
    "id_nswi_jenis_tr" SERIAL NOT NULL,
    "id_nswi_jenis" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50),
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_nswi_jenis_tr_pkey" PRIMARY KEY ("id_nswi_jenis_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_nswi_kabkot" (
    "id_nswi_kabkot" SERIAL NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL,
    "id_sektor" INTEGER NOT NULL,
    "id_jenis" INTEGER NOT NULL,
    "id_jenis_data" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "jumlah_proyek" INTEGER NOT NULL,
    "jumlah_investasi" DOUBLE PRECISION NOT NULL,
    "status" INTEGER NOT NULL,

    CONSTRAINT "tb_nswi_kabkot_pkey" PRIMARY KEY ("id_nswi_kabkot")
);

-- CreateTable
CREATE TABLE "public"."tb_nswi_kabkot_status" (
    "id_nswi_kabkot" SERIAL NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_nswi_kabkot_status_pkey" PRIMARY KEY ("id_nswi_kabkot")
);

-- CreateTable
CREATE TABLE "public"."tb_nswi_provinsi" (
    "id_nswi_provinsi" SERIAL NOT NULL,
    "id_adm_provinsi" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL,
    "id_sektor" INTEGER NOT NULL,
    "id_jenis" INTEGER NOT NULL,
    "id_jenis_data" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "jumlah_proyek" INTEGER NOT NULL,
    "jumlah_investasi" DOUBLE PRECISION NOT NULL,
    "status" INTEGER NOT NULL,

    CONSTRAINT "tb_nswi_provinsi_pkey" PRIMARY KEY ("id_nswi_provinsi")
);

-- CreateTable
CREATE TABLE "public"."tb_nswi_provinsi_status" (
    "id_nswi_provinsi_status" SERIAL NOT NULL,
    "id_nswi_provinsi" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_nswi_provinsi_status_pkey" PRIMARY KEY ("id_nswi_provinsi_status")
);

-- CreateTable
CREATE TABLE "public"."tb_nswi_sektor" (
    "id_nswi_sektor" SERIAL NOT NULL,
    "nama" VARCHAR(255),
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_nswi_sektor_pkey" PRIMARY KEY ("id_nswi_sektor")
);

-- CreateTable
CREATE TABLE "public"."tb_nswi_sektor_tr" (
    "id_nswi_sektor_tr" SERIAL NOT NULL,
    "id_nswi_sektor" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255),
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_nswi_sektor_tr_pkey" PRIMARY KEY ("id_nswi_sektor_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_pelabuhan_fungsi" (
    "id_pelabuhan_fungsi" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_pelabuhan_fungsi_pkey" PRIMARY KEY ("id_pelabuhan_fungsi")
);

-- CreateTable
CREATE TABLE "public"."tb_pelabuhan_fungsi_tr" (
    "id_pelabuhan_fungsi_tr" SERIAL NOT NULL,
    "id_pelabuhan_fungsi" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_pelabuhan_fungsi_tr_pkey" PRIMARY KEY ("id_pelabuhan_fungsi_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_pelabuhan_kelas" (
    "id_pelabuhan_kelas" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_pelabuhan_kelas_pkey" PRIMARY KEY ("id_pelabuhan_kelas")
);

-- CreateTable
CREATE TABLE "public"."tb_pelabuhan_kelas_tr" (
    "id_pelabuhan_kelas_tr" SERIAL NOT NULL,
    "id_pelabuhan_kelas" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_pelabuhan_kelas_tr_pkey" PRIMARY KEY ("id_pelabuhan_kelas_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_pelabuhan_status" (
    "id_pelabuhan_status" SERIAL NOT NULL,
    "id_pelabuhan" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_pelabuhan_status_pkey" PRIMARY KEY ("id_pelabuhan_status")
);

-- CreateTable
CREATE TABLE "public"."tb_pelabuhan_tr" (
    "id_pelabuhan_tr" SERIAL NOT NULL,
    "id_pelabuhan" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_pelabuhan_tr_pkey" PRIMARY KEY ("id_pelabuhan_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_pendidikan_jenjang" (
    "id_pendidikan_jenjang" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_pendidikan_jenjang_pkey" PRIMARY KEY ("id_pendidikan_jenjang")
);

-- CreateTable
CREATE TABLE "public"."tb_pendidikan_jenjang_tr" (
    "id_pendidikan_jenjang_tr" SERIAL NOT NULL,
    "id_pendidikan_jenjang" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_pendidikan_jenjang_tr_pkey" PRIMARY KEY ("id_pendidikan_jenjang_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_pendidikan_kategori" (
    "id_pendidikan_kategori" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_pendidikan_kategori_pkey" PRIMARY KEY ("id_pendidikan_kategori")
);

-- CreateTable
CREATE TABLE "public"."tb_pendidikan_kategori_tr" (
    "id_pendidikan_kategori_tr" SERIAL NOT NULL,
    "id_pendidikan_kategori" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_pendidikan_kategori_tr_pkey" PRIMARY KEY ("id_pendidikan_kategori_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_pendidikan_status" (
    "id_pendidikan" SERIAL NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_pendidikan_status_pkey" PRIMARY KEY ("id_pendidikan")
);

-- CreateTable
CREATE TABLE "public"."tb_pendidikan_tr" (
    "id_pendidikan_tr" SERIAL NOT NULL,
    "id_pendidikan" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_pendidikan_tr_pkey" PRIMARY KEY ("id_pendidikan_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_perusahaan" (
    "id_perusahaan" SERIAL NOT NULL,
    "id_tipe" INTEGER NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "nib" VARCHAR(15) NOT NULL,
    "kd_pa" VARCHAR(255) NOT NULL,
    "kd_kmk" VARCHAR(255) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "status_verifikasi" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,

    CONSTRAINT "tb_perusahaan_pkey" PRIMARY KEY ("id_perusahaan")
);

-- CreateTable
CREATE TABLE "public"."tb_perusahaan_cabang_tr" (
    "id_perusahaan_cabang_tr" SERIAL NOT NULL,
    "id_perusahaan_cabang" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_perusahaan_cabang_tr_pkey" PRIMARY KEY ("id_perusahaan_cabang_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_perusahaan_file" (
    "id_perusahaan_file" SERIAL NOT NULL,
    "id_perusahaan" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_perusahaan_file_pkey" PRIMARY KEY ("id_perusahaan_file")
);

-- CreateTable
CREATE TABLE "public"."tb_perusahaan_kbli" (
    "id_perusahaan_kbli" SERIAL NOT NULL,
    "id_perusahaan" INTEGER NOT NULL,
    "id_kbli" INTEGER NOT NULL,

    CONSTRAINT "tb_perusahaan_kbli_pkey" PRIMARY KEY ("id_perusahaan_kbli")
);

-- CreateTable
CREATE TABLE "public"."tb_perusahaan_kbli_produk" (
    "id_perusahaan_kbli_produk" SERIAL NOT NULL,
    "id_perusahaan_kbli" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_perusahaan_kbli_produk_pkey" PRIMARY KEY ("id_perusahaan_kbli_produk")
);

-- CreateTable
CREATE TABLE "public"."tb_perusahaan_kbli_produk_file" (
    "id_perusahaan_kbli_produk_file" SERIAL NOT NULL,
    "tb_perusahaan_kbli_produk" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_perusahaan_kbli_produk_file_pkey" PRIMARY KEY ("id_perusahaan_kbli_produk_file")
);

-- CreateTable
CREATE TABLE "public"."tb_perusahaan_kbli_produk_tr" (
    "id_perusahaan_kbli_produk_tr" SERIAL NOT NULL,
    "pk_perusahaan_kbli_produk" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_perusahaan_kbli_produk_tr_pkey" PRIMARY KEY ("id_perusahaan_kbli_produk_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_perusahaan_kebutuhan_kbli" (
    "id_perusahaan_kebutuhan_kbli" SERIAL NOT NULL,
    "id_perusahaan" INTEGER NOT NULL,
    "id_kbli" INTEGER NOT NULL,

    CONSTRAINT "tb_perusahaan_kebutuhan_kbli_pkey" PRIMARY KEY ("id_perusahaan_kebutuhan_kbli")
);

-- CreateTable
CREATE TABLE "public"."tb_perusahaan_kebutuhan_produk" (
    "id_perusahaan_kebutuhan_produk" SERIAL NOT NULL,
    "id_perusahaan_lokasi" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_perusahaan_kebutuhan_produk_pkey" PRIMARY KEY ("id_perusahaan_kebutuhan_produk")
);

-- CreateTable
CREATE TABLE "public"."tb_perusahaan_kebutuhan_produk_tr" (
    "id_perusahaan_kebutuhan_produk_tr" SERIAL NOT NULL,
    "id_perusahaan_kebutuhan_produk" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_perusahaan_kebutuhan_produk_tr_pkey" PRIMARY KEY ("id_perusahaan_kebutuhan_produk_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_perusahaan_status" (
    "id_perusahaan_status" SERIAL NOT NULL,
    "id_perusahaan" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_perusahaan_status_pkey" PRIMARY KEY ("id_perusahaan_status")
);

-- CreateTable
CREATE TABLE "public"."tb_perusahaan_tipe" (
    "id_perusahaan_tipe" SERIAL NOT NULL,
    "kode" VARCHAR(10) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_perusahaan_tipe_pkey" PRIMARY KEY ("id_perusahaan_tipe")
);

-- CreateTable
CREATE TABLE "public"."tb_perusahaan_tipe_tr" (
    "id_perusahaan_tipe_tr" SERIAL NOT NULL,
    "id_perusahaan_tipe" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_perusahaan_tipe_tr_pkey" PRIMARY KEY ("id_perusahaan_tipe_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_ref_user_jabatan" (
    "id_jabatan" SERIAL NOT NULL,
    "id_jabatan_parent" INTEGER,
    "nama" VARCHAR(255) NOT NULL,
    "level" INTEGER NOT NULL,

    CONSTRAINT "tb_ref_user_jabatan_pkey" PRIMARY KEY ("id_jabatan")
);

-- CreateTable
CREATE TABLE "public"."tb_rumah_sakit_jenis" (
    "id_rumah_sakit_jenis" SERIAL NOT NULL,
    "nama" VARCHAR(50),

    CONSTRAINT "tb_rumah_sakit_jenis_pkey" PRIMARY KEY ("id_rumah_sakit_jenis")
);

-- CreateTable
CREATE TABLE "public"."tb_rumah_sakit_jenis_tr" (
    "id_rumah_sakit_jenis_tr" SERIAL NOT NULL,
    "id_rumah_sakit_jenis" INTEGER,
    "kd_bahasa" VARCHAR(2),
    "nama" VARCHAR(50),

    CONSTRAINT "tb_rumah_sakit_jenis_tr_pkey" PRIMARY KEY ("id_rumah_sakit_jenis_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_rumah_sakit_kategori" (
    "id_rumah_sakit_kategori" SERIAL NOT NULL,
    "nama" VARCHAR(50),

    CONSTRAINT "tb_rumah_sakit_kategori_pkey" PRIMARY KEY ("id_rumah_sakit_kategori")
);

-- CreateTable
CREATE TABLE "public"."tb_rumah_sakit_kategori_tr" (
    "id_rumah_sakit_kategori_tr" SERIAL NOT NULL,
    "id_rumah_sakit_kategori" INTEGER,
    "kd_bahasa" VARCHAR(2),
    "nama" VARCHAR(50),

    CONSTRAINT "tb_rumah_sakit_kategori_tr_pkey" PRIMARY KEY ("id_rumah_sakit_kategori_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_rumah_sakit_status" (
    "id_rumah_sakit_status" SERIAL NOT NULL,
    "id_rumah_sakit" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER,
    "keterangan" TEXT,
    "created_by" INTEGER,
    "created_date" TIMESTAMP(6),
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_rumah_sakit_status_pkey" PRIMARY KEY ("id_rumah_sakit_status")
);

-- CreateTable
CREATE TABLE "public"."tb_rumah_sakit_tr" (
    "id_rumah_sakit_tr" SERIAL NOT NULL,
    "id_rumah_sakit" INTEGER,
    "kd_bahasa" VARCHAR(2),
    "nama" VARCHAR(255),
    "keterangan" TEXT,

    CONSTRAINT "tb_rumah_sakit_tr_pkey" PRIMARY KEY ("id_rumah_sakit_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_send_query" (
    "id_send_query" SERIAL NOT NULL,
    "user_type" VARCHAR(255) NOT NULL,
    "sector" VARCHAR(255) NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "email" VARCHAR(255) NOT NULL,
    "country" VARCHAR(255) NOT NULL,
    "query_type" VARCHAR(255) NOT NULL,
    "query" TEXT NOT NULL,
    "ip_pengunjung" VARCHAR(15) NOT NULL,
    "created_time" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "created_by" VARCHAR(255),
    "modified_time" TIMESTAMP(6),

    CONSTRAINT "tb_send_query_pkey" PRIMARY KEY ("id_send_query")
);

-- CreateTable
CREATE TABLE "public"."tb_role" (
    "id_role" SERIAL NOT NULL,
    "role_name" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_role_pkey" PRIMARY KEY ("id_role")
);

-- CreateTable
CREATE TABLE "public"."tb_role_appl_task" (
    "id_role" INTEGER NOT NULL,
    "id_appl_task" BIGINT NOT NULL,
    "can_read" SMALLINT NOT NULL DEFAULT 1,
    "can_edit" SMALLINT NOT NULL DEFAULT 1,
    "can_delete" SMALLINT NOT NULL DEFAULT 1,
    "can_approve" SMALLINT NOT NULL DEFAULT 1,
    "can_add" SMALLINT DEFAULT 1,

    CONSTRAINT "tb_role_appl_task_pkey" PRIMARY KEY ("id_role")
);

-- CreateTable
CREATE TABLE "public"."tb_unduh_data" (
    "id_unduh_data" SERIAL NOT NULL,
    "id_unduh_data_tujuan" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "email" VARCHAR(255) NOT NULL,
    "no_telp" VARCHAR(255) NOT NULL,
    "asal_negara" VARCHAR(255) NOT NULL,
    "id_konten" INTEGER,
    "ip_pengunjung" VARCHAR(255) NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "id_jenis_konten" VARCHAR(255),
    "status_minat" BOOLEAN,

    CONSTRAINT "tb_unduh_data_pkey" PRIMARY KEY ("id_unduh_data")
);

-- CreateTable
CREATE TABLE "public"."tb_unduh_data_keperluan" (
    "id_unduh_data_keperluan" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_unduh_data_keperluan_pkey" PRIMARY KEY ("id_unduh_data_keperluan")
);

-- CreateTable
CREATE TABLE "public"."tb_unduh_data_keperluan_tr" (
    "id_unduh_data_keperluan_tr" SERIAL NOT NULL,
    "id_unduh_data_keperluan" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_unduh_data_keperluan_tr_pkey" PRIMARY KEY ("id_unduh_data_keperluan_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_utilitas_kabkot" (
    "id_utilitas_kabkot" SERIAL NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "tahun" INTEGER,
    "keterangan" TEXT,
    "produksi_air_bersih" DOUBLE PRECISION,
    "daya_terpasang" DOUBLE PRECISION,
    "jumlah_bts" DOUBLE PRECISION,
    "panjang_jalan" DOUBLE PRECISION,
    "status" INTEGER,

    CONSTRAINT "tb_utilitas_kabkot_pkey" PRIMARY KEY ("id_utilitas_kabkot")
);

-- CreateTable
CREATE TABLE "public"."tb_utilitas_kabkot_status" (
    "id_utilitas_kabkot_status" SERIAL NOT NULL,
    "id_utilitas_kabkot" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_utilitas_kabkot_status_pkey" PRIMARY KEY ("id_utilitas_kabkot_status")
);

-- CreateTable
CREATE TABLE "public"."tb_utilitas_kabkot_tr" (
    "id_utilitas_kabkot_tr" SERIAL NOT NULL,
    "id_utilitas_kabkot" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "keterangan" TEXT,

    CONSTRAINT "tb_utilitas_kabkot_tr_pkey" PRIMARY KEY ("id_utilitas_kabkot_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_utilitas_provinsi" (
    "id_utilitas_provinsi" SERIAL NOT NULL,
    "id_adm_provinsi" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "tahun" INTEGER,
    "keterangan" TEXT,
    "produksi_air_bersih" DOUBLE PRECISION,
    "daya_terpasang" DOUBLE PRECISION,
    "jumlah_bts" DOUBLE PRECISION,
    "panjang_jalan" DOUBLE PRECISION,
    "status" INTEGER,

    CONSTRAINT "tb_utilitas_provinsi_pkey" PRIMARY KEY ("id_utilitas_provinsi")
);

-- CreateTable
CREATE TABLE "public"."tb_utilitas_provinsi_status" (
    "id_utilitas_provinsi_status" SERIAL NOT NULL,
    "id_utilitas_provinsi" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_utilitas_provinsi_status_pkey" PRIMARY KEY ("id_utilitas_provinsi_status")
);

-- CreateTable
CREATE TABLE "public"."tb_utilitas_provinsi_tr" (
    "id_utilitas_provinsi_tr" SERIAL NOT NULL,
    "id_utilitas_provinsi" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "keterangan" TEXT,

    CONSTRAINT "tb_utilitas_provinsi_tr_pkey" PRIMARY KEY ("id_utilitas_provinsi_tr")
);

-- CreateTable
CREATE TABLE "public"."z_tb_komoditi" (
    "id_komoditi" SERIAL NOT NULL,
    "id_sektor" INTEGER,
    "nama" VARCHAR(50),
    "keterangan" TEXT,
    "warna" VARCHAR(7),
    "is_internal" BOOLEAN,
    "url_service" VARCHAR(255),

    CONSTRAINT "z_tb_komoditi_pkey" PRIMARY KEY ("id_komoditi")
);

-- CreateTable
CREATE TABLE "public"."z_tb_komoditi_kabkot" (
    "id_komoditi_kabkot" SERIAL NOT NULL,
    "id_komoditi" INTEGER,

    CONSTRAINT "z_tb_komoditi_kabkot_pkey" PRIMARY KEY ("id_komoditi_kabkot")
);

-- CreateTable
CREATE TABLE "public"."z_tb_komoditi_provinsi" (
    "id_komoditi_provinsi" SERIAL NOT NULL,
    "id_komoditi" INTEGER,

    CONSTRAINT "z_tb_komoditi_provinsi_pkey" PRIMARY KEY ("id_komoditi_provinsi")
);

-- CreateTable
CREATE TABLE "public"."z_tb_komoditi_sektor" (
    "id_komoditi_sektor" SERIAL NOT NULL,
    "nama" VARCHAR(50),
    "keterangan" TEXT,

    CONSTRAINT "z_tb_komoditi_sektor_pkey" PRIMARY KEY ("id_komoditi_sektor")
);

-- CreateTable
CREATE TABLE "public"."z_tb_komoditi_sektor_tr" (
    "id_komoditi_sektor_tr" SERIAL NOT NULL,
    "id_komoditi_sektor" INTEGER,
    "kd_bahasa" VARCHAR(2),
    "nama" VARCHAR(50),
    "keterangan" TEXT,

    CONSTRAINT "z_tb_komoditi_sektor_tr_pkey" PRIMARY KEY ("id_komoditi_sektor_tr")
);

-- CreateTable
CREATE TABLE "public"."z_tb_komoditi_tr" (
    "id_komoditi_tr" SERIAL NOT NULL,
    "id_komoditi" INTEGER,
    "kd_bahasa" VARCHAR(2),
    "nama" VARCHAR(50),
    "keterangan" TEXT,
    "warna" VARCHAR(7),

    CONSTRAINT "z_tb_komoditi_tr_pkey" PRIMARY KEY ("id_komoditi_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_kode_bahasa" (
    "id_kd_bahasa" SERIAL NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "deskripsi" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_kode_bahasa_pkey" PRIMARY KEY ("id_kd_bahasa")
);
