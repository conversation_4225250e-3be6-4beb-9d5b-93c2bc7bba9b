-- CreateTable
CREATE TABLE "public"."z_tb_komoditi_daerah" (
    "id_komoditi_daerah" SERIAL NOT NULL,
    "id_komoditi_nasional" INTEGER,
    "id_sub_sektor_daerah" INTEGER,
    "sentra_produksi" VARCHAR(500),

    CONSTRAINT "z_tb_komoditi_daerah_pkey" PRIMARY KEY ("id_komoditi_daerah")
);

-- CreateTable
CREATE TABLE "public"."z_tb_komoditi_daerah_value_detail" (
    "id_komoditi_daerah_value_detail" SERIAL NOT NULL,
    "id_komoditi_daerah" INTEGER NOT NULL,
    "id_komoditi_nasional_value" INTEGER,
    "tahun" INTEGER,
    "numeric_value" DOUBLE PRECISION,
    "string_value" VARCHAR(255),
    "date_value" TIMESTAMP,

    CONSTRAINT "z_tb_komoditi_daerah_value_detail_pkey" PRIMARY KEY ("id_komoditi_daerah_value_detail")
);
