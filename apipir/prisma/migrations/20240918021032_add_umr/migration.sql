-- AlterTable
ALTER TABLE "public"."tb_adm_kabkot_tr" ALTER COLUMN "id_adm_kabkot_tr" DROP NOT NULL;

-- CreateTable
CREATE TABLE "public"."tb_umr_kabkot" (
    "id_umr_kabkot" SERIAL NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "nilai" DOUBLE PRECISION NOT NULL,
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_umr_kabkot_status" (
    "id_umr_kabkot" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_umr_provinsi" (
    "id_umr_provinsi" SERIAL NOT NULL,
    "id_adm_provinsi" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "nilai" DOUBLE PRECISION NOT NULL,
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_umr_provinsi_status" (
    "id_umr_provinsi" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);
