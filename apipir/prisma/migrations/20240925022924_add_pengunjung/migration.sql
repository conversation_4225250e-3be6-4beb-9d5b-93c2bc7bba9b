/*
  Warnings:

  - The primary key for the `tb_sektor_nasional_ref` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `id_sektor_nasonal_ref` on the `tb_sektor_nasional_ref` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_ref" DROP CONSTRAINT "tb_sektor_nasional_ref_pkey",
DROP COLUMN "id_sektor_nasonal_ref",
ADD COLUMN     "id_sektor_nasional_ref" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_nasional_ref_pkey" PRIMARY KEY ("id_sektor_nasional_ref");

-- CreateTable
CREATE TABLE "public"."tb_halaman_pengunjung" (
    "id_halaman_pengunjung" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "total_pengunjung" INTEGER,

    CONSTRAINT "tb_halaman_pengunjung_pkey" PRIMARY KEY ("id_halaman_pengunjung")
);

-- CreateTable
CREATE TABLE "public"."tb_halaman_pengunjung_det" (
    "id_halaman_pengunjung_det" SERIAL NOT NULL,
    "id_halaman_pengunjung" INTEGER NOT NULL,
    "id_konten" INTEGER,
    "ip_pengunjung" VARCHAR(255) NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "tb_halaman_pengunjung_det_pkey" PRIMARY KEY ("id_halaman_pengunjung_det")
);

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_value" ADD CONSTRAINT "tb_sektor_nasional_value_id_sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional_ref"("id_sektor_nasional_ref") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_value_detail" ADD CONSTRAINT "tb_sektor_nasional_value_detail_id_sektor_nasional_value_fkey" FOREIGN KEY ("id_sektor_nasional_value") REFERENCES "public"."tb_sektor_nasional_value"("id_sektor_nasional_value") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_value_tr" ADD CONSTRAINT "tb_sektor_nasional_value_tr_id_sektor_nasional_value_fkey" FOREIGN KEY ("id_sektor_nasional_value") REFERENCES "public"."tb_sektor_nasional_value"("id_sektor_nasional_value") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_halaman_pengunjung_det" ADD CONSTRAINT "tb_halaman_pengunjung_det_id_halaman_pengunjung_fkey" FOREIGN KEY ("id_halaman_pengunjung") REFERENCES "public"."tb_halaman_pengunjung"("id_halaman_pengunjung") ON DELETE RESTRICT ON UPDATE CASCADE;
