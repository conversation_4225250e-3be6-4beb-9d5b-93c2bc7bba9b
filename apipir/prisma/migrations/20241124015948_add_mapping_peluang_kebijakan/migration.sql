-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_kebijakan_mapping" (
    "id_peluang_kabkot" INTEGER NOT NULL,
    "id_kebijakan" INTEGER NOT NULL,

    CONSTRAINT "tb_peluang_kabkot_kebijakan_mapping_pkey" PRIMARY KEY ("id_peluang_kabkot")
);

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_kabkot_kebijakan_mapping" ADD CONSTRAINT "tb_peluang_kabkot_kebijakan_mapping_id_peluang_kabkot_fkey" FOREIGN KEY ("id_peluang_kabkot") REFERENCES "public"."tb_peluang_kabkot"("id_peluang_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;
