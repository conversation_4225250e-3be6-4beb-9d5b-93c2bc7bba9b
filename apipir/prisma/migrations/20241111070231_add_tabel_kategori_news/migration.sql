-- AlterTable
ALTER TABLE "public"."tb_news" ADD COLUMN     "id_news_kategori" INTEGER;

-- CreateTable
CREATE TABLE "public"."tb_news_kategori" (
    "id_news_kategori" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_news_kategori_pkey" PRIMARY KEY ("id_news_kategori")
);

-- AddForeignKey
ALTER TABLE "public"."tb_news" ADD CONSTRAINT "tb_news_id_news_kategori_fkey" FOREIGN KEY ("id_news_kategori") REFERENCES "public"."tb_news_kategori"("id_news_kategori") ON DELETE CASCADE ON UPDATE CASCADE;
