-- AddForeignKey
ALTER TABLE "public"."tb_kawasan_industri" ADD CONSTRAINT "tb_kawasan_industri_id_bandara_terdekat_fkey" FOREIGN KEY ("id_bandara_terdekat") REFERENCES "public"."tb_bandara"("id_bandara") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_kawasan_industri_file" ADD CONSTRAINT "tb_kawasan_industri_file_id_kawasan_industri_fkey" FOREIGN KEY ("id_kawasan_industri") REFERENCES "public"."tb_kawasan_industri"("id_kawasan_industri") ON DELETE CASCADE ON UPDATE CASCADE;
