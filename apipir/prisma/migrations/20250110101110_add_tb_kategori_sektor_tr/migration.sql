-- CreateTable
CREATE TABLE "public"."tb_kategori_sektor_tr" (
    "id_kategori_sektor_tr" SERIAL NOT NULL,
    "id_kategori_sektor" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_kategori_sektor_tr_pkey" PRIMARY KEY ("id_kategori_sektor_tr")
);

-- AddForeignKey
ALTER TABLE "public"."tb_kategori_sektor_tr" ADD CONSTRAINT "tb_kategori_sektor_tr_id_kategori_sektor_fkey" FOREIGN KEY ("id_kategori_sektor") REFERENCES "public"."tb_kategori_sektor"("id_kategori_sektor") ON DELETE CASCADE ON UPDATE CASCADE;
