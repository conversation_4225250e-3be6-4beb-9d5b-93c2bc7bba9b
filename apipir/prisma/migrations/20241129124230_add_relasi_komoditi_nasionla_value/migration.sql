/*
  Warnings:

  - The primary key for the `tb_komoditi_nasional_value_tr` table will be changed. If it partially fails, the table could be left without primary key constraint.

*/
-- AlterTable
ALTER TABLE "public"."tb_komoditi_nasional_value_tr" DROP CONSTRAINT "tb_komoditi_nasional_value_tr_pkey",
ADD COLUMN     "id_komoditi_nasional_value_tr" SERIAL NOT NULL,
ALTER COLUMN "id_komoditi_nasional_value" DROP DEFAULT,
ADD CONSTRAINT "tb_komoditi_nasional_value_tr_pkey" PRIMARY KEY ("id_komoditi_nasional_value_tr");
DROP SEQUENCE "tb_komoditi_nasional_value_tr_id_komoditi_nasional_value_seq";

-- AddForeignKey
ALTER TABLE "public"."tb_komoditi_nasional_value_tr" ADD CONSTRAINT "tb_komoditi_nasional_value_tr_id_komoditi_nasional_value_fkey" FOREIGN KEY ("id_komoditi_nasional_value") REFERENCES "public"."tb_komoditi_nasional_value"("id_komoditi_nasional_value") ON DELETE CASCADE ON UPDATE CASCADE;
