/*
  Warnings:

  - The primary key for the `tb_kategori_infrastruktur` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `tb_kategori_infrastruktur` on the `tb_kategori_infrastruktur` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "public"."tb_bandara" ADD COLUMN     "id_kategori_infrastruktur" INTEGER;

-- AlterTable
ALTER TABLE "public"."tb_hotel" ADD COLUMN     "id_kategori_infrastruktur" INTEGER;

-- AlterTable
ALTER TABLE "public"."tb_kategori_infrastruktur" DROP CONSTRAINT "tb_kategori_infrastruktur_pkey",
DROP COLUMN "tb_kategori_infrastruktur",
ADD COLUMN     "id_kategori_infrastruktur" SERIAL NOT NULL,
ADD CONSTRAINT "tb_kategori_infrastruktur_pkey" PRIMARY KEY ("id_kategori_infrastruktur");

-- AlterTable
ALTER TABLE "public"."tb_pelabuhan" ADD COLUMN     "id_kategori_infrastruktur" INTEGER;

-- AlterTable
ALTER TABLE "public"."tb_pendidikan" ADD COLUMN     "id_kategori_infrastruktur" INTEGER;

-- AlterTable
ALTER TABLE "public"."tb_rumah_sakit" ADD COLUMN     "id_kategori_infrastruktur" INTEGER;

-- AddForeignKey
ALTER TABLE "public"."tb_bandara" ADD CONSTRAINT "tb_bandara_id_kategori_infrastruktur_fkey" FOREIGN KEY ("id_kategori_infrastruktur") REFERENCES "public"."tb_kategori_infrastruktur"("id_kategori_infrastruktur") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_hotel" ADD CONSTRAINT "tb_hotel_id_kategori_infrastruktur_fkey" FOREIGN KEY ("id_kategori_infrastruktur") REFERENCES "public"."tb_kategori_infrastruktur"("id_kategori_infrastruktur") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_pelabuhan" ADD CONSTRAINT "tb_pelabuhan_id_kategori_infrastruktur_fkey" FOREIGN KEY ("id_kategori_infrastruktur") REFERENCES "public"."tb_kategori_infrastruktur"("id_kategori_infrastruktur") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_pendidikan" ADD CONSTRAINT "tb_pendidikan_id_kategori_infrastruktur_fkey" FOREIGN KEY ("id_kategori_infrastruktur") REFERENCES "public"."tb_kategori_infrastruktur"("id_kategori_infrastruktur") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_rumah_sakit" ADD CONSTRAINT "tb_rumah_sakit_id_kategori_infrastruktur_fkey" FOREIGN KEY ("id_kategori_infrastruktur") REFERENCES "public"."tb_kategori_infrastruktur"("id_kategori_infrastruktur") ON DELETE CASCADE ON UPDATE CASCADE;
