-- AddForeignKey
ALTER TABLE "public"."tb_kawasan_industri_blok" ADD CONSTRAINT "tb_kawasan_industri_blok_id_kondisi_fkey" FOREIGN KEY ("id_kondisi") REFERENCES "public"."tb_kawasan_industri_blok_kondisi"("id_kawasan_industri_blok_kondisi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_kawasan_industri_blok" ADD CONSTRAINT "tb_kawasan_industri_blok_id_fungsi_fkey" FOREIGN KEY ("id_fungsi") REFERENCES "public"."tb_kawasan_industri_blok_fungsi"("id_kawasan_industri_blok_fungsi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_kawasan_industri_blok_tr" ADD CONSTRAINT "tb_kawasan_industri_blok_tr_id_kawasan_industri_blok_fkey" FOREIGN KEY ("id_kawasan_industri_blok") REFERENCES "public"."tb_kawasan_industri_blok"("id_kawasan_industri_blok") ON DELETE CASCADE ON UPDATE CASCADE;
