-- AddForeignKey
ALTER TABLE "public"."tb_unduh_data" ADD CONSTRAINT "tb_unduh_data_id_unduh_data_tujuan_fkey" FOREIGN KEY ("id_unduh_data_tujuan") REFERENCES "public"."tb_unduh_data_keperluan"("id_unduh_data_keperluan") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_unduh_data_keperluan_tr" ADD CONSTRAINT "tb_unduh_data_keperluan_tr_id_unduh_data_keperluan_fkey" FOREIGN KEY ("id_unduh_data_keperluan") REFERENCES "public"."tb_unduh_data_keperluan"("id_unduh_data_keperluan") ON DELETE CASCADE ON UPDATE CASCADE;
