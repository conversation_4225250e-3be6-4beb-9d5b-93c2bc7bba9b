-- AddForeignKey
ALTER TABLE "public"."tb_sektor_daerah_value_detail" ADD CONSTRAINT "tb_sektor_daerah_value_detail_id_sektor_daerah_fkey" FOREIGN KEY ("id_sektor_daerah") REFERENCES "public"."tb_sektor_daerah"("id_sektor_daerah") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_daerah_sumber_data" ADD CONSTRAINT "tb_sub_sektor_daerah_sumber_data_id_sub_sektor_daerah_fkey" FOREIGN KEY ("id_sub_sektor_daerah") REFERENCES "public"."tb_sub_sektor_daerah"("id_sub_sektor_daerah") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_daerah_sumber_data" ADD CONSTRAINT "tb_sub_sektor_daerah_sumber_data_id_sumber_data_fkey" FOREIGN KEY ("id_sumber_data") REFERENCES "public"."tb_sumber_data"("id_sumber_data") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_rumah_sakit" ADD CONSTRAINT "tb_rumah_sakit_id_jenis_fkey" FOREIGN KEY ("id_jenis") REFERENCES "public"."tb_rumah_sakit_jenis"("id_rumah_sakit_jenis") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_rumah_sakit" ADD CONSTRAINT "tb_rumah_sakit_id_kategori_fkey" FOREIGN KEY ("id_kategori") REFERENCES "public"."tb_rumah_sakit_kategori"("id_rumah_sakit_kategori") ON DELETE SET NULL ON UPDATE CASCADE;
