/*
  Warnings:

  - A unique constraint covering the columns `[id_komoditi,id_sub_sektor_daerah]` on the table `tb_komoditi_daerah` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[id_sektor_daerah,id_sub_sektor_nasional]` on the table `tb_sub_sektor_daerah` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateIndex
CREATE UNIQUE INDEX "tb_komoditi_daerah_id_komoditi_id_sub_sektor_daerah_key" ON "public"."tb_komoditi_daerah"("id_komoditi", "id_sub_sektor_daerah");

-- CreateIndex
CREATE UNIQUE INDEX "tb_sub_sektor_daerah_id_sektor_daerah_id_sub_sektor_nasiona_key" ON "public"."tb_sub_sektor_daerah"("id_sektor_daerah", "id_sub_sektor_nasional");

-- AddForeignKey
ALTER TABLE "public"."tb_adm_user" ADD CONSTRAINT "tb_adm_user_id_user_fkey" FOREIGN KEY ("id_user") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sub_sektor_daerah_file" ADD CONSTRAINT "tb_sub_sektor_daerah_file_id_sub_sektor_daerah_fkey" FOREIGN KEY ("id_sub_sektor_daerah") REFERENCES "public"."tb_sub_sektor_daerah"("id_sub_sektor_daerah") ON DELETE CASCADE ON UPDATE CASCADE;
