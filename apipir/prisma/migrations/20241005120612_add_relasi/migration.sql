-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_insentif_file" ADD CONSTRAINT "tb_sektor_nasional_insentif_file_id_sektor_nasional_insent_fkey" FOREIGN KEY ("id_sektor_nasional_insentif") REFERENCES "public"."tb_sektor_nasional_insentif"("id_sektor_nasional_insentif") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_insentif_tr" ADD CONSTRAINT "tb_sektor_nasional_insentif_tr_id_sektor_nasional_insentif_fkey" FOREIGN KEY ("id_sektor_nasional_insentif") REFERENCES "public"."tb_sektor_nasional_insentif"("id_sektor_nasional_insentif") ON DELETE RESTRICT ON UPDATE CASCADE;
