-- CreateTable
CREATE TABLE "public"."tb_komoditi" (
    "id_komoditi" INTEGER NOT NULL,
    "id_sektor" INTEGER NOT NULL,
    "nama" VARCHAR(50),
    "keterangan" TEXT NOT NULL,
    "warna" VARCHAR(7),
    "is_internal" BOOLEAN,
    "url_service" VARCHAR(255),

    CONSTRAINT "tb_komoditi_pkey" PRIMARY KEY ("id_komoditi")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_daerah" (
    "id_komoditi_daerah" INTEGER NOT NULL,
    "id_komoditi" INTEGER NOT NULL,
    "id_sub_sektor_daerah" INTEGER NOT NULL,
    "deskripsi_singkat" TEXT NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "file_icon" VARCHAR(255) NOT NULL,
    "sentra_produksi" VARCHAR(500),
    "status" INTEGER NOT NULL,

    CONSTRAINT "tb_komoditi_daerah_pkey" PRIMARY KEY ("id_komoditi_daerah")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_daerah_file" (
    "id_komoditi_daerah_file" INTEGER NOT NULL,
    "id_komoditi_daerah" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_komoditi_daerah_file_pkey" PRIMARY KEY ("id_komoditi_daerah_file")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_daerah_file_tr" (
    "id_komoditi_daerah_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_komoditi_daerah_file_tr_pkey" PRIMARY KEY ("id_komoditi_daerah_file")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_daerah_status" (
    "id_komoditi_daerah" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_komoditi_daerah_status_pkey" PRIMARY KEY ("id_komoditi_daerah")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_daerah_tr" (
    "id_komoditi_daerah" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "deskripsi_singkat" TEXT NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "file_icon" VARCHAR(255) NOT NULL,
    "sentra_produksi" VARCHAR(500),

    CONSTRAINT "tb_komoditi_daerah_tr_pkey" PRIMARY KEY ("id_komoditi_daerah")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_daerah_value" (
    "id_komoditi_daerah_value" INTEGER NOT NULL,
    "id_komoditi_daerah" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "satuan" VARCHAR(255) NOT NULL,
    "tipe" SMALLINT NOT NULL,

    CONSTRAINT "tb_komoditi_daerah_value_pkey" PRIMARY KEY ("id_komoditi_daerah_value")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_daerah_value_detail" (
    "id_komoditi_daerah_value" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "numeric_value" REAL,
    "string_value" VARCHAR(255),
    "date_value" TIMESTAMP(6),

    CONSTRAINT "tb_komoditi_daerah_value_detail_pkey" PRIMARY KEY ("id_komoditi_daerah_value")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_daerah_value_tr" (
    "id_komoditi_daerah_value" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "satuan" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_komoditi_daerah_value_tr_pkey" PRIMARY KEY ("id_komoditi_daerah_value")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_kabkot_status" (
    "id_komoditi_kabkot" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_komoditi_kabkot_status_pkey" PRIMARY KEY ("id_komoditi_kabkot")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_nasional" (
    "id_komoditi_nasional" INTEGER NOT NULL,
    "id_sub_sektor_nasional" INTEGER NOT NULL,
    "id_komoditi" INTEGER NOT NULL,
    "deskripsi_singkat" TEXT NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "file_icon" VARCHAR(255) NOT NULL,
    "status" INTEGER NOT NULL,

    CONSTRAINT "tb_komoditi_nasional_pkey" PRIMARY KEY ("id_komoditi_nasional")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_nasional_file" (
    "id_komoditi_nasional_file" INTEGER NOT NULL,
    "id_komoditi_nasional" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_komoditi_nasional_file_pkey" PRIMARY KEY ("id_komoditi_nasional_file")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_nasional_file_tr" (
    "id_komoditi_nasional_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_komoditi_nasional_file_tr_pkey" PRIMARY KEY ("id_komoditi_nasional_file")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_nasional_ref" (
    "id_komoditi" INTEGER NOT NULL,
    "id_sub_sektor" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_komoditi_nasional_ref_pkey" PRIMARY KEY ("id_komoditi")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_nasional_ref_tr" (
    "id_komoditi" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_komoditi_nasional_ref_tr_pkey" PRIMARY KEY ("id_komoditi")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_nasional_status" (
    "id_komoditi_nasional" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_komoditi_nasional_status_pkey" PRIMARY KEY ("id_komoditi_nasional")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_nasional_tr" (
    "id_komoditi_nasional" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "deskripsi_singkat" TEXT NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "file_icon" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_komoditi_nasional_tr_pkey" PRIMARY KEY ("id_komoditi_nasional")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_nasional_value" (
    "id_komoditi_nasional_value" INTEGER NOT NULL,
    "id_komoditi_nasional" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "satuan" VARCHAR(255) NOT NULL,
    "tipe" SMALLINT NOT NULL,

    CONSTRAINT "tb_komoditi_nasional_value_pkey" PRIMARY KEY ("id_komoditi_nasional_value")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_nasional_value_detail" (
    "id_komoditi_nasional_value" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "numeric_value" REAL,
    "string_value" VARCHAR(255),
    "date_value" TIMESTAMP(6),

    CONSTRAINT "tb_komoditi_nasional_value_detail_pkey" PRIMARY KEY ("id_komoditi_nasional_value")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_nasional_value_tr" (
    "id_komoditi_nasional_value" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "satuan" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_komoditi_nasional_value_tr_pkey" PRIMARY KEY ("id_komoditi_nasional_value")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_provinsi_status" (
    "id_komoditi_provinsi" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_komoditi_provinsi_status_pkey" PRIMARY KEY ("id_komoditi_provinsi")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_satuan" (
    "id_komoditi_satuan" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_komoditi_satuan_pkey" PRIMARY KEY ("id_komoditi_satuan")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_sektor" (
    "id_komoditi_sektor" INTEGER NOT NULL,
    "nama" VARCHAR(50),
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_komoditi_sektor_pkey" PRIMARY KEY ("id_komoditi_sektor")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_sektor_tr" (
    "id_komoditi_sektor_tr" INTEGER NOT NULL,
    "id_komoditi_sektor" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50),
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_komoditi_sektor_tr_pkey" PRIMARY KEY ("id_komoditi_sektor_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_komoditi_tr" (
    "id_komoditi_tr" INTEGER NOT NULL,
    "id_komoditi" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50),
    "keterangan" TEXT NOT NULL,
    "warna" VARCHAR(7),

    CONSTRAINT "tb_komoditi_tr_pkey" PRIMARY KEY ("id_komoditi_tr")
);
