-- CreateTable
CREATE TABLE "public"."tb_indikator_pic" (
    "id_indikator_pic" SERIAL NOT NULL,
    "nama_indikator" VARCHAR(500) NOT NULL,
    "nama" VARCHAR(500) NOT NULL,

    CONSTRAINT "tb_indikator_pic_pkey" PRIMARY KEY ("id_indikator_pic")
);

-- CreateTable
CREATE TABLE "public"."tb_mapping_indikator_pic" (
    "id_mapping_indikator_pic" SERIAL NOT NULL,
    "id_indikator_pic" INTEGER NOT NULL,
    "id_user" INTEGER NOT NULL,
    "kd_prov" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,

    CONSTRAINT "tb_mapping_indikator_pic_pkey" PRIMARY KEY ("id_mapping_indikator_pic")
);
