-- CreateTable
CREATE TABLE "public"."tb_kebijakan_tr" (
    "id_kebijakan_tr" SERIAL NOT NULL,
    "id_kebijakan" INTEGER NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "nama_file" VARCHAR(500) NOT NULL,
    "id_kategori" INTEGER,
    "deskripsi" VARCHAR(255),

    CONSTRAINT "tb_kebijakan_tr_pkey" PRIMARY KEY ("id_kebijakan_tr")
);

-- AddForeignKey
ALTER TABLE "public"."tb_kebijakan_tr" ADD CONSTRAINT "tb_kebijakan_tr_id_kebijakan_fkey" FOREIGN KEY ("id_kebijakan") REFERENCES "public"."tb_kebijakan"("id_kebijakan") ON DELETE CASCADE ON UPDATE CASCADE;
