/*
  Warnings:

  - The primary key for the `tb_adm_kabkot_alias` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_peluang_daerah_file_tr` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_peluang_daerah_kelayakan` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_peluang_daerah_kelayakan_detail` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_peluang_daerah_kontak` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_peluang_daerah_status` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_peluang_daerah_tr` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_peluang_kabkot` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_peluang_kabkot_insentif` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_peluang_provinsi_status` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_daerah_file_tr` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_daerah_insentif_file_tr` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_daerah_insentif_tr` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_daerah_pdrb_status` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_daerah_status` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_daerah_sumber_data` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_daerah_value_detail` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_nasional_file_tr` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_nasional_insentif_file_tr` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_nasional_insentif_tr` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_nasional_pdb` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_nasional_pdb_status` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_nasional_ref` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_nasional_ref_tr` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_nasional_status` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_nasional_sumber_data` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_nasional_tr` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_nasional_value_detail` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_sektor_nasional_value_tr` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_umr_kabkot_status` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tb_umr_provinsi_status` table will be changed. If it partially fails, the table could be left without primary key constraint.

*/
-- AlterTable
ALTER TABLE "public"."tb_adm_kabkot_alias" DROP CONSTRAINT "tb_adm_kabkot_alias_pkey",
ADD COLUMN     "id_adm_kabkot_alias" SERIAL NOT NULL,
ADD CONSTRAINT "tb_adm_kabkot_alias_pkey" PRIMARY KEY ("id_adm_kabkot_alias");

-- AlterTable
CREATE SEQUENCE "public".tb_adm_wilayah_id_adm_wilayah_seq;
ALTER TABLE "public"."tb_adm_wilayah" ALTER COLUMN "id_adm_wilayah" SET DEFAULT nextval('"public".tb_adm_wilayah_id_adm_wilayah_seq');
ALTER SEQUENCE "public".tb_adm_wilayah_id_adm_wilayah_seq OWNED BY "public"."tb_adm_wilayah"."id_adm_wilayah";

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_file_tr" DROP CONSTRAINT "tb_peluang_daerah_file_tr_pkey",
ADD COLUMN     "id_peluang_daerah_file_tr" SERIAL NOT NULL,
ADD CONSTRAINT "tb_peluang_daerah_file_tr_pkey" PRIMARY KEY ("id_peluang_daerah_file_tr");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_kelayakan" DROP CONSTRAINT "tb_peluang_daerah_kelayakan_pkey",
ADD COLUMN     "id_peluang_daerah_kelayakan" SERIAL NOT NULL,
ADD CONSTRAINT "tb_peluang_daerah_kelayakan_pkey" PRIMARY KEY ("id_peluang_daerah_kelayakan");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_kelayakan_detail" DROP CONSTRAINT "tb_peluang_daerah_kelayakan_detail_pkey",
ADD COLUMN     "id_peluang_daerah_kelayakan_detai" SERIAL NOT NULL,
ADD CONSTRAINT "tb_peluang_daerah_kelayakan_detail_pkey" PRIMARY KEY ("id_peluang_daerah_kelayakan_detai");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_kontak" DROP CONSTRAINT "tb_peluang_daerah_kontak_pkey",
ADD COLUMN     "id_peluang_daerah_kontak" SERIAL NOT NULL,
ADD CONSTRAINT "tb_peluang_daerah_kontak_pkey" PRIMARY KEY ("id_peluang_daerah_kontak");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_status" DROP CONSTRAINT "tb_peluang_daerah_status_pkey",
ADD COLUMN     "id_peluang_daerah_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_peluang_daerah_status_pkey" PRIMARY KEY ("id_peluang_daerah_status");

-- AlterTable
ALTER TABLE "public"."tb_peluang_daerah_tr" DROP CONSTRAINT "tb_peluang_daerah_tr_pkey",
ADD COLUMN     "id_peluang_daerah_tr" SERIAL NOT NULL,
ADD CONSTRAINT "tb_peluang_daerah_tr_pkey" PRIMARY KEY ("id_peluang_daerah_tr");

-- AlterTable
ALTER TABLE "public"."tb_peluang_kabkot" DROP CONSTRAINT "tb_peluang_kabkot_pkey",
ADD COLUMN     "id_peluang_kabkot" SERIAL NOT NULL,
ADD CONSTRAINT "tb_peluang_kabkot_pkey" PRIMARY KEY ("id_peluang_kabkot");

-- AlterTable
CREATE SEQUENCE "public".tb_peluang_kabkot_file_tr_id_peluang_kabkot_file_tr_seq;
ALTER TABLE "public"."tb_peluang_kabkot_file_tr" ALTER COLUMN "id_peluang_kabkot_file_tr" SET DEFAULT nextval('"public".tb_peluang_kabkot_file_tr_id_peluang_kabkot_file_tr_seq');
ALTER SEQUENCE "public".tb_peluang_kabkot_file_tr_id_peluang_kabkot_file_tr_seq OWNED BY "public"."tb_peluang_kabkot_file_tr"."id_peluang_kabkot_file_tr";

-- AlterTable
ALTER TABLE "public"."tb_peluang_kabkot_insentif" DROP CONSTRAINT "tb_peluang_kabkot_insentif_pkey",
ADD COLUMN     "id_peluang_kabkot_insentif" SERIAL NOT NULL,
ADD CONSTRAINT "tb_peluang_kabkot_insentif_pkey" PRIMARY KEY ("id_peluang_kabkot_insentif");

-- AlterTable
CREATE SEQUENCE "public".tb_peluang_kabkot_kontak_id_peluang_kabkot_seq;
ALTER TABLE "public"."tb_peluang_kabkot_kontak" ALTER COLUMN "id_peluang_kabkot" SET DEFAULT nextval('"public".tb_peluang_kabkot_kontak_id_peluang_kabkot_seq');
ALTER SEQUENCE "public".tb_peluang_kabkot_kontak_id_peluang_kabkot_seq OWNED BY "public"."tb_peluang_kabkot_kontak"."id_peluang_kabkot";

-- AlterTable
CREATE SEQUENCE "public".tb_peluang_kabkot_likes_counter_id_likes_seq;
ALTER TABLE "public"."tb_peluang_kabkot_likes_counter" ALTER COLUMN "id_likes" SET DEFAULT nextval('"public".tb_peluang_kabkot_likes_counter_id_likes_seq');
ALTER SEQUENCE "public".tb_peluang_kabkot_likes_counter_id_likes_seq OWNED BY "public"."tb_peluang_kabkot_likes_counter"."id_likes";

-- AlterTable
CREATE SEQUENCE "public".tb_peluang_kabkot_status_id_peluang_kabkot_seq;
ALTER TABLE "public"."tb_peluang_kabkot_status" ALTER COLUMN "id_peluang_kabkot" SET DEFAULT nextval('"public".tb_peluang_kabkot_status_id_peluang_kabkot_seq');
ALTER SEQUENCE "public".tb_peluang_kabkot_status_id_peluang_kabkot_seq OWNED BY "public"."tb_peluang_kabkot_status"."id_peluang_kabkot";

-- AlterTable
ALTER TABLE "public"."tb_peluang_provinsi_status" DROP CONSTRAINT "tb_peluang_provinsi_status_pkey",
ADD COLUMN     "id_peluang_provinsi_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_peluang_provinsi_status_pkey" PRIMARY KEY ("id_peluang_provinsi_status");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_file_tr" DROP CONSTRAINT "tb_sektor_daerah_file_tr_pkey",
ADD COLUMN     "id_sektor_daerah_file_tr" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_daerah_file_tr_pkey" PRIMARY KEY ("id_sektor_daerah_file_tr");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_insentif_file_tr" DROP CONSTRAINT "tb_sektor_daerah_insentif_file_tr_pkey",
ADD COLUMN     "id_sektor_daerah_insentif_file_tr" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_daerah_insentif_file_tr_pkey" PRIMARY KEY ("id_sektor_daerah_insentif_file_tr");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_insentif_tr" DROP CONSTRAINT "tb_sektor_daerah_insentif_tr_pkey",
ADD COLUMN     "id_sektor_daerah_insentif_tr" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_daerah_insentif_tr_pkey" PRIMARY KEY ("id_sektor_daerah_insentif_tr");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_pdrb_status" DROP CONSTRAINT "tb_sektor_daerah_pdrb_status_pkey",
ADD COLUMN     "id_sektor_daerah_pdrb_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_daerah_pdrb_status_pkey" PRIMARY KEY ("id_sektor_daerah_pdrb_status");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_status" DROP CONSTRAINT "tb_sektor_daerah_status_pkey",
ADD COLUMN     "id_sektor_daerah_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_daerah_status_pkey" PRIMARY KEY ("id_sektor_daerah_status");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_sumber_data" DROP CONSTRAINT "tb_sektor_daerah_sumber_data_pkey",
ADD COLUMN     "id_sektor_daerah_sumber_data" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_daerah_sumber_data_pkey" PRIMARY KEY ("id_sektor_daerah_sumber_data");

-- AlterTable
ALTER TABLE "public"."tb_sektor_daerah_value_detail" DROP CONSTRAINT "tb_sektor_daerah_value_detail_pkey",
ADD COLUMN     "id_sektor_daerah_value_detail" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_daerah_value_detail_pkey" PRIMARY KEY ("id_sektor_daerah_value_detail");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_file_tr" DROP CONSTRAINT "tb_sektor_nasional_file_tr_pkey",
ADD COLUMN     "id_sektor_nasional_file_tr" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_nasional_file_tr_pkey" PRIMARY KEY ("id_sektor_nasional_file_tr");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_insentif_file_tr" DROP CONSTRAINT "tb_sektor_nasional_insentif_file_tr_pkey",
ADD COLUMN     "id_sektor_nasional_insentif_file_tr" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_nasional_insentif_file_tr_pkey" PRIMARY KEY ("id_sektor_nasional_insentif_file_tr");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_insentif_tr" DROP CONSTRAINT "tb_sektor_nasional_insentif_tr_pkey",
ADD COLUMN     "id_sektor_nasional_insentif_tr" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_nasional_insentif_tr_pkey" PRIMARY KEY ("id_sektor_nasional_insentif_tr");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_pdb" DROP CONSTRAINT "tb_sektor_nasional_pdb_pkey",
ADD COLUMN     "id_sektor_nasional_pdb" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_nasional_pdb_pkey" PRIMARY KEY ("id_sektor_nasional_pdb");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_pdb_status" DROP CONSTRAINT "tb_sektor_nasional_pdb_status_pkey",
ADD COLUMN     "id_sektor_nasional_pdb_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_nasional_pdb_status_pkey" PRIMARY KEY ("id_sektor_nasional_pdb_status");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_ref" DROP CONSTRAINT "tb_sektor_nasional_ref_pkey",
ADD COLUMN     "id_sektor_nasonal_ref" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_nasional_ref_pkey" PRIMARY KEY ("id_sektor_nasonal_ref");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_ref_tr" DROP CONSTRAINT "tb_sektor_nasional_ref_tr_pkey",
ADD COLUMN     "id_sektor_nasonal_ref_tr" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_nasional_ref_tr_pkey" PRIMARY KEY ("id_sektor_nasonal_ref_tr");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_status" DROP CONSTRAINT "tb_sektor_nasional_status_pkey",
ADD COLUMN     "id_sektor_nasional_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_nasional_status_pkey" PRIMARY KEY ("id_sektor_nasional_status");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_sumber_data" DROP CONSTRAINT "tb_sektor_nasional_sumber_data_pkey",
ADD COLUMN     "id_sektor_nasional_sumber_data" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_nasional_sumber_data_pkey" PRIMARY KEY ("id_sektor_nasional_sumber_data");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_tr" DROP CONSTRAINT "tb_sektor_nasional_tr_pkey",
ADD COLUMN     "id_sektor_nasional_tr" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_nasional_tr_pkey" PRIMARY KEY ("id_sektor_nasional_tr");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_value_detail" DROP CONSTRAINT "tb_sektor_nasional_value_detail_pkey",
ADD COLUMN     "id_sektor_nasional_value_detail" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_nasional_value_detail_pkey" PRIMARY KEY ("id_sektor_nasional_value_detail");

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_value_tr" DROP CONSTRAINT "tb_sektor_nasional_value_tr_pkey",
ADD COLUMN     "id_sektor_nasional_value_tr" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_nasional_value_tr_pkey" PRIMARY KEY ("id_sektor_nasional_value_tr");

-- AlterTable
ALTER TABLE "public"."tb_umr_kabkot_status" DROP CONSTRAINT "tb_umr_kabkot_status_pkey",
ADD COLUMN     "id_umr_kabkot_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_umr_kabkot_status_pkey" PRIMARY KEY ("id_umr_kabkot_status");

-- AlterTable
ALTER TABLE "public"."tb_umr_provinsi_status" DROP CONSTRAINT "tb_umr_provinsi_status_pkey",
ADD COLUMN     "id_umr_provinsi_status" SERIAL NOT NULL,
ADD CONSTRAINT "tb_umr_provinsi_status_pkey" PRIMARY KEY ("id_umr_provinsi_status");
