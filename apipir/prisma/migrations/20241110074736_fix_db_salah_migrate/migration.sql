/*
  Warnings:

  - You are about to drop the column `id_peluang_kabkot` on the `tb_kebijakan` table. All the data in the column will be lost.
  - The primary key for the `tb_sektor_nasional_ref_tr` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `id_sektor_nasonal_ref_tr` on the `tb_sektor_nasional_ref_tr` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."tb_kebijakan" DROP CONSTRAINT "tb_kebijakan_id_peluang_kabkot_fkey";

-- AlterTable
ALTER TABLE "public"."tb_kebijakan" DROP COLUMN "id_peluang_kabkot",
ADD COLUMN     "id_kategori" INTEGER;

-- AlterTable
ALTER TABLE "public"."tb_sektor_nasional_ref_tr" DROP CONSTRAINT "tb_sektor_nasional_ref_tr_pkey",
DROP COLUMN "id_sektor_nasonal_ref_tr",
ADD COLUMN     "id_sektor_nasional_ref_tr" SERIAL NOT NULL,
ADD CONSTRAINT "tb_sektor_nasional_ref_tr_pkey" PRIMARY KEY ("id_sektor_nasional_ref_tr");

-- AlterTable
ALTER TABLE "public"."tb_umkm" ADD COLUMN     "id_peluang_kabkot" INTEGER,
ADD COLUMN     "id_peluang_sektor" INTEGER,
ADD COLUMN     "id_umkm_jenis" INTEGER;

-- CreateTable
CREATE TABLE "public"."tb_adm_provinsi_kantor" (
    "id_adm_provinsi_kantor" SERIAL NOT NULL,
    "jenis" VARCHAR(20) NOT NULL,
    "id_adm_provinsi" INTEGER NOT NULL,
    "nama_pic" VARCHAR(255) NOT NULL,
    "file_foto_pic" VARCHAR(255) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "alamat" VARCHAR(255) NOT NULL,
    "no_telp" VARCHAR(255) NOT NULL,
    "no_fax" VARCHAR(255) NOT NULL,
    "url_web" VARCHAR(255) NOT NULL,
    "status" INTEGER NOT NULL,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_adm_provinsi_kantor_pkey" PRIMARY KEY ("id_adm_provinsi_kantor")
);

-- CreateTable
CREATE TABLE "public"."tb_adm_kabkot_kantor" (
    "id_adm_kabkot_kantor" SERIAL NOT NULL,
    "jenis" VARCHAR(5) NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "nama_pic" VARCHAR(255) NOT NULL,
    "file_foto_pic" VARCHAR(255) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "alamat" VARCHAR(255) NOT NULL,
    "no_telp" VARCHAR(255) NOT NULL,
    "no_fax" VARCHAR(255) NOT NULL,
    "url_web" VARCHAR(255) NOT NULL,
    "status" INTEGER NOT NULL,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),

    CONSTRAINT "tb_adm_kabkot_kantor_pkey" PRIMARY KEY ("id_adm_kabkot_kantor")
);

-- CreateTable
CREATE TABLE "public"."tb_umkm_jenis" (
    "id_umkm_jenis" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_umkm_jenis_pkey" PRIMARY KEY ("id_umkm_jenis")
);

-- CreateTable
CREATE TABLE "public"."tb_kebijakan_file" (
    "id_kebijakan_file" SERIAL NOT NULL,
    "id_kebijakan" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_kebijakan_file_pkey" PRIMARY KEY ("id_kebijakan_file")
);

-- CreateTable
CREATE TABLE "public"."tb_kebijakan_kategori" (
    "id_kategori" SERIAL NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_kebijakan_kategori_pkey" PRIMARY KEY ("id_kategori")
);

-- CreateTable
CREATE TABLE "public"."tb_roadmap" (
    "id_roadmap" SERIAL NOT NULL,
    "id_komoditi" INTEGER NOT NULL,
    "judul" VARCHAR(200) NOT NULL,
    "deskripsi" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_roadmap_pkey" PRIMARY KEY ("id_roadmap")
);

-- CreateTable
CREATE TABLE "public"."tb_roadmap_file" (
    "id_roadmap_file" SERIAL NOT NULL,
    "id_roadmap" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_roadmap_file_pkey" PRIMARY KEY ("id_roadmap_file")
);

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_daerah_pdrb" ADD CONSTRAINT "tb_sektor_daerah_pdrb_id_sumber_data_fkey" FOREIGN KEY ("id_sumber_data") REFERENCES "public"."tb_sumber_data"("id_sumber_data") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_daerah_pdrb" ADD CONSTRAINT "tb_sektor_daerah_pdrb_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_daerah_pdrb" ADD CONSTRAINT "tb_sektor_daerah_pdrb_id_adm_provinsi_fkey" FOREIGN KEY ("id_adm_provinsi") REFERENCES "public"."tb_adm_provinsi"("id_adm_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_umr_provinsi" ADD CONSTRAINT "tb_umr_provinsi_id_adm_provinsi_fkey" FOREIGN KEY ("id_adm_provinsi") REFERENCES "public"."tb_adm_provinsi"("id_adm_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_umr_provinsi" ADD CONSTRAINT "tb_umr_provinsi_id_sumber_data_fkey" FOREIGN KEY ("id_sumber_data") REFERENCES "public"."tb_sumber_data"("id_sumber_data") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_kawasan_industri" ADD CONSTRAINT "tb_kawasan_industri_id_kawasan_industri_ref_range_fkey" FOREIGN KEY ("id_kawasan_industri_ref_range") REFERENCES "public"."tb_kawasan_industri_ref_range"("id_kawasan_industri_ref_range") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_umkm" ADD CONSTRAINT "tb_umkm_id_umkm_jenis_fkey" FOREIGN KEY ("id_umkm_jenis") REFERENCES "public"."tb_umkm_jenis"("id_umkm_jenis") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_umkm" ADD CONSTRAINT "tb_umkm_id_peluang_kabkot_fkey" FOREIGN KEY ("id_peluang_kabkot") REFERENCES "public"."tb_peluang_kabkot"("id_peluang_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_umkm" ADD CONSTRAINT "tb_umkm_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_umkm" ADD CONSTRAINT "tb_umkm_id_adm_provinsi_fkey" FOREIGN KEY ("id_adm_provinsi") REFERENCES "public"."tb_adm_provinsi"("id_adm_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_umkm" ADD CONSTRAINT "tb_umkm_id_peluang_sektor_fkey" FOREIGN KEY ("id_peluang_sektor") REFERENCES "public"."tb_peluang_sektor"("id_peluang_sektor") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_kebijakan" ADD CONSTRAINT "tb_kebijakan_id_kategori_fkey" FOREIGN KEY ("id_kategori") REFERENCES "public"."tb_kebijakan_kategori"("id_kategori") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_kebijakan_file" ADD CONSTRAINT "tb_kebijakan_file_id_kebijakan_fkey" FOREIGN KEY ("id_kebijakan") REFERENCES "public"."tb_kebijakan"("id_kebijakan") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_kawasan_industri_peluang" ADD CONSTRAINT "tb_kawasan_industri_peluang_id_kawasan_industri_fkey" FOREIGN KEY ("id_kawasan_industri") REFERENCES "public"."tb_kawasan_industri"("id_kawasan_industri") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_kawasan_industri_peluang_file" ADD CONSTRAINT "tb_kawasan_industri_peluang_file_id_kawasan_industri_pelua_fkey" FOREIGN KEY ("id_kawasan_industri_peluang") REFERENCES "public"."tb_kawasan_industri_peluang"("id_kawasan_industri_peluang") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_kawasan_industri_peluang_tr" ADD CONSTRAINT "tb_kawasan_industri_peluang_tr_id_kawasan_industri_peluang_fkey" FOREIGN KEY ("id_kawasan_industri_peluang") REFERENCES "public"."tb_kawasan_industri_peluang"("id_kawasan_industri_peluang") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_roadmap" ADD CONSTRAINT "tb_roadmap_id_komoditi_fkey" FOREIGN KEY ("id_komoditi") REFERENCES "public"."tb_komoditi_nasional_ref"("id_komoditi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_roadmap_file" ADD CONSTRAINT "tb_roadmap_file_id_roadmap_fkey" FOREIGN KEY ("id_roadmap") REFERENCES "public"."tb_roadmap"("id_roadmap") ON DELETE CASCADE ON UPDATE CASCADE;
