-- CreateTable
CREATE TABLE "public"."tb_sumber_data" (
    "id_sumber_data" INTEGER NOT NULL,
    "id_sumber_data_judul" INTEGER NOT NULL,
    "tahun_rumus" INTEGER,

    CONSTRAINT "tb_sumber_data_pkey" PRIMARY KEY ("id_sumber_data")
);

-- CreateTable
CREATE TABLE "public"."tb_sumber_data_instansi" (
    "id_sumber_data_instansi" INTEGER NOT NULL,
    "id_adm_provinsi" INTEGER,
    "id_adm_kabkot" INTEGER,
    "nama" VARCHAR(255),
    "alamat" VARCHAR(255),
    "url_web" VARCHAR(255),
    "cp" VARCHAR(255),
    "email" VARCHAR(255),
    "no_telp" VARCHAR(255),
    "no_fax" VARCHAR(255),

    CONSTRAINT "tb_sumber_data_instansi_pkey" PRIMARY KEY ("id_sumber_data_instansi")
);

-- CreateTable
CREATE TABLE "public"."tb_sumber_data_instansi_tr" (
    "id_sumber_data_instansi_tr" INTEGER NOT NULL,
    "id_sumber_data_instansi" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,

    CONSTRAINT "tb_sumber_data_instansi_tr_pkey" PRIMARY KEY ("id_sumber_data_instansi_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_sumber_data_judul" (
    "id_sumber_data_judul" INTEGER NOT NULL,
    "id_sumber_data_instansi" INTEGER NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_sumber_data_judul_pkey" PRIMARY KEY ("id_sumber_data_judul")
);

-- CreateTable
CREATE TABLE "public"."tb_sumber_data_judul_tr" (
    "id_sumber_data_judul_tr" INTEGER NOT NULL,
    "id_sumber_data_judul" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_sumber_data_judul_tr_pkey" PRIMARY KEY ("id_sumber_data_judul_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_sumber_data_tr" (
    "id_sumber_data_tr" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,

    CONSTRAINT "tb_sumber_data_tr_pkey" PRIMARY KEY ("id_sumber_data_tr")
);

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_kabkot" ADD CONSTRAINT "tb_peluang_kabkot_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE RESTRICT ON UPDATE CASCADE;
