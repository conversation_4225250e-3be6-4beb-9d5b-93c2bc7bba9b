-- AddForeignKey
ALTER TABLE "public"."tb_sumber_data" ADD CONSTRAINT "tb_sumber_data_id_sumber_data_judul_fkey" FOREIGN KEY ("id_sumber_data_judul") REFERENCES "public"."tb_sumber_data_judul"("id_sumber_data_judul") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sumber_data_instansi_tr" ADD CONSTRAINT "tb_sumber_data_instansi_tr_id_sumber_data_instansi_fkey" FOREIGN KEY ("id_sumber_data_instansi") REFERENCES "public"."tb_sumber_data_instansi"("id_sumber_data_instansi") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_sumber_data_judul" ADD CONSTRAINT "tb_sumber_data_judul_id_sumber_data_instansi_fkey" FOREIGN KEY ("id_sumber_data_instansi") REFERENCES "public"."tb_sumber_data_instansi"("id_sumber_data_instansi") ON DELETE RESTRICT ON UPDATE CASCADE;
