/*
  Warnings:

  - You are about to alter the column `judul` on the `tb_roadmap` table. The data in that column could be lost. The data in that column will be cast from `VarChar(200)` to `VarChar(100)`.

*/
-- AlterTable
ALTER TABLE "public"."tb_roadmap" ALTER COLUMN "judul" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "deskripsi" SET DATA TYPE VARCHAR(500);

-- AddForeignKey
ALTER TABLE "public"."tb_peluang_daerah" ADD CONSTRAINT "tb_peluang_daerah_id_sub_sektor_daerah_fkey" FOREIGN KEY ("id_sub_sektor_daerah") REFERENCES "public"."tb_sub_sektor_daerah"("id_sub_sektor_daerah") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_ikn_data_tr" ADD CONSTRAINT "tb_ikn_data_tr_id_ikn_data_fkey" FOREIGN KEY ("id_ikn_data") REFERENCES "public"."tb_ikn_data"("id_ikn_data") ON DELETE CASCADE ON UPDATE CASCADE;
