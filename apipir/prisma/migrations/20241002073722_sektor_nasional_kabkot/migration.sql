-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_kontak" (
    "id_sektor_nasional_kontak" SERIAL NOT NULL,
    "id_sektor_nasional" INTEGER NOT NULL,
    "nama_pic" TEXT NOT NULL,
    "file_foto_pic" TEXT,
    "nama" TEXT NOT NULL,
    "alamat" TEXT NOT NULL,
    "no_telp" TEXT NOT NULL,
    "no_fax" TEXT,
    "url_web" TEXT,
    "status" TEXT NOT NULL,
    "lon" DOUBLE PRECISION NOT NULL,
    "lat" DOUBLE PRECISION NOT NULL,

    CONSTRAINT "tb_sektor_nasional_kontak_pkey" PRIMARY KEY ("id_sektor_nasional_kontak")
);

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_nasional_kontak" ADD CONSTRAINT "tb_sektor_nasional_kontak_id_sektor_nasional_fkey" FOREIGN KEY ("id_sektor_nasional") REFERENCES "public"."tb_sektor_nasional"("id_sektor_nasional") ON DELETE RESTRICT ON UPDATE CASCADE;
