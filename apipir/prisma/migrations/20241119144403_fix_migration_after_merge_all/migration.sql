/*
  Warnings:

  - Made the column `id_adm_kabkot` on table `tb_rumah_sakit` required. This step will fail if there are existing NULL values in that column.
  - Made the column `id_rumah_sakit` on table `tb_rumah_sakit_tr` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
CREATE SEQUENCE "public".tb_adm_kabkot_id_adm_kabkot_seq;
ALTER TABLE "public"."tb_adm_kabkot" ALTER COLUMN "id_adm_kabkot" SET DEFAULT nextval('"public".tb_adm_kabkot_id_adm_kabkot_seq');
ALTER SEQUENCE "public".tb_adm_kabkot_id_adm_kabkot_seq OWNED BY "public"."tb_adm_kabkot"."id_adm_kabkot";

-- AlterTable
ALTER TABLE "public"."tb_ekspor_kabkot_tr" ALTER COLUMN "kd_bahasa" DROP NOT NULL;

-- AlterTable
ALTER TABLE "public"."tb_hotel" ALTER COLUMN "file_logo" DROP NOT NULL;

-- AlterTable
ALTER TABLE "public"."tb_hotel_tr" ALTER COLUMN "file_logo" DROP NOT NULL;

-- AlterTable
ALTER TABLE "public"."tb_komoditi" ALTER COLUMN "id_komoditi" DROP DEFAULT;
DROP SEQUENCE "tb_komoditi_id_komoditi_seq";

-- AlterTable
ALTER TABLE "public"."tb_komoditi_nasional" ALTER COLUMN "id_komoditi_nasional" DROP DEFAULT;
DROP SEQUENCE "tb_komoditi_nasional_id_komoditi_nasional_seq";

-- AlterTable
ALTER TABLE "public"."tb_komoditi_nasional_file" ALTER COLUMN "id_komoditi_nasional_file" DROP DEFAULT;
DROP SEQUENCE "tb_komoditi_nasional_file_id_komoditi_nasional_file_seq";

-- AlterTable
ALTER TABLE "public"."tb_rumah_sakit" ALTER COLUMN "id_adm_kabkot" SET NOT NULL;

-- AlterTable
ALTER TABLE "public"."tb_rumah_sakit_tr" ALTER COLUMN "id_rumah_sakit" SET NOT NULL;

-- AlterTable
ALTER TABLE "public"."tb_sumber_data" ALTER COLUMN "id_sumber_data" DROP DEFAULT;
DROP SEQUENCE "tb_sumber_data_id_sumber_data_seq";

-- AlterTable
ALTER TABLE "public"."tb_sumber_data_instansi" ALTER COLUMN "id_sumber_data_instansi" DROP DEFAULT;
DROP SEQUENCE "tb_sumber_data_instansi_id_sumber_data_instansi_seq";

-- AlterTable
ALTER TABLE "public"."tb_sumber_data_instansi_tr" ALTER COLUMN "id_sumber_data_instansi_tr" DROP DEFAULT;
DROP SEQUENCE "tb_sumber_data_instansi_tr_id_sumber_data_instansi_tr_seq";

-- CreateTable
CREATE TABLE "public"."_tb_adm_provinsiTotb_adm_provinsi_kantor" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_tb_adm_provinsiTotb_adm_provinsi_kantor_AB_unique" ON "public"."_tb_adm_provinsiTotb_adm_provinsi_kantor"("A", "B");

-- CreateIndex
CREATE INDEX "_tb_adm_provinsiTotb_adm_provinsi_kantor_B_index" ON "public"."_tb_adm_provinsiTotb_adm_provinsi_kantor"("B");

-- RenameForeignKey
ALTER TABLE "public"."tb_sektor_daerah" RENAME CONSTRAINT "sektor_nasional_ref_fkey" TO "tb_sektor_daerah_id_sektor_nasional_fkey";

-- RenameForeignKey
ALTER TABLE "public"."tb_sektor_nasional_value" RENAME CONSTRAINT "tb_sektor_nasional_value_id_sektor_nasional_fkey" TO "unique_constraint_name_tb_sektor_nasional_ref";

-- AddForeignKey
ALTER TABLE "public"."tb_sektor_daerah" ADD CONSTRAINT "tb_sektor_daerah_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_demografi_provinsi" ADD CONSTRAINT "tb_demografi_provinsi_id_kategori_fkey" FOREIGN KEY ("id_kategori") REFERENCES "public"."tb_demografi_kategori"("id_demografi_kategori") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_investasi_provinsi" ADD CONSTRAINT "tb_investasi_provinsi_id_jenis_data_fkey" FOREIGN KEY ("id_jenis_data") REFERENCES "public"."tb_investasi_jenis_data"("id_investasi_jenis_data") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_komoditi_kabkot" ADD CONSTRAINT "tb_komoditi_kabkot_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_komoditi_provinsi" ADD CONSTRAINT "tb_komoditi_provinsi_id_adm_provinsi_fkey" FOREIGN KEY ("id_adm_provinsi") REFERENCES "public"."tb_adm_provinsi"("id_adm_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_ekspor_kabkot_tr" ADD CONSTRAINT "tb_ekspor_kabkot_tr_id_ekspor_kabkot_fkey" FOREIGN KEY ("id_ekspor_kabkot") REFERENCES "public"."tb_ekspor_kabkot"("id_ekspor_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_ekspor_provinsi_tr" ADD CONSTRAINT "tb_ekspor_provinsi_tr_id_ekspor_provinsi_fkey" FOREIGN KEY ("id_ekspor_provinsi") REFERENCES "public"."tb_ekspor_provinsi"("id_ekspor_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_hotel_tr" ADD CONSTRAINT "tb_hotel_tr_id_hotel_fkey" FOREIGN KEY ("id_hotel") REFERENCES "public"."tb_hotel"("id_hotel") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_investasi_kabkot" ADD CONSTRAINT "tb_investasi_kabkot_id_jenis_fkey" FOREIGN KEY ("id_jenis") REFERENCES "public"."tb_investasi_jenis"("id_investasi_jenis") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_investasi_kabkot" ADD CONSTRAINT "tb_investasi_kabkot_id_jenis_data_fkey" FOREIGN KEY ("id_jenis_data") REFERENCES "public"."tb_investasi_jenis_data"("id_investasi_jenis_data") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_pendidikan_tr" ADD CONSTRAINT "tb_pendidikan_tr_id_pendidikan_fkey" FOREIGN KEY ("id_pendidikan") REFERENCES "public"."tb_pendidikan"("id_pendidikan") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_rumah_sakit_tr" ADD CONSTRAINT "tb_rumah_sakit_tr_id_rumah_sakit_fkey" FOREIGN KEY ("id_rumah_sakit") REFERENCES "public"."tb_rumah_sakit"("id_rumah_sakit") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_utilitas_provinsi_tr" ADD CONSTRAINT "tb_utilitas_provinsi_tr_id_utilitas_provinsi_fkey" FOREIGN KEY ("id_utilitas_provinsi") REFERENCES "public"."tb_utilitas_provinsi"("id_utilitas_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_bandara" ADD CONSTRAINT "tb_bandara_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_bandara" ADD CONSTRAINT "tb_bandara_id_sumber_data_fkey" FOREIGN KEY ("id_sumber_data") REFERENCES "public"."tb_sumber_data"("id_sumber_data") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_bandara" ADD CONSTRAINT "tb_bandara_id_kelas_fkey" FOREIGN KEY ("id_kelas") REFERENCES "public"."tb_bandara_kelas"("id_bandara_kelas") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_bandara" ADD CONSTRAINT "tb_bandara_id_kategori_fkey" FOREIGN KEY ("id_kategori") REFERENCES "public"."tb_bandara_kategori"("id_bandara_kategori") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_hotel" ADD CONSTRAINT "tb_hotel_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_hotel" ADD CONSTRAINT "tb_hotel_id_sumber_data_fkey" FOREIGN KEY ("id_sumber_data") REFERENCES "public"."tb_sumber_data"("id_sumber_data") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_hotel" ADD CONSTRAINT "tb_hotel_id_kelas_fkey" FOREIGN KEY ("id_kelas") REFERENCES "public"."tb_hotel_kelas"("id_hotel_kelas") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_pelabuhan" ADD CONSTRAINT "tb_pelabuhan_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_pelabuhan" ADD CONSTRAINT "tb_pelabuhan_id_sumber_data_fkey" FOREIGN KEY ("id_sumber_data") REFERENCES "public"."tb_sumber_data"("id_sumber_data") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_pendidikan" ADD CONSTRAINT "tb_pendidikan_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_pendidikan" ADD CONSTRAINT "tb_pendidikan_id_sumber_data_fkey" FOREIGN KEY ("id_sumber_data") REFERENCES "public"."tb_sumber_data"("id_sumber_data") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_pendidikan" ADD CONSTRAINT "tb_pendidikan_id_jenjang_fkey" FOREIGN KEY ("id_jenjang") REFERENCES "public"."tb_pendidikan_jenjang"("id_pendidikan_jenjang") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_pendidikan" ADD CONSTRAINT "tb_pendidikan_id_kategori_fkey" FOREIGN KEY ("id_kategori") REFERENCES "public"."tb_pendidikan_kategori"("id_pendidikan_kategori") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_rumah_sakit" ADD CONSTRAINT "tb_rumah_sakit_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_rumah_sakit" ADD CONSTRAINT "tb_rumah_sakit_id_sumber_data_fkey" FOREIGN KEY ("id_sumber_data") REFERENCES "public"."tb_sumber_data"("id_sumber_data") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_tb_adm_provinsiTotb_adm_provinsi_kantor" ADD CONSTRAINT "_tb_adm_provinsiTotb_adm_provinsi_kantor_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."tb_adm_provinsi"("id_adm_provinsi") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."_tb_adm_provinsiTotb_adm_provinsi_kantor" ADD CONSTRAINT "_tb_adm_provinsiTotb_adm_provinsi_kantor_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."tb_adm_provinsi_kantor"("id_adm_provinsi_kantor") ON DELETE CASCADE ON UPDATE CASCADE;
