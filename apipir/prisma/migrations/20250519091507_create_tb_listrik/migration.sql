-- CreateTable
CREATE TABLE "public"."tb_listrik" (
    "id_listrik" SERIAL NOT NULL,
    "id_jenis" INTEGER NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_sumber_data" INTEGER,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "file_logo" VARCHAR(255),
    "alamat" TEXT NOT NULL,
    "no_telp" VARCHAR(30) NOT NULL,
    "no_fax" VARCHAR(30) NOT NULL,
    "email" VARCHAR(50) NOT NULL,
    "status" INTEGER NOT NULL,
    "url_web" VARCHAR(255) NOT NULL,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "is_ikn" BOOLEAN,
    "id_kategori_infrastruktur" INTEGER,

    CONSTRAINT "tb_listrik_pkey" PRIMARY KEY ("id_listrik")
);

-- CreateTable
CREATE TABLE "public"."tb_listrik_jenis" (
    "id_jenis" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_listrik_jenis_pkey" PRIMARY KEY ("id_jenis")
);

-- CreateTable
CREATE TABLE "public"."tb_listrik_jenis_tr" (
    "id_listrik_jenis_tr" SERIAL NOT NULL,
    "id_listrik_jenis" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,

    CONSTRAINT "tb_listrik_jenis_tr_pkey" PRIMARY KEY ("id_listrik_jenis_tr")
);

-- CreateTable
CREATE TABLE "public"."tb_listrik_status" (
    "id_listrik" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),
    "id_listrik_status" SERIAL NOT NULL,

    CONSTRAINT "tb_listrik_status_pkey" PRIMARY KEY ("id_listrik_status")
);

-- CreateTable
CREATE TABLE "public"."tb_listrik_tr" (
    "id_listrik_tr" SERIAL NOT NULL,
    "id_listrik" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "file_logo" VARCHAR(255),

    CONSTRAINT "tb_listrik_tr_pkey" PRIMARY KEY ("id_listrik_tr")
);

-- AddForeignKey
ALTER TABLE "public"."tb_listrik" ADD CONSTRAINT "tb_listrik_id_adm_kabkot_fkey" FOREIGN KEY ("id_adm_kabkot") REFERENCES "public"."tb_adm_kabkot"("id_adm_kabkot") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_listrik" ADD CONSTRAINT "tb_listrik_id_jenis_fkey" FOREIGN KEY ("id_jenis") REFERENCES "public"."tb_listrik_jenis"("id_jenis") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_listrik" ADD CONSTRAINT "tb_listrik_id_sumber_data_fkey" FOREIGN KEY ("id_sumber_data") REFERENCES "public"."tb_sumber_data"("id_sumber_data") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_listrik" ADD CONSTRAINT "tb_listrik_id_kategori_infrastruktur_fkey" FOREIGN KEY ("id_kategori_infrastruktur") REFERENCES "public"."tb_kategori_infrastruktur"("id_kategori_infrastruktur") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_listrik_status" ADD CONSTRAINT "tb_listrik_status_id_listrik_fkey" FOREIGN KEY ("id_listrik") REFERENCES "public"."tb_listrik"("id_listrik") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tb_listrik_tr" ADD CONSTRAINT "tb_listrik_tr_id_listrik_fkey" FOREIGN KEY ("id_listrik") REFERENCES "public"."tb_listrik"("id_listrik") ON DELETE CASCADE ON UPDATE CASCADE;
