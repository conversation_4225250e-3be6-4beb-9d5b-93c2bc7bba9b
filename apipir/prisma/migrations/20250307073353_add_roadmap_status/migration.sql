-- AlterTable
ALTER TABLE "public"."tb_roadmap" ADD COLUMN     "status" INTEGER;

-- CreateTable
CREATE TABLE "public"."tb_roadmap_status" (
    "tb_roadmap_status" SERIAL NOT NULL,
    "id_roadmap" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6),
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,

    CONSTRAINT "tb_roadmap_status_pkey" PRIMARY KEY ("tb_roadmap_status")
);

-- AddForeignKey
ALTER TABLE "public"."tb_roadmap_status" ADD CONSTRAINT "tb_roadmap_status_id_roadmap_fkey" FOREIGN KEY ("id_roadmap") REFERENCES "public"."tb_roadmap"("id_roadmap") ON DELETE CASCADE ON UPDATE CASCADE;
