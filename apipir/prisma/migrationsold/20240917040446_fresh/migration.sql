-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah" (
    "id_peluang_daerah" SERIAL NOT NULL,
    "id_adm_provinsi" INTEGER,
    "id_adm_kabkot" INTEGER,
    "id_sub_sektor_daerah" INTEGER NOT NULL,
    "status_peluang" SMALLINT NOT NULL,
    "status" SMALLINT NOT NULL,
    "id_bandara" INTEGER,
    "jarak_bandara" SMALLINT NOT NULL DEFAULT 0,
    "id_pelabuhan" INTEGER,
    "jarak_pelabuhan" SMALLINT NOT NULL DEFAULT 0,
    "jarak_ibukota" SMALLINT NOT NULL DEFAULT 0,
    "kode_kbli" VARCHAR(150),
    "judul" VARCHAR(255),
    "lokasi" VARCHAR(255),
    "tahun" SMALLINT NOT NULL DEFAULT 2022,
    "keterangan" TEXT,
    "aspek_pasar" TEXT,
    "aspek_teknis" TEXT,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "zoom_peta_default" INTEGER,
    "is_ikn" BOOLEAN,

    CONSTRAINT "pk_peluang_daerah" PRIMARY KEY ("id_peluang_daerah")
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_file" (
    "id_peluang_daerah_file" SERIAL NOT NULL,
    "id_peluang_daerah" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_file_tr" (
    "id_peluang_daerah_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_insentif" (
    "id_peluang_daerah_insentif" SERIAL NOT NULL,
    "id_peluang_daerah" INTEGER NOT NULL,
    "id_sektor_nasional_insentif" INTEGER,
    "id_sektor_daerah_insentif" INTEGER
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_kelayakan" (
    "id_peluang_daerah" INTEGER NOT NULL,
    "pct_cost_capital" SMALLINT NOT NULL,
    "year_period" SMALLINT NOT NULL,
    "base_revenue" DOUBLE PRECISION NOT NULL,
    "pct_revenue_growth" SMALLINT NOT NULL,
    "base_opex" DOUBLE PRECISION NOT NULL,
    "pct_inflation" SMALLINT NOT NULL,
    "initial_invesment" DOUBLE PRECISION NOT NULL,
    "pct_salvage_value" SMALLINT NOT NULL,
    "irr" DOUBLE PRECISION NOT NULL,
    "npv" DOUBLE PRECISION NOT NULL,
    "pp" SMALLINT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_kelayakan_detail" (
    "id_peluang_daerah" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "nilai" DOUBLE PRECISION NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_komoditi" (
    "id_peluang_daerah" INTEGER NOT NULL,
    "id_komoditi_daerah" INTEGER NOT NULL,
    "jenis" VARCHAR(255) NOT NULL,
    "manfaat" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_kontak" (
    "id_peluang_daerah" INTEGER NOT NULL,
    "id_peluang_kontak" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_layer_spasial" (
    "id_peluang_daerah_layer_spasial" SERIAL NOT NULL,
    "id_peluang_daerah" INTEGER NOT NULL,
    "nama_layer" VARCHAR(500) NOT NULL,
    "tipe" INTEGER NOT NULL,
    "url_service" VARCHAR(500) NOT NULL,
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_status" (
    "id_peluang_daerah" INTEGER NOT NULL,
    "status" SMALLINT NOT NULL,
    "status_proses" SMALLINT NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_daerah_tr" (
    "id_peluang_daerah" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "judul" VARCHAR(255),
    "lokasi" VARCHAR(255),
    "keterangan" TEXT,
    "aspek_pasar" TEXT,
    "aspek_teknis" TEXT
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_berita_mapping" (
    "id_peluang_kabkot" INTEGER NOT NULL,
    "id_berita" INTEGER NOT NULL,
    "created_time" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_file" (
    "id_peluang_kabkot_file" SERIAL NOT NULL,
    "id_peluang_kabkot" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "nama" VARCHAR(255),
    "url_rest" VARCHAR(255),
    "status" INTEGER DEFAULT 0
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_file_tr" (
    "id_peluang_kabkot_file_tr" INTEGER NOT NULL,
    "id_peluang_kabkot" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "tipe" INTEGER NOT NULL,
    "nama" VARCHAR(255),
    "url_rest" VARCHAR(255),
    "status" INTEGER DEFAULT 0
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_insentif" (
    "id_peluang_kabkot" INTEGER NOT NULL,
    "id_jenis_insentif" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_kontak" (
    "id_peluang_kabkot" INTEGER NOT NULL,
    "id_peluang_kontak" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_likes_counter" (
    "id_likes" INTEGER NOT NULL,
    "id_peluang_kabkot" INTEGER NOT NULL,
    "ip_pengunjung" VARCHAR(15) NOT NULL,
    "created_time" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_status" (
    "id_peluang_kabkot" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kabkot_tr" (
    "id_peluang_kabkot_tr" SERIAL NOT NULL,
    "id_peluang_kabkot" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "nama_singkat" VARCHAR(36),
    "deskripsi_singkat" VARCHAR(120),
    "deskripsi" VARCHAR(600),
    "lokasi_kawasan" VARCHAR(255)
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_kontak" (
    "id_peluang_kontak" SERIAL NOT NULL,
    "nama" VARCHAR(255),
    "alamat" VARCHAR(255),
    "url_web" VARCHAR(255),
    "cp" VARCHAR(255),
    "jabatan_cp" VARCHAR(255),
    "email" VARCHAR(255),
    "no_telp" VARCHAR(255),
    "no_fax" VARCHAR(255),
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "id_adm_provinsi" INTEGER,
    "id_adm_kabkot" INTEGER
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_prioritas" (
    "id_peluang_prioritas" SERIAL NOT NULL,
    "nama" VARCHAR(50) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_prioritas_tr" (
    "id_peluang_prioritas_tr" SERIAL NOT NULL,
    "id_peluang_prioritas" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50),
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_provinsi_file" (
    "id_peluang_provinsi_file" SERIAL NOT NULL,
    "id_peluang_provinsi" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_provinsi_status" (
    "id_peluang_provinsi" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT NOT NULL,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_provinsi_tr" (
    "id_peluang_provinsi_tr" SERIAL NOT NULL,
    "id_peluang_provinsi" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_sektor" (
    "id_peluang_sektor" INTEGER NOT NULL,
    "nama" VARCHAR(50) NOT NULL,
    "keterangan" TEXT NOT NULL,
    "icon" VARCHAR(255),
    "iconmap" VARCHAR(255),
    "image" VARCHAR(255)
);

-- CreateTable
CREATE TABLE "public"."tb_peluang_sektor_tr" (
    "id_peluang_sektor_tr" SERIAL NOT NULL,
    "id_peluang_sektor" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,
    "keterangan" TEXT NOT NULL
);
