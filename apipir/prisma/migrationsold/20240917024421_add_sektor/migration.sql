-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah" (
    "id_sektor_daerah" SERIAL NOT NULL,
    "tipe" INTEGER NOT NULL,
    "id_sektor_nasional" INTEGER NOT NULL,
    "id_adm_kabkot" INTEGER,
    "id_adm_provinsi" INTEGER,
    "deskripsi_singkat" TEXT NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "potensi_pasar" TEXT NOT NULL,
    "no_dokumen" VARCHAR(255),
    "perihal" VARCHAR(255),
    "file_dokumen" VARCHAR(255),
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_file" (
    "id_sektor_daerah_file" SERIAL NOT NULL,
    "id_sektor_daerah" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_file_tr" (
    "id_sektor_daerah_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_insentif" (
    "id_sektor_daerah_insentif" SERIAL NOT NULL,
    "id_sektor_daerah" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_insentif_file" (
    "id_sektor_daerah_insentif_file" SERIAL NOT NULL,
    "id_sektor_daerah_insentif" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_insentif_file_tr" (
    "id_sektor_daerah_insentif_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_insentif_status" (
    "id_sektor_daerah_insentif" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_insentif_tr" (
    "id_sektor_daerah_insentif" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "deskripsi" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_lq" (
    "id_sektor_daerah" INTEGER NOT NULL,
    "tahun_pdrb" INTEGER NOT NULL,
    "pdrb_sektor" DOUBLE PRECISION NOT NULL,
    "pdrb_total" DOUBLE PRECISION NOT NULL,
    "tahun_pdb" INTEGER NOT NULL,
    "pdb_sektor" DOUBLE PRECISION NOT NULL,
    "pdb_total" DOUBLE PRECISION NOT NULL,
    "nilai_lq" DOUBLE PRECISION NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_pdrb" (
    "id_sektor_daerah_pdrb" SERIAL NOT NULL,
    "id_adm_provinsi" INTEGER,
    "id_adm_kabkot" INTEGER,
    "id_sektor_nasional" INTEGER NOT NULL,
    "tahun_pdrb" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL,
    "jumlah_pdrb" DECIMAL(19,0) NOT NULL,
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_pdrb_status" (
    "id_sektor_daerah_pdrb" INTEGER NOT NULL,
    "tahun_pdrb" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_status" (
    "id_sektor_daerah" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_sumber_data" (
    "id_sektor_daerah" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_tr" (
    "id_sektor_daerah" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "deskripsi_singkat" TEXT NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "potensi_pasar" TEXT NOT NULL,
    "perihal" VARCHAR(255),
    "file_dokumen" VARCHAR(255)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_daerah_value_detail" (
    "id_sektor_daerah" INTEGER NOT NULL,
    "id_sektor_nasional_value" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "numeric_value" REAL,
    "string_value" VARCHAR(255),
    "date_value" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional" (
    "id_sektor_nasional" INTEGER NOT NULL,
    "deskripsi_singkat" TEXT NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "potensi_pasar" TEXT NOT NULL,
    "file_icon" VARCHAR(255) NOT NULL,
    "file_image" VARCHAR(255) NOT NULL,
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_file" (
    "id_sektor_nasional_file" SERIAL NOT NULL,
    "id_sektor_nasional" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_file_tr" (
    "id_sektor_nasional_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_insentif" (
    "id_sektor_nasional_insentif" SERIAL NOT NULL,
    "id_sektor_nasional" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_insentif_file" (
    "id_sektor_nasional_insentif_file" SERIAL NOT NULL,
    "id_sektor_nasional_insentif" INTEGER NOT NULL,
    "tipe" INTEGER NOT NULL,
    "jenis" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_insentif_file_tr" (
    "id_sektor_nasional_insentif_file" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "judul" VARCHAR(255) NOT NULL,
    "keterangan" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_insentif_status" (
    "id_sektor_nasional_insentif" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_insentif_tr" (
    "id_sektor_nasional_insentif" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "deskripsi" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_pdb" (
    "id_sektor_nasional" INTEGER NOT NULL,
    "tahun_pdb" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL,
    "jumlah_pdb" DECIMAL(19,0) NOT NULL,
    "status" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_pdb_status" (
    "id_sektor_nasional" INTEGER NOT NULL,
    "tahun_pdb" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_ref" (
    "id_sektor" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_ref_tr" (
    "id_sektor" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_status" (
    "id_sektor_nasional" INTEGER NOT NULL,
    "status" INTEGER NOT NULL,
    "status_proses" INTEGER NOT NULL,
    "keterangan" TEXT,
    "created_by" INTEGER NOT NULL,
    "created_date" TIMESTAMP(6) NOT NULL,
    "updated_by" INTEGER,
    "updated_date" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_sumber_data" (
    "id_sektor_nasional" INTEGER NOT NULL,
    "id_sumber_data" INTEGER NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_tr" (
    "id_sektor_nasional" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "deskripsi_singkat" TEXT NOT NULL,
    "deskripsi" TEXT NOT NULL,
    "potensi_pasar" TEXT NOT NULL,
    "file_icon" VARCHAR(255) NOT NULL,
    "file_image" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_value" (
    "id_sektor_nasional_value" SERIAL NOT NULL,
    "id_sektor_nasional" INTEGER NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "tipe" SMALLINT NOT NULL,
    "satuan" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_value_detail" (
    "id_sektor_nasional_value" INTEGER NOT NULL,
    "tahun" INTEGER NOT NULL,
    "numeric_value" REAL,
    "string_value" VARCHAR(255),
    "date_value" TIMESTAMP(6)
);

-- CreateTable
CREATE TABLE "public"."tb_sektor_nasional_value_tr" (
    "id_sektor_nasional_value" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(255) NOT NULL,
    "satuan" VARCHAR(255) NOT NULL
);
