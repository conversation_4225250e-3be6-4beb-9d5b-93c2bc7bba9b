/*
  Warnings:

  - You are about to drop the column `account_name` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `login_id` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `tes_name` on the `users` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[login_name]` on the table `users` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "users_account_name_key";

-- DropIndex
DROP INDEX "users_login_id_key";

-- DropIndex
DROP INDEX "users_tes_name_key";

-- AlterTable
ALTER TABLE "users" DROP COLUMN "account_name",
DROP COLUMN "login_id",
DROP COLUMN "tes_name",
ADD COLUMN     "login_name" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "users_login_name_key" ON "users"("login_name");
