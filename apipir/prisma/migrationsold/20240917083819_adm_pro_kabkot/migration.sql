-- CreateTable
CREATE TABLE "public"."tb_adm_provinsi" (
    "id_adm_provinsi" INTEGER NOT NULL,
    "id_adm_wilayah" INTEGER NOT NULL,
    "kd_adm" TEXT NOT NULL,
    "nama" TEXT NOT NULL,
    "nama_ibukota" TEXT NOT NULL,
    "file_logo" TEXT,
    "file_image" TEXT,
    "deskripsi" TEXT,
    "luas_wilayah" DOUBLE PRECISION,
    "jumlah_penduduk" INTEGER,
    "alamat" TEXT,
    "no_telp" TEXT,
    "no_fax" TEXT,
    "url_web" TEXT,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "shape" TEXT
);

-- CreateTable
CREATE TABLE "public"."tb_adm_provinsi_tr" (
    "id_adm_provinsi_tr" SERIAL NOT NULL,
    "id_adm_provinsi" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "nama" VARCHAR(50) NOT NULL,
    "nama_ibukota" VARCHAR(50) NOT NULL,
    "file_logo" VARCHAR(255) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."tb_adm_provinsi_layer_spasial" (
    "id_adm_provinsi_layer_spasial" SERIAL NOT NULL,
    "id_adm_provinsi" INTEGER NOT NULL,
    "nama_layer" VARCHAR(500) NOT NULL,
    "tipe" INTEGER NOT NULL,
    "url_service" VARCHAR(500) NOT NULL,
    "status" INTEGER NOT NULL,
    "is_active" BOOLEAN
);

-- CreateTable
CREATE TABLE "public"."tb_adm_kabkot" (
    "id_adm_kabkot" INTEGER NOT NULL,
    "id_adm_provinsi" INTEGER NOT NULL,
    "kd_adm" TEXT NOT NULL,
    "jenis" TEXT NOT NULL,
    "nama" TEXT NOT NULL,
    "nama_ibukota" TEXT NOT NULL,
    "file_logo" TEXT,
    "file_image" TEXT,
    "deskripsi" TEXT,
    "luas_wilayah" DOUBLE PRECISION,
    "jumlah_penduduk" INTEGER,
    "alamat" TEXT,
    "no_telp" TEXT,
    "no_fax" TEXT,
    "url_web" TEXT,
    "lon" DOUBLE PRECISION,
    "lat" DOUBLE PRECISION,
    "shape" TEXT,
    "is_daerah_tertinggal" BOOLEAN NOT NULL DEFAULT false
);

-- CreateTable
CREATE TABLE "public"."tb_adm_kabkot_alias" (
    "id_adm_kabkot" INTEGER NOT NULL,
    "nama_mineral_logam" VARCHAR(50),
    "nama_mineral_non_logam" VARCHAR(50),
    "nama_batubara" VARCHAR(50),
    "nama_panas_bumi" VARCHAR(50)
);

-- CreateTable
CREATE TABLE "public"."tb_adm_kabkot_layer_spasial" (
    "id_adm_kabkot_layer_spasial" SERIAL NOT NULL,
    "id_adm_kabkot" INTEGER NOT NULL,
    "nama_layer" VARCHAR(500) NOT NULL,
    "tipe" INTEGER NOT NULL,
    "url_service" VARCHAR(500) NOT NULL,
    "status" INTEGER NOT NULL,
    "is_active" BOOLEAN
);

-- CreateTable
CREATE TABLE "public"."tb_adm_kabkot_tr" (
    "id_adm_kabkot_tr" SERIAL,
    "id_adm_kabkot" INTEGER,
    "kd_bahasa" VARCHAR(2),
    "nama" VARCHAR(50),
    "nama_ibukota" VARCHAR(50),
    "file_logo" VARCHAR(255)
);
