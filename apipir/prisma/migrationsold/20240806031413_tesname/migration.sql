/*
  Warnings:

  - You are about to drop the column `tes` on the `users` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[tes_name]` on the table `users` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[login_id]` on the table `users` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "users_tes_key";

-- AlterTable
ALTER TABLE "users" DROP COLUMN "tes",
ADD COLUMN     "full_name" TEXT,
ADD COLUMN     "login_id" TEXT,
ADD COLUMN     "tes_name" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "users_tes_name_key" ON "users"("tes_name");

-- CreateIndex
CREATE UNIQUE INDEX "users_login_id_key" ON "users"("login_id");
