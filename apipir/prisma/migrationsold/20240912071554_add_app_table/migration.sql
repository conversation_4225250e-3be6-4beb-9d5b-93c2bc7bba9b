/*
  Warnings:

  - You are about to drop the `tes` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
DROP TABLE "pu"."tes";

-- CreateTable
CREATE TABLE "public"."tb_app_slider" (
    "id_app_slider" INTEGER NOT NULL,
    "judul" VARCHAR(255),
    "deskripsi" TEXT,
    "nama_file_image" VARCHAR(255) NOT NULL,
    "url_link" VARCHAR(255),
    "ordering" INTEGER
);

-- CreateTable
CREATE TABLE "public"."tb_app_slider_peluang" (
    "id_app_slider" INTEGER NOT NULL,
    "judul" VARCHAR(255),
    "deskripsi" TEXT,
    "nama_file_image" VARCHAR(255) NOT NULL,
    "url_link" VARCHAR(255),
    "ordering" INTEGER
);

-- CreateTable
CREATE TABLE "public"."tb_app_slider_peluang_tr" (
    "id_app_slider_tr" INTEGER NOT NULL,
    "id_app_slider" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "judul" VARCHAR(255),
    "deskripsi" TEXT,
    "ordering" INTEGER,
    "nama_file_image" VARCHAR(255)
);

-- CreateTable
CREATE TABLE "public"."tb_app_slider_tr" (
    "id_app_slider_tr" INTEGER NOT NULL,
    "id_app_slider" INTEGER NOT NULL,
    "kd_bahasa" VARCHAR(2) NOT NULL,
    "judul" VARCHAR(255),
    "deskripsi" TEXT,
    "ordering" INTEGER,
    "nama_file_image" VARCHAR(255)
);
