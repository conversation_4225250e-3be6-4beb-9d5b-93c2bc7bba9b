/*
  Warnings:

  - You are about to drop the column `created_at` on the `tes` table. All the data in the column will be lost.
  - You are about to drop the column `email` on the `tes` table. All the data in the column will be lost.
  - You are about to drop the column `expires_at` on the `tes` table. All the data in the column will be lost.
  - You are about to drop the column `token` on the `tes` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `tes` table. All the data in the column will be lost.
  - Added the required column `name` to the `tes` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "pu"."tes_token_key";

-- AlterTable
ALTER TABLE "pu"."tes" DROP COLUMN "created_at",
DROP COLUMN "email",
DROP COLUMN "expires_at",
DROP COLUMN "token",
DROP COLUMN "updated_at",
ADD COLUMN     "name" TEXT NOT NULL;
