/*
  Warnings:

  - You are about to drop the column `created_at` on the `menu` table. All the data in the column will be lost.
  - You are about to drop the column `order` on the `menu` table. All the data in the column will be lost.
  - You are about to drop the column `parent_id` on the `menu` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `menu` table. All the data in the column will be lost.
  - You are about to drop the column `url` on the `menu` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "menu" DROP CONSTRAINT "menu_parent_id_fkey";

-- AlterTable
ALTER TABLE "menu" DROP COLUMN "created_at",
DROP COLUMN "order",
DROP COLUMN "parent_id",
DROP COLUMN "updated_at",
DROP COLUMN "url";
