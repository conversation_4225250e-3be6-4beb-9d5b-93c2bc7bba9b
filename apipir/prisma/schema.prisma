generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
  binaryTargets   = ["native", "darwin", "linux-musl"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["public"]
}

model users {
  id                                Int                                @id @default(autoincrement())
  email                             String?                            @unique
  full_name                         String?
  login_name                        String?                            @unique
  password                          String
  role_id                           Int                                @default(0)
  created_at                        DateTime                           @default(now())
  updated_at                        DateTime                           @updatedAt
  address                           String?
  code_expired                      DateTime?                          @db.Timestamp(6)
  code_forgot                       String?                            @db.VarChar(255)
  file_image                        String?                            @db.VarChar(50)
  first_name                        String?                            @db.VarChar(30)
  instansi                          String?
  jabatan                           String?                            @db.VarChar(255)
  keterangan                        String?
  last_login                        DateTime?                          @db.Timestamp(6)
  last_name                         String?                            @db.VarChar(30)
  middle_name                       String?                            @db.VarChar(30)
  mobile_number                     String?                            @db.Var<PERSON>har(30)
  phone_number                      String?                            @db.VarChar(30)
  status                            Int?                               @db.SmallInt
  updated_by                        Int?
  user_api_key                      String?                            @db.VarChar(36)
  user_token                        String?                            @db.VarChar(255)
  tb_user_internal                  tb_user_internal[]
  tb_user_internal_kawasan_industri tb_user_internal_kawasan_industri[]
  tb_kawasan_user                   tb_kawasan_user[]
  tb_user_internal_provinsi         tb_user_internal_provinsi[]
  tb_user_internal_kabkot           tb_user_internal_kabkot[]
  tb_adm_user                       tb_adm_user[]
  roles                             role                               @relation(fields: [role_id], references: [id], onDelete: Cascade)

  @@schema("public")
}

model auth_access_tokens {
  id           Int       @id @default(autoincrement())
  tokenable_id String
  hash         String
  type         String
  name         String?
  abilities    String
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt
  last_used_at DateTime? @updatedAt
  expires_at   DateTime? @updatedAt

  @@schema("public")
}

model password_reset {
  id         Int      @id @default(autoincrement())
  email      String
  token      String   @unique
  expires_at DateTime
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@schema("public")
}

model config_upload {
  id       Int    @id @default(autoincrement())
  jenis    String
  size     String
  extnames String

  @@schema("public")
}

model menu {
  id          Int                @id @default(autoincrement())
  menu_name   String
  url         String?
  parent_id   Int?
  icon        String?
  order       Int                @default(0)
  created_at  DateTime           @default(now())
  updated_at  DateTime           @updatedAt
  parent      menu?              @relation("MenuToParent", fields: [parent_id], references: [id], onDelete: Cascade)
  children    menu[]             @relation("MenuToParent")
  role_access role_menu_access[]

  @@schema("public")
}

model role {
  id          Int                @id @default(autoincrement())
  role_name   String
  menu_access role_menu_access[]
  user        users[]

  @@schema("public")
}

model role_menu_access {
  id      Int  @id @default(autoincrement())
  role_id Int
  menu_id Int
  menu    menu @relation(fields: [menu_id], references: [id], onDelete: Cascade)
  role    role @relation(fields: [role_id], references: [id], onDelete: Cascade)

  @@unique([role_id, menu_id])
  @@schema("public")
}

model tb_app_slider {
  id_app_slider    Int                @id @default(autoincrement())
  judul            String?            @db.VarChar(255)
  deskripsi        String?
  nama_file_image  String             @db.VarChar(255)
  url_link         String?            @db.VarChar(255)
  ordering         Int?
  id_komoditi         Int?
  is_background    Boolean?           @default(false)
  is_roadmap       Boolean?           @default(false)
  tb_komoditi_nasional_ref tb_komoditi_nasional_ref? @relation(fields: [id_komoditi], references: [id_komoditi], onDelete: Cascade)
  tb_app_slider_tr tb_app_slider_tr[]

  @@schema("public")
}

model tb_app_slider_peluang {
  id_app_slider   Int                        @id @default(autoincrement())
  judul           String?                    @db.VarChar(255)
  deskripsi       String?
  nama_file_image String                     @db.VarChar(255)
  url_link        String?                    @db.VarChar(255)
  ordering        Int?
  tr              tb_app_slider_peluang_tr[] @relation("SliderTranslations")

  @@schema("public")
}

model tb_app_slider_peluang_tr {
  id_app_slider_tr Int                   @id @default(autoincrement())
  id_app_slider    Int
  kd_bahasa        String                @db.VarChar(2)
  judul            String?               @db.VarChar(255)
  deskripsi        String?
  ordering         Int?
  nama_file_image  String?               @db.VarChar(255)
  slider           tb_app_slider_peluang @relation("SliderTranslations", fields: [id_app_slider], references: [id_app_slider], onDelete: Cascade)

  @@schema("public")
}

model tb_app_slider_tr {
  id_app_slider_tr Int           @id @default(autoincrement())
  id_app_slider    Int
  kd_bahasa        String        @db.VarChar(2)
  judul            String?       @db.VarChar(255)
  deskripsi        String?
  ordering         Int?
  nama_file_image  String?       @db.VarChar(255)
  is_roadmap       Boolean?      @default(false)
  tb_app_slider    tb_app_slider @relation(fields: [id_app_slider], references: [id_app_slider], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_daerah {
  id_sektor_daerah              Int                             @id @default(autoincrement())
  tipe                          Int
  id_sektor_nasional            Int
  id_adm_kabkot                 Int?
  id_adm_provinsi               Int?
  deskripsi_singkat             String
  deskripsi                     String
  potensi_pasar                 String
  no_dokumen                    String?                         @db.VarChar(255)
  perihal                       String?                         @db.VarChar(255)
  file_dokumen                  String?                         @db.VarChar(255)
  status                        Int
  sektor_nasional               tb_sektor_nasional              @relation(fields: [id_sektor_nasional], references: [id_sektor_nasional], onDelete: Cascade, map: "sektor_nasional_fkey")
  
  tb_adm_kabkot                 tb_adm_kabkot?                  @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_adm_provinsi               tb_adm_provinsi?                @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)
  
  tb_sektor_daerah_file         tb_sektor_daerah_file[]
  tb_sektor_daerah_insentif     tb_sektor_daerah_insentif[]
  tb_sektor_daerah_lq           tb_sektor_daerah_lq?
  tb_sektor_daerah_sumber_data  tb_sektor_daerah_sumber_data[]
  tb_sektor_daerah_tr           tb_sektor_daerah_tr?
  tb_sektor_daerah_value_detail tb_sektor_daerah_value_detail[]
  tb_sub_sektor_daerah          tb_sub_sektor_daerah[]

  @@schema("public")
}

model tb_sektor_daerah_file {
  id_sektor_daerah_file    Int                        @id @default(autoincrement())
  id_sektor_daerah         Int
  tipe                     Int
  jenis                    Int
  nama                     String                     @db.VarChar(255)
  judul                    String                     @db.VarChar(255)
  keterangan               String
  tb_sektor_daerah         tb_sektor_daerah           @relation(fields: [id_sektor_daerah], references: [id_sektor_daerah], onDelete: Cascade)
  tb_sektor_daerah_file_tr tb_sektor_daerah_file_tr[]

  @@schema("public")
}

model tb_sektor_daerah_file_tr {
  id_sektor_daerah_file    Int
  kd_bahasa                String                @db.VarChar(2)
  nama                     String                @db.VarChar(255)
  judul                    String                @db.VarChar(255)
  keterangan               String
  id_sektor_daerah_file_tr Int                   @id @default(autoincrement())
  tb_sektor_daerah_file    tb_sektor_daerah_file @relation(fields: [id_sektor_daerah_file], references: [id_sektor_daerah_file], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_daerah_insentif {
  id_sektor_daerah_insentif      Int                              @id @default(autoincrement())
  id_sektor_daerah               Int
  nama                           String                           @db.VarChar(255)
  deskripsi                      String
  status                         Int
  tb_sektor_daerah               tb_sektor_daerah                 @relation(fields: [id_sektor_daerah], references: [id_sektor_daerah], onDelete: Cascade)
  tb_sektor_daerah_insentif_tr   tb_sektor_daerah_insentif_tr[]
  tb_sektor_daerah_insentif_file tb_sektor_daerah_insentif_file[]

  @@schema("public")
}

model tb_sektor_daerah_insentif_file {
  id_sektor_daerah_insentif_file    Int                                 @id @default(autoincrement())
  id_sektor_daerah_insentif         Int
  tipe                              Int
  jenis                             Int
  nama                              String                              @db.VarChar(255)
  judul                             String                              @db.VarChar(255)
  keterangan                        String
  tb_sektor_daerah_insentif         tb_sektor_daerah_insentif           @relation(fields: [id_sektor_daerah_insentif], references: [id_sektor_daerah_insentif], onDelete: Cascade)
  tb_sektor_daerah_insentif_file_tr tb_sektor_daerah_insentif_file_tr[]

  @@schema("public")
}

model tb_sektor_daerah_insentif_file_tr {
  id_sektor_daerah_insentif_file    Int
  kd_bahasa                         String                         @db.VarChar(2)
  nama                              String                         @db.VarChar(255)
  judul                             String                         @db.VarChar(255)
  keterangan                        String
  id_sektor_daerah_insentif_file_tr Int                            @id @default(autoincrement())
  tb_sektor_daerah_insentif_file    tb_sektor_daerah_insentif_file @relation(fields: [id_sektor_daerah_insentif_file], references: [id_sektor_daerah_insentif_file], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_daerah_insentif_status {
  id_sektor_daerah_insentif        Int
  status                           Int
  status_proses                    Int
  keterangan                       String?
  created_by                       Int
  created_date                     DateTime  @db.Timestamp(6)
  updated_by                       Int?
  updated_date                     DateTime? @db.Timestamp(6)
  id_sektor_daerah_insentif_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_sektor_daerah_insentif_tr {
  id_sektor_daerah_insentif    Int
  kd_bahasa                    String @db.VarChar(2)
  nama                         String @db.VarChar(255)
  deskripsi                    String
  id_sektor_daerah_insentif_tr Int    @id @default(autoincrement())
  tb_sektor_daerah_insentif    tb_sektor_daerah_insentif           @relation(fields: [id_sektor_daerah_insentif], references: [id_sektor_daerah_insentif], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_daerah_lq {
  id_sektor_daerah Int              @id
  tahun_pdrb       Int              
  pdrb_sektor      Float
  pdrb_total       Float
  tahun_pdb        Int
  pdb_sektor       Float
  pdb_total        Float
  nilai_lq         Float
  tb_sektor_daerah tb_sektor_daerah @relation(fields: [id_sektor_daerah], references: [id_sektor_daerah], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_daerah_pdrb {
  id_sektor_daerah_pdrb Int                @id @default(autoincrement())
  id_adm_provinsi       Int?
  id_adm_kabkot         Int?
  id_sektor_nasional    Int
  tahun_pdrb            Int
  id_sumber_data        Int
  jumlah_pdrb           Decimal            @db.Decimal(19, 0)
  status                Int
  tb_adm_kabkot         tb_adm_kabkot?     @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_adm_provinsi       tb_adm_provinsi?   @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)
  tb_sektor_nasional    tb_sektor_nasional @relation(fields: [id_sektor_nasional], references: [id_sektor_nasional], onDelete: Cascade)
  tb_sumber_data        tb_sumber_data     @relation(fields: [id_sumber_data], references: [id_sumber_data], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_daerah_pdrb_status {
  id_sektor_daerah_pdrb        Int
  tahun_pdrb                   Int
  status                       Int
  status_proses                Int
  keterangan                   String?
  created_by                   Int
  created_date                 DateTime  @db.Timestamp(6)
  updated_by                   Int?
  updated_date                 DateTime? @db.Timestamp(6)
  id_sektor_daerah_pdrb_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_sektor_daerah_status {
  id_sektor_daerah        Int
  status                  Int
  status_proses           Int
  keterangan              String?
  created_by              Int
  created_date            DateTime  @db.Timestamp(6)
  updated_by              Int?
  updated_date            DateTime? @db.Timestamp(6)
  id_sektor_daerah_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_sektor_daerah_sumber_data {
  id_sektor_daerah             Int
  id_sumber_data               Int
  id_sektor_daerah_sumber_data Int              @id @default(autoincrement())
  tb_sektor_daerah             tb_sektor_daerah @relation(fields: [id_sektor_daerah], references: [id_sektor_daerah], onDelete: Cascade)
  tb_sumber_data               tb_sumber_data   @relation(fields: [id_sumber_data], references: [id_sumber_data], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_daerah_tr {
  id_sektor_daerah  Int              @id
  kd_bahasa         String           @db.VarChar(2)
  deskripsi_singkat String
  deskripsi         String
  potensi_pasar     String
  perihal           String?          @db.VarChar(255)
  file_dokumen      String?          @db.VarChar(255)
  tb_sektor_daerah  tb_sektor_daerah @relation(fields: [id_sektor_daerah], references: [id_sektor_daerah], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_daerah_value_detail {
  id_sektor_daerah              Int
  id_sektor_nasional_value      Int
  tahun                         Int
  numeric_value                 Float?           @db.Real
  string_value                  String?          @db.VarChar(255)
  date_value                    DateTime?        @db.Timestamp(6)
  id_sektor_daerah_value_detail Int              @id @default(autoincrement())
  tb_sektor_daerah              tb_sektor_daerah @relation(fields: [id_sektor_daerah], references: [id_sektor_daerah], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_nasional {
  id_sektor_nasional          Int                              @id @unique
  deskripsi_singkat           String
  deskripsi                   String
  potensi_pasar               String
  file_icon                   String                           @db.VarChar(255)
  file_image                  String                           @db.VarChar(255)
  status                      Int
  sektor_daerah               tb_sektor_daerah[]
  sektor_daerah_pdrb          tb_sektor_daerah_pdrb[]
  sektor                      tb_sektor_nasional_ref           @relation(fields: [id_sektor_nasional], references: [id_sektor], onDelete: Cascade)
  sektor_nasional_file        tb_sektor_nasional_file[]
  sektor_nasional_insentif    tb_sektor_nasional_insentif[]
  sektor_nasional_kontak      tb_sektor_nasional_kontak[]
  sektor_nasional_pdb         tb_sektor_nasional_pdb[]
  sektor_nasional_sumber_data tb_sektor_nasional_sumber_data[]
  sektor_nasional_tr          tb_sektor_nasional_tr[]
  sektor_nasional_value       tb_sektor_nasional_value[]

  @@schema("public")
}

model tb_sektor_nasional_file {
  id_sektor_nasional_file Int                          @id @default(autoincrement())
  id_sektor_nasional      Int
  tipe                    Int
  jenis                   Int
  nama                    String                       @db.VarChar(255)
  judul                   String                       @db.VarChar(255)
  keterangan              String
  sektor_nasional         tb_sektor_nasional           @relation(fields: [id_sektor_nasional], references: [id_sektor_nasional], onDelete: Cascade)
  sektor_nasional_file_tr tb_sektor_nasional_file_tr[]

  @@schema("public")
}

model tb_sektor_nasional_file_tr {
  id_sektor_nasional_file    Int
  kd_bahasa                  String                  @db.VarChar(2)
  nama                       String                  @db.VarChar(255)
  judul                      String                  @db.VarChar(255)
  keterangan                 String
  id_sektor_nasional_file_tr Int                     @id @default(autoincrement())
  sektor_nasional_file       tb_sektor_nasional_file @relation(fields: [id_sektor_nasional_file], references: [id_sektor_nasional_file], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_nasional_insentif {
  id_sektor_nasional_insentif   Int                                @id @default(autoincrement())
  id_sektor_nasional            Int
  nama                          String                             @db.VarChar(255)
  deskripsi                     String
  status                        Int
  sektor_nasional               tb_sektor_nasional                 @relation(fields: [id_sektor_nasional], references: [id_sektor_nasional], onDelete: Cascade)
  sektor_nasional_insentif_file tb_sektor_nasional_insentif_file[]
  sektor_nasional_insentif_tr   tb_sektor_nasional_insentif_tr[]

  @@schema("public")
}

model tb_sektor_nasional_insentif_file {
  id_sektor_nasional_insentif_file Int                         @id @default(autoincrement())
  id_sektor_nasional_insentif      Int
  tipe                             Int
  jenis                            Int
  nama                             String                      @db.VarChar(255)
  judul                            String                      @db.VarChar(255)
  keterangan                       String
  sektor_nasional_intensif         tb_sektor_nasional_insentif @relation(fields: [id_sektor_nasional_insentif], references: [id_sektor_nasional_insentif], onDelete: Cascade)
  tb_sektor_nasional_insentif_file_tr tb_sektor_nasional_insentif_file_tr[]
  @@schema("public")
}

model tb_sektor_nasional_insentif_file_tr {
  id_sektor_nasional_insentif_file    Int
  kd_bahasa                           String @db.VarChar(2)
  nama                                String @db.VarChar(255)
  judul                               String @db.VarChar(255)
  keterangan                          String
  id_sektor_nasional_insentif_file_tr Int    @id @default(autoincrement())
  sektor_nasional_insentif_file         tb_sektor_nasional_insentif_file @relation(fields: [id_sektor_nasional_insentif_file], references: [id_sektor_nasional_insentif_file], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_nasional_insentif_status {
  id_sektor_nasional_insentif        Int
  status                             Int
  status_proses                      Int
  keterangan                         String?
  created_by                         Int
  created_date                       DateTime  @default(now()) @db.Timestamp(6)
  updated_by                         Int?
  updated_date                       DateTime? @updatedAt @db.Timestamp(6)
  id_sektor_nasional_insentif_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_sektor_nasional_insentif_tr {
  id_sektor_nasional_insentif    Int
  kd_bahasa                      String                      @db.VarChar(2)
  nama                           String                      @db.VarChar(255)
  deskripsi                      String
  id_sektor_nasional_insentif_tr Int                         @id @default(autoincrement())
  sektor_nasional_intensif       tb_sektor_nasional_insentif @relation(fields: [id_sektor_nasional_insentif], references: [id_sektor_nasional_insentif], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_nasional_pdb {
  id_sektor_nasional     Int
  tahun_pdb              Int
  id_sumber_data         Int
  jumlah_pdb             Decimal                        @db.Decimal(19, 0)
  status                 Int
  id_sektor_nasional_pdb Int                            @id @default(autoincrement())
  sektor_nasional        tb_sektor_nasional             @relation(fields: [id_sektor_nasional], references: [id_sektor_nasional], onDelete: Cascade)
  sumber_data            tb_sumber_data @relation(fields: [id_sumber_data], references: [id_sumber_data], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_nasional_pdb_status {
  id_sektor_nasional            Int
  tahun_pdb                     Int
  status                        Int
  status_proses                 Int
  keterangan                    String?
  created_by                    Int
  created_date                  DateTime  @default(now()) @db.Timestamp(6)
  updated_by                    Int?
  updated_date                  DateTime? @updatedAt @db.Timestamp(6)
  id_sektor_nasional_pdb_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_sektor_nasional_ref {
  id_sektor               Int                          @unique
  nama                    String                       @db.VarChar(255)
  icon                    String?                      @db.VarChar(255)
  id_sektor_nasional_ref  Int                          @id @default(autoincrement())
  id_kategori_sektor      Int?
  iconmap                 String?                      @db.VarChar(50)
//   sektor_daerah           tb_sektor_daerah[]
  sektor_nasional         tb_sektor_nasional?
  kategori_sektor         tb_kategori_sektor?          @relation(fields: [id_kategori_sektor], references: [id_kategori_sektor], onDelete: Cascade)
  sektor_nasional_tr      tb_sektor_nasional_ref_tr?
  sektor_nasional_value   tb_sektor_nasional_value[]
  sub_sektor_nasional     tb_sub_sektor_nasional[]
  sub_sektor_nasional_ref tb_sub_sektor_nasional_ref[]

  @@schema("public")
}

model tb_sektor_nasional_ref_tr {
  id_sektor                 Int                    @unique
  kd_bahasa                 String                 @db.VarChar(2)
  nama                      String                 @db.VarChar(255)
  icon                      String?                @db.VarChar(255)
  id_sektor_nasional_ref_tr Int                    @id @default(autoincrement())
  sektor_nasional_ref       tb_sektor_nasional_ref @relation(fields: [id_sektor], references: [id_sektor], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_nasional_status {
  id_sektor_nasional        Int
  status                    Int
  status_proses             Int
  keterangan                String?
  created_by                Int
  created_date              DateTime  @default(now()) @db.Timestamp(6)
  updated_by                Int?
  updated_date              DateTime? @updatedAt @db.Timestamp(6)
  id_sektor_nasional_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_sektor_nasional_sumber_data {
  id_sektor_nasional             Int
  id_sumber_data                 Int
  id_sektor_nasional_sumber_data Int                      @id @default(autoincrement())
  sektor_nasional                tb_sektor_nasional       @relation(fields: [id_sektor_nasional], references: [id_sektor_nasional], onDelete: Cascade)
  sumber_data                    tb_sumber_data           @relation(fields: [id_sumber_data], references: [id_sumber_data], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_nasional_tr {
  id_sektor_nasional    Int
  kd_bahasa             String             @db.VarChar(2)
  deskripsi_singkat     String
  deskripsi             String
  potensi_pasar         String
  file_icon             String             @db.VarChar(255)
  file_image            String             @db.VarChar(255)
  id_sektor_nasional_tr Int                @id @default(autoincrement())
  sektor_nasional       tb_sektor_nasional @relation(fields: [id_sektor_nasional], references: [id_sektor_nasional], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_nasional_value {
  id_sektor_nasional_value     Int                               @id @default(autoincrement())
  id_sektor_nasional           Int
  nama                         String                            @db.VarChar(255)
  tipe                         Int                               @db.SmallInt
  satuan                       String                            @db.VarChar(255)
  sektor_nasional              tb_sektor_nasional                @relation(fields: [id_sektor_nasional], references: [id_sektor_nasional], onDelete: Cascade, map: "unique_constraint_name_tb_sektor_nasional")
  sektor_nasional_ref              tb_sektor_nasional_ref            @relation(fields: [id_sektor_nasional], references: [id_sektor], onDelete: Cascade, map: "unique_constraint_name_tb_sektor_nasional_ref")
  sektor_nasional_value_detail tb_sektor_nasional_value_detail[]
  sektor_nasional_value_tr     tb_sektor_nasional_value_tr[]

  @@schema("public")
}

model tb_sektor_nasional_value_detail {
  id_sektor_nasional_value        Int
  tahun                           Int
  numeric_value                   Float?                   @db.Real
  string_value                    String?                  @db.VarChar(255)
  date_value                      DateTime?                @db.Timestamp(6)
  id_sektor_nasional_value_detail Int                      @id @default(autoincrement())
  sektor_nasional_value           tb_sektor_nasional_value @relation(fields: [id_sektor_nasional_value], references: [id_sektor_nasional_value], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_nasional_value_tr {
  id_sektor_nasional_value    Int
  kd_bahasa                   String                   @db.VarChar(2)
  nama                        String                   @db.VarChar(255)
  satuan                      String                   @db.VarChar(255)
  id_sektor_nasional_value_tr Int                      @id @default(autoincrement())
  sektor_nasional_value       tb_sektor_nasional_value @relation(fields: [id_sektor_nasional_value], references: [id_sektor_nasional_value], onDelete: Cascade)

  @@schema("public")
}

model tb_peluang_daerah {
  id_peluang_daerah           Int                           @id @default(autoincrement())
  id_adm_provinsi             Int?
  id_adm_kabkot               Int?
  id_sub_sektor_daerah        Int
  status_peluang              Int                           @db.SmallInt
  status                      Int                           @db.SmallInt
  id_bandara                  Int?
  jarak_bandara               Int                           @default(0) @db.SmallInt
  id_pelabuhan                Int?
  jarak_pelabuhan             Int                           @default(0) @db.SmallInt
  jarak_ibukota               Int                           @default(0) @db.SmallInt
  kode_kbli                   String?                       @db.VarChar(150)
  judul                       String?                       @db.VarChar(255)
  lokasi                      String?                       @db.VarChar(255)
  tahun                       Int                           @default(2022) @db.SmallInt
  keterangan                  String?
  aspek_pasar                 String?
  aspek_teknis                String?
  lon                         Float?
  lat                         Float?
  zoom_peta_default           Int?
  is_ikn                      Boolean?
  project_status_enum         Int?
  tb_adm_kabkot               tb_adm_kabkot?                @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_adm_provinsi             tb_adm_provinsi?              @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)
  tb_sub_sektor_daerah        tb_sub_sektor_daerah          @relation(fields: [id_sub_sektor_daerah], references: [id_sub_sektor_daerah], onDelete: Cascade)
  tb_peluang_status           tb_peluang_status?          @relation(fields: [project_status_enum], references: [id_peluang_status], onDelete: Cascade)
  tb_peluang_daerah_file      tb_peluang_daerah_file[]
  tb_peluang_daerah_kelayakan tb_peluang_daerah_kelayakan[]
  tb_peluang_daerah_komoditi  tb_peluang_daerah_komoditi[]
  tb_peluang_daerah_kontak    tb_peluang_daerah_kontak[]
  tb_halaman_pengunjung_det   tb_halaman_pengunjung_det[]
  tb_peluang_daerah_tr        tb_peluang_daerah_tr[]
  tb_peluang_daerah_layer_spasial tb_peluang_daerah_layer_spasial[]
  @@schema("public")
}

model tb_peluang_daerah_file {
  id_peluang_daerah_file Int               @id @default(autoincrement())
  id_peluang_daerah      Int
  tipe                   Int
  jenis                  Int
  nama                   String            @db.VarChar(255)
  judul                  String            @db.VarChar(255)
  keterangan             String
  tb_peluang_daerah      tb_peluang_daerah @relation(fields: [id_peluang_daerah], references: [id_peluang_daerah], onDelete: Cascade)
  tb_peluang_daerah_file_tr tb_peluang_daerah_file_tr[]
  @@schema("public")
}

model tb_peluang_daerah_file_tr {
  id_peluang_daerah_file    Int
  kd_bahasa                 String @db.VarChar(2)
  nama                      String @db.VarChar(255)
  judul                     String @db.VarChar(255)
  keterangan                String
  id_peluang_daerah_file_tr Int    @id @default(autoincrement())
  tb_peluang_daerah_file    tb_peluang_daerah_file @relation(fields: [id_peluang_daerah_file], references: [id_peluang_daerah_file], onDelete: Cascade)

  @@schema("public")
}

model tb_peluang_daerah_insentif {
  id_peluang_daerah_insentif  Int  @id @default(autoincrement())
  id_peluang_daerah           Int
  id_sektor_nasional_insentif Int?
  id_sektor_daerah_insentif   Int?

  @@schema("public")
}

model tb_peluang_daerah_kelayakan {
  id_peluang_daerah           Int
  pct_cost_capital            Int               @db.SmallInt
  year_period                 Int               @db.SmallInt
  base_revenue                Float
  pct_revenue_growth          Int               @db.SmallInt
  base_opex                   Float
  pct_inflation               Int               @db.SmallInt
  initial_invesment           Float
  pct_salvage_value           Int               @db.SmallInt
  irr                         Float
  npv                         Float
  pp                          Int               @db.SmallInt
  id_peluang_daerah_kelayakan Int               @id @default(autoincrement())
  tb_peluang_daerah           tb_peluang_daerah @relation(fields: [id_peluang_daerah], references: [id_peluang_daerah], onDelete: Cascade)

  @@schema("public")
}

model tb_peluang_daerah_kelayakan_detail {
  id_peluang_daerah                 Int
  jenis                             Int
  tahun                             Int
  nilai                             Float
  id_peluang_daerah_kelayakan_detai Int   @id @default(autoincrement())

  @@schema("public")
}

model tb_peluang_daerah_komoditi {
  id_peluang_daerah          Int
  id_komoditi_daerah         Int
  jenis                      String             @db.VarChar(255)
  manfaat                    String             @db.VarChar(255)
  id_peluang_daerah_komoditi Int                @id @default(autoincrement())
  tb_komoditi_daerah         tb_komoditi_daerah @relation(fields: [id_komoditi_daerah], references: [id_komoditi_daerah], onDelete: Cascade)
  tb_peluang_daerah          tb_peluang_daerah  @relation(fields: [id_peluang_daerah], references: [id_peluang_daerah], onDelete: Cascade)

  @@schema("public")
}

model tb_peluang_daerah_kontak {
  id_peluang_daerah        Int
  id_peluang_kontak        Int
  id_peluang_daerah_kontak Int               @id @default(autoincrement())
  tb_peluang_daerah        tb_peluang_daerah @relation(fields: [id_peluang_daerah], references: [id_peluang_daerah], onDelete: Cascade)
  tb_peluang_kontak        tb_peluang_kontak @relation(fields: [id_peluang_kontak], references: [id_peluang_kontak], onDelete: Cascade)

  @@schema("public")
}

model tb_peluang_daerah_layer_spasial {
  id_peluang_daerah_layer_spasial Int    @id @default(autoincrement())
  id_peluang_daerah               Int
  nama_layer                      String @db.VarChar(500)
  tipe                            Int
  url_service                     String @db.VarChar(500)
  status                          Int
  peluang_daerah                  tb_peluang_daerah @relation(fields: [id_peluang_daerah], references: [id_peluang_daerah], onDelete: Cascade)

  @@schema("public")
}

model tb_peluang_daerah_status {
  id_peluang_daerah        Int
  status                   Int       @db.SmallInt
  status_proses            Int       @db.SmallInt
  keterangan               String?
  created_by               Int
  created_date             DateTime  @db.Timestamp(6)
  updated_by               Int?
  updated_date             DateTime? @db.Timestamp(6)
  id_peluang_daerah_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_peluang_daerah_tr {
  id_peluang_daerah    Int
  kd_bahasa            String  @db.VarChar(2)
  judul                String? @db.VarChar(255)
  lokasi               String? @db.VarChar(255)
  keterangan           String?
  aspek_pasar          String?
  aspek_teknis         String?
  id_peluang_daerah_tr Int     @id @default(autoincrement())
  tb_peluang_daerah      tb_peluang_daerah @relation(fields: [id_peluang_daerah], references: [id_peluang_daerah], onDelete: Cascade)

  @@schema("public")
}

model tb_peluang_kabkot {
  id_adm_kabkot                       Int
  id_sektor                           Int
  tahun                               Int
  id_sumber_data                      Int?
  id_prioritas                        Int
  id_adm_kabkot_kantor                Int
  nama                                String
  keterangan                          String?
  nilai_investasi                     Float?
  nilai_irr                           Float?
  nilai_npv                           Float?
  nilai_pp                            Float?
  status                              String?
  lon                                 Float?
  lat                                 Float?
  shape                               String?
  id_kontak                           Int?
  nama_singkat                        String?
  deskripsi_singkat                   String?
  deskripsi                           String?
  lokasi_kawasan                      String?
  modified_time                       DateTime?
  kode_kbli                           String?
  zoom_peta_default                   Int?
  project_status_enum                 String?
  is_ikn                              Boolean?
  id_peluang_kabkot                   Int                                   @id @default(autoincrement())
  is_ipro                             Boolean?
  tb_adm_kabkot                       tb_adm_kabkot                         @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_peluang_sektor                   tb_peluang_sektor                     @relation(fields: [id_sektor], references: [id_peluang_sektor], onDelete: Cascade)
  tb_sumber_data                      tb_sumber_data?                       @relation(fields: [id_sumber_data], references: [id_sumber_data], onDelete: Cascade)
  tb_peluang_kabkot_file              tb_peluang_kabkot_file[]
  tb_peluang_kabkot_file_tr           tb_peluang_kabkot_file_tr[]
  tb_peluang_kabkot_insentif          tb_peluang_kabkot_insentif[]
  tb_peluang_kabkot_kebijakan_mapping tb_peluang_kabkot_kebijakan_mapping[]
  tb_peluang_kabkot_kontak            tb_peluang_kabkot_kontak[]
  tb_peluang_kabkot_tr                tb_peluang_kabkot_tr[]
  tb_peluang_layers                   tb_peluang_layers[]
  tb_umkm                             tb_umkm[]
  tb_halaman_pengunjung_det           tb_halaman_pengunjung_det[]

  @@schema("public")
}

model tb_peluang_kabkot_berita_mapping {
  id_peluang_kabkot                Int
  id_berita                        Int
  created_time                     DateTime? @default(now()) @db.Timestamptz(6)
  tb_peluang_kabkot_berita_mapping Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_peluang_kabkot_kebijakan_mapping {
  id_peluang_kabkot                   Int
  id_kebijakan                        Int
  tb_peluang_kabkot_kebijakan_mapping Int               @id @default(autoincrement())
  tb_kebijakan                        tb_kebijakan      @relation(fields: [id_kebijakan], references: [id_kebijakan], onDelete: Cascade)
  tb_peluang_kabkot                   tb_peluang_kabkot @relation(fields: [id_peluang_kabkot], references: [id_peluang_kabkot], onDelete: Cascade)

  @@schema("public")
}

model tb_peluang_kabkot_file {
  id_peluang_kabkot_file Int               @id @default(autoincrement())
  id_peluang_kabkot      Int
  tipe                   Int
  nama                   String?           @db.VarChar(255)
  url_rest               String?           @db.VarChar(255)
  status                 Int?              @default(0)
  tb_peluang_kabkot      tb_peluang_kabkot @relation(fields: [id_peluang_kabkot], references: [id_peluang_kabkot], onDelete: Cascade)

  @@schema("public")
}

model tb_peluang_kabkot_file_tr {
  id_peluang_kabkot_file_tr Int               @id @default(autoincrement())
  id_peluang_kabkot         Int
  kd_bahasa                 String            @db.VarChar(2)
  tipe                      Int
  nama                      String?           @db.VarChar(255)
  url_rest                  String?           @db.VarChar(255)
  status                    Int?              @default(0)
  tb_peluang_kabkot         tb_peluang_kabkot @relation(fields: [id_peluang_kabkot], references: [id_peluang_kabkot], onDelete: Cascade)

  @@schema("public")
}

model tb_peluang_kabkot_insentif {
  id_peluang_kabkot          Int
  id_jenis_insentif          Int
  id_peluang_kabkot_insentif Int               @id @default(autoincrement())
  tb_jenis_insentif          tb_jenis_insentif @relation(fields: [id_jenis_insentif], references: [id_jenis_insentif], onDelete: Cascade)
  tb_peluang_kabkot          tb_peluang_kabkot @relation(fields: [id_peluang_kabkot], references: [id_peluang_kabkot], onDelete: Cascade)

  @@schema("public")
}

model tb_peluang_kabkot_kontak {
  id_peluang_kabkot        Int
  id_peluang_kontak        Int
  id_peluang_kabkot_kontak Int               @id @default(autoincrement())
  tb_peluang_kabkot        tb_peluang_kabkot @relation(fields: [id_peluang_kabkot], references: [id_peluang_kabkot], onDelete: Cascade)
  tb_peluang_kontak        tb_peluang_kontak @relation(fields: [id_peluang_kontak], references: [id_peluang_kontak], onDelete: Cascade)

  @@schema("public")
}

model tb_peluang_kabkot_likes_counter {
  id_likes          Int       @id @default(autoincrement())
  id_peluang_kabkot Int
  ip_pengunjung     String    @db.VarChar(15)
  created_time      DateTime? @default(now()) @db.Timestamptz(6)

  @@schema("public")
}
model tb_peluang_daerah_likes_counter {
  id_likes          Int       @id @default(autoincrement())
  id_peluang_daerah Int
  ip_pengunjung     String    @db.VarChar(15)
  created_time      DateTime? @default(now()) @db.Timestamptz(6)

  @@schema("public")
}

model tb_peluang_kabkot_status {
  id_peluang_kabkot        Int
  status                   Int
  status_proses            Int
  keterangan               String?
  created_by               Int
  created_date             DateTime  @default(now()) @db.Timestamp(6)
  updated_by               Int?
  updated_date             DateTime? @updatedAt @db.Timestamp(6)
  id_peluang_kabkot_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_peluang_kabkot_tr {
  id_peluang_kabkot_tr Int               @id @default(autoincrement())
  id_peluang_kabkot    Int
  kd_bahasa            String            @db.VarChar(2)
  nama                 String            @db.VarChar(255)
  keterangan           String
  nama_singkat         String?           @db.VarChar(36)
  deskripsi_singkat    String?           @db.VarChar(120)
  deskripsi            String?           @db.VarChar(600)
  lokasi_kawasan       String?           @db.VarChar(255)
  tb_peluang_kabkot    tb_peluang_kabkot @relation(fields: [id_peluang_kabkot], references: [id_peluang_kabkot], onDelete: Cascade)

  @@schema("public")
}

model tb_peluang_kontak {
  id_peluang_kontak        Int                        @id @default(autoincrement())
  nama                     String?                    @db.VarChar(255)
  nama_tr                     String?                    @db.VarChar(255)
  alamat                   String?                    @db.VarChar(255)
  url_web                  String?                    @db.VarChar(255)
  cp                       String?                    @db.VarChar(255)
  jabatan_cp               String?                    @db.VarChar(255)
  email                    String?                    @db.VarChar(255)
  no_telp                  String?                    @db.VarChar(255)
  no_fax                   String?                    @db.VarChar(255)
  lon                      Float?
  lat                      Float?
  id_adm_provinsi          Int?
  id_adm_kabkot            Int?
  tb_peluang_daerah_kontak tb_peluang_daerah_kontak[]
  tb_peluang_kabkot_kontak tb_peluang_kabkot_kontak[]

  @@schema("public")
}

model tb_peluang_prioritas {
  id_peluang_prioritas Int    @id @default(autoincrement())
  nama                 String @db.VarChar(50)
  keterangan           String

  @@schema("public")
}

model tb_peluang_prioritas_tr {
  id_peluang_prioritas_tr Int     @id @default(autoincrement())
  id_peluang_prioritas    Int
  kd_bahasa               String  @db.VarChar(2)
  nama                    String? @db.VarChar(50)
  keterangan              String

  @@schema("public")
}

model tb_peluang_provinsi_file {
  id_peluang_provinsi_file Int    @id @default(autoincrement())
  id_peluang_provinsi      Int
  tipe                     Int
  nama                     String @db.VarChar(255)

  @@schema("public")
}

model tb_peluang_provinsi_status {
  id_peluang_provinsi        Int
  status                     Int
  status_proses              Int
  keterangan                 String
  created_by                 Int
  created_date               DateTime  @db.Timestamp(6)
  updated_by                 Int?
  updated_date               DateTime? @db.Timestamp(6)
  id_peluang_provinsi_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_peluang_provinsi_tr {
  id_peluang_provinsi_tr Int    @id @default(autoincrement())
  id_peluang_provinsi    Int
  kd_bahasa              String @db.VarChar(2)
  nama                   String @db.VarChar(255)
  keterangan             String

  @@schema("public")
}

model tb_peluang_sektor {
  id_peluang_sektor    Int                    @unique @default(autoincrement())
  nama                 String                 @id @db.VarChar(50)
  keterangan           String
  icon                 String?                @db.VarChar(255)
  iconmap              String?                @db.VarChar(255)
  image                String?                @db.VarChar(255)
  id_kategori_sektor   Int?
  tb_peluang_kabkot    tb_peluang_kabkot[]
  kategori_sektor      tb_kategori_sektor?    @relation(fields: [id_kategori_sektor], references: [id_kategori_sektor], onDelete: Cascade)
  tb_peluang_sektor_tr tb_peluang_sektor_tr[]
  tb_umkm              tb_umkm[]

  @@schema("public")
}

model tb_peluang_sektor_tr {
  id_peluang_sektor_tr Int               @id @default(autoincrement())
  id_peluang_sektor    Int
  kd_bahasa            String            @db.VarChar(2)
  nama                 String            @db.VarChar(50)
  keterangan           String
  tb_peluang_sektor    tb_peluang_sektor @relation(fields: [id_peluang_sektor], references: [id_peluang_sektor], onDelete: Cascade)

  @@schema("public")
}

model tb_adm_provinsi {
  id_adm_provinsi        Int                      @id
  id_adm_wilayah         Int
  kd_adm                 String
  nama                   String
  nama_ibukota           String
  file_logo              String?
  file_image             String?
  deskripsi              String?
  luas_wilayah           Float?
  jumlah_penduduk        Int?
  alamat                 String?
  no_telp                String?
  no_fax                 String?
  url_web                String?
  lon                    Float?
  lat                    Float?
  shape                  String?
  id_zona_waktu          Int
  tb_adm_kabkot          tb_adm_kabkot[]
  tb_adm_wilayah         tb_adm_wilayah           @relation(fields: [id_adm_wilayah], references: [id_adm_wilayah], onDelete: Cascade)
  tb_zona_waktu          tb_zona_waktu            @relation(fields: [id_zona_waktu], references: [id_zona_waktu], onDelete: Cascade)
  tb_adm_provinsi_kantor tb_adm_provinsi_kantor[]
  tb_adm_provinsi_tr     tb_adm_provinsi_tr[]
  tb_demografi_provinsi  tb_demografi_provinsi[]
  tb_investasi_provinsi  tb_investasi_provinsi[]
  tb_komoditi_provinsi   tb_komoditi_provinsi[]
  tb_news                tb_news[]
  tb_peluang_daerah      tb_peluang_daerah[]
  sektor_daerah          tb_sektor_daerah[]
  tb_sektor_daerah_pdrb  tb_sektor_daerah_pdrb[]
  tb_umkm                tb_umkm[]
  tb_umr_provinsi        tb_umr_provinsi[]
  tb_utilitas_provinsi   tb_utilitas_provinsi[]
  tb_sumber_data_instansi   tb_sumber_data_instansi[]
  tb_user_internal_provinsi   tb_user_internal_provinsi[]
  tb_ekspor_provinsi tb_ekspor_provinsi[]

  @@schema("public")
}

model tb_adm_provinsi_tr {
  id_adm_provinsi_tr Int             @id @default(autoincrement())
  id_adm_provinsi    Int
  kd_bahasa          String          @db.VarChar(2)
  nama               String          @db.VarChar(50)
  nama_ibukota       String          @db.VarChar(50)
  file_logo          String          @db.VarChar(255)
  tb_adm_provinsi    tb_adm_provinsi @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)

  @@schema("public")
}

model tb_adm_provinsi_kantor {
  id_adm_provinsi_kantor Int             @id @default(autoincrement())
  jenis                  String          @db.VarChar(20)
  id_adm_provinsi        Int
  nama_pic               String          @db.VarChar(255)
  file_foto_pic          String          @db.VarChar(255)
  nama                   String          @db.VarChar(255)
  alamat                 String          @db.VarChar(255)
  no_telp                String          @db.VarChar(255)
  no_fax                 String          @db.VarChar(255)
  url_web                String          @db.VarChar(255)
  status                 Int
  lon                    Float?
  lat                    Float?
  created_by             Int
  created_date           DateTime        @db.Timestamp(6)
  updated_by             Int?
  updated_date           DateTime?       @db.Timestamp(6)
  tb_adm_provinsi        tb_adm_provinsi @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)

  @@schema("public")
}

model tb_adm_provinsi_layer_spasial {
  id_adm_provinsi_layer_spasial Int      @id @default(autoincrement())
  id_adm_provinsi               Int
  nama_layer                    String   @db.VarChar(500)
  tipe                          Int
  url_service                   String   @db.VarChar(500)
  status                        Int
  is_active                     Boolean?

  @@schema("public")
}

model tb_adm_kabkot {
  id_adm_kabkot         Int                     @id @default(autoincrement())
  id_adm_provinsi       Int
  kd_adm                String
  jenis                 String
  nama                  String
  nama_ibukota          String
  file_logo             String?
  file_image            String?
  deskripsi             String?
  luas_wilayah          Float?
  jumlah_penduduk       Int?
  alamat                String?
  no_telp               String?
  no_fax                String?
  url_web               String?
  lon                   Float?
  lat                   Float?
  shape                 String?
  is_daerah_tertinggal  Boolean                 @default(false)
  tb_adm_provinsi       tb_adm_provinsi         @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)
  tb_adm_kabkot_tr      tb_adm_kabkot_tr[]
  tb_bandara            tb_bandara[]            @relation("adm_kabkot_bandara")
  tb_hotel              tb_hotel[]              @relation("AdmKabkotToHotel")
  tb_kawasan_industri   tb_kawasan_industri[]
  tb_komoditi_kabkot    tb_komoditi_kabkot[]
  tb_news               tb_news[]
  tb_pelabuhan          tb_pelabuhan[]          @relation("adm_kabkot_pelabuhan")
  tb_peluang_daerah     tb_peluang_daerah[]
  tb_peluang_kabkot     tb_peluang_kabkot[]
  tb_pendidikan         tb_pendidikan[]         @relation("pendidikan_adm_kabkot")
  tb_rumah_sakit        tb_rumah_sakit[]        @relation("RSToAdmKabkot")
  tb_sektor_daerah      tb_sektor_daerah[]
  tb_sektor_daerah_pdrb tb_sektor_daerah_pdrb[]
  tb_umkm               tb_umkm[]
  tb_utilitas_kabkot    tb_utilitas_kabkot[]
  tb_sumber_data_instansi    tb_sumber_data_instansi[]
  tb_investasi_kabkot   tb_investasi_kabkot[]
  tb_demografi_kabkot   tb_demografi_kabkot[]
  tb_umr_kabkot tb_umr_kabkot[]
  tb_ekspor_kabkot tb_ekspor_kabkot[]
  tb_adm_kabkot_kantor tb_adm_kabkot_kantor[]
  tb_listrik    tb_listrik[]
  tb_gas    tb_gas[]
  tb_air    tb_air[]


  @@schema("public")
}

model tb_adm_kabkot_alias {
  id_adm_kabkot          Int
  nama_mineral_logam     String? @db.VarChar(50)
  nama_mineral_non_logam String? @db.VarChar(50)
  nama_batubara          String? @db.VarChar(50)
  nama_panas_bumi        String? @db.VarChar(50)
  id_adm_kabkot_alias    Int     @id @default(autoincrement())

  @@schema("public")
}

model tb_adm_kabkot_kantor {
  id_adm_kabkot_kantor Int       @id @default(autoincrement())
  jenis                String    @db.VarChar(20)
  id_adm_kabkot        Int
  nama_pic             String    @db.VarChar(255)
  file_foto_pic        String    @db.VarChar(255)
  nama                 String    @db.VarChar(255)
  alamat               String    @db.VarChar(255)
  no_telp              String    @db.VarChar(255)
  no_fax               String    @db.VarChar(255)
  url_web              String    @db.VarChar(255)
  status               Int
  lon                  Float?
  lat                  Float?
  created_by           Int
  created_date         DateTime  @db.Timestamp(6)
  updated_by           Int?
  updated_date         DateTime? @db.Timestamp(6)

  tb_adm_kabkot                 tb_adm_kabkot?                  @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)

  @@schema("public")
}

model tb_adm_kabkot_layer_spasial {
  id_adm_kabkot_layer_spasial Int      @id @default(autoincrement())
  id_adm_kabkot               Int
  nama_layer                  String   @db.VarChar(500)
  tipe                        Int
  url_service                 String   @db.VarChar(500)
  status                      Int
  is_active                   Boolean?

  @@schema("public")
}

model tb_adm_kabkot_tr {
  id_adm_kabkot_tr Int            @id @default(autoincrement())
  id_adm_kabkot    Int?
  kd_bahasa        String?        @db.VarChar(2)
  nama             String?        @db.VarChar(50)
  nama_ibukota     String?        @db.VarChar(50)
  file_logo        String?        @db.VarChar(255)
  tb_adm_kabkot    tb_adm_kabkot? @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)

  @@schema("public")
}

model tb_adm_wilayah {
  id_adm_wilayah  Int               @id @default(autoincrement())
  nama            String            @db.VarChar(50)
  deskripsi       String            @db.VarChar(255)
  index           Int
  tb_adm_provinsi tb_adm_provinsi[]

  @@schema("public")
}

model tb_adm_wilayah_tr {
  id_adm_wilayah_tr Int    @id @default(autoincrement())
  id_adm_wilayah    Int
  kd_bahasa         String @db.VarChar(2)
  nama              String @db.VarChar(50)
  deskripsi         String @db.VarChar(255)

  @@schema("public")
}

model tb_umr_kabkot {
  id_umr_kabkot  Int   @id @default(autoincrement())
  id_adm_kabkot  Int
  tahun          Int
  id_sumber_data Int?
  nilai          Float
  status         Int

  tb_adm_kabkot                 tb_adm_kabkot?                  @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)

  @@schema("public")
}

model tb_umr_kabkot_status {
  id_umr_kabkot        Int
  status               Int
  status_proses        Int
  keterangan           String
  created_by           Int
  created_date         DateTime  @db.Timestamp(6)
  updated_by           Int?
  updated_date         DateTime? @db.Timestamp(6)
  id_umr_kabkot_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_umr_provinsi {
  id_umr_provinsi Int             @id @default(autoincrement())
  id_adm_provinsi Int
  tahun           Int
  id_sumber_data  Int?
  nilai           Float
  status          Int
  tb_adm_provinsi tb_adm_provinsi @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)
  tb_sumber_data  tb_sumber_data? @relation(fields: [id_sumber_data], references: [id_sumber_data], onDelete: Cascade)
  // tb_umr_provinsi_status tb_umr_provinsi_status[]
  @@schema("public")
}

model tb_umr_provinsi_status {
  id_umr_provinsi        Int
  status                 Int
  status_proses          Int
  keterangan             String
  created_by             Int
  created_date           DateTime  @db.Timestamp(6)
  updated_by             Int?
  updated_date           DateTime? @db.Timestamp(6)
  id_umr_provinsi_status Int       @id @default(autoincrement())
  // tb_umr_provinsi tb_umr_provinsi @relation(fields: [id_umr_provinsi], references: [id_umr_provinsi], onDelete: Cascade)

  @@schema("public")
}

model tb_kawasan_industri {
  id_kawasan_industri           Int                            @id @default(autoincrement())
  id_adm_kabkot                 Int
  id_sumber_data                Int?
  id_kategori                   Int?
  nama                          String                         @db.VarChar(255)
  keterangan                    String?
  alamat                        String?                        @db.VarChar(255)
  luas                          Float?
  luas_satuan                   String?                        @db.VarChar(50)
  id_bandara_terdekat           Int?
  jarak_bandara_terdekat        Float?
  id_pelabuhan_terdekat         Int?
  jarak_pelabuhan_terdekat      Float?
  jarak_ibukota                 Float?
  url_web                       String?                        @db.VarChar(255)
  no_telp                       String?                        @db.VarChar(50)
  no_fax                        String?                        @db.VarChar(50)
  email                         String?                        @db.VarChar(255)
  cp                            String?                        @db.VarChar(255)
  ketersediaan                  String?                        @db.VarChar(50)
  status                        String?                        @db.VarChar(50)
  lon                           Float?
  lat                           Float?
  shape                         String?
  is_ikn                        Boolean?
  id_kawasan_industri_ref_range Int?
  major_tenants                 String?
  id_kawasan_industri_occupancy Int?
  tb_adm_kabkot                 tb_adm_kabkot                  @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_bandara                    tb_bandara?                    @relation(fields: [id_bandara_terdekat], references: [id_bandara], onDelete: Cascade)
  tb_kawasan_industri_kategori  tb_kawasan_industri_kategori?  @relation(fields: [id_kategori], references: [id_kawasan_industri_kategori], onDelete: Cascade)
  tb_kawasan_industri_occupancy tb_kawasan_industri_occupancy? @relation(fields: [id_kawasan_industri_occupancy], references: [id_kawasan_industri_occupancy], onDelete: Cascade)
  tb_kawasan_industri_ref_range tb_kawasan_industri_ref_range? @relation(fields: [id_kawasan_industri_ref_range], references: [id_kawasan_industri_ref_range], onDelete: Cascade)
  tb_pelabuhan                  tb_pelabuhan?                  @relation(fields: [id_pelabuhan_terdekat], references: [id_pelabuhan], onDelete: Cascade)
  tb_kawasan_industri_blok      tb_kawasan_industri_blok[]
  tb_kawasan_industri_file      tb_kawasan_industri_file[]
  tb_kawasan_industri_peluang   tb_kawasan_industri_peluang[]
  tb_kawasan_layers             tb_kawasan_layers[]
  tb_kawasan_industri_tr        tb_kawasan_industri_tr[]
  tb_kawasan_industri_status    tb_kawasan_industri_status[]
  tb_user_internal_kawasan_industri         tb_user_internal_kawasan_industri[]

  @@schema("public")
}

model tb_demografi_kabkot {
  id_demografi_kabkot  Int   @id @default(autoincrement())
  id_adm_kabkot        Int
  tahun                Int
  id_kategori          Int
  id_sumber_data       Int?
  jumlah_pria          Int
  jumlah_wanita        Int
  kepadatan_penduduk   Float
  pertumbuhan_penduduk Float
  status               Int
  tb_adm_kabkot                 tb_adm_kabkot                  @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_demografi_kategori         tb_demografi_kategori          @relation(fields: [id_kategori], references: [id_demografi_kategori], onDelete: Cascade)

  @@schema("public")
}

model tb_demografi_kabkot_status {
  id_demografi_kabkot_status Int       @id @default(autoincrement())
  id_demografi_kabkot        Int
  status                     Int
  status_proses              Int
  keterangan                 String
  created_by                 Int
  created_date               DateTime  @db.Timestamp(6)
  updated_by                 Int?
  updated_date               DateTime? @db.Timestamp(6)

  @@schema("public")
}

model tb_demografi_kategori {
  id_demografi_kategori Int                     @id @default(autoincrement())
  nama                  String                  @db.VarChar(255)
  warna                 String?                 @db.VarChar(7)
  tb_demografi_provinsi tb_demografi_provinsi[]
  tb_demografi_kabkot   tb_demografi_kabkot[]

  @@schema("public")
}

model tb_demografi_kategori_tr {
  id_demografi_kategori_tr Int    @id @default(autoincrement())
  id_demografi_kategori    Int
  kd_bahasa                String @db.VarChar(2)
  nama                     String @db.VarChar(255)

  @@schema("public")
}

model tb_demografi_provinsi {
  id_demografi_provinsi Int                   @id @default(autoincrement())
  id_adm_provinsi       Int
  tahun                 Int
  id_kategori           Int
  id_sumber_data        Int?
  jumlah_pria           Int
  jumlah_wanita         Int
  kepadatan_penduduk    Float
  pertumbuhan_penduduk  Float
  status                Int
  tb_adm_provinsi       tb_adm_provinsi       @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)
  tb_demografi_kategori tb_demografi_kategori @relation(fields: [id_kategori], references: [id_demografi_kategori], onDelete: Cascade)
  tb_demografi_provinsi_status tb_demografi_provinsi_status[]
  @@schema("public")
}

model tb_demografi_provinsi_status {
  id_demografi_provinsi_status Int       @id @default(autoincrement())
  id_demografi_provinsi        Int
  status                       Int
  status_proses                Int
  keterangan                   String
  created_by                   Int
  created_date                 DateTime  @db.Timestamp(6)
  updated_by                   Int?
  updated_date                 DateTime? @db.Timestamp(6)
  tb_demografi_provinsi tb_demografi_provinsi @relation(fields: [id_demografi_provinsi], references: [id_demografi_provinsi], onDelete: Cascade)

  @@schema("public")
}

model tb_investasi_provinsi {
  id_investasi_provinsi   Int                     @id @default(autoincrement())
  id_adm_provinsi         Int
  id_sumber_data          Int?
  id_sektor               Int
  id_jenis                Int
  id_jenis_data           Int
  tahun                   Int
  jumlah_proyek           Int
  jumlah_investasi        Float
  status                  Int
  tb_adm_provinsi         tb_adm_provinsi         @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)
  tb_investasi_jenis_data tb_investasi_jenis_data @relation(fields: [id_jenis_data], references: [id_investasi_jenis_data], onDelete: Cascade)
  tb_investasi_jenis      tb_investasi_jenis      @relation(fields: [id_jenis], references: [id_investasi_jenis], onDelete: Cascade)
  tb_investasi_sektor     tb_investasi_sektor     @relation(fields: [id_sektor], references: [id_investasi_sektor], onDelete: Cascade)
  tb_sumber_data          tb_sumber_data?          @relation(fields: [id_sumber_data], references: [id_sumber_data])

  @@schema("public")
}

model tb_investasi_jenis {
  id_investasi_jenis  Int                     @id @default(autoincrement())
  nama                String?                 @db.VarChar(50)
  keterangan          String
  tb_investasi_kabkot tb_investasi_kabkot[]
  tb_investasi_       tb_investasi_provinsi[]

  @@schema("public")
}

model tb_komoditi {
  id_komoditi Int      @id
  id_sektor   Int
  nama        String?  @db.VarChar(50)
  keterangan  String
  warna       String?  @db.VarChar(7)
  is_internal Boolean?
  url_service String?  @db.VarChar(255)

  @@schema("public")
}

model tb_komoditi_daerah {
  id_komoditi_daerah         Int                          @id @default(autoincrement())
  id_komoditi                Int                          
  id_sub_sektor_daerah       Int                          
  deskripsi_singkat          String
  deskripsi                  String
  file_icon                  String                       @db.VarChar(255)
  sentra_produksi            String?                      @db.VarChar(500)
  status                     Int
  tb_komoditi_nasional_ref   tb_komoditi_nasional_ref?     @relation(fields: [id_komoditi], references: [id_komoditi], onDelete: Cascade)
  tb_sub_sektor_daerah       tb_sub_sektor_daerah?         @relation(fields: [id_sub_sektor_daerah], references: [id_sub_sektor_daerah], onDelete: Cascade)
  tb_komoditi_daerah_file    tb_komoditi_daerah_file[]
  tb_komoditi_daerah_value   tb_komoditi_daerah_value[]
  tb_peluang_daerah_komoditi tb_peluang_daerah_komoditi[]
  tb_komoditi_daerah_tr tb_komoditi_daerah_tr[]

  @@unique([id_komoditi, id_sub_sektor_daerah])
  @@schema("public")
}

model tb_komoditi_daerah_file {
  id_komoditi_daerah_file Int                @id @default(autoincrement())
  id_komoditi_daerah      Int
  tipe                    Int
  jenis                   Int
  nama                    String             @db.VarChar(255)
  judul                   String             @db.VarChar(255)
  keterangan              String
  tb_komoditi_daerah      tb_komoditi_daerah @relation(fields: [id_komoditi_daerah], references: [id_komoditi_daerah], onDelete: Cascade)
  tb_komoditi_daerah_file_tr tb_komoditi_daerah_file_tr[]
  @@schema("public")
}

model tb_komoditi_daerah_file_tr {
  id_komoditi_daerah_file    Int
  kd_bahasa                  String @db.VarChar(2)
  nama                       String @db.VarChar(255)
  judul                      String @db.VarChar(255)
  keterangan                 String
  id_komoditi_daerah_file_tr Int    @id @default(autoincrement())
  tb_komoditi_daerah_file    tb_komoditi_daerah_file @relation(fields: [id_komoditi_daerah_file], references: [id_komoditi_daerah_file], onDelete: Cascade)

  @@schema("public")
}

model tb_komoditi_daerah_status {
  id_komoditi_daerah        Int
  status                    Int
  status_proses             Int
  keterangan                String?
  created_by                Int
  created_date              DateTime  @db.Timestamp(6)
  updated_by                Int?
  updated_date              DateTime? @db.Timestamp(6)
  id_komoditi_daerah_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_komoditi_daerah_tr {
  id_komoditi_daerah    Int
  kd_bahasa             String  @db.VarChar(2)
  deskripsi_singkat     String
  deskripsi             String
  file_icon             String  @db.VarChar(255)
  sentra_produksi       String? @db.VarChar(500)
  id_komoditi_daerah_tr Int     @id @default(autoincrement())
  tb_komoditi_daerah    tb_komoditi_daerah                @relation(fields: [id_komoditi_daerah], references: [id_komoditi_daerah], onDelete: Cascade)

  @@schema("public")
}

model tb_komoditi_daerah_value {
  id_komoditi_daerah_value        Int                               @id @default(autoincrement())
  id_komoditi_daerah              Int
  nama                            String                            @db.VarChar(255)
  satuan                          String                            @db.VarChar(255)
  tipe                            Int                               @db.SmallInt
  tb_komoditi_daerah              tb_komoditi_daerah                @relation(fields: [id_komoditi_daerah], references: [id_komoditi_daerah], onDelete: Cascade)
  tb_komoditi_daerah_value_tr tb_komoditi_daerah_value_tr[]
  tb_komoditi_daerah_value_detail tb_komoditi_daerah_value_detail[]

  @@schema("public")
}

model tb_komoditi_daerah_value_detail {
  id_komoditi_daerah_value        Int
  tahun                           Int
  numeric_value                   Float?                   @db.Real
  string_value                    String?                  @db.VarChar(255)
  date_value                      DateTime?                @db.Timestamp(6)
  id_komoditi_daerah_value_detail Int                      @id @default(autoincrement())
  tb_komoditi_daerah_value        tb_komoditi_daerah_value @relation(fields: [id_komoditi_daerah_value], references: [id_komoditi_daerah_value], onDelete: Cascade)

  @@schema("public")
}

model tb_komoditi_daerah_value_tr {
  id_komoditi_daerah_value    Int
  kd_bahasa                   String @db.VarChar(2)
  nama                        String @db.VarChar(255)
  satuan                      String @db.VarChar(255)
  id_komoditi_daerah_value_tr Int    @id @default(autoincrement())
  tb_komoditi_daerah_value              tb_komoditi_daerah_value                @relation(fields: [id_komoditi_daerah_value], references: [id_komoditi_daerah_value], onDelete: Cascade)
  

  @@schema("public")
}

model tb_komoditi_kabkot_status {
  id_komoditi_kabkot        Int
  status                    Int
  status_proses             Int
  keterangan                String
  created_by                Int
  created_date              DateTime  @db.Timestamp(6)
  updated_by                Int?
  updated_date              DateTime? @db.Timestamp(6)
  id_komoditi_kabkot_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_komoditi_nasional {
  id_komoditi_nasional    Int                          @id @default(autoincrement())
  id_sub_sektor_nasional  Int
  id_komoditi             Int
  deskripsi_singkat       String
  deskripsi               String
  file_icon               String                       @db.VarChar(255)
  status                  Int
  komoditi_nasional_ref   tb_komoditi_nasional_ref     @relation(fields: [id_komoditi], references: [id_komoditi], onDelete: Cascade)
  sub_sektor              tb_sub_sektor_nasional       @relation(fields: [id_sub_sektor_nasional], references: [id_sub_sektor_nasional], onDelete: Cascade)
  komoditi_nasional_file  tb_komoditi_nasional_file[]
  komoditi_nasional_tr    tb_komoditi_nasional_tr[]
  komoditi_nasional_value tb_komoditi_nasional_value[]

  @@schema("public")
}

model tb_komoditi_nasional_file {
  id_komoditi_nasional_file Int                            @id @default(autoincrement())
  id_komoditi_nasional      Int
  tipe                      Int
  jenis                     Int
  nama                      String                         @db.VarChar(255)
  judul                     String                         @db.VarChar(255)
  keterangan                String
  komoditi_nasional         tb_komoditi_nasional           @relation(fields: [id_komoditi_nasional], references: [id_komoditi_nasional], onDelete: Cascade)
  komoditi_nasional_file_tr tb_komoditi_nasional_file_tr[]

  @@schema("public")
}

model tb_komoditi_nasional_file_tr {
  id_komoditi_nasional_file    Int
  kd_bahasa                    String                    @db.VarChar(2)
  nama                         String                    @db.VarChar(255)
  judul                        String                    @db.VarChar(255)
  keterangan                   String
  id_komoditi_nasional_file_tr Int                       @id @default(autoincrement())
  komoditi_nasional_file       tb_komoditi_nasional_file @relation(fields: [id_komoditi_nasional_file], references: [id_komoditi_nasional_file], onDelete: Cascade)

  @@schema("public")
}

model tb_komoditi_nasional_ref {
  id_komoditi                 Int                          @id @default(autoincrement())
  id_sub_sektor               Int
  nama                        String                       @db.VarChar(255)
  tb_komoditi_daerah          tb_komoditi_daerah[]
  tb_komoditi_kabkot          tb_komoditi_kabkot[]
  komoditi_nasional           tb_komoditi_nasional[]
  tb_sub_sektor_nasional_ref  tb_sub_sektor_nasional_ref   @relation(fields: [id_sub_sektor], references: [id_sub_sektor], onDelete: Cascade)
  tb_komoditi_nasional_ref_tr tb_komoditi_nasional_ref_tr?
  tb_komoditi_provinsi        tb_komoditi_provinsi[]
  tb_news                     tb_news[]
  tb_roadmap                  tb_roadmap[]
  tb_app_slider               tb_app_slider[]

  @@schema("public")
}

model tb_komoditi_nasional_ref_tr {
  id_komoditi           Int                      @id @default(autoincrement())
  kd_bahasa             String                   @db.VarChar(2)
  nama                  String                   @db.VarChar(255)
  komoditi_nasional_ref tb_komoditi_nasional_ref @relation(fields: [id_komoditi], references: [id_komoditi], onDelete: Cascade)

  @@schema("public")
}

model tb_komoditi_nasional_status {
  id_komoditi_nasional        Int
  status                      Int
  status_proses               Int
  keterangan                  String?
  created_by                  Int
  created_date                DateTime  @db.Timestamp(6)
  updated_by                  Int?
  updated_date                DateTime? @db.Timestamp(6)
  id_komoditi_nasional_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_komoditi_nasional_tr {
  id_komoditi_nasional    Int
  kd_bahasa               String               @db.VarChar(2)
  deskripsi_singkat       String
  deskripsi               String
  file_icon               String               @db.VarChar(255)
  id_komoditi_nasional_tr Int                  @id @default(autoincrement())
  komoditi_nasional       tb_komoditi_nasional @relation(fields: [id_komoditi_nasional], references: [id_komoditi_nasional], onDelete: Cascade)

  @@schema("public")
}

model tb_komoditi_nasional_value {
  id_komoditi_nasional_value     Int                                 @id @default(autoincrement())
  id_komoditi_nasional           Int
  nama                           String                              @db.VarChar(255)
  satuan                         String                              @db.VarChar(255)
  tipe                           Int                                 @db.SmallInt
  komoditi_nasional              tb_komoditi_nasional                @relation(fields: [id_komoditi_nasional], references: [id_komoditi_nasional], onDelete: Cascade)
  komoditi_nasional_value_detail tb_komoditi_nasional_value_detail[]
  komoditi_nasional_value_tr     tb_komoditi_nasional_value_tr[]

  @@schema("public")
}

model tb_komoditi_nasional_value_detail {
  id_komoditi_nasional_value        Int
  tahun                             Int
  numeric_value                     Float?                     @db.Real
  string_value                      String?                    @db.VarChar(255)
  date_value                        DateTime?                  @db.Timestamp(6)
  id_komoditi_nasional_value_detail Int                        @id @default(autoincrement())
  komoditi_nasional_value           tb_komoditi_nasional_value @relation(fields: [id_komoditi_nasional_value], references: [id_komoditi_nasional_value], onDelete: Cascade)

  @@schema("public")
}

model tb_komoditi_nasional_value_tr {
  id_komoditi_nasional_value    Int
  kd_bahasa                     String                     @db.VarChar(2)
  nama                          String                     @db.VarChar(255)
  satuan                        String                     @db.VarChar(255)
  id_komoditi_nasional_value_tr Int                        @id @default(autoincrement())
  komoditi_nasional_value       tb_komoditi_nasional_value @relation(fields: [id_komoditi_nasional_value], references: [id_komoditi_nasional_value], onDelete: Cascade)

  @@schema("public")
}

model tb_komoditi_provinsi_status {
  id_komoditi_provinsi        Int
  status                      Int
  status_proses               Int
  keterangan                  String
  created_by                  Int
  created_date                DateTime  @db.Timestamp(6)
  updated_by                  Int?
  updated_date                DateTime? @db.Timestamp(6)
  id_komoditi_provinsi_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_komoditi_satuan {
  id_komoditi_satuan   Int                    @id @default(autoincrement())
  nama                 String                 @db.VarChar(255)
  tb_komoditi_kabkot   tb_komoditi_kabkot[]
  tb_komoditi_provinsi tb_komoditi_provinsi[]

  @@schema("public")
}

model tb_komoditi_sektor {
  id_komoditi_sektor Int     @id @default(autoincrement())
  nama               String? @db.VarChar(50)
  keterangan         String

  @@schema("public")
}

model tb_komoditi_sektor_tr {
  id_komoditi_sektor_tr Int     @id @default(autoincrement())
  id_komoditi_sektor    Int
  kd_bahasa             String  @db.VarChar(2)
  nama                  String? @db.VarChar(50)
  keterangan            String

  @@schema("public")
}

model tb_komoditi_tr {
  id_komoditi_tr Int     @id @default(autoincrement())
  id_komoditi    Int
  kd_bahasa      String  @db.VarChar(2)
  nama           String? @db.VarChar(50)
  keterangan     String
  warna          String? @db.VarChar(7)

  @@schema("public")
}

model tb_sumber_data {
  id_sumber_data                   Int                                @id @default(autoincrement())
  id_sumber_data_judul             Int
  tahun_rumus                      Int?
  tb_bandara                       tb_bandara[]                       @relation("sumber_data_bandara")
  tb_hotel                         tb_hotel[]                         @relation("SumberDataToHotel")
  tb_pelabuhan                     tb_pelabuhan[]                     @relation("sumber_data_pelabuhan")
  tb_peluang_kabkot                tb_peluang_kabkot[]
  tb_pendidikan                    tb_pendidikan[]                    @relation("pendidikan_sumber_data")
  tb_rumah_sakit                   tb_rumah_sakit[]                   @relation("RSToSumberData")
  tb_sektor_daerah_pdrb            tb_sektor_daerah_pdrb[]
  tb_sektor_daerah_sumber_data     tb_sektor_daerah_sumber_data[]
  sektor_nasional_sumber_data      tb_sektor_nasional_sumber_data[]
  tb_sub_sektor_daerah_sumber_data tb_sub_sektor_daerah_sumber_data[]
  tb_sumber_data_judul             tb_sumber_data_judul               @relation(fields: [id_sumber_data_judul], references: [id_sumber_data_judul], onDelete: Cascade)
  tb_umr_provinsi                  tb_umr_provinsi[]
  tb_sektor_nasional_pdb           tb_sektor_nasional_pdb[]
  tb_investasi_provinsi            tb_investasi_provinsi[]
  tb_investasi_kabkot              tb_investasi_kabkot[]
  tb_listrik                       tb_listrik[]
  tb_gas                           tb_gas[]
  tb_air                           tb_air[]
  @@schema("public")
}

model tb_sumber_data_instansi {
  id_sumber_data_instansi    Int                          @id @default(autoincrement())
  id_adm_provinsi            Int?
  id_adm_kabkot              Int?
  nama                       String?                      @db.VarChar(255)
  alamat                     String?                      @db.VarChar(255)
  url_web                    String?                      @db.VarChar(255)
  cp                         String?                      @db.VarChar(255)
  email                      String?                      @db.VarChar(255)
  no_telp                    String?                      @db.VarChar(255)
  no_fax                     String?                      @db.VarChar(255)
  tb_sumber_data_instansi_tr tb_sumber_data_instansi_tr[]
  tb_sumber_data_judul       tb_sumber_data_judul[]
  tb_adm_provinsi            tb_adm_provinsi? @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)
  tb_adm_kabkot              tb_adm_kabkot? @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)


  @@schema("public")
}

model tb_sumber_data_instansi_tr {
  id_sumber_data_instansi_tr Int                     @id @default(autoincrement())
  id_sumber_data_instansi    Int
  kd_bahasa                  String                  @db.VarChar(2)
  nama                       String                  @db.VarChar(255)
  tb_sumber_data_instansi    tb_sumber_data_instansi @relation(fields: [id_sumber_data_instansi], references: [id_sumber_data_instansi], onDelete: Cascade)

  @@schema("public")
}

model tb_sumber_data_judul {
  id_sumber_data_judul    Int                       @id @default(autoincrement())
  id_sumber_data_instansi Int
  judul                   String                    @db.VarChar(255)
  keterangan              String
  tb_sumber_data          tb_sumber_data[]
  tb_sumber_data_instansi tb_sumber_data_instansi   @relation(fields: [id_sumber_data_instansi], references: [id_sumber_data_instansi], onDelete: Cascade)
  tb_sumber_data_judul_tr tb_sumber_data_judul_tr[]

  @@schema("public")
}

model tb_sumber_data_judul_tr {
  id_sumber_data_judul_tr Int                  @id @default(autoincrement())
  id_sumber_data_judul    Int
  kd_bahasa               String               @db.VarChar(2)
  judul                   String               @db.VarChar(255)
  keterangan              String
  tb_sumber_data_judul    tb_sumber_data_judul @relation(fields: [id_sumber_data_judul], references: [id_sumber_data_judul], onDelete: Cascade)

  @@schema("public")
}

model tb_sumber_data_tr {
  id_sumber_data_tr Int    @id @default(autoincrement())
  id_sumber_data    Int
  kd_bahasa         String @db.VarChar(2)
  judul             String @db.VarChar(255)
  keterangan        String

  @@schema("public")
}

model tb_umkm {
  id_umkm           Int                @id @default(autoincrement())
  nama              String             @db.VarChar(255)
  bidang_usaha      String             @db.VarChar(255)
  email             String             @db.VarChar(255)
  alamat            String             @db.VarChar(500)
  id_adm_kabkot     Int?
  nama_kabkot       String             @db.VarChar(255)
  id_adm_provinsi   Int?
  nama_provinsi     String             @db.VarChar(255)
  image             String?            @db.VarChar(255)
  lon               Float?
  lat               Float?
  id_peluang_kabkot Int?
  id_peluang_sektor Int?
  id_umkm_jenis     Int?
  tb_adm_kabkot     tb_adm_kabkot?     @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_adm_provinsi   tb_adm_provinsi?   @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)
  tb_peluang_kabkot tb_peluang_kabkot? @relation(fields: [id_peluang_kabkot], references: [id_peluang_kabkot], onDelete: Cascade)
  tb_peluang_sektor tb_peluang_sektor? @relation(fields: [id_peluang_sektor], references: [id_peluang_sektor], onDelete: Cascade)
  tb_umkm_jenis     tb_umkm_jenis?     @relation(fields: [id_umkm_jenis], references: [id_umkm_jenis], onDelete: Cascade)

  @@schema("public")
}

model tb_halaman_pengunjung {
  id_halaman_pengunjung  Int                         @id @default(autoincrement())
  nama                   String                      @db.VarChar(255)
  total_pengunjung       Int?
  halaman_pengunjung_det tb_halaman_pengunjung_det[]

  @@schema("public")
}

model tb_halaman_pengunjung_det {
  id_halaman_pengunjung_det Int                   @id @default(autoincrement())
  id_halaman_pengunjung     Int
  id_konten                 Int?
  ip_pengunjung             String                @db.VarChar(255)
  asal_negara               String?                @db.VarChar(100)
  created_date              DateTime              @db.Timestamp(6)
  id_peluang_kabkot         Int?
  id_peluang_daerah         Int?
  halaman_pengunjung        tb_halaman_pengunjung @relation(fields: [id_halaman_pengunjung], references: [id_halaman_pengunjung], onDelete: Cascade)
  peluang_kabkot            tb_peluang_kabkot? @relation(fields: [id_peluang_kabkot], references: [id_peluang_kabkot], onDelete: Cascade)
  peluang_daerah            tb_peluang_daerah? @relation(fields: [id_peluang_daerah], references: [id_peluang_daerah], onDelete: Cascade)
  @@schema("public")
}

model tb_peluang_status {
  id_peluang_status Int     @id @default(autoincrement())
  nama              String  @db.VarChar(255)
  keterangan        String? @db.VarChar(255)

  tb_peluang_daerah tb_peluang_daerah[]
  @@schema("public")
}

model tb_user {
  id_user       Int       @id @default(autoincrement())
  id_role       Int
  username      String    @db.VarChar(100)
  password      String    @db.VarChar(128)
  email         String    @db.VarChar(100)
  first_name    String    @db.VarChar(30)
  middle_name   String?   @db.VarChar(30)
  last_name     String?   @db.VarChar(30)
  address       String?
  phone_number  String?   @db.VarChar(30)
  mobile_number String?   @db.VarChar(30)
  file_image    String?   @db.VarChar(50)
  status        Int       @db.SmallInt
  last_login    DateTime? @db.Timestamp(6)
  created_date  DateTime  @db.Timestamp(6)
  updated_by    Int?
  updated_date  DateTime? @db.Timestamp(6)
  code_forgot   String?   @db.VarChar(255)
  code_expired  DateTime? @db.Timestamp(6)
  keterangan    String?
  user_token    String?   @db.VarChar(255)
  jabatan       String?   @db.VarChar(255)
  instansi      String?
  user_api_key  String?   @db.VarChar(36)

  @@schema("public")
}


model tb_user_internal {
  id_user_internal       Int       @id @default(autoincrement())
  id_user    Int   
  id_jabatan Int
  users      users @relation(fields: [id_user], references: [id], onDelete: Cascade)
  tb_ref_user_jabatan    tb_ref_user_jabatan @relation( fields: [id_jabatan], references: [id_jabatan], onDelete: Cascade)
  
  @@schema("public")
}

model tb_adm_user {
  id_adm_user         Int @id @default(autoincrement())
  id_user         Int 
  id_adm_provinsi Int?
  id_adm_kabkot   Int?
  users      users @relation(fields: [id_user], references: [id], onDelete: Cascade)
  
  @@schema("public")
}


model tb_user_internal_kawasan_industri {
  id_user_internal_kawasan_industri       Int       @id @default(autoincrement())
  id_user             Int   
  id_kawasan_industri Int
  users               users @relation(fields: [id_user], references: [id], onDelete: Cascade)
  tb_kawasan_industri tb_kawasan_industri @relation(fields: [id_kawasan_industri], references: [id_kawasan_industri], onDelete: Cascade)

  @@schema("public")
}

model tb_user_internal_provinsi {
  id_user_internal_provinsi       Int       @id @default(autoincrement())
  id_user         Int   
  id_adm_provinsi Int
  users           users @relation(fields: [id_user], references: [id], onDelete: Cascade)
  tb_adm_provinsi tb_adm_provinsi @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)
  
  @@schema("public")
}

model tb_user_internal_kabkot {
  id_user_internal_kabkot       Int       @id @default(autoincrement())
  id_user         Int   
  id_adm_kabkot   Int
  users           users @relation(fields: [id_user], references: [id], onDelete: Cascade)

  @@schema("public")
}

model tb_jenis_insentif {
  id_jenis_insentif          Int                          @id @default(autoincrement())
  nama                       String                       @db.VarChar(255)
  keterangan                 String
  status                     Int
  tipe                       Int?
  id_adm_provinsi            Int?
  id_adm_kabkot              Int?
  id_kategori_insentif       Int?
  urutan                     Int?
  tb_kategori_insentif       tb_kategori_insentif?        @relation(fields: [id_kategori_insentif], references: [id_kategori_insentif], onDelete: Cascade)
  tb_jenis_insentif_file     tb_jenis_insentif_file[]
  tb_jenis_insentif_file_tr  tb_jenis_insentif_file_tr[]
  tb_jenis_insentif_kbli     tb_jenis_insentif_kbli?
  tb_jenis_insentif_status   tb_jenis_insentif_status[]
  tb_jenis_insentif_tr       tb_jenis_insentif_tr[]
  tb_peluang_kabkot_insentif tb_peluang_kabkot_insentif[]

  @@schema("public")
}

model tb_kategori_insentif {
  id_kategori_insentif Int                 @id @default(autoincrement())
  nama                 String              @db.VarChar(255)
  tb_jenis_insentif    tb_jenis_insentif[]

  @@schema("public")
}

model tb_jenis_insentif_file {
  id_jenis_insentif_file Int               @id @default(autoincrement())
  id_jenis_insentif      Int
  tipe                   Int
  nama                   String            @db.VarChar(255)
  judul                  String            @db.VarChar(255)
  keterangan             String
  tb_jenis_insentif      tb_jenis_insentif @relation(fields: [id_jenis_insentif], references: [id_jenis_insentif], onDelete: Cascade)

  @@schema("public")
}

model tb_jenis_insentif_file_tr {
  id_jenis_insentif_file_tr Int               @id @default(autoincrement())
  id_jenis_insentif         Int
  kd_bahasa                 String            @db.VarChar(2)
  judul                     String            @db.VarChar(255)
  keterangan                String
  nama                      String            @db.VarChar(255)
  tipe                      Int?
  tb_jenis_insentif         tb_jenis_insentif @relation(fields: [id_jenis_insentif], references: [id_jenis_insentif], onDelete: Cascade)

  @@schema("public")
}

model tb_jenis_insentif_kbli {
  id_jenis_insentif Int               @id
  id_kbli           Int
  tb_jenis_insentif tb_jenis_insentif @relation(fields: [id_jenis_insentif], references: [id_jenis_insentif], onDelete: Cascade)

  @@schema("public")
}

model tb_jenis_insentif_status {
  id_jenis_insentif_status Int               @id @default(autoincrement())
  id_jenis_insentif        Int
  status                   Int
  status_proses            Int
  keterangan               String
  created_by               Int
  created_date             DateTime          @default(now()) @db.Timestamp(6)
  updated_by               Int?
  updated_date             DateTime?         @updatedAt @db.Timestamp(6)
  tb_jenis_insentif        tb_jenis_insentif @relation(fields: [id_jenis_insentif], references: [id_jenis_insentif], onDelete: Cascade)

  @@schema("public")
}

model tb_jenis_insentif_tr {
  id_jenis_insentif_tr Int               @id @default(autoincrement())
  id_jenis_insentif    Int
  kd_bahasa            String            @db.VarChar(2)
  nama                 String            @db.VarChar(255)
  keterangan           String
  tb_jenis_insentif    tb_jenis_insentif @relation(fields: [id_jenis_insentif], references: [id_jenis_insentif], onDelete: Cascade)

  @@schema("public")
}

model tb_sub_sektor_daerah {
        id_sub_sektor_daerah       Int                         @id @default(autoincrement())
        id_sektor_daerah           Int                         
        id_sub_sektor_nasional     Int                         
        deskripsi_singkat          String
        deskripsi                  String
        status                     Int
        tb_komoditi_daerah         tb_komoditi_daerah[]
        tb_sektor_daerah           tb_sektor_daerah            @relation(fields: [id_sektor_daerah], references: [id_sektor_daerah], onDelete: Cascade)
        tb_sub_sektor_nasional     tb_sub_sektor_nasional      @relation(fields: [id_sub_sektor_nasional], references: [id_sub_sektor_nasional], onDelete: Cascade, map: "sub_sektor_daerah_sektor_nasional_fkey")
        tb_sub_sektor_daerah_file  tb_sub_sektor_daerah_file[]
        tb_peluang_daerah          tb_peluang_daerah[]
        tb_sub_sektor_daerah_sumber_data tb_sub_sektor_daerah_sumber_data[]
        tb_sub_sektor_daerah_value_detail tb_sub_sektor_daerah_value_detail[]
        tb_sub_sektor_daerah_tr    tb_sub_sektor_daerah_tr[]

        @@unique([id_sektor_daerah, id_sub_sektor_nasional])
        
  @@schema("public")
}

model tb_sub_sektor_daerah_file {
  id_sub_sektor_daerah_file Int    @id @default(autoincrement())
  id_sub_sektor_daerah      Int
  tipe                      Int
  jenis                     Int
  nama                      String @db.VarChar(255)
  judul                     String @db.VarChar(255)
  keterangan                String
  tb_sub_sektor_daerah      tb_sub_sektor_daerah           @relation(fields: [id_sub_sektor_daerah], references: [id_sub_sektor_daerah], onDelete: Cascade)
  tb_sub_sektor_daerah_file_tr tb_sub_sektor_daerah_file_tr[]

  @@schema("public")
}

model tb_sub_sektor_daerah_file_tr {
  id_sub_sektor_daerah_file_tr Int    @id @default(autoincrement())
  id_sub_sektor_daerah_file    Int
  kd_bahasa                    String @db.VarChar(2)
  nama                         String @db.VarChar(255)
  judul                        String @db.VarChar(255)
  keterangan                   String
  tb_sub_sektor_daerah_file      tb_sub_sektor_daerah_file           @relation(fields: [id_sub_sektor_daerah_file], references: [id_sub_sektor_daerah_file], onDelete: Cascade)

  @@schema("public")
}

model tb_sub_sektor_daerah_status {
  id_sub_sektor_daerah_status Int       @id @default(autoincrement())
  id_sub_sektor_daerah        Int
  status                      Int
  status_proses               Int
  keterangan                  String?
  created_by                  Int
  created_date                DateTime  @db.Timestamp(6)
  updated_by                  Int?
  updated_date                DateTime? @db.Timestamp(6)

  @@schema("public")
}

model tb_sub_sektor_daerah_sumber_data {
        id_sub_sektor_daerah_daerah_sumber_data Int @id @default(autoincrement())
        id_sub_sektor_daerah                    Int
        id_sumber_data                          Int
        tb_sub_sektor_daerah     tb_sub_sektor_daerah      @relation(fields: [id_sub_sektor_daerah], references: [id_sub_sektor_daerah], onDelete: Cascade)
        tb_sumber_data     tb_sumber_data?      @relation(fields: [id_sumber_data], references: [id_sumber_data], onDelete: Cascade)

  @@schema("public")
}

model tb_sub_sektor_daerah_tr {
        id_sub_sektor_daerah_tr Int    @id @default(autoincrement())
        id_sub_sektor_daerah    Int @unique
        kd_bahasa               String @db.VarChar(2)
        deskripsi_singkat       String
        deskripsi               String
        tb_sub_sektor_daerah     tb_sub_sektor_daerah      @relation(fields: [id_sub_sektor_daerah], references: [id_sub_sektor_daerah], onDelete: Cascade)


  @@schema("public")
}

model tb_sub_sektor_daerah_value_detail {
        id_sub_sektor_nasional_value_detail Int       @id @default(autoincrement())
        id_sub_sektor_daerah                Int
        id_sub_sektor_nasional_value        Int
        tahun                               Int
        numeric_value                       Float?    @db.Real
        string_value                        String?   @db.VarChar(255)
        date_value                          DateTime? @db.Timestamp(6)
        tb_sub_sektor_daerah     tb_sub_sektor_daerah      @relation(fields: [id_sub_sektor_daerah], references: [id_sub_sektor_daerah], onDelete: Cascade)

  @@schema("public")
}

model tb_sub_sektor_nasional {
  id_sub_sektor_nasional          Int                                  @id @default(autoincrement())
  id_sektor_nasional              Int
  id_sub_sektor                   Int
  deskripsi_singkat               String?
  deskripsi                       String
  file_icon                       String                               @db.VarChar(255)
  file_image                      String?                              @db.VarChar(255)
  status                          Int
  komoditi_nasional               tb_komoditi_nasional[]
  sub_sektor_daerah               tb_sub_sektor_daerah[]
  sektor                          tb_sektor_nasional_ref               @relation(fields: [id_sektor_nasional], references: [id_sektor], onDelete: Cascade)
  sub_sektor_ref                  tb_sub_sektor_nasional_ref           @relation(fields: [id_sub_sektor], references: [id_sub_sektor], onDelete: Cascade)
  sub_sektor_nasional_file        tb_sub_sektor_nasional_file[]
  sub_sektor_nasional_sumber_data tb_sub_sektor_nasional_sumber_data[]
  sub_sektor_nasional_tr          tb_sub_sektor_nasional_tr[]
  sub_sektor_nasional_value       tb_sub_sektor_nasional_value[]

  @@schema("public")
}

model tb_sub_sektor_nasional_file {
  id_sub_sektor_nasional_file Int                              @id @default(autoincrement())
  id_sub_sektor_nasional      Int
  tipe                        Int
  jenis                       Int
  nama                        String                           @db.VarChar(255)
  judul                       String                           @db.VarChar(255)
  keterangan                  String
  sub_sektor_nasional         tb_sub_sektor_nasional           @relation(fields: [id_sub_sektor_nasional], references: [id_sub_sektor_nasional], onDelete: Cascade)
  sub_sektor_nasional_file_tr tb_sub_sektor_nasional_file_tr[]

  @@schema("public")
}

model tb_sub_sektor_nasional_file_tr {
  id_sub_sektor_nasional_file_tr Int                         @id @default(autoincrement())
  id_sub_sektor_nasional_file    Int
  kd_bahasa                      String                      @db.VarChar(2)
  nama                           String                      @db.VarChar(255)
  judul                          String                      @db.VarChar(255)
  keterangan                     String
  sub_sektor_nasional_file       tb_sub_sektor_nasional_file @relation(fields: [id_sub_sektor_nasional_file], references: [id_sub_sektor_nasional_file], onDelete: Cascade)

  @@schema("public")
}

model tb_sub_sektor_nasional_ref {
  id_sub_sektor              Int                            @id @default(autoincrement())
  id_sektor                  Int
  nama                       String                         @db.VarChar(255)
  iconmap                    String?                        @db.VarChar(255)
  komoditi_nasional_ref      tb_komoditi_nasional_ref[]
  sub_sektor_nasional        tb_sub_sektor_nasional[]
  sektor_nasional_ref        tb_sektor_nasional_ref         @relation(fields: [id_sektor], references: [id_sektor], onDelete: Cascade)
  sub_sektor_nasional_ref_tr tb_sub_sektor_nasional_ref_tr?

  @@schema("public")
}

model tb_sub_sektor_nasional_ref_tr {
  id_sub_sektor_tr        Int                        @id @default(autoincrement())
  id_sub_sektor           Int                        @unique
  kd_bahasa               String                     @db.VarChar(2)
  nama                    String                     @db.VarChar(255)
  sub_sektor_nasional_ref tb_sub_sektor_nasional_ref @relation(fields: [id_sub_sektor], references: [id_sub_sektor], onDelete: Cascade)

  @@schema("public")
}

model tb_sub_sektor_nasional_status {
  id_sub_sektor_nasional_status Int       @id @default(autoincrement())
  id_sub_sektor_nasional        Int
  status                        Int
  status_proses                 Int
  keterangan                    String?
  created_by                    Int
  created_date                  DateTime  @default(now()) @db.Timestamp(6)
  updated_by                    Int?
  updated_date                  DateTime? @updatedAt @db.Timestamp(6)

  @@schema("public")
}

model tb_sub_sektor_nasional_sumber_data {
  id_sub_sektor_nasional_sumber_data Int                    @id @default(autoincrement())
  id_sub_sektor_nasional             Int
  id_sumber_data                     Int
  sub_sektor_nasional                tb_sub_sektor_nasional @relation(fields: [id_sub_sektor_nasional], references: [id_sub_sektor_nasional], onDelete: Cascade)

  @@schema("public")
}

model tb_sub_sektor_nasional_tr {
  id_sub_sektor_nasional_tr Int                    @id @default(autoincrement())
  id_sub_sektor_nasional    Int
  kd_bahasa                 String                 @db.VarChar(2)
  deskripsi_singkat         String?
  deskripsi                 String
  file_icon                 String                 @db.VarChar(255)
  file_image                String?                @db.VarChar(255)
  sub_sektor                tb_sub_sektor_nasional @relation(fields: [id_sub_sektor_nasional], references: [id_sub_sektor_nasional], onDelete: Cascade)

  @@schema("public")
}

model tb_sub_sektor_nasional_value {
  id_sub_sektor_nasional_value     Int                                   @id @default(autoincrement())
  id_sub_sektor_nasional           Int
  nama                             String                                @db.VarChar(255)
  tipe                             Int                                   @db.SmallInt
  satuan                           String                                @db.VarChar(255)
  sub_sektor_nasional              tb_sub_sektor_nasional                @relation(fields: [id_sub_sektor_nasional], references: [id_sub_sektor_nasional], onDelete: Cascade)
  sub_sektor_nasional_value_detail tb_sub_sektor_nasional_value_detail[]
  sub_sektor_nasional_value_tr     tb_sub_sektor_nasional_value_tr[]

  @@schema("public")
}

model tb_sub_sektor_nasional_value_detail {
        id_sub_sektor_nasional_value_detail Int                          @id @default(autoincrement())
        id_sub_sektor_nasional_value        Int
        tahun                               Int
        numeric_value                       Float?                              @db.Real
        string_value                        String?                             @db.VarChar(255)
        date_value                          DateTime?                           @db.Timestamp(6)
        sub_sektor_nasional_value           tb_sub_sektor_nasional_value        @relation(fields: [id_sub_sektor_nasional_value], references: [id_sub_sektor_nasional_value], onDelete: Cascade)

  @@schema("public")
}

model tb_sub_sektor_nasional_value_tr {
  id_sub_sektor_nasional_value_tr Int                          @id @default(autoincrement())
  id_sub_sektor_nasional_value    Int
  kd_bahasa                       String                       @db.VarChar(2)
  nama                            String                       @db.VarChar(255)
  satuan                          String                       @db.VarChar(255)
  sub_sektor_nasional_value       tb_sub_sektor_nasional_value @relation(fields: [id_sub_sektor_nasional_value], references: [id_sub_sektor_nasional_value], onDelete: Cascade)

  @@schema("public")
}

model tb_link_terkait {
  id_link_terkait Int      @id @default(autoincrement())
  nama            String   @db.VarChar(255)
  alamat          String   @db.VarChar(500)
  no_telpon       String   @db.VarChar(255)
  url_web         String   @db.VarChar(255)
  lon             Float?
  lat             Float?
  nama_pic        String?  @db.VarChar(255)
  foto_pic        String?  @db.VarChar(255)
  file_logo       String   @db.VarChar(255)
  file_image      String?   @db.VarChar(255)
  status          Int
  is_ikn          Boolean?
  id_kategori     Int
  is_kontak       Boolean?

  @@schema("public")
}

model tb_vidio_beranda {
  id_vidio Int    @id @default(autoincrement())
  url      String @db.VarChar(255)

  @@schema("public")
}

model tb_ikn {
  id_ikn     Int         @id @default(autoincrement())
  nama       String      @db.VarChar(255)
  deskripsi  String
  file_image String      @db.VarChar(255)
  file_logo  String      @db.VarChar(255)
  lon        Float
  lat        Float
  view_count Int
  last_view  DateTime    @db.Timestamp(6)
  file_video String      @db.VarChar(255)
  tb_ikn_tr  tb_ikn_tr[]

  @@schema("public")
}

model tb_ikn_data {
  id_ikn_data      Int                @id @default(autoincrement())
  id_ikn           Int
  jenis            Int
  nama             String             @db.VarChar(255)
  deskripsi        String
  file_image       String?            @db.VarChar(255)
  status           Int
  is_fe            Boolean?
  fe_posisi        Int
  tb_ikn_data_file tb_ikn_data_file[]
  tb_ikn_data_tr   tb_ikn_data_tr[]

  @@schema("public")
}

model tb_ikn_data_file {
  id_ikn_data_file    Int                   @id @default(autoincrement())
  id_ikn_data         Int
  tipe                Int
  nama                String                @db.VarChar(255)
  judul               String                @db.VarChar(255)
  tb_ikn_data         tb_ikn_data           @relation(fields: [id_ikn_data], references: [id_ikn_data], onDelete: Cascade)
  tb_ikn_data_file_tr tb_ikn_data_file_tr[]

  @@schema("public")
}

model tb_ikn_data_file_tr {
  id_ikn_data_file_tr Int              @id @default(autoincrement())
  id_ikn_data_file    Int
  kd_bahasa           String           @db.VarChar(2)
  nama                String           @db.VarChar(255)
  judul               String           @db.VarChar(255)
  tb_ikn_data_file    tb_ikn_data_file @relation(fields: [id_ikn_data_file], references: [id_ikn_data_file], onDelete: Cascade)

  @@schema("public")
}

model tb_ikn_data_tr {
  id_ikn_data_tr Int         @id @default(autoincrement())
  id_ikn_data    Int
  kd_bahasa      String      @db.VarChar(2)
  nama           String      @db.VarChar(255)
  deskripsi      String
  tb_ikn_data    tb_ikn_data @relation(fields: [id_ikn_data], references: [id_ikn_data], onDelete: Cascade)

  @@schema("public")
}

model tb_ikn_file {
  id_ikn_file Int    @id @default(autoincrement())
  id_ikn      Int
  jenis       Int
  tipe        Int
  nama        String @db.VarChar(255)
  judul       String @db.VarChar(255)
  status      Int

  @@schema("public")
}

model tb_ikn_file_tr {
  id_ikn_file_tr Int    @id @default(autoincrement())
  id_ikn_file    Int
  kd_bahasa      String @db.VarChar(2)
  nama           String @db.VarChar(255)
  judul          String @db.VarChar(255)

  @@schema("public")
}

model tb_ikn_kabkot {
  id_ikn_kabkot Int @id @default(autoincrement())
  id_ikn        Int
  id_adm_kabkot Int

  @@schema("public")
}

model tb_ikn_layer {
  id_ikn_layer Int    @id @default(autoincrement())
  id_ikn       Int
  judul        String @db.VarChar(255)
  nama_layer   String @db.VarChar(255)
  tipe         Int
  url_service  String @db.VarChar(255)
  status       Int

  @@schema("public")
}

model tb_ikn_peluang {
  id_ikn_peluang    Int  @id @default(autoincrement())
  id_ikn            Int
  id_peluang_kabkot Int?
  id_peluang_daerah Int?
  status            Int

  @@schema("public")
}

model tb_ikn_tr {
  id_ikn_tr Int    @id @default(autoincrement())
  id_ikn    Int
  kd_bahasa String @db.VarChar(2)
  nama      String @db.VarChar(255)
  deskripsi String
  tb_ikn    tb_ikn @relation(fields: [id_ikn], references: [id_ikn], onDelete: Cascade)

  @@schema("public")
}

model tb_sektor_nasional_kontak {
  id_sektor_nasional_kontak Int                @id @default(autoincrement())
  id_sektor_nasional        Int
  nama_pic                  String?
  file_foto_pic             String?
  nama                      String?
  alamat                    String?
  no_telp                   String?
  no_fax                    String?
  url_web                   String?
  status                    String
  lon                       Float?
  lat                       Float?
  sektor_nasional           tb_sektor_nasional @relation(fields: [id_sektor_nasional], references: [id_sektor_nasional], onDelete: Cascade)

  @@schema("public")
}

model tb_news {
  id                       Int                       @id @default(autoincrement())
  jenis                    Int
  judul                    String                    @db.VarChar(255)
  deskripsi_singkat        String                    @db.VarChar(500)
  deskripsi                String
  status                   Int
  file_cover               String?                   @db.VarChar(255)
  id_news_kategori         Int?
  id_adm_kabkot            Int?
  id_adm_provinsi          Int?
  id_komoditi              Int?
  tb_adm_kabkot            tb_adm_kabkot?            @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_adm_provinsi          tb_adm_provinsi?          @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)
  tb_komoditi_nasional_ref tb_komoditi_nasional_ref? @relation(fields: [id_komoditi], references: [id_komoditi], onDelete: Cascade)
  tb_news_kategori         tb_news_kategori?         @relation(fields: [id_news_kategori], references: [id_news_kategori], onDelete: Cascade)
  files                    tb_news_file[]            @relation("NewsFiles")
  files_tr                 tb_news_file_tr[]         @relation("NewsFilesTr")
  statuses                 tb_news_status[]          @relation("NewsStatuses")
  translations             tb_news_tr[]              @relation("NewsTranslations")

  @@schema("public")
}

model tb_news_file {
  id_news_file Int       @id @default(autoincrement())
  id_news      Int
  tipe         Int
  nama         String    @db.VarChar(255)
  judul        String    @db.VarChar(255)
  created_at   DateTime? @db.Timestamp(6)
  updated_at   DateTime? @updatedAt
  tb_news      tb_news   @relation("NewsFiles", fields: [id_news], references: [id], onDelete: Cascade)

  @@schema("public")
}

model tb_news_file_tr {
  id_news_file_tr Int       @id @default(autoincrement())
  id_news      Int
  tipe         Int
  nama         String    @db.VarChar(255)
  judul        String    @db.VarChar(255)
  created_at   DateTime? @db.Timestamp(6)
  updated_at   DateTime? @updatedAt
  tb_news      tb_news   @relation("NewsFilesTr", fields: [id_news], references: [id], onDelete: Cascade)

  @@schema("public")
}


model tb_news_status {
  id_news_status Int       @id @default(autoincrement())
  id_news        Int
  status         Int
  created_by     Int
  created_date   DateTime  @db.Timestamp(6)
  updated_by     Int?
  updated_date   DateTime? @db.Timestamp(6)
  status_proses  Int
  keterangan     String?
  tb_news        tb_news   @relation("NewsStatuses", fields: [id_news], references: [id], onDelete: Cascade)

  @@schema("public")
}

model tb_news_tr {
  id_news_tr        Int     @id @default(autoincrement())
  id_news           Int
  kd_bahasa         String  @db.VarChar(2)
  judul             String  @db.VarChar(255)
  deskripsi_singkat String  @db.VarChar(500)
  deskripsi         String
  tb_news           tb_news @relation("NewsTranslations", fields: [id_news], references: [id], onDelete: Cascade)

  @@schema("public")
}

model tb_news_kategori {
  id_news_kategori Int       @id @default(autoincrement())
  nama             String    @db.VarChar(255)
  tb_news          tb_news[]

  @@schema("public")
}

model tb_kebijakan {
  id_kebijakan                        Int                                   @id @default(autoincrement())
  judul                               String                                @db.VarChar(255)
  nama_file                           String                                @db.VarChar(500)
  id_kategori                         Int?
  deskripsi                           String?                               @db.VarChar(255)
  status                              Int?
  tb_kebijakan_kategori               tb_kebijakan_kategori?                @relation(fields: [id_kategori], references: [id_kategori], onDelete: Cascade)
  tb_kebijakan_file                   tb_kebijakan_file[]
  tb_peluang_kabkot_kebijakan_mapping tb_peluang_kabkot_kebijakan_mapping[]
  tb_kebijakan_status                 tb_kebijakan_status[]
  tb_kebijakan_file_tr                tb_kebijakan_file_tr[]
  tb_kebijakan_tr                     tb_kebijakan_tr[]
  @@schema("public")
}
model tb_kebijakan_tr {
  id_kebijakan_tr                     Int                                   @id @default(autoincrement())
  id_kebijakan                        Int
  judul                               String                                @db.VarChar(255)
  nama_file                           String                                @db.VarChar(500)
  deskripsi                           String?                               @db.VarChar(255)
  tb_kebijakan      tb_kebijakan @relation(fields: [id_kebijakan], references: [id_kebijakan], onDelete: Cascade)
  @@schema("public")
}
model tb_kebijakan_file {
  id_kebijakan_file Int          @id @default(autoincrement())
  id_kebijakan      Int
  nama              String       @db.VarChar(255)
  tb_kebijakan      tb_kebijakan @relation(fields: [id_kebijakan], references: [id_kebijakan], onDelete: Cascade)

  @@schema("public")
}


model tb_kebijakan_file_tr {
  id_kebijakan_file_tr Int          @id @default(autoincrement())
  id_kebijakan      Int
  nama              String       @db.VarChar(255)
  tb_kebijakan      tb_kebijakan @relation(fields: [id_kebijakan], references: [id_kebijakan], onDelete: Cascade)

  @@schema("public")
}

model tb_kebijakan_kategori {
  id_kategori  Int            @id @default(autoincrement())
  nama         String         @db.VarChar(255)
  tb_kebijakan tb_kebijakan[]

  @@schema("public")
}


model tb_kebijakan_status {
  tb_kebijakan_status Int       @id @default(autoincrement())
  id_kebijakan        Int
  status         Int
  created_by     Int
  created_date   DateTime  @db.Timestamp(6)
  updated_by     Int?
  updated_date   DateTime? @db.Timestamp(6)
  status_proses  Int
  keterangan     String?
  tb_kebijakan        tb_kebijakan  @relation( fields: [id_kebijakan], references: [id_kebijakan], onDelete: Cascade)

  @@schema("public")
}

model tb_komoditi_kabkot {
  id_komoditi_kabkot       Int                       @id @default(autoincrement())
  id_adm_kabkot            Int
  id_komoditi              Int?
  tahun                    Int
  id_sumber_data           Int?
  status                   Int
  lon                      Float?
  lat                      Float?
  id_satuan                Int
  luas_lahan               Float?
  nama                     String?                   @db.VarChar(255)
  nilai_produksi           Float?
  bijih_hipotik            Float?
  logam_hipotik            Float?
  bijih_tereka             Float?
  logam_tereka             Float?
  bijih_tertunjuk          Float?
  logam_tertunjuk          Float?
  bijih_terukur            Float?
  logam_terukur            Float?
  bijih_terkira            Float?
  logam_terkira            Float?
  bijih_terbukti           Float?
  logam_terbukti           Float?
  logam_status             String?                   @db.VarChar(255)
  non_logam_hipotik        Float?
  non_logam_tereka         Float?
  non_logam_tertunjuk      Float?
  non_logam_terukur        Float?
  non_logam_terkira        Float?
  non_logam_terbukti       Float?
  non_logam_status         String?                   @db.VarChar(255)
  panas_spekulasi          Float?
  panas_hipotik            Float?
  panas_terduga            Float?
  panas_mungkin            Float?
  panas_terbukti           Float?
  panas_terpasang          Float?
  panas_temperatur         Float?
  panas_klasifikasi        String?                   @db.VarChar(255)
  tb_adm_kabkot            tb_adm_kabkot             @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_komoditi_nasional_ref tb_komoditi_nasional_ref? @relation(fields: [id_komoditi], references: [id_komoditi], onDelete: Cascade)
  tb_komoditi_satuan       tb_komoditi_satuan        @relation(fields: [id_satuan], references: [id_komoditi_satuan], onDelete: Cascade)
  tb_komoditi_layers       tb_komoditi_layers[]

  @@schema("public")
}

model tb_komoditi_provinsi {
  id_komoditi_provinsi     Int                       @id @default(autoincrement())
  id_adm_provinsi          Int
  id_komoditi              Int?
  tahun                    Int
  id_sumber_data           Int?
  status                   Int
  lon                      Float?
  lat                      Float?
  id_satuan                Int
  luas_lahan               Float?
  nama                     String?                   @db.VarChar(255)
  nilai_produksi           Float?
  bijih_hipotik            Float?
  logam_hipotik            Float?
  bijih_tereka             Float?
  logam_tereka             Float?
  bijih_tertunjuk          Float?
  logam_tertunjuk          Float?
  bijih_terukur            Float?
  logam_terukur            Float?
  bijih_terkira            Float?
  logam_terkira            Float?
  bijih_terbukti           Float?
  logam_terbukti           Float?
  logam_status             String?                   @db.VarChar(255)
  non_logam_hipotik        Float?
  non_logam_tereka         Float?
  non_logam_tertunjuk      Float?
  non_logam_terukur        Float?
  non_logam_terkira        Float?
  non_logam_terbukti       Float?
  non_logam_status         String?                   @db.VarChar(255)
  panas_spekulasi          Float?
  panas_hipotik            Float?
  panas_terduga            Float?
  panas_mungkin            Float?
  panas_terbukti           Float?
  panas_terpasang          Float?
  panas_temperatur         Float?
  panas_klasifikasi        String?                   @db.VarChar(255)
  tb_adm_provinsi          tb_adm_provinsi           @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)
  tb_komoditi_nasional_ref tb_komoditi_nasional_ref? @relation(fields: [id_komoditi], references: [id_komoditi], onDelete: Cascade)
  tb_komoditi_satuan       tb_komoditi_satuan        @relation(fields: [id_satuan], references: [id_komoditi_satuan], onDelete: Cascade)
  tb_komoditi_layers       tb_komoditi_layers[]

  @@schema("public")
}

model tb_bandara_kategori {
  id_bandara_kategori Int          @id @default(autoincrement())
  nama                String       @db.VarChar(50)
  tb_bandara          tb_bandara[] @relation("bandara_kategori")

  @@schema("public")
}

model tb_bandara_kategori_tr {
  id_bandara_kategori_tr Int    @id @default(autoincrement())
  id_bandara_kategori    Int
  kd_bahasa              String @db.VarChar(2)
  nama                   String @db.VarChar(50)

  @@schema("public")
}

model tb_bandara_kelas {
  id_bandara_kelas Int          @id @default(autoincrement())
  nama             String       @db.VarChar(50)
  tb_bandara       tb_bandara[] @relation("bandara_kelas")

  @@schema("public")
}

model tb_bandara_kelas_tr {
  id_bandara_kelas_tr Int    @id @default(autoincrement())
  id_bandara_kelas    Int
  kd_bahasa           String @db.VarChar(2)
  nama                String @db.VarChar(50)

  @@schema("public")
}

model tb_bandara_status {
  id_bandara        Int
  status            Int
  status_proses     Int
  keterangan        String
  created_by        Int
  created_date      DateTime  @db.Timestamp(6)
  updated_by        Int?
  updated_date      DateTime? @db.Timestamp(6)
  id_bandara_status Int       @id @default(autoincrement())
  tb_bandara        tb_bandara        @relation(fields: [id_bandara], references: [id_bandara], onDelete: Cascade)

  @@schema("public")
}

model tb_bandara_tr {
  id_bandara_tr Int    @id @default(autoincrement())
  id_bandara    Int
  kd_bahasa     String @db.VarChar(2)
  nama          String @db.VarChar(50)
  keterangan    String

  @@schema("public")
}

model tb_buku_tamu {
  id_buku_tamu  Int      @id @default(autoincrement())
  id_adm_kabkot Int?
  tujuan        String?  @db.VarChar(100)
  instansi      String?  @db.VarChar(100)
  deskripsi     String?
  file          String?  @db.VarChar(100)
  created_date  DateTime @db.Timestamp(6)

  @@schema("public")
}

model tb_buku_tamu_pengunjung {
  id_buku_tamu_pengunjung Int     @id @default(autoincrement())
  id_buku_tamu            Int?
  nama                    String? @db.VarChar(100)
  nip                     String? @db.VarChar(20)
  jabatan                 String? @db.VarChar(100)
  file_ttd                String? @db.VarChar(100)
  no_hp                   String? @db.VarChar(14)

  @@schema("public")
}

model tb_ekspor_kabkot {
  id_ekspor_kabkot    Int                   @id @default(autoincrement())
  id_adm_kabkot       Int
  id_sumber_data      Int?
  tahun               Int?
  keterangan          String?
  nilai_ekspor        Float?
  status              Int?
  nilai_impor         Float?
  tb_ekspor_kabkot_tr tb_ekspor_kabkot_tr[] @relation("EksporKabkotToEksporKabkotTr")
  tb_adm_kabkot                 tb_adm_kabkot?                  @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)

  @@schema("public")
}

model tb_ekspor_kabkot_status {
  id_ekspor_kabkot_status Int       @id @default(autoincrement())
  id_ekspor_kabkot        Int
  status                  Int
  status_proses           Int
  keterangan              String
  created_by              Int
  created_date            DateTime  @db.Timestamp(6)
  updated_by              Int?
  updated_date            DateTime? @db.Timestamp(6)

  @@schema("public")
}

model tb_ekspor_kabkot_tr {
  id_ekspor_kabkot_tr Int              @id @default(autoincrement())
  id_ekspor_kabkot    Int
  kd_bahasa           String?          @db.VarChar(2)
  keterangan          String?
  tb_ekspor_kabkot    tb_ekspor_kabkot @relation("EksporKabkotToEksporKabkotTr", fields: [id_ekspor_kabkot], references: [id_ekspor_kabkot], onDelete: Cascade)

  @@schema("public")
}

model tb_ekspor_provinsi {
  id_ekspor_provinsi    Int                     @id @default(autoincrement())
  id_adm_provinsi       Int
  id_sumber_data        Int?
  tahun                 Int?
  keterangan            String?
  nilai_ekspor          Float?
  status                Int?
  nilai_impor           Float?
  tb_ekspor_provinsi_tr tb_ekspor_provinsi_tr[]
  tb_adm_provinsi         tb_adm_provinsi           @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)
  tb_ekspor_provinsi_status tb_ekspor_provinsi_status[]

  @@schema("public")
}

model tb_ekspor_provinsi_status {
  id_ekspor_provinsi_status Int       @id @default(autoincrement())
  id_ekspor_provinsi        Int
  status                    Int
  status_proses             Int
  keterangan                String
  created_by                Int
  created_date              DateTime  @db.Timestamp(6)
  updated_by                Int?
  updated_date              DateTime? @db.Timestamp(6)
  tb_ekspor_provinsi         tb_ekspor_provinsi           @relation(fields: [id_ekspor_provinsi], references: [id_ekspor_provinsi], onDelete: Cascade)

  @@schema("public")
}

model tb_ekspor_provinsi_tr {
  id_ekspor_provinsi_tr Int                @id @default(autoincrement())
  id_ekspor_provinsi    Int
  kd_bahasa             String             @db.VarChar(2)
  keterangan            String?
  tb_ekspor_provinsi    tb_ekspor_provinsi @relation(fields: [id_ekspor_provinsi], references: [id_ekspor_provinsi], onDelete: Cascade)

  @@schema("public")
}

model tb_email_subscription {
  id_email_subscription Int       @id @default(autoincrement())
  email                 String    @db.VarChar(40)
  lang                  String    @db.VarChar(10)
  ip_pengunjung         String    @db.VarChar(15)
  token_exp_date        DateTime? @default(dbgenerated("(CURRENT_TIMESTAMP + '3 days'::interval day)")) @db.Timestamptz(6)
  token                 String    @db.VarChar(50)
  is_active             Boolean
  created_time          DateTime? @default(now()) @db.Timestamptz(6)
  created_by            String?   @db.VarChar(255)
  modified_time         DateTime? @db.Timestamp(6)
  nama                  String?   @db.VarChar(50)
  tujuan                Int?

  @@schema("public")
}

model tb_glossary {
  id_glossary    Int              @id @default(autoincrement())
  glossary_type  String           @db.VarChar(20)
  title          String           @db.VarChar(150)
  description    String           @db.VarChar(2000)
  modified_time  DateTime?        @default(dbgenerated("CURRENT_DATE")) @db.Date
  tb_glossary_tr tb_glossary_tr[]

  @@schema("public")
}

model tb_glossary_tr {
  id_glossary_tr Int         @id @default(autoincrement())
  id_glossary    Int
  kode_bahasa    String      @db.VarChar(2)
  title          String      @db.VarChar(150)
  description    String      @db.VarChar(2000)
  modified_time  DateTime?   @default(dbgenerated("CURRENT_DATE")) @db.Date
  tb_glossary    tb_glossary @relation(fields: [id_glossary], references: [id_glossary], onDelete: Cascade)

  @@schema("public")
}

model tb_hotel_kelas {
  id_hotel_kelas Int        @id @default(autoincrement())
  nama           String     @db.VarChar(50)
  tb_hotel       tb_hotel[] @relation("HotelKelasToHotel")

  @@schema("public")
}

model tb_hotel_kelas_tr {
  id_hotel_kelas_tr Int    @id @default(autoincrement())
  id_hotel_kelas    Int
  kd_bahasa         String @db.VarChar(2)
  nama              String @db.VarChar(50)

  @@schema("public")
}

model tb_hotel_status {
  id_hotel        Int
  status          Int
  status_proses   Int
  keterangan      String
  created_by      Int
  created_date    DateTime  @db.Timestamp(6)
  updated_by      Int?
  updated_date    DateTime? @db.Timestamp(6)
  id_hotel_status Int       @id @default(autoincrement())
  tb_hotel        tb_hotel        @relation(fields: [id_hotel], references: [id_hotel], onDelete: Cascade)

  @@schema("public")
}

model tb_hotel_tr {
  id_hotel_tr Int      @id @default(autoincrement())
  id_hotel    Int
  kd_bahasa   String   @db.VarChar(2)
  nama        String   @db.VarChar(255)
  keterangan  String
  file_logo   String?  @db.VarChar(255)
  tb_hotel    tb_hotel @relation("HotelToHotelTr", fields: [id_hotel], references: [id_hotel], onDelete: Cascade)

  @@schema("public")
}

model tb_investasi_jenis_data {
  id_investasi_jenis_data Int                     @id @default(autoincrement())
  nama                    String?                 @db.VarChar(50)
  keterangan              String
  tb_investasi_kabkot     tb_investasi_kabkot[]
  tb_investasi_provinsi   tb_investasi_provinsi[]

  @@schema("public")
}

model tb_investasi_jenis_data_tr {
  id_investasi_jenis_data_tr Int     @id @default(autoincrement())
  id_investasi_jenis_data    Int
  kd_bahasa                  String  @db.VarChar(2)
  nama                       String? @db.VarChar(50)
  keterangan                 String

  @@schema("public")
}

model tb_investasi_jenis_tr {
  id_investasi_jenis_tr Int     @id @default(autoincrement())
  id_investasi_jenis    Int
  kd_bahasa             String  @db.VarChar(2)
  nama                  String? @db.VarChar(50)
  keterangan            String

  @@schema("public")
}

model tb_investasi_kabkot {
  id_investasi_kabkot     Int                     @id @default(autoincrement())
  id_adm_kabkot           Int
  id_sumber_data          Int?
  id_sektor               Int
  id_jenis                Int
  id_jenis_data           Int
  tahun                   Int
  jumlah_proyek           Int
  jumlah_investasi        Float
  status                  Int
  tb_investasi_jenis_data tb_investasi_jenis_data @relation(fields: [id_jenis_data], references: [id_investasi_jenis_data], onDelete: Cascade)
  tb_investasi_jenis      tb_investasi_jenis      @relation(fields: [id_jenis], references: [id_investasi_jenis], onDelete: Cascade)
  tb_investasi_sektor     tb_investasi_sektor     @relation(fields: [id_sektor], references: [id_investasi_sektor], onDelete: Cascade)
  tb_adm_kabkot           tb_adm_kabkot @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_sumber_data          tb_sumber_data?          @relation(fields: [id_sumber_data], references: [id_sumber_data])

  @@schema("public")
}

model tb_investasi_kabkot_status {
  id_investasi_kabkot_status Int       @id @default(autoincrement())
  id_investasi_kabkot        Int
  status                     Int
  status_proses              Int
  keterangan                 String
  created_by                 Int
  created_date               DateTime  @db.Timestamp(6)
  updated_by                 Int?
  updated_date               DateTime? @db.Timestamp(6)

  @@schema("public")
}

model tb_investasi_provinsi_status {
  id_investasi_provinsi_status Int       @id @default(autoincrement())
  id_investasi_provinsi        Int
  status                       Int
  status_proses                Int
  keterangan                   String
  created_by                   Int
  created_date                 DateTime  @db.Timestamp(6)
  updated_by                   Int?
  updated_date                 DateTime? @db.Timestamp(6)

  @@schema("public")
}

model tb_investasi_sektor {
  id_investasi_sektor   Int                     @id @default(autoincrement())
  nama                  String?                 @db.VarChar(255)
  keterangan            String
  tb_investasi_kabkot   tb_investasi_kabkot[]
  tb_investasi_provinsi tb_investasi_provinsi[]
  tb_investasi_sektor_tr   tb_investasi_sektor_tr[]

  @@schema("public")
}

model tb_investasi_sektor_tr {
  id_investasi_sektor_tr Int     @id @default(autoincrement())
  id_investasi_sektor    Int
  kd_bahasa              String  @db.VarChar(2)
  nama                   String? @db.VarChar(255)
  keterangan             String
  tb_investasi_sektor    tb_investasi_sektor              @relation(fields: [id_investasi_sektor], references: [id_investasi_sektor], onDelete: Cascade)

  @@schema("public")
}

model tb_kawasan_industri_blok {
  id_kawasan_industri_blok         Int                               @id @default(autoincrement())
  id_kawasan_industri              Int?
  kode                             String?                           @db.VarChar(255)
  nama                             String?                           @db.VarChar(255)
  id_kondisi                       Int?
  id_fungsi                        Int?
  luas                             Float?
  harga                            Float?
  nama_perusahaan                  String?                           @db.VarChar(255)
  id_negara                        Int?
  status                           Int?
  lon                              Float?
  lat                              Float?
  tb_kawasan_industri_blok_fungsi  tb_kawasan_industri_blok_fungsi?  @relation(fields: [id_fungsi], references: [id_kawasan_industri_blok_fungsi], onDelete: Cascade)
  tb_kawasan_industri              tb_kawasan_industri?              @relation(fields: [id_kawasan_industri], references: [id_kawasan_industri], onDelete: Cascade)
  tb_kawasan_industri_blok_kondisi tb_kawasan_industri_blok_kondisi? @relation(fields: [id_kondisi], references: [id_kawasan_industri_blok_kondisi], onDelete: Cascade)
  tb_kawasan_industri_blok_tr      tb_kawasan_industri_blok_tr[]

  @@schema("public")
}

model tb_kawasan_industri_blok_fungsi {
  id_kawasan_industri_blok_fungsi Int                        @id @default(autoincrement())
  nama                            String                     @db.VarChar(255)
  keterangan                      String
  tb_kawasan_industri_blok        tb_kawasan_industri_blok[]

  @@schema("public")
}

model tb_kawasan_industri_blok_fungsi_tr {
  id_kawasan_industri_blok_fungsi_tr Int    @id @default(autoincrement())
  id_kawasan_industri_blok_fungsi    Int
  kd_bahasa                          String @db.VarChar(2)
  nama                               String @db.VarChar(255)
  keterangan                         String

  @@schema("public")
}

model tb_kawasan_industri_blok_kondisi {
  id_kawasan_industri_blok_kondisi Int                        @id @default(autoincrement())
  nama                             String                     @db.VarChar(255)
  keterangan                       String
  tb_kawasan_industri_blok         tb_kawasan_industri_blok[]

  @@schema("public")
}

model tb_kawasan_industri_blok_kondisi_tr {
  id_kawasan_industri_blok_kondisi_tr Int    @id @default(autoincrement())
  id_kawasan_industri_blok_kondisi    Int
  kd_bahasa                           String @db.VarChar(2)
  nama                                String @db.VarChar(255)
  keterangan                          String

  @@schema("public")
}

model tb_kawasan_industri_blok_staging {
  id_kawasan_industri_blok_staging Int       @id @default(autoincrement())
  id_kawasan_industri              Int?
  status                           Int?
  kode                             String?   @db.VarChar(255)
  kondisi                          String?   @db.VarChar(255)
  fungsi                           String?   @db.VarChar(255)
  luas                             String?   @db.VarChar(255)
  harga                            String?   @db.VarChar(255)
  perusahaan                       String?   @db.VarChar(255)
  negara                           String?   @db.VarChar(255)
  lon                              String?   @db.VarChar(255)
  lat                              String?   @db.VarChar(255)
  prj                              String?   @db.VarChar(255)
  shape                            String?
  log                              String?
  publish_by                       Int?
  publish_dt                       DateTime? @db.Timestamp(6)

  @@schema("public")
}

model tb_kawasan_industri_blok_status {
  id_kawasan_industri_blok        Int
  status                          Int
  status_proses                   Int
  keterangan                      String
  created_by                      Int
  created_date                    DateTime  @db.Timestamp(6)
  updated_by                      Int?
  updated_date                    DateTime? @db.Timestamp(6)
  id_kawasan_industri_blok_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_kawasan_industri_blok_status_tr {
  id_kawasan_industri_blok_status_tr Int    @id @default(autoincrement())
  id_kawasan_industri_blok_status    Int
  kd_bahasa                          String @db.VarChar(2)
  nama                               String @db.VarChar(255)
  keterangan                         String

  @@schema("public")
}

model tb_kawasan_industri_blok_tr {
  id_kawasan_industri_blok_tr Int                      @id @default(autoincrement())
  id_kawasan_industri_blok    Int
  kd_bahasa                   String                   @db.VarChar(2)
  nama                        String                   @db.VarChar(255)
  nama_perusahaan             String                   @db.VarChar(255)
  tb_kawasan_industri_blok    tb_kawasan_industri_blok @relation(fields: [id_kawasan_industri_blok], references: [id_kawasan_industri_blok], onDelete: Cascade)

  @@schema("public")
}

model tb_kawasan_industri_file {
  id_kawasan_industri_file Int                 @id @default(autoincrement())
  id_kawasan_industri      Int
  tipe                     Int
  nama                     String              @db.VarChar(255)
  judul                    String              @db.VarChar(255)
  keterangan               String
  tb_kawasan_industri      tb_kawasan_industri @relation(fields: [id_kawasan_industri], references: [id_kawasan_industri], onDelete: Cascade)
  tb_kawasan_industri_file_tr tb_kawasan_industri_file_tr[]
  @@schema("public")
}

model tb_kawasan_industri_file_tr {
  id_kawasan_industri_file_tr Int    @id @default(autoincrement())
  id_kawasan_industri_file    Int
  kd_bahasa                   String @db.VarChar(2)
  judul                       String @db.VarChar(255)
  keterangan                  String
  nama                        String @db.VarChar(255)
  tb_kawasan_industri_file      tb_kawasan_industri_file @relation(fields: [id_kawasan_industri_file], references: [id_kawasan_industri_file], onDelete: Cascade)

  @@schema("public")
}

model tb_kawasan_industri_kategori {
  id_kawasan_industri_kategori Int                   @id @default(autoincrement())
  nama                         String                @db.VarChar(50)
  tb_kawasan_industri          tb_kawasan_industri[]
  tb_kawasan_industri_kategori_tr       tb_kawasan_industri_kategori_tr[]

  @@schema("public")
}

model tb_kawasan_industri_kategori_tr {
  id_kawasan_industri_kategori_tr Int    @id @default(autoincrement())
  id_kawasan_industri_kategori    Int
  kd_bahasa                       String @db.VarChar(2)
  nama                            String @db.VarChar(50)
  tb_kawasan_industri_kategori    tb_kawasan_industri_kategori @relation(fields: [id_kawasan_industri_kategori], references: [id_kawasan_industri_kategori], onDelete: Cascade)

  @@schema("public")
}

model tb_kawasan_industri_layer_spasial {
  id_kawasan_industri_layer_spasial Int      @id @default(autoincrement())
  id_kawasan_industri               Int
  nama_layer                        String   @db.VarChar(500)
  tipe                              Int
  url_service                       String   @db.VarChar(500)
  status                            Int
  is_active                         Boolean?

  @@schema("public")
}

model tb_kawasan_industri_occupancy {
  id_kawasan_industri_occupancy Int                   @id @default(autoincrement())
  nama                          String                @db.VarChar(255)
  tb_kawasan_industri           tb_kawasan_industri[]

  @@schema("public")
}

model tb_kawasan_industri_peluang {
  id_kawasan_industri_peluang      Int                                @id @default(autoincrement())
  id_kawasan_industri              Int
  judul                            String                             @db.VarChar
  nilai_investasi                  Float?
  deskripsi                        String
  luas_lahan                       Float?
  tb_kawasan_industri              tb_kawasan_industri                @relation(fields: [id_kawasan_industri], references: [id_kawasan_industri], onDelete: Cascade)
  tb_kawasan_industri_peluang_file tb_kawasan_industri_peluang_file[]
  tb_kawasan_industri_peluang_tr   tb_kawasan_industri_peluang_tr[]

  @@schema("public")
}

model tb_kawasan_industri_peluang_file {
  id_kawasan_industri_peluang_file Int                         @id @default(autoincrement())
  id_kawasan_industri_peluang      Int
  tipe                             Int
  jenis                            Int
  nama                             String                      @db.VarChar(255)
  judul                            String                      @db.VarChar(255)
  keterangan                       String
  tb_kawasan_industri_peluang      tb_kawasan_industri_peluang @relation(fields: [id_kawasan_industri_peluang], references: [id_kawasan_industri_peluang], onDelete: Cascade)

  @@schema("public")
}

model tb_kawasan_industri_peluang_tr {
  id_kawasan_industri_peluang_tr Int                         @id @default(autoincrement())
  id_kawasan_industri_peluang    Int
  kd_bahasa                      String                      @db.VarChar
  judul                          String                      @db.VarChar
  deskripsi                      String
  tb_kawasan_industri_peluang    tb_kawasan_industri_peluang @relation(fields: [id_kawasan_industri_peluang], references: [id_kawasan_industri_peluang], onDelete: Cascade)

  @@schema("public")
}

model tb_kawasan_industri_ref_range {
  id_kawasan_industri_ref_range Int                   @id @default(autoincrement())
  min                           Float
  max                           Float?
  tb_kawasan_industri           tb_kawasan_industri[]

  @@schema("public")
}

model tb_kawasan_industri_status {
  id_kawasan_industri_status Int       @id @default(autoincrement())
  id_kawasan_industri        Int
  status                     Int
  status_proses              Int
  keterangan                 String
  created_by                 Int
  created_date               DateTime  @db.Timestamp(6)
  updated_by                 Int?
  updated_date               DateTime? @db.Timestamp(6)
  tb_kawasan_industri tb_kawasan_industri @relation(fields: [id_kawasan_industri], references: [id_kawasan_industri], onDelete: Cascade)

  @@schema("public")
}

model tb_kawasan_industri_tr {
  id_kawasan_industri_tr Int    @id @default(autoincrement())
  id_kawasan_industri    Int
  kd_bahasa              String @db.VarChar(2)
  nama                   String @db.VarChar(255)
  keterangan             String
  tb_kawasan_industri    tb_kawasan_industri                  @relation(fields: [id_kawasan_industri], references: [id_kawasan_industri], onDelete: Cascade)

  @@schema("public")
}

model tb_kawasan_user {
  id_kawasan_user     Int @id @default(autoincrement())
  id_kawasan_industri Int
  id_user             Int
  users           users @relation(fields: [id_user], references: [id], onDelete: Cascade)

  @@schema("public")
}

model tb_kawasan_user_hapus {
  id_kawasan_user_hapus Int  @id @default(autoincrement())
  id_kawasan_user       Int?
  id_kawasan_industri   Int?
  id_user               Int?

  @@schema("public")
}

model tb_negara {
  id_negara  Int    @id @default(autoincrement())
  nama       String @db.VarChar(255)
  ibu_kota   String @db.VarChar(255)
  keterangan String

  @@schema("public")
}

model tb_negara_tr {
  id_negara_tr Int    @id @default(autoincrement())
  id_negara    Int
  kd_bahasa    String @db.VarChar(2)
  nama         String @db.VarChar(255)
  ibu_kota     String @db.VarChar(255)
  keterangan   String

  @@schema("public")
}

model tb_nswi_jenis {
  id_nswi_jenis Int     @id @default(autoincrement())
  nama          String? @db.VarChar(50)
  keterangan    String

  @@schema("public")
}

model tb_nswi_jenis_data {
  id_nswi_jenis_data Int     @id @default(autoincrement())
  nama               String? @db.VarChar(50)
  keterangan         String

  @@schema("public")
}

model tb_nswi_jenis_data_tr {
  id_nswi_jenis_data_tr Int     @id @default(autoincrement())
  id_nswi_jenis_data    Int
  kd_bahasa             String  @db.VarChar(2)
  nama                  String? @db.VarChar(50)
  keterangan            String

  @@schema("public")
}

model tb_nswi_jenis_tr {
  id_nswi_jenis_tr Int     @id @default(autoincrement())
  id_nswi_jenis    Int
  kd_bahasa        String  @db.VarChar(2)
  nama             String? @db.VarChar(50)
  keterangan       String

  @@schema("public")
}

model tb_nswi_kabkot {
  id_nswi_kabkot   Int   @id @default(autoincrement())
  id_adm_kabkot    Int
  id_sumber_data   Int
  id_sektor        Int
  id_jenis         Int
  id_jenis_data    Int
  tahun            Int
  jumlah_proyek    Int
  jumlah_investasi Float
  status           Int

  @@schema("public")
}

model tb_nswi_kabkot_status {
  id_nswi_kabkot        Int
  status                Int
  status_proses         Int
  keterangan            String
  created_by            Int
  created_date          DateTime  @db.Timestamp(6)
  updated_by            Int?
  updated_date          DateTime? @db.Timestamp(6)
  id_nswi_kabkot_status Int       @id @default(autoincrement())

  @@schema("public")
}

model tb_nswi_provinsi {
  id_nswi_provinsi Int   @id @default(autoincrement())
  id_adm_provinsi  Int
  id_sumber_data   Int
  id_sektor        Int
  id_jenis         Int
  id_jenis_data    Int
  tahun            Int
  jumlah_proyek    Int
  jumlah_investasi Float
  status           Int

  @@schema("public")
}

model tb_nswi_provinsi_status {
  id_nswi_provinsi_status Int       @id @default(autoincrement())
  id_nswi_provinsi        Int
  status                  Int
  status_proses           Int
  keterangan              String
  created_by              Int
  created_date            DateTime  @db.Timestamp(6)
  updated_by              Int?
  updated_date            DateTime? @db.Timestamp(6)

  @@schema("public")
}

model tb_nswi_sektor {
  id_nswi_sektor Int     @id @default(autoincrement())
  nama           String? @db.VarChar(255)
  keterangan     String

  @@schema("public")
}

model tb_nswi_sektor_tr {
  id_nswi_sektor_tr Int     @id @default(autoincrement())
  id_nswi_sektor    Int
  kd_bahasa         String  @db.VarChar(2)
  nama              String? @db.VarChar(255)
  keterangan        String

  @@schema("public")
}

model tb_pelabuhan_fungsi {
  id_pelabuhan_fungsi Int    @id @default(autoincrement())
  nama                String @db.VarChar(50)
  tb_pelabuhan    tb_pelabuhan[]

  @@schema("public")
}

model tb_pelabuhan_fungsi_tr {
  id_pelabuhan_fungsi_tr Int    @id @default(autoincrement())
  id_pelabuhan_fungsi    Int
  kd_bahasa              String @db.VarChar(2)
  nama                   String @db.VarChar(50)

  @@schema("public")
}

model tb_pelabuhan_kelas {
  id_pelabuhan_kelas Int    @id @default(autoincrement())
  nama               String @db.VarChar(50)

  @@schema("public")
}

model tb_pelabuhan_kelas_tr {
  id_pelabuhan_kelas_tr Int    @id @default(autoincrement())
  id_pelabuhan_kelas    Int
  kd_bahasa             String @db.VarChar(2)
  nama                  String @db.VarChar(50)

  @@schema("public")
}

model tb_pelabuhan_status {
  id_pelabuhan_status Int       @id @default(autoincrement())
  id_pelabuhan        Int
  status              Int
  status_proses       Int
  keterangan          String
  created_by          Int
  created_date        DateTime  @db.Timestamp(6)
  updated_by          Int?
  updated_date        DateTime? @db.Timestamp(6)
  tb_pelabuhan        tb_pelabuhan        @relation(fields: [id_pelabuhan], references: [id_pelabuhan], onDelete: Cascade)

  @@schema("public")
}

model tb_pelabuhan_tr {
  id_pelabuhan_tr Int    @id @default(autoincrement())
  id_pelabuhan    Int
  kd_bahasa       String @db.VarChar(2)
  nama            String @db.VarChar(255)
  keterangan      String

  @@schema("public")
}

model tb_pendidikan_jenjang {
  id_pendidikan_jenjang Int             @id @default(autoincrement())
  nama                  String          @db.VarChar(50)
  tb_pendidikan         tb_pendidikan[] @relation("pendidikan_jenjang")

  @@schema("public")
}

model tb_pendidikan_jenjang_tr {
  id_pendidikan_jenjang_tr Int    @id @default(autoincrement())
  id_pendidikan_jenjang    Int
  kd_bahasa                String @db.VarChar(2)
  nama                     String @db.VarChar(50)

  @@schema("public")
}

model tb_pendidikan_kategori {
  id_pendidikan_kategori Int             @id @default(autoincrement())
  nama                   String          @db.VarChar(50)
  tb_pendidikan          tb_pendidikan[] @relation("pendidikan_kategori")

  @@schema("public")
}

model tb_pendidikan_kategori_tr {
  id_pendidikan_kategori_tr Int    @id @default(autoincrement())
  id_pendidikan_kategori    Int
  kd_bahasa                 String @db.VarChar(2)
  nama                      String @db.VarChar(50)

  @@schema("public")
}

model tb_pendidikan_status {
  id_pendidikan        Int
  status               Int
  status_proses        Int
  keterangan           String
  created_by           Int
  created_date         DateTime  @db.Timestamp(6)
  updated_by           Int?
  updated_date         DateTime? @db.Timestamp(6)
  id_pendidikan_status Int       @id @default(autoincrement())
  tb_pendidikan        tb_pendidikan        @relation(fields: [id_pendidikan], references: [id_pendidikan], onDelete: Cascade)

  @@schema("public")
}

model tb_pendidikan_tr {
  id_pendidikan_tr Int           @id @default(autoincrement())
  id_pendidikan    Int
  kd_bahasa        String        @db.VarChar(2)
  nama             String        @db.VarChar(255)
  keterangan       String
  tb_pendidikan    tb_pendidikan @relation("pendidikan_tr", fields: [id_pendidikan], references: [id_pendidikan])

  @@schema("public")
}

model tb_perusahaan {
  id_perusahaan     Int    @id @default(autoincrement())
  id_tipe           Int
  id_adm_kabkot     Int
  nib               String @db.VarChar(15)
  kd_pa             String @db.VarChar(255)
  kd_kmk            String @db.VarChar(255)
  nama              String @db.VarChar(255)
  status_verifikasi Int
  status            Int

  @@schema("public")
}

model tb_perusahaan_cabang_tr {
  id_perusahaan_cabang_tr Int    @id @default(autoincrement())
  id_perusahaan_cabang    Int
  kd_bahasa               String @db.VarChar(2)
  nama                    String @db.VarChar(255)

  @@schema("public")
}

model tb_perusahaan_file {
  id_perusahaan_file Int    @id @default(autoincrement())
  id_perusahaan      Int
  tipe               Int
  nama               String @db.VarChar(255)
  judul              String @db.VarChar(255)
  keterangan         String

  @@schema("public")
}

model tb_perusahaan_kbli {
  id_perusahaan_kbli Int @id @default(autoincrement())
  id_perusahaan      Int
  id_kbli            Int

  @@schema("public")
}

model tb_perusahaan_kbli_produk {
  id_perusahaan_kbli_produk Int       @id @default(autoincrement())
  id_perusahaan_kbli        Int
  nama                      String    @db.VarChar(255)
  keterangan                String
  created_by                Int
  created_date              DateTime  @db.Timestamp(6)
  updated_by                Int?
  updated_date              DateTime? @db.Timestamp(6)

  @@schema("public")
}

model tb_perusahaan_kbli_produk_file {
  id_perusahaan_kbli_produk_file Int    @id @default(autoincrement())
  tb_perusahaan_kbli_produk      Int
  tipe                           Int
  nama                           String @db.VarChar(255)
  judul                          String @db.VarChar(255)
  keterangan                     String

  @@schema("public")
}

model tb_perusahaan_kbli_produk_tr {
  id_perusahaan_kbli_produk_tr Int    @id @default(autoincrement())
  pk_perusahaan_kbli_produk    Int
  kd_bahasa                    String @db.VarChar(2)
  nama                         String @db.VarChar(255)
  keterangan                   String

  @@schema("public")
}

model tb_perusahaan_kebutuhan_kbli {
  id_perusahaan_kebutuhan_kbli Int @id @default(autoincrement())
  id_perusahaan                Int
  id_kbli                      Int

  @@schema("public")
}

model tb_perusahaan_kebutuhan_produk {
  id_perusahaan_kebutuhan_produk Int       @id @default(autoincrement())
  id_perusahaan_lokasi           Int
  nama                           String    @db.VarChar(255)
  keterangan                     String
  created_by                     Int
  created_date                   DateTime  @db.Timestamp(6)
  updated_by                     Int?
  updated_date                   DateTime? @db.Timestamp(6)

  @@schema("public")
}

model tb_perusahaan_kebutuhan_produk_tr {
  id_perusahaan_kebutuhan_produk_tr Int    @id @default(autoincrement())
  id_perusahaan_kebutuhan_produk    Int
  kd_bahasa                         String @db.VarChar(2)
  nama                              String @db.VarChar(255)
  keterangan                        String

  @@schema("public")
}

model tb_perusahaan_status {
  id_perusahaan_status Int       @id @default(autoincrement())
  id_perusahaan        Int
  status               Int
  status_proses        Int
  keterangan           String
  created_by           Int
  created_date         DateTime  @db.Timestamp(6)
  updated_by           Int?
  updated_date         DateTime? @db.Timestamp(6)

  @@schema("public")
}

model tb_perusahaan_tipe {
  id_perusahaan_tipe Int    @id @default(autoincrement())
  kode               String @db.VarChar(10)
  nama               String @db.VarChar(255)

  @@schema("public")
}

model tb_perusahaan_tipe_tr {
  id_perusahaan_tipe_tr Int    @id @default(autoincrement())
  id_perusahaan_tipe    Int
  kd_bahasa             String @db.VarChar(2)
  nama                  String @db.VarChar(255)

  @@schema("public")
}

model tb_ref_user_jabatan {
  id_jabatan        Int    @id @default(autoincrement())
  id_jabatan_parent Int?
  nama              String @db.VarChar(255)
  level             Int
  tb_user_internal tb_user_internal[]


  @@schema("public")
}

model tb_rumah_sakit_jenis {
  id_rumah_sakit_jenis Int              @id @default(autoincrement())
  nama                 String?          @db.VarChar(50)
  tb_rumah_sakit       tb_rumah_sakit[] @relation("rumah_sakit_jenis")

  @@schema("public")
}

model tb_rumah_sakit_jenis_tr {
  id_rumah_sakit_jenis_tr Int     @id @default(autoincrement())
  id_rumah_sakit_jenis    Int?
  kd_bahasa               String? @db.VarChar(2)
  nama                    String? @db.VarChar(50)

  @@schema("public")
}

model tb_rumah_sakit_kategori {
  id_rumah_sakit_kategori Int              @id @default(autoincrement())
  nama                    String?          @db.VarChar(50)
  tb_rumah_sakit          tb_rumah_sakit[] @relation("rumah_sakit_kategori")

  @@schema("public")
}

model tb_rumah_sakit_kategori_tr {
  id_rumah_sakit_kategori_tr Int     @id @default(autoincrement())
  id_rumah_sakit_kategori    Int?
  kd_bahasa                  String? @db.VarChar(2)
  nama                       String? @db.VarChar(50)

  @@schema("public")
}

model tb_rumah_sakit_status {
  id_rumah_sakit_status Int       @id @default(autoincrement())
  id_rumah_sakit        Int
  status                Int
  status_proses         Int?
  keterangan            String?
  created_by            Int?
  created_date          DateTime? @db.Timestamp(6)
  updated_by            Int?
  updated_date          DateTime? @db.Timestamp(6)
  tb_rumah_sakit        tb_rumah_sakit        @relation(fields: [id_rumah_sakit], references: [id_rumah_sakit], onDelete: Cascade)

  @@schema("public")
}

model tb_rumah_sakit_tr {
  id_rumah_sakit_tr Int            @id @default(autoincrement())
  id_rumah_sakit    Int
  kd_bahasa         String?        @db.VarChar(2)
  nama              String?        @db.VarChar(255)
  keterangan        String?
  tb_rumah_sakit    tb_rumah_sakit @relation("RSToRSTR", fields: [id_rumah_sakit], references: [id_rumah_sakit], onDelete: Cascade)

  @@schema("public")
}

model tb_send_query {
  id_send_query Int       @id @default(autoincrement())
  user_type     String    @db.VarChar(255)
  sector        String    @db.VarChar(255)
  name          String    @db.VarChar(255)
  email         String    @db.VarChar(255)
  country       String    @db.VarChar(255)
  query_type    String    @db.VarChar(255)
  query         String
  ip_pengunjung String    @db.VarChar(15)
  created_time  DateTime? @default(now()) @db.Timestamptz(6)
  created_by    String?   @db.VarChar(255)
  modified_time DateTime? @db.Timestamp(6)

  @@schema("public")
}

model tb_role {
  id_role   Int    @id @default(autoincrement())
  role_name String @db.VarChar(255)

  @@schema("public")
}

model tb_role_appl_task {
  id_role_appl_task      Int    @id @default(autoincrement())
  id_role      Int    
  id_appl_task BigInt
  can_read     Int    @default(1) @db.SmallInt
  can_edit     Int    @default(1) @db.SmallInt
  can_delete   Int    @default(1) @db.SmallInt
  can_approve  Int    @default(1) @db.SmallInt
  can_add      Int?   @default(1) @db.SmallInt

  @@schema("public")
}

model tb_unduh_data {
  id_unduh_data           Int                     @id @default(autoincrement())
  id_unduh_data_tujuan    Int
  nama                    String                  @db.VarChar(255)
  email                   String                  @db.VarChar(255)
  no_telp                 String                  @db.VarChar(255)
  asal_negara             String                  @db.VarChar(255)
  id_konten               Int?
  ip_pengunjung           String                  @db.VarChar(255)
  created_date            DateTime                @default(now()) @db.Timestamp(6)
  id_jenis_konten         String?                 @db.VarChar(255)
  status_minat            Boolean?
  tb_unduh_data_keperluan tb_unduh_data_keperluan @relation(fields: [id_unduh_data_tujuan], references: [id_unduh_data_keperluan], onDelete: Cascade)

  @@schema("public")
}

model tb_unduh_data_keperluan {
  id_unduh_data_keperluan    Int                          @id @default(autoincrement())
  nama                       String                       @db.VarChar(255)
  tb_unduh_data              tb_unduh_data[]
  tb_unduh_data_keperluan_tr tb_unduh_data_keperluan_tr[]

  @@schema("public")
}

model tb_unduh_data_keperluan_tr {
  id_unduh_data_keperluan_tr Int                     @id @default(autoincrement())
  id_unduh_data_keperluan    Int
  kd_bahasa                  String                  @db.VarChar(2)
  nama                       String                  @db.VarChar(255)
  tb_unduh_data_keperluan    tb_unduh_data_keperluan @relation(fields: [id_unduh_data_keperluan], references: [id_unduh_data_keperluan], onDelete: Cascade)

  @@schema("public")
}

model tb_utilitas_kabkot {
  id_utilitas_kabkot  Int           @id @default(autoincrement())
  id_adm_kabkot       Int
  id_sumber_data      Int?
  tahun               Int?
  keterangan          String?
  produksi_air_bersih Float?
  daya_terpasang      Float?
  jumlah_bts          Float?
  panjang_jalan       Float?
  status              Int?
  tb_adm_kabkot       tb_adm_kabkot @relation(fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  
  @@schema("public")
}

model tb_utilitas_kabkot_status {
  id_utilitas_kabkot_status Int       @id @default(autoincrement())
  id_utilitas_kabkot        Int
  status                    Int
  status_proses             Int
  keterangan                String?
  created_by                Int
  created_date              DateTime  @db.Timestamp(6)
  updated_by                Int?
  updated_date              DateTime? @db.Timestamp(6)

  @@schema("public")
}

model tb_utilitas_kabkot_tr {
  id_utilitas_kabkot_tr Int     @id @default(autoincrement())
  id_utilitas_kabkot    Int
  kd_bahasa             String  @db.VarChar(2)
  keterangan            String?

  @@schema("public")
}

model tb_utilitas_provinsi {
  id_utilitas_provinsi    Int                       @id @default(autoincrement())
  id_adm_provinsi         Int
  id_sumber_data          Int?
  tahun                   Int?
  keterangan              String?
  produksi_air_bersih     Float?
  daya_terpasang          Float?
  jumlah_bts              Float?
  panjang_jalan           Float?
  status                  Int?
  tb_adm_provinsi         tb_adm_provinsi           @relation(fields: [id_adm_provinsi], references: [id_adm_provinsi], onDelete: Cascade)
  tb_utilitas_provinsi_tr tb_utilitas_provinsi_tr[]

  @@schema("public")
}

model tb_utilitas_provinsi_status {
  id_utilitas_provinsi_status Int       @id @default(autoincrement())
  id_utilitas_provinsi        Int
  status                      Int
  status_proses               Int
  keterangan                  String?
  created_by                  Int
  created_date                DateTime  @db.Timestamp(6)
  updated_by                  Int?
  updated_date                DateTime? @db.Timestamp(6)

  @@schema("public")
}

model tb_utilitas_provinsi_tr {
  id_utilitas_provinsi_tr Int                  @id @default(autoincrement())
  id_utilitas_provinsi    Int
  kd_bahasa               String               @db.VarChar(2)
  keterangan              String?
  tb_utilitas_provinsi    tb_utilitas_provinsi @relation(fields: [id_utilitas_provinsi], references: [id_utilitas_provinsi], onDelete: Cascade)

  @@schema("public")
}

model z_tb_komoditi {
  id_komoditi Int      @id @default(autoincrement())
  id_sektor   Int?
  nama        String?  @db.VarChar(50)
  keterangan  String?
  warna       String?  @db.VarChar(7)
  is_internal Boolean?
  url_service String?  @db.VarChar(255)

  @@schema("public")
}

model z_tb_komoditi_kabkot {
  id_komoditi_kabkot Int  @id @default(autoincrement())
  id_komoditi        Int?

  @@schema("public")
}

model z_tb_komoditi_provinsi {
  id_komoditi_provinsi Int  @id @default(autoincrement())
  id_komoditi          Int?

  @@schema("public")
}

model z_tb_komoditi_sektor {
  id_komoditi_sektor Int     @id @default(autoincrement())
  nama               String? @db.VarChar(50)
  keterangan         String?

  @@schema("public")
}

model z_tb_komoditi_sektor_tr {
  id_komoditi_sektor_tr Int     @id @default(autoincrement())
  id_komoditi_sektor    Int?
  kd_bahasa             String? @db.VarChar(2)
  nama                  String? @db.VarChar(50)
  keterangan            String?

  @@schema("public")
}

model z_tb_komoditi_tr {
  id_komoditi_tr Int     @id @default(autoincrement())
  id_komoditi    Int?
  kd_bahasa      String? @db.VarChar(2)
  nama           String? @db.VarChar(50)
  keterangan     String?
  warna          String? @db.VarChar(7)

  @@schema("public")
}

model tb_kode_bahasa {
  id_kd_bahasa Int    @id @default(autoincrement())
  kd_bahasa    String @db.VarChar(2)
  deskripsi    String @db.VarChar(255)

  @@schema("public")
}

model tb_kategori_infrastruktur {
  id_kategori_infrastruktur         Int                 @id @default(autoincrement())
  nama                              String              @db.VarChar(500)
  tb_bandara      tb_bandara[]
  tb_hotel        tb_hotel[]
  tb_pendidikan   tb_pendidikan[]
  tb_pelabuhan    tb_pelabuhan[]
  tb_rumah_sakit  tb_rumah_sakit[]
  tb_listrik  tb_listrik[]
  tb_gas  tb_gas[]
  tb_air  tb_air[]
  @@schema("public")
}



model tb_bandara {
  id_bandara              Int                   @id @default(autoincrement())
  id_adm_kabkot           Int
  id_sumber_data          Int?
  id_kelas                Int
  id_kategori             Int
  nama                    String                @db.VarChar(50)
  keterangan              String
  jarak_ibu_kota_provinsi Float
  iata                    String                @db.VarChar(10)
  alamat                  String                @db.VarChar(255)
  no_telp                 String                @db.VarChar(50)
  no_fax                  String                @db.VarChar(30)
  url_web                 String                @db.VarChar(255)
  jam_operasional_awal    DateTime              @db.Time(6)
  jam_operasional_akhir   DateTime              @db.Time(6)
  id_zona_waktu           Int
  jenis_pesawat           String?               @db.VarChar(255)
  maskapai                String?               @db.VarChar(255)
  lon                     Float?
  lat                     Float?
  status                  Int
  is_ikn                  Boolean?
  id_kategori_infrastruktur Int?
  tb_adm_kabkot           tb_adm_kabkot         @relation("adm_kabkot_bandara", fields: [id_adm_kabkot], references: [id_adm_kabkot])
  tb_bandara_kategori     tb_bandara_kategori   @relation("bandara_kategori", fields: [id_kategori], references: [id_bandara_kategori])
  tb_bandara_kelas        tb_bandara_kelas      @relation("bandara_kelas", fields: [id_kelas], references: [id_bandara_kelas])
  tb_sumber_data          tb_sumber_data?       @relation("sumber_data_bandara", fields: [id_sumber_data], references: [id_sumber_data])
  tb_kawasan_industri     tb_kawasan_industri[]
  tb_kategori_infrastruktur    tb_kategori_infrastruktur? @relation(fields: [id_kategori_infrastruktur], references: [id_kategori_infrastruktur], onDelete: Cascade)
  tb_bandara_status       tb_bandara_status[]
  @@schema("public")
}

model tb_hotel {
  id_hotel       Int             @id @default(autoincrement())
  id_kelas       Int
  id_adm_kabkot  Int
  id_sumber_data Int?
  nama           String          @db.VarChar(255)
  keterangan     String
  file_logo      String?         @db.VarChar(255)
  alamat         String
  no_telp        String          @db.VarChar(30)
  no_fax         String          @db.VarChar(30)
  email          String          @db.VarChar(50)
  status         Int
  url_web        String          @db.VarChar(255)
  lon            Float?
  lat            Float?
  is_ikn         Boolean?
  id_kategori_infrastruktur Int?
  tb_adm_kabkot  tb_adm_kabkot   @relation("AdmKabkotToHotel", fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_hotel_kelas tb_hotel_kelas  @relation("HotelKelasToHotel", fields: [id_kelas], references: [id_hotel_kelas], onDelete: Cascade)
  tb_sumber_data tb_sumber_data? @relation("SumberDataToHotel", fields: [id_sumber_data], references: [id_sumber_data], onDelete: Cascade)
  tb_hotel_tr    tb_hotel_tr[]   @relation("HotelToHotelTr")
  tb_kategori_infrastruktur    tb_kategori_infrastruktur? @relation(fields: [id_kategori_infrastruktur], references: [id_kategori_infrastruktur], onDelete: Cascade)
  tb_hotel_status tb_hotel_status[]
  @@schema("public")
}

model tb_pelabuhan {
  id_pelabuhan        Int                   @id @default(autoincrement())
  nama                String                @db.VarChar(255)
  keterangan          String
  id_adm_kabkot       Int
  id_sumber_data      Int?
  alamat              String                @db.VarChar(255)
  no_telp             String                @db.VarChar(50)
  no_fax              String                @db.VarChar(50)
  url_web             String                @db.VarChar(255)
  panjang_dermaga     Float
  kedalaman           Float
  id_fungsi           Int
  id_kelas            Int
  status              Int
  lon                 Float?
  lat                 Float?
  is_ikn              Boolean?
  id_kategori_infrastruktur Int?
  tb_kawasan_industri tb_kawasan_industri[]
  tb_adm_kabkot       tb_adm_kabkot         @relation("adm_kabkot_pelabuhan", fields: [id_adm_kabkot], references: [id_adm_kabkot])
  tb_sumber_data      tb_sumber_data?       @relation("sumber_data_pelabuhan", fields: [id_sumber_data], references: [id_sumber_data])
  tb_kategori_infrastruktur    tb_kategori_infrastruktur? @relation(fields: [id_kategori_infrastruktur], references: [id_kategori_infrastruktur], onDelete: Cascade)
  tb_pelabuhan_fungsi    tb_pelabuhan_fungsi? @relation(fields: [id_fungsi], references: [id_pelabuhan_fungsi])
  tb_pelabuhan_status tb_pelabuhan_status[]
  @@schema("public")
}

model tb_pendidikan {
  id_pendidikan          Int                    @id @default(autoincrement())
  id_adm_kabkot          Int
  id_sumber_data         Int?
  nama                   String                 @db.VarChar(255)
  keterangan             String
  id_kategori            Int
  id_jenjang             Int
  alamat                 String                 @db.VarChar(255)
  no_telp                String                 @db.VarChar(30)
  no_fax                 String                 @db.VarChar(30)
  email                  String                 @db.VarChar(255)
  url_web                String                 @db.VarChar(255)
  status                 Int
  lon                    Float?
  lat                    Float?
  is_ikn                 Boolean?
  id_kategori_infrastruktur Int?
  tb_adm_kabkot          tb_adm_kabkot          @relation("pendidikan_adm_kabkot", fields: [id_adm_kabkot], references: [id_adm_kabkot])
  tb_pendidikan_jenjang  tb_pendidikan_jenjang  @relation("pendidikan_jenjang", fields: [id_jenjang], references: [id_pendidikan_jenjang])
  tb_pendidikan_kategori tb_pendidikan_kategori @relation("pendidikan_kategori", fields: [id_kategori], references: [id_pendidikan_kategori])
  tb_sumber_data         tb_sumber_data?        @relation("pendidikan_sumber_data", fields: [id_sumber_data], references: [id_sumber_data])
  tb_pendidikan_tr       tb_pendidikan_tr[]     @relation("pendidikan_tr")
  tb_kategori_infrastruktur    tb_kategori_infrastruktur? @relation(fields: [id_kategori_infrastruktur], references: [id_kategori_infrastruktur], onDelete: Cascade)
  tb_pendidikan_status tb_pendidikan_status[]
  @@schema("public")
}

model tb_rumah_sakit {
  id_rumah_sakit          Int                      @id @default(autoincrement())
  id_adm_kabkot           Int
  id_sumber_data          Int?
  nama                    String?                  @db.VarChar(255)
  keterangan              String?
  id_kategori             Int?
  id_jenis                Int?
  alamat                  String?                  @db.VarChar(255)
  no_telp                 String?                  @db.VarChar(30)
  no_fax                  String?                  @db.VarChar(30)
  email                   String?                  @db.VarChar(255)
  url_web                 String?                  @db.VarChar(255)
  status                  Int?
  lon                     Float?
  lat                     Float?
  is_ikn                  Boolean?
  id_kategori_infrastruktur Int?
  tb_adm_kabkot           tb_adm_kabkot            @relation("RSToAdmKabkot", fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_rumah_sakit_jenis    tb_rumah_sakit_jenis?    @relation("rumah_sakit_jenis", fields: [id_jenis], references: [id_rumah_sakit_jenis])
  tb_rumah_sakit_kategori tb_rumah_sakit_kategori? @relation("rumah_sakit_kategori", fields: [id_kategori], references: [id_rumah_sakit_kategori])
  tb_sumber_data          tb_sumber_data?          @relation("RSToSumberData", fields: [id_sumber_data], references: [id_sumber_data], onDelete: Cascade)
  tb_rumah_sakit_tr       tb_rumah_sakit_tr[]      @relation("RSToRSTR")
  tb_kategori_infrastruktur    tb_kategori_infrastruktur? @relation(fields: [id_kategori_infrastruktur], references: [id_kategori_infrastruktur], onDelete: Cascade)
  tb_rumah_sakit_status tb_rumah_sakit_status[]
  @@schema("public")
}

model tb_zona_waktu {
  id_zona_waktu    Int                @id @default(autoincrement())
  nama             String             @db.VarChar(50)
  tb_adm_provinsi  tb_adm_provinsi[]
  tb_zona_waktu_tr tb_zona_waktu_tr[]

  @@schema("public")
}

model tb_zona_waktu_tr {
  id_zona_waktu_tr Int           @id @default(autoincrement())
  id_zona_waktu    Int
  kd_bahasa        String        @db.VarChar(2)
  nama             String        @db.VarChar(50)
  tb_zona_waktu    tb_zona_waktu @relation(fields: [id_zona_waktu], references: [id_zona_waktu], onDelete: Cascade)

  @@schema("public")
}

model tb_bpn_rdtr {
  id_rdtr         Int    @id @default(autoincrement())
  url             String @db.VarChar(200)
  kd_adm_kabkot   Int
  kd_adm_provinsi Int
  nama_provinsi   String @db.VarChar(50)
  nama_kabkot     String @db.VarChar(50)

  @@schema("public")
}

model tb_bpn_rtr {
  id_rtr          Int    @id @default(autoincrement())
  url             String @db.VarChar(200)
  kd_adm_kabkot   Int
  kd_adm_provinsi Int
  nama_provinsi   String @db.VarChar(50)
  nama_kabkot     String @db.VarChar(50)

  @@schema("public")
}

model tb_roadmap {
  id_roadmap               Int                      @id @default(autoincrement())
  id_komoditi              Int
  judul                    String                   @db.VarChar(100)
  deskripsi                String                   @db.VarChar(1000)
  status                   Int?
  tb_komoditi_nasional_ref tb_komoditi_nasional_ref @relation(fields: [id_komoditi], references: [id_komoditi], onDelete: Cascade)
  tb_roadmap_file          tb_roadmap_file[]
  tb_roadmap_status        tb_roadmap_status[]
  tb_roadmap_tr            tb_roadmap_tr[]
  tb_roadmap_file_tr       tb_roadmap_file_tr[]

  @@schema("public")
}


model tb_roadmap_tr {
  id_roadmap_tr               Int                      @id @default(autoincrement())
  id_roadmap               Int
  judul                    String                   @db.VarChar(100)
  deskripsi                String                   @db.VarChar(1000)
  tb_roadmap tb_roadmap @relation(fields: [id_roadmap], references: [id_roadmap], onDelete: Cascade)

  @@schema("public")
}

model tb_roadmap_file {
  id_roadmap_file Int        @id @default(autoincrement())
  id_roadmap      Int
  nama            String     @db.VarChar(255)
  tb_roadmap      tb_roadmap @relation(fields: [id_roadmap], references: [id_roadmap], onDelete: Cascade)
  @@schema("public")
}

model tb_roadmap_file_tr {
  id_roadmap_file_tr Int        @id @default(autoincrement())
  id_roadmap    Int
  nama               String     @db.VarChar(255)
  tb_roadmap    tb_roadmap @relation(fields: [id_roadmap], references: [id_roadmap], onDelete: Cascade)
  @@schema("public")
}

model tb_roadmap_status {
  tb_roadmap_status Int       @id @default(autoincrement())
  id_roadmap        Int
    status         Int
    created_by     Int
    created_date   DateTime  @db.Timestamp(6)
    updated_by     Int?
    updated_date   DateTime? @db.Timestamp(6)
    status_proses  Int
    keterangan     String?
  tb_roadmap        tb_roadmap  @relation( fields: [id_roadmap], references: [id_roadmap], onDelete: Cascade)

  @@schema("public")
}

model tb_umkm_jenis {
  id_umkm_jenis Int       @id @default(autoincrement())
  nama          String    @db.VarChar(255)
  tb_umkm       tb_umkm[]

  @@schema("public")
}

model tb_kategori_sektor {
  id_kategori_sektor     Int                      @id @default(autoincrement())
  nama                   String                   @db.VarChar(255)
  tb_peluang_sektor      tb_peluang_sektor[]
  tb_sektor_nasional_ref tb_sektor_nasional_ref[]
  tb_kategori_sektor_tr  tb_kategori_sektor_tr[]

  @@schema("public")
}

model tb_kategori_sektor_tr {
  id_kategori_sektor_tr     Int                   @id @default(autoincrement())
  id_kategori_sektor     Int                      
  nama                   String                   @db.VarChar(255)
  tb_kategori_sektor tb_kategori_sektor @relation(fields: [id_kategori_sektor], references: [id_kategori_sektor], onDelete: Cascade)

  @@schema("public")
}

model z_tb_komoditi_daerah {
  id_komoditi_daerah   Int     @id @default(autoincrement())
  id_komoditi_nasional Int?
  id_sub_sektor_daerah Int?
  sentra_produksi      String? @db.VarChar(500)

  @@schema("public")
}

model z_tb_komoditi_daerah_value_detail {
  id_komoditi_daerah_value_detail Int       @id @default(autoincrement())
  id_komoditi_daerah              Int
  id_komoditi_nasional_value      Int?
  tahun                           Int?
  numeric_value                   Float?
  string_value                    String?   @db.VarChar(255)
  date_value                      DateTime? @db.Timestamp(6)

  @@schema("public")
}

model tb_peluang_layers {
  id_pl             Int               @id @default(autoincrement())
  id_peluang        Int
  layeruid          String            @db.VarChar(16)
  kategori          Int
  keterangan        String?           @db.VarChar(255)
  is_active         Boolean?
  tb_peluang_kabkot tb_peluang_kabkot @relation(fields: [id_peluang], references: [id_peluang_kabkot], onDelete: Cascade)

  @@schema("public")
}

model tb_komoditi_layers {
  id_kl                 Int      @id @default(autoincrement())
  id_komoditi_kabkot    Int?     @unique
  id_komoditi_provinsi  Int?     @unique
  layeruid              String   @db.VarChar(16)
  kategori              Int
  keterangan            String?  @db.VarChar(255)
  is_active             Boolean?

  tb_komoditi_kabkot tb_komoditi_kabkot? @relation(fields: [id_komoditi_kabkot], references: [id_komoditi_kabkot], onDelete: Cascade)
  tb_komoditi_provinsi tb_komoditi_provinsi? @relation(fields: [id_komoditi_provinsi], references: [id_komoditi_provinsi], onDelete: Cascade)


  @@schema("public")
}

model tb_kawasan_layers {
  id_kl               Int                 @id @default(autoincrement())
  id_kawasan_industri Int
  layeruid            String              @db.VarChar(16)
  kategori            Int
  keterangan          String?             @db.VarChar(255)
  is_active           Boolean?
  tb_kawasan_industri tb_kawasan_industri @relation(fields: [id_kawasan_industri], references: [id_kawasan_industri], onDelete: Cascade)

  @@schema("public")
}

model tb_status_proyek_email {
  id_status_proyek_email  Int                 @id @default(autoincrement())
  jenis                   Int                 // 1. Nodin 2. Informasi 
  email_penerima          String              @db.VarChar(40)
  id_peluang_kabkot       Int?
  id_peluang_daerah       Int?
  keterangan              String?             @db.VarChar(2000)
  created_at              DateTime            @default(now())

  @@schema("public")
}

model tb_running_text {
  id_running_text         Int                 @id @default(autoincrement())
  keterangan              String              @db.VarChar(500)
  keterangan_tr           String?              @db.VarChar(500)
  date                    DateTime
  status                  Int

  @@schema("public")
}

model tb_indikator_pic {
  id_indikator_pic         Int                 @id @default(autoincrement())
  nama_indikator           String              @db.VarChar(500)
  nama_tabel               String?              @db.VarChar(500)
  tb_mapping_indikator_pic  tb_mapping_indikator_pic[]
  @@schema("public")
}

model tb_mapping_indikator_pic {
  id_mapping_indikator_pic Int         @id @default(autoincrement())
  id_indikator_pic         Int                 
  id_user                  Int              
  kd_prov                  Int              
  tahun                    Int              
  tb_indikator_pic tb_indikator_pic @relation(fields: [id_indikator_pic], references: [id_indikator_pic], onDelete: Cascade)

  @@schema("public")
}


model tb_listrik {
  id_listrik       Int             @id @default(autoincrement())
  id_jenis       Int
  id_adm_kabkot  Int
  id_sumber_data Int?
  nama           String          @db.VarChar(255)
  keterangan     String
  file_logo      String?         @db.VarChar(255)
  alamat         String
  no_telp        String          @db.VarChar(30)
  no_fax         String          @db.VarChar(30)
  email          String          @db.VarChar(50)
  status         Int
  url_web        String?          @db.VarChar(255)
  lon            Float?
  lat            Float?
  is_ikn         Boolean?
  id_kategori_infrastruktur Int?
  tb_adm_kabkot  tb_adm_kabkot   @relation( fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_listrik_jenis tb_listrik_jenis  @relation( fields: [id_jenis], references: [id_jenis], onDelete: Cascade)
  tb_sumber_data tb_sumber_data? @relation(fields: [id_sumber_data], references: [id_sumber_data], onDelete: Cascade)
  tb_listrik_tr    tb_listrik_tr[]   
  tb_kategori_infrastruktur    tb_kategori_infrastruktur? @relation(fields: [id_kategori_infrastruktur], references: [id_kategori_infrastruktur], onDelete: Cascade)
  tb_listrik_status tb_listrik_status[]
  @@schema("public")
}


model tb_listrik_jenis {
  id_jenis Int        @id @default(autoincrement())
  nama           String     @db.VarChar(50)
  tb_listrik       tb_listrik[] 
  @@schema("public")
}

model tb_listrik_jenis_tr {
  id_listrik_jenis_tr Int    @id @default(autoincrement())
  id_listrik_jenis    Int
  kd_bahasa         String @db.VarChar(2)
  nama              String @db.VarChar(50)

  @@schema("public")
}

model tb_listrik_status {
  id_listrik        Int
  status          Int
  status_proses   Int
  keterangan      String
  created_by      Int
  created_date    DateTime  @db.Timestamp(6)
  updated_by      Int?
  updated_date    DateTime? @db.Timestamp(6)
  id_listrik_status Int       @id @default(autoincrement())
  tb_listrik        tb_listrik        @relation(fields: [id_listrik], references: [id_listrik], onDelete: Cascade)

  @@schema("public")
}

model tb_listrik_tr {
  id_listrik_tr Int      @id @default(autoincrement())
  id_listrik    Int
  kd_bahasa   String   @db.VarChar(2)
  nama        String   @db.VarChar(255)
  keterangan  String
  file_logo   String?  @db.VarChar(255)
  tb_listrik    tb_listrik @relation( fields: [id_listrik], references: [id_listrik], onDelete: Cascade)

  @@schema("public")
}


model tb_gas {
  id_gas       Int             @id @default(autoincrement())
  id_jenis       Int
  id_adm_kabkot  Int
  id_sumber_data Int?
  nama           String          @db.VarChar(255)
  keterangan     String
  file_logo      String?         @db.VarChar(255)
  alamat         String
  no_telp        String          @db.VarChar(30)
  no_fax         String          @db.VarChar(30)
  email          String          @db.VarChar(50)
  status         Int
  url_web        String?          @db.VarChar(255)
  lon            Float?
  lat            Float?
  is_ikn         Boolean?
  id_kategori_infrastruktur Int?
  tb_adm_kabkot  tb_adm_kabkot   @relation( fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_gas_jenis tb_gas_jenis  @relation( fields: [id_jenis], references: [id_jenis], onDelete: Cascade)
  tb_sumber_data tb_sumber_data? @relation(fields: [id_sumber_data], references: [id_sumber_data], onDelete: Cascade)
  tb_gas_tr    tb_gas_tr[]   
  tb_kategori_infrastruktur    tb_kategori_infrastruktur? @relation(fields: [id_kategori_infrastruktur], references: [id_kategori_infrastruktur], onDelete: Cascade)
  tb_gas_status tb_gas_status[]
  @@schema("public")
}


model tb_gas_jenis {
  id_jenis Int        @id @default(autoincrement())
  nama           String     @db.VarChar(50)
  tb_gas       tb_gas[] 
  @@schema("public")
}

model tb_gas_jenis_tr {
  id_gas_jenis_tr Int    @id @default(autoincrement())
  id_gas_jenis    Int
  kd_bahasa         String @db.VarChar(2)
  nama              String @db.VarChar(50)

  @@schema("public")
}

model tb_gas_status {
  id_gas        Int
  status          Int
  status_proses   Int
  keterangan      String
  created_by      Int
  created_date    DateTime  @db.Timestamp(6)
  updated_by      Int?
  updated_date    DateTime? @db.Timestamp(6)
  id_gas_status Int       @id @default(autoincrement())
  tb_gas        tb_gas        @relation(fields: [id_gas], references: [id_gas], onDelete: Cascade)

  @@schema("public")
}

model tb_gas_tr {
  id_gas_tr Int      @id @default(autoincrement())
  id_gas    Int
  kd_bahasa   String   @db.VarChar(2)
  nama        String   @db.VarChar(255)
  keterangan  String
  file_logo   String?  @db.VarChar(255)
  tb_gas    tb_gas @relation( fields: [id_gas], references: [id_gas], onDelete: Cascade)

  @@schema("public")
}


model tb_air {
  id_air       Int             @id @default(autoincrement())
  id_jenis       Int
  id_adm_kabkot  Int
  id_sumber_data Int?
  nama           String          @db.VarChar(255)
  keterangan     String
  file_logo      String?         @db.VarChar(255)
  alamat         String
  no_telp        String          @db.VarChar(30)
  no_fax         String          @db.VarChar(30)
  email          String          @db.VarChar(50)
  status         Int
  url_web        String?          @db.VarChar(255)
  lon            Float?
  lat            Float?
  is_ikn         Boolean?
  id_kategori_infrastruktur Int?
  tb_adm_kabkot  tb_adm_kabkot   @relation( fields: [id_adm_kabkot], references: [id_adm_kabkot], onDelete: Cascade)
  tb_air_jenis tb_air_jenis  @relation( fields: [id_jenis], references: [id_jenis], onDelete: Cascade)
  tb_sumber_data tb_sumber_data? @relation(fields: [id_sumber_data], references: [id_sumber_data], onDelete: Cascade)
  tb_air_tr    tb_air_tr[]   
  tb_kategori_infrastruktur    tb_kategori_infrastruktur? @relation(fields: [id_kategori_infrastruktur], references: [id_kategori_infrastruktur], onDelete: Cascade)
  tb_air_status tb_air_status[]
  @@schema("public")
}


model tb_air_jenis {
  id_jenis Int        @id @default(autoincrement())
  nama           String     @db.VarChar(50)
  tb_air       tb_air[] 
  @@schema("public")
}

model tb_air_jenis_tr {
  id_air_jenis_tr Int    @id @default(autoincrement())
  id_air_jenis    Int
  kd_bahasa         String @db.VarChar(2)
  nama              String @db.VarChar(50)

  @@schema("public")
}

model tb_air_status {
  id_air        Int
  status          Int
  status_proses   Int
  keterangan      String
  created_by      Int
  created_date    DateTime  @db.Timestamp(6)
  updated_by      Int?
  updated_date    DateTime? @db.Timestamp(6)
  id_air_status Int       @id @default(autoincrement())
  tb_air        tb_air        @relation(fields: [id_air], references: [id_air], onDelete: Cascade)

  @@schema("public")
}

model tb_air_tr {
  id_air_tr Int      @id @default(autoincrement())
  id_air    Int
  kd_bahasa   String   @db.VarChar(2)
  nama        String   @db.VarChar(255)
  keterangan  String
  file_logo   String?  @db.VarChar(255)
  tb_air    tb_air @relation( fields: [id_air], references: [id_air], onDelete: Cascade)

  @@schema("public")
}







