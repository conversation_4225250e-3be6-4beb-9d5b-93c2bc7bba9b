// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init



generator client {
  provider = "prisma-client-js"
  previewFeatures = ["multiSchema"]
  
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["public", "pu"]
}


model menu {
  id        Int      @id @default(autoincrement())
  menu_name     String
  url       String?  
  parent_id  Int?     
  parent    menu?    @relation("MenuToParent", fields: [parent_id], references: [id])
  children  menu[]   @relation("MenuToParent")
  order     Int      @default(0)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  role_access role_menu_access[]

  @@schema("public")
}


model auth_access_tokens {
  id    Int    @id @default(autoincrement())
  tokenable_id String
  hash  String
  type String 
  name  String?
  abilities  String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  last_used_at DateTime? @updatedAt
  expires_at DateTime? @updatedAt
  @@schema("public")
}


model role {
  id         Int      @id @default(autoincrement())
  role_name      String   
  user       users[]
  menu_access role_menu_access[]
  @@schema("public")
}

model role_menu_access {
  id        Int      @id @default(autoincrement())
  role_id    Int
  menu_id    Int
  role      role     @relation(fields: [role_id], references: [id])
  menu      menu     @relation(fields: [menu_id], references: [id])

  @@unique([role_id, menu_id])
  @@schema("public")
}


model users {
  id         Int      @id @default(autoincrement())
  email      String?  @unique 
  full_name  String?  
  login_name    String?  @unique 
  password   String   
  role_id    Int      @default(0)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  posts      post[]
  roles      role     @relation(fields: [role_id], references: [id])
  @@schema("public")
}

model post {
  id        Int      @id @default(autoincrement())
  title     String?
  desc      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user_id    Int
  user      users     @relation(fields: [user_id], references: [id])
  category_id    Int
  category      category     @relation(fields: [category_id], references: [id]) 
  
  @@schema("public")
}

model category {
  id    Int    @id @default(autoincrement())
  name  String @unique
  posts post[]
  @@schema("public")
}

model password_reset {
  id         Int      @id @default(autoincrement())
  email      String
  token      String   @unique
  expires_at  DateTime
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
  @@schema("public")
}

model tes {
  id         Int      @id @default(autoincrement())
  name      String
  
  @@schema("pu")
}

