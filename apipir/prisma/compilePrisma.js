import fs from 'fs'
import path from 'path'

function getPrismaFilesFromDirectory(directory) {
  return fs.readdirSync(directory)
    .filter(file => file.endsWith('.prisma'))
    .map(file => path.join(directory, file));
}

const schemaDirectories = [
  path.join('prisma/models'),
];

let schema = '';
schemaDirectories.forEach(directory => {
  const files = getPrismaFilesFromDirectory(directory);
  files.forEach(file => {
    schema += fs.readFileSync(file, 'utf-8') + '\n';
  });
});

fs.writeFileSync(path.join('prisma/schema.prisma'), schema);
