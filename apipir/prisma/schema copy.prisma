generator client {
  provider = "prisma-client-js"
  previewFeatures = ["multiSchema"]
  
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["public", "pu"]
}



model users {
  id         Int      @id @default(autoincrement())
  email      String?  @unique 
  full_name  String?  
  login_name    String?  @unique 
  password   String   
  role_id    Int      @default(0)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  roles      role     @relation(fields: [role_id], references: [id])
  @@schema("public")
}


model auth_access_tokens {
  id    Int    @id @default(autoincrement())
  tokenable_id String
  hash  String
  type String 
  name  String?
  abilities  String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  last_used_at DateTime? @updatedAt
  expires_at DateTime? @updatedAt
  @@schema("public")
}

model password_reset {
  id         Int      @id @default(autoincrement())
  email      String
  token      String   @unique
  expires_at  DateTime
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
  @@schema("public")
}

model config_upload {
  id         Int      @id @default(autoincrement())
  jenis      String   
  size      String   
  extnames      String   
  
  @@schema("public")
}

model menu {
  id        Int      @id @default(autoincrement())
  menu_name     String
  url       String?  
  parent_id  Int?     
  icon String?
  parent    menu?    @relation("MenuToParent", fields: [parent_id], references: [id])
  children  menu[]   @relation("MenuToParent")
  order     Int      @default(0)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  role_access role_menu_access[]

  @@schema("public")
}
model role {
  id         Int      @id @default(autoincrement())
  role_name      String   
  user       users[]
  menu_access role_menu_access[]
  @@schema("public")
}


model role_menu_access {
  id        Int      @id @default(autoincrement())
  role_id    Int
  menu_id    Int
  role      role     @relation(fields: [role_id], references: [id])
  menu      menu     @relation(fields: [menu_id], references: [id])

  @@unique([role_id, menu_id])
  @@schema("public")
}

model tb_app_slider {
  id_app_slider   Int @id @default(autoincrement())
  judul           String? @db.VarChar(255)
  deskripsi       String?
  nama_file_image String  @db.VarChar(255)
  url_link        String? @db.VarChar(255)
  ordering        Int?

  
  @@schema("public")
}

model tb_app_slider_peluang {
  id_app_slider   Int                        @id @default(autoincrement())
  judul           String?                    @db.VarChar(255)
  deskripsi       String?
  nama_file_image String                     @db.VarChar(255)
  url_link        String?                    @db.VarChar(255)
  ordering        Int?
  
  // Relasi ke tb_app_slider_peluang_tr
  tr              tb_app_slider_peluang_tr[]  @relation("SliderTranslations")

  @@schema("public")
}

model tb_app_slider_peluang_tr {
  id_app_slider_tr Int                        @id @default(autoincrement())
  id_app_slider    Int
  kd_bahasa        String                     @db.VarChar(2)
  judul            String?                    @db.VarChar(255)
  deskripsi        String?
  ordering         Int?
  nama_file_image  String?                    @db.VarChar(255)

  // Relasi ke tb_app_slider_peluang dengan nama relasi
  slider           tb_app_slider_peluang      @relation("SliderTranslations", fields: [id_app_slider], references: [id_app_slider])

  @@schema("public")
}



model tb_app_slider_tr {
  id_app_slider_tr Int @id @default(autoincrement())
  id_app_slider    Int
  kd_bahasa        String  @db.VarChar(2)
  judul            String? @db.VarChar(255)
  deskripsi        String?
  ordering         Int?
  nama_file_image  String? @db.VarChar(255)

  
  @@schema("public")
}

model tb_sektor_daerah {
  id_sektor_daerah   Int @default(autoincrement())
  tipe               Int
  id_sektor_nasional Int
  id_adm_kabkot      Int?
  id_adm_provinsi    Int?
  deskripsi_singkat  String
  deskripsi          String
  potensi_pasar      String
  no_dokumen         String? @db.VarChar(255)
  perihal            String? @db.VarChar(255)
  file_dokumen       String? @db.VarChar(255)
  status             Int

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_file {
  id_sektor_daerah_file Int  @default(autoincrement())
  id_sektor_daerah      Int
  tipe                  Int
  jenis                 Int
  nama                  String @db.VarChar(255)
  judul                 String @db.VarChar(255)
  keterangan            String

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_file_tr {
  id_sektor_daerah_file Int
  kd_bahasa             String @db.VarChar(2)
  nama                  String @db.VarChar(255)
  judul                 String @db.VarChar(255)
  keterangan            String

  @@ignore
  @@schema("public")
}


model tb_sektor_daerah_insentif {
  id_sektor_daerah_insentif Int  @default(autoincrement())
  id_sektor_daerah          Int
  nama                      String @db.VarChar(255)
  deskripsi                 String
  status                    Int

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_insentif_file {
  id_sektor_daerah_insentif_file Int  @default(autoincrement())
  id_sektor_daerah_insentif      Int
  tipe                           Int
  jenis                          Int
  nama                           String @db.VarChar(255)
  judul                          String @db.VarChar(255)
  keterangan                     String

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_insentif_file_tr {
  id_sektor_daerah_insentif_file Int
  kd_bahasa                      String @db.VarChar(2)
  nama                           String @db.VarChar(255)
  judul                          String @db.VarChar(255)
  keterangan                     String

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_insentif_status {
  id_sektor_daerah_insentif Int
  status                    Int
  status_proses             Int
  keterangan                String?
  created_by                Int
  created_date              DateTime  @db.Timestamp(6)
  updated_by                Int?
  updated_date              DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_insentif_tr {
  id_sektor_daerah_insentif Int
  kd_bahasa                 String @db.VarChar(2)
  nama                      String @db.VarChar(255)
  deskripsi                 String

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_lq {
  id_sektor_daerah Int
  tahun_pdrb       Int
  pdrb_sektor      Float
  pdrb_total       Float
  tahun_pdb        Int
  pdb_sektor       Float
  pdb_total        Float
  nilai_lq         Float

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_pdrb {
  id_sektor_daerah_pdrb Int  @default(autoincrement())
  id_adm_provinsi       Int?
  id_adm_kabkot         Int?
  id_sektor_nasional    Int
  tahun_pdrb            Int
  id_sumber_data        Int
  jumlah_pdrb           Decimal @db.Decimal(19, 0)
  status                Int

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_pdrb_status {
  id_sektor_daerah_pdrb Int
  tahun_pdrb            Int
  status                Int
  status_proses         Int
  keterangan            String?
  created_by            Int
  created_date          DateTime  @db.Timestamp(6)
  updated_by            Int?
  updated_date          DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_status {
  id_sektor_daerah Int
  status           Int
  status_proses    Int
  keterangan       String?
  created_by       Int
  created_date     DateTime  @db.Timestamp(6)
  updated_by       Int?
  updated_date     DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_sumber_data {
  id_sektor_daerah Int
  id_sumber_data   Int

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_tr {
  id_sektor_daerah  Int
  kd_bahasa         String  @db.VarChar(2)
  deskripsi_singkat String
  deskripsi         String
  potensi_pasar     String
  perihal           String? @db.VarChar(255)
  file_dokumen      String? @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sektor_daerah_value_detail {
  id_sektor_daerah         Int
  id_sektor_nasional_value Int
  tahun                    Int
  numeric_value            Float?    @db.Real
  string_value             String?   @db.VarChar(255)
  date_value               DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional {
  id_sektor_nasional Int
  deskripsi_singkat  String
  deskripsi          String
  potensi_pasar      String
  file_icon          String @db.VarChar(255)
  file_image         String @db.VarChar(255)
  status             Int

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_file {
  id_sektor_nasional_file Int  @default(autoincrement())
  id_sektor_nasional      Int
  tipe                    Int
  jenis                   Int
  nama                    String @db.VarChar(255)
  judul                   String @db.VarChar(255)
  keterangan              String

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_file_tr {
  id_sektor_nasional_file Int
  kd_bahasa               String @db.VarChar(2)
  nama                    String @db.VarChar(255)
  judul                   String @db.VarChar(255)
  keterangan              String

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_insentif {
  id_sektor_nasional_insentif Int  @default(autoincrement())
  id_sektor_nasional          Int
  nama                        String @db.VarChar(255)
  deskripsi                   String
  status                      Int

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_insentif_file {
  id_sektor_nasional_insentif_file Int  @default(autoincrement())
  id_sektor_nasional_insentif      Int
  tipe                             Int
  jenis                            Int
  nama                             String @db.VarChar(255)
  judul                            String @db.VarChar(255)
  keterangan                       String

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_insentif_file_tr {
  id_sektor_nasional_insentif_file Int
  kd_bahasa                        String @db.VarChar(2)
  nama                             String @db.VarChar(255)
  judul                            String @db.VarChar(255)
  keterangan                       String

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_insentif_status {
  id_sektor_nasional_insentif Int
  status                      Int
  status_proses               Int
  keterangan                  String?
  created_by                  Int
  created_date                DateTime  @db.Timestamp(6)
  updated_by                  Int?
  updated_date                DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_insentif_tr {
  id_sektor_nasional_insentif Int
  kd_bahasa                   String @db.VarChar(2)
  nama                        String @db.VarChar(255)
  deskripsi                   String

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_pdb {
  id_sektor_nasional Int
  tahun_pdb          Int
  id_sumber_data     Int
  jumlah_pdb         Decimal @db.Decimal(19, 0)
  status             Int

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_pdb_status {
  id_sektor_nasional Int
  tahun_pdb          Int
  status             Int
  status_proses      Int
  keterangan         String?
  created_by         Int
  created_date       DateTime  @db.Timestamp(6)
  updated_by         Int?
  updated_date       DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_ref {
  id_sektor Int
  nama      String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_ref_tr {
  id_sektor Int
  kd_bahasa String @db.VarChar(2)
  nama      String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_status {
  id_sektor_nasional Int
  status             Int
  status_proses      Int
  keterangan         String?
  created_by         Int
  created_date       DateTime  @db.Timestamp(6)
  updated_by         Int?
  updated_date       DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_sumber_data {
  id_sektor_nasional Int
  id_sumber_data     Int

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_tr {
  id_sektor_nasional Int
  kd_bahasa          String @db.VarChar(2)
  deskripsi_singkat  String
  deskripsi          String
  potensi_pasar      String
  file_icon          String @db.VarChar(255)
  file_image         String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_value {
  id_sektor_nasional_value Int  @default(autoincrement())
  id_sektor_nasional       Int
  nama                     String @db.VarChar(255)
  tipe                     Int    @db.SmallInt
  satuan                   String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_value_detail {
  id_sektor_nasional_value Int
  tahun                    Int
  numeric_value            Float?    @db.Real
  string_value             String?   @db.VarChar(255)
  date_value               DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_sektor_nasional_value_tr {
  id_sektor_nasional_value Int
  kd_bahasa                String @db.VarChar(2)
  nama                     String @db.VarChar(255)
  satuan                   String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah {
  id_peluang_daerah    Int      @id(map: "pk_peluang_daerah") @default(autoincrement())
  id_adm_provinsi      Int?
  id_adm_kabkot        Int?
  id_sub_sektor_daerah Int
  status_peluang       Int      @db.SmallInt
  status               Int      @db.SmallInt
  id_bandara           Int?
  jarak_bandara        Int      @default(0) @db.SmallInt
  id_pelabuhan         Int?
  jarak_pelabuhan      Int      @default(0) @db.SmallInt
  jarak_ibukota        Int      @default(0) @db.SmallInt
  kode_kbli            String?  @db.VarChar(150)
  judul                String?  @db.VarChar(255)
  lokasi               String?  @db.VarChar(255)
  tahun                Int      @default(2022) @db.SmallInt
  keterangan           String?
  aspek_pasar          String?
  aspek_teknis         String?
  lon                  Float?
  lat                  Float?
  zoom_peta_default    Int?
  is_ikn               Boolean?

  @@schema("public")
}

model tb_peluang_daerah_file {
  id_peluang_daerah_file Int @default(autoincrement())
  id_peluang_daerah      Int
  tipe                   Int
  jenis                  Int
  nama                   String @db.VarChar(255)
  judul                  String @db.VarChar(255)
  keterangan             String

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_file_tr {
  id_peluang_daerah_file Int
  kd_bahasa              String @db.VarChar(2)
  nama                   String @db.VarChar(255)
  judul                  String @db.VarChar(255)
  keterangan             String

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_insentif {
  id_peluang_daerah_insentif  Int @default(autoincrement())
  id_peluang_daerah           Int
  id_sektor_nasional_insentif Int?
  id_sektor_daerah_insentif   Int?

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_kelayakan {
  id_peluang_daerah  Int
  pct_cost_capital   Int   @db.SmallInt
  year_period        Int   @db.SmallInt
  base_revenue       Float
  pct_revenue_growth Int   @db.SmallInt
  base_opex          Float
  pct_inflation      Int   @db.SmallInt
  initial_invesment  Float
  pct_salvage_value  Int   @db.SmallInt
  irr                Float
  npv                Float
  pp                 Int   @db.SmallInt

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_kelayakan_detail {
  id_peluang_daerah Int
  jenis             Int
  tahun             Int
  nilai             Float

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_komoditi {
  id_peluang_daerah  Int
  id_komoditi_daerah Int
  jenis              String @db.VarChar(255)
  manfaat            String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_kontak {
  id_peluang_daerah Int
  id_peluang_kontak Int

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_layer_spasial {
  id_peluang_daerah_layer_spasial Int @default(autoincrement())
  id_peluang_daerah               Int
  nama_layer                      String @db.VarChar(500)
  tipe                            Int
  url_service                     String @db.VarChar(500)
  status                          Int

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_status {
  id_peluang_daerah Int
  status            Int       @db.SmallInt
  status_proses     Int       @db.SmallInt
  keterangan        String?
  created_by        Int
  created_date      DateTime  @db.Timestamp(6)
  updated_by        Int?
  updated_date      DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_peluang_daerah_tr {
  id_peluang_daerah Int
  kd_bahasa         String  @db.VarChar(2)
  judul             String? @db.VarChar(255)
  lokasi            String? @db.VarChar(255)
  keterangan        String?
  aspek_pasar       String?
  aspek_teknis      String?

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot {
  id_adm_kabkot           Int
  id_sektor               Int
  tahun                   Int
  id_sumber_data          Int
  id_prioritas            Int
  id_adm_kabkot_kantor    Int
  nama                    String
  keterangan              String?
  nilai_investasi         Float?
  nilai_irr               Float?
  nilai_npv               Float?
  nilai_pp                Float?
  status                  String?
  lon                     Float?
  lat                     Float?
  shape                   String?
  id_kontak               Int?
  nama_singkat            String?
  deskripsi_singkat       String?
  deskripsi               String?
  lokasi_kawasan          String?
  modified_time           DateTime?
  kode_kbli               String?
  zoom_peta_default       Int?
  project_status_enum     String?
  is_ikn                  Boolean


  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_berita_mapping {
  id_peluang_kabkot Int
  id_berita         Int
  created_time      DateTime? @default(now()) @db.Timestamptz(6)

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_file {
  id_peluang_kabkot_file Int @default(autoincrement())
  id_peluang_kabkot      Int
  tipe                   Int
  nama                   String? @db.VarChar(255)
  url_rest               String? @db.VarChar(255)
  status                 Int?    @default(0)

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_file_tr {
  id_peluang_kabkot_file_tr Int
  id_peluang_kabkot         Int
  kd_bahasa                 String  @db.VarChar(2)
  tipe                      Int
  nama                      String? @db.VarChar(255)
  url_rest                  String? @db.VarChar(255)
  status                    Int?    @default(0)

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_insentif {
  id_peluang_kabkot Int
  id_jenis_insentif Int

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_kontak {
  id_peluang_kabkot Int
  id_peluang_kontak Int

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_likes_counter {
  id_likes          Int
  id_peluang_kabkot Int
  ip_pengunjung     String    @db.VarChar(15)
  created_time      DateTime? @default(now()) @db.Timestamptz(6)

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_status {
  id_peluang_kabkot Int
  status            Int
  status_proses     Int
  keterangan        String?
  created_by        Int
  created_date      DateTime  @db.Timestamp(6)
  updated_by        Int?
  updated_date      DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_peluang_kabkot_tr {
  id_peluang_kabkot_tr Int @default(autoincrement())
  id_peluang_kabkot    Int
  kd_bahasa            String  @db.VarChar(2)
  nama                 String  @db.VarChar(255)
  keterangan           String
  nama_singkat         String? @db.VarChar(36)
  deskripsi_singkat    String? @db.VarChar(120)
  deskripsi            String? @db.VarChar(600)
  lokasi_kawasan       String? @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_peluang_kontak {
  id_peluang_kontak Int @default(autoincrement())
  nama              String? @db.VarChar(255)
  alamat            String? @db.VarChar(255)
  url_web           String? @db.VarChar(255)
  cp                String? @db.VarChar(255)
  jabatan_cp        String? @db.VarChar(255)
  email             String? @db.VarChar(255)
  no_telp           String? @db.VarChar(255)
  no_fax            String? @db.VarChar(255)
  lon               Float?
  lat               Float?
  id_adm_provinsi   Int?
  id_adm_kabkot     Int?

  @@ignore
  @@schema("public")
}

model tb_peluang_prioritas {
  id_peluang_prioritas Int @default(autoincrement())
  nama                 String @db.VarChar(50)
  keterangan           String

  @@ignore
  @@schema("public")
}

model tb_peluang_prioritas_tr {
  id_peluang_prioritas_tr Int @default(autoincrement())
  id_peluang_prioritas    Int
  kd_bahasa               String  @db.VarChar(2)
  nama                    String? @db.VarChar(50)
  keterangan              String

  @@ignore
  @@schema("public")
}

model tb_peluang_provinsi_file {
  id_peluang_provinsi_file Int @default(autoincrement())
  id_peluang_provinsi      Int
  tipe                     Int
  nama                     String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_peluang_provinsi_status {
  id_peluang_provinsi Int
  status              Int
  status_proses       Int
  keterangan          String
  created_by          Int
  created_date        DateTime  @db.Timestamp(6)
  updated_by          Int?
  updated_date        DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_peluang_provinsi_tr {
  id_peluang_provinsi_tr Int @default(autoincrement())
  id_peluang_provinsi    Int
  kd_bahasa              String @db.VarChar(2)
  nama                   String @db.VarChar(255)
  keterangan             String

  @@ignore
  @@schema("public")
}

model tb_peluang_sektor {
  id_peluang_sektor Int @default(autoincrement())
  nama              String  @db.VarChar(50)
  keterangan        String
  icon              String? @db.VarChar(255)
  iconmap           String? @db.VarChar(255)
  image             String? @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_peluang_sektor_tr {
  id_peluang_sektor_tr Int @default(autoincrement())
  id_peluang_sektor    Int
  kd_bahasa            String @db.VarChar(2)
  nama                 String @db.VarChar(50)
  keterangan           String

  @@ignore
  @@schema("public")
}

model tb_adm_provinsi {
  id_adm_provinsi   Int     
  id_adm_wilayah    Int
  kd_adm            String
  nama              String
  nama_ibukota      String
  file_logo         String?
  file_image        String?
  deskripsi         String?
  luas_wilayah      Float?
  jumlah_penduduk   Int?
  alamat            String?
  no_telp           String?
  no_fax            String?
  url_web           String?
  lon               Float?
  lat               Float?
  shape             String?
  
  @@ignore
  @@schema("public")
}


model tb_adm_provinsi_tr {
  id_adm_provinsi_tr Int    @default(autoincrement())
  id_adm_provinsi    Int
  kd_bahasa          String @db.VarChar(2)
  nama               String @db.VarChar(50)
  nama_ibukota       String @db.VarChar(50)
  file_logo          String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_adm_provinsi_layer_spasial {
  id_adm_provinsi_layer_spasial Int      @default(autoincrement())
  id_adm_provinsi               Int
  nama_layer                    String   @db.VarChar(500)
  tipe                          Int
  url_service                   String   @db.VarChar(500)
  status                        Int
  is_active                     Boolean?

  @@ignore
  @@schema("public")
}

model tb_adm_kabkot {
  id_adm_kabkot       Int     
  id_adm_provinsi     Int
  kd_adm              String
  jenis               String
  nama                String
  nama_ibukota        String
  file_logo           String?
  file_image          String?
  deskripsi           String?
  luas_wilayah        Float?
  jumlah_penduduk     Int?
  alamat              String?
  no_telp             String?
  no_fax              String?
  url_web             String?
  lon                 Float?
  lat                 Float?
  shape               String?
  is_daerah_tertinggal Boolean @default(false)
    @@ignore
  @@schema("public")
}

model tb_adm_kabkot_alias {
  id_adm_kabkot          Int
  nama_mineral_logam     String? @db.VarChar(50)
  nama_mineral_non_logam String? @db.VarChar(50)
  nama_batubara          String? @db.VarChar(50)
  nama_panas_bumi        String? @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_adm_kabkot_layer_spasial {
  id_adm_kabkot_layer_spasial Int      @default(autoincrement())
  id_adm_kabkot               Int
  nama_layer                  String   @db.VarChar(500)
  tipe                        Int
  url_service                 String   @db.VarChar(500)
  status                      Int
  is_active                   Boolean?

  @@ignore
  @@schema("public")
}

model tb_adm_kabkot_tr {
  id_adm_kabkot_tr Int?    @default(autoincrement())
  id_adm_kabkot    Int?
  kd_bahasa        String? @db.VarChar(2)
  nama             String? @db.VarChar(50)
  nama_ibukota     String? @db.VarChar(50)
  file_logo        String? @db.VarChar(255)

  @@ignore
  @@schema("public")
}


model tb_adm_wilayah {
  id_adm_wilayah Int
  nama           String @db.VarChar(50)
  deskripsi      String @db.VarChar(255)
  index          Int

  @@ignore
  @@schema("public")
}

model tb_adm_wilayah_tr {
  id_adm_wilayah_tr Int    @default(autoincrement())
  id_adm_wilayah    Int
  kd_bahasa         String @db.VarChar(2)
  nama              String @db.VarChar(50)
  deskripsi         String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_umr_kabkot {
  id_umr_kabkot  Int @default(autoincrement())
  id_adm_kabkot  Int
  tahun          Int
  id_sumber_data Int?
  nilai          Float
  status         Int

  @@ignore
  @@schema("public")
}

model tb_umr_kabkot_status {
  id_umr_kabkot Int
  status        Int
  status_proses Int
  keterangan    String
  created_by    Int
  created_date  DateTime  @db.Timestamp(6)
  updated_by    Int?
  updated_date  DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_umr_provinsi {
  id_umr_provinsi Int @default(autoincrement())
  id_adm_provinsi Int
  tahun           Int
  id_sumber_data  Int?
  nilai           Float
  status          Int

  @@ignore
  @@schema("public")
}

model tb_umr_provinsi_status {
  id_umr_provinsi Int
  status          Int
  status_proses   Int
  keterangan      String
  created_by      Int
  created_date    DateTime  @db.Timestamp(6)
  updated_by      Int?
  updated_date    DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}


model tb_kawasan_industri {
  id_kawasan_industri               Int @id @default(autoincrement())
  id_adm_kabkot                     Int
  id_sumber_data                    Int?
  id_kategori                       Int?
  nama                              String   @db.VarChar(255)
  keterangan                        String?  @db.Text
  alamat                            String?  @db.VarChar(255)
  luas                              Float?
  luas_satuan                       String?  @db.VarChar(50)
  id_bandara_terdekat                Int?
  jarak_bandara_terdekat             Float?
  id_pelabuhan_terdekat              Int?
  jarak_pelabuhan_terdekat           Float?
  jarak_ibukota                      Float?
  url_web                           String?  @db.VarChar(255)
  no_telp                           String?  @db.VarChar(50)
  no_fax                            String?  @db.VarChar(50)
  email                             String?  @db.VarChar(255)
  cp                                String?  @db.VarChar(255)
  ketersediaan                      String?  @db.VarChar(50)
  status                            String?  @db.VarChar(50)
  lon                               Float?
  lat                               Float?
  shape                             String?  @db.Text
  is_ikn                            Boolean?
  id_kawasan_industri_ref_range      Int?
  major_tenants                     String?  @db.Text
  id_kawasan_industri_occupancy      Int?

  @@schema("public")
}

model tes_table {
    id Int @id @default(autoincrement())
    text String?
  @@schema("public")
}