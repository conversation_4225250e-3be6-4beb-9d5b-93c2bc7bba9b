model tb_buku_tamu {
  id_buku_tamu  Int      @default(autoincrement())
  id_adm_kabkot Int?
  tujuan        String?  @db.VarChar(100)
  instansi      String?  @db.VarChar(100)
  deskripsi     String?
  file          String?  @db.Var<PERSON>har(100)
  created_date  DateTime @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_buku_tamu_pengunjung {
  id_buku_tamu_pengunjung Int     @default(autoincrement())
  id_buku_tamu            Int?
  nama                    String? @db.VarChar(100)
  nip                     String? @db.VarChar(20)
  jabatan                 String? @db.VarChar(100)
  file_ttd                String? @db.VarChar(100)
  no_hp                   String? @db.VarChar(14)

  @@ignore
  @@schema("public")
}