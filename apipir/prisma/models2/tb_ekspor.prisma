model tb_ekspor_kabkot {
  id_ekspor_kabkot Int
  id_adm_kabkot    Int
  id_sumber_data   Int?
  tahun            Int?
  keterangan       String?
  nilai_ekspor     Float?
  status           Int?
  nilai_impor      Float?

  @@ignore
  @@schema("public")
}

model tb_ekspor_kabkot_status {
  id_ekspor_kabkot Int
  status           Int
  status_proses    Int
  keterangan       String
  created_by       Int
  created_date     DateTime  @db.Timestamp(6)
  updated_by       Int?
  updated_date     DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_ekspor_kabkot_tr {
  id_ekspor_kabkot_tr Int
  id_ekspor_kabkot    Int
  kd_bahasa           String  @db.VarChar(2)
  keterangan          String?

  @@ignore
  @@schema("public")
}

model tb_ekspor_provinsi {
  id_ekspor_provinsi Int
  id_adm_provinsi    Int
  id_sumber_data     Int?
  tahun              Int?
  keterangan         String?
  nilai_ekspor       Float?
  status             Int?
  nilai_impor        Float?

  @@ignore
  @@schema("public")
}

model tb_ekspor_provinsi_status {
  id_ekspor_provinsi Int
  status             Int
  status_proses      Int
  keterangan         String
  created_by         Int
  created_date       DateTime  @db.Timestamp(6)
  updated_by         Int?
  updated_date       DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_ekspor_provinsi_tr {
  id_ekspor_provinsi_tr Int
  id_ekspor_provinsi    Int
  kd_bahasa             String  @db.VarChar(2)
  keterangan            String?

  @@ignore
  @@schema("public")
}