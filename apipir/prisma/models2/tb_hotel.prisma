model tb_hotel_kelas {
  id_hotel_kelas Int
  nama           String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_hotel_kelas_tr {
  id_hotel_kelas_tr Int
  id_hotel_kelas    Int
  kd_bahasa         String @db.VarChar(2)
  nama              String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_hotel_status {
  id_hotel      Int
  status        Int
  status_proses Int
  keterangan    String
  created_by    Int
  created_date  DateTime  @db.Timestamp(6)
  updated_by    Int?
  updated_date  DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_hotel_tr {
  id_hotel_tr Int
  id_hotel    Int
  kd_bahasa   String @db.VarChar(2)
  nama        String @db.VarChar(255)
  keterangan  String
  file_logo   String @db.VarChar(255)

  @@ignore
  @@schema("public")
}