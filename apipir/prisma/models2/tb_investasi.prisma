model tb_investasi_jenis {
  id_investasi_jenis Int
  nama               String? @db.VarChar(50)
  keterangan         String

  @@ignore
  @@schema("public")
}

model tb_investasi_jenis_data {
  id_investasi_jenis_data Int
  nama                    String? @db.Var<PERSON>har(50)
  keterangan              String

  @@ignore
  @@schema("public")
}

model tb_investasi_jenis_data_tr {
  id_investasi_jenis_data_tr Int
  id_investasi_jenis_data    Int
  kd_bahasa                  String  @db.VarChar(2)
  nama                       String? @db.VarChar(50)
  keterangan                 String

  @@ignore
  @@schema("public")
}

model tb_investasi_jenis_tr {
  id_investasi_jenis_tr Int
  id_investasi_jenis    Int
  kd_bahasa             String  @db.VarChar(2)
  nama                  String? @db.VarChar(50)
  keterangan            String

  @@ignore
  @@schema("public")
}

model tb_investasi_kabkot {
  id_investasi_kabkot Int
  id_adm_kabkot       Int
  id_sumber_data      Int?
  id_sektor           Int
  id_jenis            Int
  id_jenis_data       Int
  tahun               Int
  jumlah_proyek       Int
  jumlah_investasi    Float
  status              Int

  @@ignore
  @@schema("public")
}

model tb_investasi_kabkot_status {
  id_investasi_kabkot Int
  status              Int
  status_proses       Int
  keterangan          String
  created_by          Int
  created_date        DateTime  @db.Timestamp(6)
  updated_by          Int?
  updated_date        DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_investasi_provinsi {
  id_investasi_provinsi Int
  id_adm_provinsi       Int
  id_sumber_data        Int?
  id_sektor             Int
  id_jenis              Int
  id_jenis_data         Int
  tahun                 Int
  jumlah_proyek         Int
  jumlah_investasi      Float
  status                Int

  @@ignore
  @@schema("public")
}

model tb_investasi_provinsi_status {
  id_investasi_provinsi Int
  status                Int
  status_proses         Int
  keterangan            String
  created_by            Int
  created_date          DateTime  @db.Timestamp(6)
  updated_by            Int?
  updated_date          DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_investasi_sektor {
  id_investasi_sektor Int
  nama                String? @db.VarChar(255)
  keterangan          String

  @@ignore
  @@schema("public")
}

model tb_investasi_sektor_tr {
  id_investasi_sektor_tr Int
  id_investasi_sektor    Int
  kd_bahasa              String  @db.VarChar(2)
  nama                   String? @db.VarChar(255)
  keterangan             String

  @@ignore
  @@schema("public")
}