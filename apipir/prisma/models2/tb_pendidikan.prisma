model tb_pendidikan_jenjang {
  id_pendidikan_jenjang Int
  nama                  String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_pendidikan_jenjang_tr {
  id_pendidikan_jenjang_tr Int
  id_pendidikan_jenjang    Int
  kd_bahasa                String @db.VarChar(2)
  nama                     String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_pendidikan_kategori {
  id_pendidikan_kategori Int
  nama                   String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_pendidikan_kategori_tr {
  id_pendidikan_kategori_tr Int
  id_pendidikan_kategori    Int
  kd_bahasa                 String @db.VarChar(2)
  nama                      String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_pendidikan_status {
  id_pendidikan Int
  status        Int
  status_proses Int
  keterangan    String
  created_by    Int
  created_date  DateTime  @db.Timestamp(6)
  updated_by    Int?
  updated_date  DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_pendidikan_tr {
  id_pendidikan_tr Int
  id_pendidikan    Int
  kd_bahasa        String @db.VarChar(2)
  nama             String @db.VarChar(255)
  keterangan       String

  @@ignore
  @@schema("public")
}