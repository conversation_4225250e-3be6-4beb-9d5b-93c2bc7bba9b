model tb_adm_kabkot_alias {
  id_adm_kabkot          Int
  nama_mineral_logam     String? @db.VarChar(50)
  nama_mineral_non_logam String? @db.VarChar(50)
  nama_batubara          String? @db.VarChar(50)
  nama_panas_bumi        String? @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_adm_kabkot_layer_spasial {
  id_adm_kabkot_layer_spasial Int      @default(autoincrement())
  id_adm_kabkot               Int
  nama_layer                  String   @db.VarChar(500)
  tipe                        Int
  url_service                 String   @db.VarChar(500)
  status                      Int
  is_active                   Boolean?

  @@ignore
  @@schema("public")
}

model tb_adm_kabkot_tr {
  id_adm_kabkot_tr Int
  id_adm_kabkot    Int
  kd_bahasa        String @db.VarChar(2)
  nama             String @db.VarChar(50)
  nama_ibukota     String @db.VarChar(50)
  file_logo        String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_adm_kecamatan_tr {
  id_adm_kecamatan_tr Int
  id_adm_kecamatan    Int
  kd_bahasa           String @db.VarChar(2)
  nama                String @db.VarChar(50)
  nama_ibukota        String @db.VarChar(50)
  file_logo           String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_adm_provinsi_alias {
  id_adm_provinsi        Int
  nama_mineral_logam     String? @db.VarChar(50)
  nama_mineral_non_logam String? @db.VarChar(50)
  nama_batubara          String? @db.VarChar(50)
  nama_panas_bumi        String? @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_adm_provinsi_layer_spasial {
  id_adm_provinsi_layer_spasial Int      @default(autoincrement())
  id_adm_provinsi               Int
  nama_layer                    String   @db.VarChar(500)
  tipe                          Int
  url_service                   String   @db.VarChar(500)
  status                        Int
  is_active                     Boolean?

  @@ignore
  @@schema("public")
}

model tb_adm_provinsi_tr {
  id_adm_provinsi_tr Int
  id_adm_provinsi    Int
  kd_bahasa          String @db.VarChar(2)
  nama               String @db.VarChar(50)
  nama_ibukota       String @db.VarChar(50)
  file_logo          String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_adm_user {
  id_user         Int
  id_adm_provinsi Int?
  id_adm_kabkot   Int?

  @@ignore
  @@schema("public")
}

model tb_adm_user_hapus {
  id_user         Int?
  id_adm_provinsi Int?
  id_adm_kabkot   Int?

  @@ignore
  @@schema("public")
}

model tb_adm_wilayah {
  id_adm_wilayah Int
  nama           String @db.VarChar(50)
  deskripsi      String @db.VarChar(255)
  index          Int

  @@ignore
  @@schema("public")
}

model tb_adm_wilayah_tr {
  id_adm_wilayah_tr Int
  id_adm_wilayah    Int
  kd_bahasa         String @db.VarChar(2)
  nama              String @db.VarChar(50)
  deskripsi         String @db.VarChar(255)

  @@ignore
  @@schema("public")
}