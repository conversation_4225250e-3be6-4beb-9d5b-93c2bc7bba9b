model tb_rumah_sakit_jenis {
  id_rumah_sakit_jenis Int
  nama                 String? @db.<PERSON>ar<PERSON>(50)

  @@ignore
  @@schema("public")
}

model tb_rumah_sakit_jenis_tr {
  id_rumah_sakit_jenis_tr Int
  id_rumah_sakit_jenis    Int?
  kd_bahasa               String? @db.VarChar(2)
  nama                    String? @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_rumah_sakit_kategori {
  id_rumah_sakit_kategori Int
  nama                    String? @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_rumah_sakit_kategori_tr {
  id_rumah_sakit_kategori_tr Int
  id_rumah_sakit_kategori    Int?
  kd_bahasa                  String? @db.VarChar(2)
  nama                       String? @db.<PERSON>(50)

  @@ignore
  @@schema("public")
}

model tb_rumah_sakit_status {
  id_rumah_sakit Int
  status         Int
  status_proses  Int?
  keterangan     String?
  created_by     Int?
  created_date   DateTime? @db.Timestamp(6)
  updated_by     Int?
  updated_date   DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_rumah_sakit_tr {
  id_rumah_sakit_tr Int
  id_rumah_sakit    Int?
  kd_bahasa         String? @db.VarChar(2)
  nama              String? @db.VarChar(255)
  keterangan        String?

  @@ignore
  @@schema("public")
}