model tb_jenis_insentif {
  id_jenis_insentif Int
  nama              String @db.VarChar(255)
  keterangan        String
  status            Int
  tipe              Int?
  id_adm_provinsi   Int?
  id_adm_kabkot     Int?

  @@ignore
  @@schema("public")
}

model tb_jenis_insentif_file {
  id_jenis_insentif_file Int
  id_jenis_insentif      Int
  tipe                   Int
  nama                   String @db.VarChar(255)
  judul                  String @db.VarChar(255)
  keterangan             String

  @@ignore
  @@schema("public")
}

model tb_jenis_insentif_file_tr {
  id_jenis_insentif_file_tr Int
  id_jenis_insentif         Int
  kd_bahasa                 String @db.VarChar(2)
  judul                     String @db.VarChar(255)
  keterangan                String
  nama                      String @db.VarChar(255)
  tipe                      Int?

  @@ignore
  @@schema("public")
}

model tb_jenis_insentif_kbli {
  id_jenis_insentif Int
  id_kbli           Int

  @@ignore
  @@schema("public")
}

model tb_jenis_insentif_status {
  id_jenis_insentif Int
  status            Int
  status_proses     Int
  keterangan        String
  created_by        Int
  created_date      DateTime  @db.Timestamp(6)
  updated_by        Int?
  updated_date      DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_jenis_insentif_tr {
  id_jenis_insentif_tr Int
  id_jenis_insentif    Int
  kd_bahasa            String @db.VarChar(2)
  nama                 String @db.VarChar(255)
  keterangan           String

  @@ignore
  @@schema("public")
}