model tb_sumber_data {
  id_sumber_data       Int
  id_sumber_data_judul Int
  tahun_rumus          Int?

  @@ignore
  @@schema("public")
}

model tb_sumber_data_instansi {
  id_sumber_data_instansi Int
  id_adm_provinsi         Int?
  id_adm_kabkot           Int?
  nama                    String? @db.VarChar(255)
  alamat                  String? @db.VarChar(255)
  url_web                 String? @db.VarChar(255)
  cp                      String? @db.Var<PERSON>har(255)
  email                   String? @db.VarChar(255)
  no_telp                 String? @db.VarChar(255)
  no_fax                  String? @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sumber_data_instansi_tr {
  id_sumber_data_instansi_tr Int
  id_sumber_data_instansi    Int
  kd_bahasa                  String @db.VarChar(2)
  nama                       String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_sumber_data_judul {
  id_sumber_data_judul    Int
  id_sumber_data_instansi Int
  judul                   String @db.VarChar(255)
  keterangan              String

  @@ignore
  @@schema("public")
}

model tb_sumber_data_judul_tr {
  id_sumber_data_judul_tr Int
  id_sumber_data_judul    Int
  kd_bahasa               String @db.VarChar(2)
  judul                   String @db.VarChar(255)
  keterangan              String

  @@ignore
  @@schema("public")
}

model tb_sumber_data_tr {
  id_sumber_data_tr Int
  id_sumber_data    Int
  kd_bahasa         String @db.VarChar(2)
  judul             String @db.VarChar(255)
  keterangan        String

  @@ignore
  @@schema("public")
}