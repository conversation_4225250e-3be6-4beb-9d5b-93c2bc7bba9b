model tb_umr_kabkot {
  id_umr_kabkot  Int
  id_adm_kabkot  Int
  tahun          Int
  id_sumber_data Int?
  nilai          Float
  status         Int

  @@ignore
  @@schema("public")
}

model tb_umr_kabkot_status {
  id_umr_kabkot Int
  status        Int
  status_proses Int
  keterangan    String
  created_by    Int
  created_date  DateTime  @db.Timestamp(6)
  updated_by    Int?
  updated_date  DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_umr_provinsi {
  id_umr_provinsi Int
  id_adm_provinsi Int
  tahun           Int
  id_sumber_data  Int?
  nilai           Float
  status          Int

  @@ignore
  @@schema("public")
}