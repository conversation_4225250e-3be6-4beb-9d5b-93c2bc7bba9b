model tb_demografi_kabkot {
  id_demografi_kabkot  Int
  id_adm_kabkot        Int
  tahun                Int
  id_kategori          Int
  id_sumber_data       Int?
  jumlah_pria          Int
  jumlah_wanita        Int
  kepadatan_penduduk   Float
  pertumbuhan_penduduk Float
  status               Int

  @@ignore
  @@schema("public")
}

model tb_demografi_kabkot_status {
  id_demografi_kabkot Int
  status              Int
  status_proses       Int
  keterangan          String
  created_by          Int
  created_date        DateTime  @db.Timestamp(6)
  updated_by          Int?
  updated_date        DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_demografi_kategori {
  id_demografi_kategori Int
  nama                  String  @db.VarChar(255)
  warna                 String? @db.VarChar(7)

  @@ignore
  @@schema("public")
}

model tb_demografi_kategori_tr {
  id_demografi_kategori_tr Int
  id_demografi_kategori    Int
  kd_bahasa                String @db.VarChar(2)
  nama                     String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_demografi_provinsi {
  id_demografi_provinsi Int
  id_adm_provinsi       Int
  tahun                 Int
  id_kategori           Int
  id_sumber_data        Int?
  jumlah_pria           Int
  jumlah_wanita         Int
  kepadatan_penduduk    Float
  pertumbuhan_penduduk  Float
  status                Int

  @@ignore
  @@schema("public")
}

model tb_demografi_provinsi_status {
  id_demografi_provinsi Int
  status                Int
  status_proses         Int
  keterangan            String
  created_by            Int
  created_date          DateTime  @db.Timestamp(6)
  updated_by            Int?
  updated_date          DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}