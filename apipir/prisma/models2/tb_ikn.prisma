model tb_ikn {
  id_ikn     Int
  nama       String   @db.VarChar(255)
  deskripsi  String
  file_image String   @db.VarChar(255)
  file_logo  String   @db.VarChar(255)
  lon        Float
  lat        Float
  view_count Int
  last_view  DateTime @db.Timestamp(6)
  file_video String   @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_ikn_data {
  id_ikn_data Int
  id_ikn      Int
  jenis       Int
  nama        String   @db.VarChar(255)
  deskripsi   String
  file_image  String?  @db.VarChar(255)
  status      Int
  is_fe       Boolean?
  fe_posisi   Int

  @@ignore
  @@schema("public")
}

model tb_ikn_data_file {
  id_ikn_data_file Int
  id_ikn_data      Int
  tipe             Int
  nama             String @db.VarChar(255)
  judul            String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_ikn_data_file_tr {
  id_ikn_data_file Int
  kd_bahasa        String @db.VarChar(2)
  nama             String @db.Var<PERSON>har(255)
  judul            String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_ikn_data_tr {
  id_ikn_data Int
  kd_bahasa   String @db.VarChar(2)
  nama        String @db.VarChar(255)
  deskripsi   String

  @@ignore
  @@schema("public")
}

model tb_ikn_file {
  id_ikn_file Int
  id_ikn      Int
  jenis       Int
  tipe        Int
  nama        String @db.VarChar(255)
  judul       String @db.VarChar(255)
  status      Int

  @@ignore
  @@schema("public")
}

model tb_ikn_file_tr {
  id_ikn_file Int
  kd_bahasa   String @db.VarChar(2)
  nama        String @db.VarChar(255)
  judul       String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_ikn_kabkot {
  id_ikn        Int
  id_adm_kabkot Int

  @@ignore
  @@schema("public")
}

model tb_ikn_layer {
  id_ikn_layer Int
  id_ikn       Int
  judul        String @db.VarChar(255)
  nama_layer   String @db.VarChar(255)
  tipe         Int
  url_service  String @db.VarChar(255)
  status       Int

  @@ignore
  @@schema("public")
}

model tb_ikn_peluang {
  id_ikn_peluang    Int
  id_ikn            Int
  id_peluang_kabkot Int?
  id_peluang_daerah Int?
  status            Int

  @@ignore
  @@schema("public")
}

model tb_ikn_tr {
  id_ikn    Int
  kd_bahasa String @db.VarChar(2)
  nama      String @db.VarChar(255)
  deskripsi String

  @@ignore
  @@schema("public")
}