model tb_kawasan_industri_blok_fungsi {
  id_kawasan_industri_blok_fungsi Int
  nama                            String @db.VarChar(255)
  keterangan                      String

  @@ignore
  @@schema("public")
}

model tb_kawasan_industri_blok_fungsi_tr {
  id_kawasan_industri_blok_fungsi_tr Int
  id_kawasan_industri_blok_fungsi    Int
  kd_bahasa                          String @db.VarChar(2)
  nama                               String @db.VarChar(255)
  keterangan                         String

  @@ignore
  @@schema("public")
}

model tb_kawasan_industri_blok_kondisi {
  id_kawasan_industri_blok_kondisi Int
  nama                             String @db.VarChar(255)
  keterangan                       String

  @@ignore
  @@schema("public")
}

model tb_kawasan_industri_blok_kondisi_tr {
  id_kawasan_industri_blok_kondisi_tr Int
  id_kawasan_industri_blok_kondisi    Int
  kd_bahasa                           String @db.VarChar(2)
  nama                                String @db.VarChar(255)
  keterangan                          String

  @@ignore
  @@schema("public")
}

model tb_kawasan_industri_blok_staging {
  id_kawasan_industri_blok_staging Int
  id_kawasan_industri              Int?
  status                           Int?
  kode                             String?   @db.VarChar(255)
  kondisi                          String?   @db.VarChar(255)
  fungsi                           String?   @db.VarChar(255)
  luas                             String?   @db.VarChar(255)
  harga                            String?   @db.VarChar(255)
  perusahaan                       String?   @db.VarChar(255)
  negara                           String?   @db.VarChar(255)
  lon                              String?   @db.VarChar(255)
  lat                              String?   @db.VarChar(255)
  prj                              String?   @db.VarChar(255)
  shape                            String?
  log                              String?
  publish_by                       Int?
  publish_dt                       DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_kawasan_industri_blok_status {
  id_kawasan_industri_blok Int
  status                   Int
  status_proses            Int
  keterangan               String
  created_by               Int
  created_date             DateTime  @db.Timestamp(6)
  updated_by               Int?
  updated_date             DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_kawasan_industri_blok_status_tr {
  id_kawasan_industri_blok_status_tr Int
  id_kawasan_industri_blok_status    Int
  kd_bahasa                          String @db.VarChar(2)
  nama                               String @db.VarChar(255)
  keterangan                         String

  @@ignore
  @@schema("public")
}

model tb_kawasan_industri_blok_tr {
  id_kawasan_industri_blok_tr Int
  id_kawasan_industri_blok    Int
  kd_bahasa                   String @db.VarChar(2)
  nama                        String @db.VarChar(255)
  nama_perusahaan             String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_kawasan_industri_file {
  id_kawasan_industri_file Int
  id_kawasan_industri      Int
  tipe                     Int
  nama                     String @db.VarChar(255)
  judul                    String @db.VarChar(255)
  keterangan               String

  @@ignore
  @@schema("public")
}

model tb_kawasan_industri_file_tr {
  id_kawasan_industri_file_tr Int
  id_kawasan_industri_file    Int
  kd_bahasa                   String @db.VarChar(2)
  judul                       String @db.VarChar(255)
  keterangan                  String
  nama                        String @db.VarChar(255)

  @@ignore
  @@schema("public")
}

model tb_kawasan_industri_kategori {
  id_kawasan_industri_kategori Int
  nama                         String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_kawasan_industri_kategori_tr {
  id_kawasan_industri_kategori_tr Int
  id_kawasan_industri_kategori    Int
  kd_bahasa                       String @db.VarChar(2)
  nama                            String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_kawasan_industri_layer_spasial {
  id_kawasan_industri_layer_spasial Int      @default(autoincrement())
  id_kawasan_industri               Int
  nama_layer                        String   @db.VarChar(500)
  tipe                              Int
  url_service                       String   @db.VarChar(500)
  status                            Int
  is_active                         Boolean?

  @@ignore
  @@schema("public")
}

model tb_kawasan_user {
  id_kawasan_user     Int
  id_kawasan_industri Int
  id_user             Int

  @@ignore
  @@schema("public")
}

model tb_kawasan_user_hapus {
  id_kawasan_user     Int?
  id_kawasan_industri Int?
  id_user             Int?

  @@ignore
  @@schema("public")
}