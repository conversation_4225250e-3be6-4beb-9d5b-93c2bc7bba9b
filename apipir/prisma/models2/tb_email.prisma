model tb_email_subscription {
  id_email_subscription Int
  email                 String    @db.VarChar(255)
  lang                  String    @db.VarChar(10)
  ip_pengunjung         String    @db.VarChar(15)
  token_exp_date        DateTime? @default(dbgenerated("(CURRENT_TIMESTAMP + '3 days'::interval day)")) @db.Timestamptz(6)
  token                 String    @db.VarChar(255)
  is_active             Boolean
  created_time          DateTime? @default(now()) @db.Timestamptz(6)
  created_by            String?   @db.VarChar(255)
  modified_time         DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}