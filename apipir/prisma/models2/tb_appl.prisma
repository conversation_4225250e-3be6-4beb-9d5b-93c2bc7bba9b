model tb_appl {
  id_appl   Int
  appl_name String @db.Var<PERSON>har(255)

  @@ignore
  @@schema("public")
}

model tb_appl_settings {
  code_type         String    @db.VarChar(50)
  code              String    @db.VarChar(20)
  name              String    @db.Var<PERSON>har(64)
  value_type        Int
  value_date        DateTime? @db.Date
  value_number      Float?
  value_string      String?   @db.VarChar(255)
  value_order       Int?
  value_description String?

  @@ignore
  @@schema("public")
}

model tb_appl_task {
  id_appl_task        BigInt
  id_appl_task_parent BigInt?
  id_appl             Int
  appl_task_name      String  @db.VarChar(255)
  controller_name     String? @db.VarChar(255)
  action_name         String? @db.Var<PERSON>har(255)
  description         String? @db.VarChar(255)
  icon_name           String? @db.VarChar(64)
  sort_number         BigInt?

  @@ignore
  @@schema("public")
}

model tb_appl_task_delegation {
  id_appl_task_delegation Int
  id_appl_task            BigInt
  delegate_for            Int
  delegate_by             Int
  approved_by             Int
  start_date              DateTime  @db.Date
  end_date                DateTime  @db.Date
  status                  Int       @default(0)
  created_by              Int
  created_date            DateTime  @db.Timestamp(6)
  updated_by              Int?
  updated_date            DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}