model tb_pelabuhan_fungsi {
  id_pelabuhan_fungsi Int
  nama                String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_pelabuhan_fungsi_tr {
  id_pelabuhan_fungsi_tr Int
  id_pelabuhan_fungsi    Int
  kd_bahasa              String @db.VarChar(2)
  nama                   String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_pelabuhan_kelas {
  id_pelabuhan_kelas Int
  nama               String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_pelabuhan_kelas_tr {
  id_pelabuhan_kelas_tr Int
  id_pelabuhan_kelas    Int
  kd_bahasa             String @db.VarChar(2)
  nama                  String @db.VarChar(50)

  @@ignore
  @@schema("public")
}

model tb_pelabuhan_status {
  id_pelabuhan  Int
  status        Int
  status_proses Int
  keterangan    String
  created_by    Int
  created_date  DateTime  @db.Timestamp(6)
  updated_by    Int?
  updated_date  DateTime? @db.Timestamp(6)

  @@ignore
  @@schema("public")
}

model tb_pelabuhan_tr {
  id_pelabuhan_tr Int
  id_pelabuhan    Int
  kd_bahasa       String @db.VarChar(2)
  nama            String @db.VarChar(255)
  keterangan      String

  @@ignore
  @@schema("public")
}