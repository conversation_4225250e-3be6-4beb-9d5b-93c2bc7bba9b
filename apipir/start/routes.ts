/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { HttpContext } from '@adonisjs/core/http'
import { middleware } from './kernel.js';
// import { Application } from '@adonisjs/core/app';
import fs from 'fs';
import path from 'path';
import crypto from 'node:crypto';

const CustomController = () => import('#controllers/custom_controller')
const AuthController = () => import('#controllers/auth_controller')
const RoleController = () => import('#controllers/roles_controller')
const MenusController = () => import('#controllers/menus_controller')
const RoleMenusController = () => import('#controllers/role_menus_controller')
const ConfigUploadsController = () => import('#controllers/config_uploads_controller')
// const SektorNasionalRef = () => import('#controllers/sektor_nasional_refs_controller')
const SektorNasional = () => import('#controllers/sektor_nasionals_controller')
const JenisInsentif = () => import('#controllers/jenis_insentifs_controller')
const SubSektorNasional = () => import('#controllers/sub_sektor_nasionals_controller')
// const AdmProvinsi = () => import('#controllers/adm_provinsis_controller')
const AppSLiderPeluang = () => import('#controllers/app_slider_peluangs_controller')
const AppSLider = () => import('#controllers/app_sliders_controller')
const Home = () => import('#controllers/home_controller')
const Fe = () => import('#controllers/fe_controller')
const Faq = () => import('#controllers/faqs_controller')
const Daerah = () => import('#controllers/daerahs_controller')
const PeluangInvestasi = () => import('#controllers/peluang_investasis_controller')
const PeluangController = () => import('#controllers/peluangs_controller')
const PdbSNController = () => import('#controllers/pdb_sektor_nasionals_controller')
const PdrbSDController = () => import('#controllers/pdrb_sektor_daerahs_controller')
const InsentifSNController = () => import('#controllers/sektor_nasional_insentifs_controller')
const UmrProvController = () => import('#controllers/umr_provinsis_controller')
const UmkmController = () => import('#controllers/umkms_controller')
const DashboardController = () => import('#controllers/dashboard_controller')
const RefSNController = () => import('#controllers/ref_sektor_nasionals_controller')
const RefSSNController = () => import('#controllers/ref_sub_sektor_nasionals_controller')
const RefKNController = () => import('#controllers/ref_komoditi_nasionals_controller')
const RefSDController = () => import('#controllers/ref_sumber_data_controller')
const RefSDIController = () => import('#controllers/ref_sumber_data_instansi_controller')
const RefPSController = () => import('#controllers/ref_peluang_sektor_controller')
const RefPKController = () => import('#controllers/ref_peluang_kontak_controller')
const LinkTerkaitController = () => import('#controllers/link_terkaits_controller')
const RunningTextController = () => import('#controllers/running_text_controller')
const VidioBerandaController = () => import('#controllers/vidio_berandas_controller')
const NewsController = () => import('#controllers/news_controller')
const IknController = () => import('#controllers/ikns_controller')
const IknDataController = () => import('#controllers/ikn_datas_controller')
const IknLayersController = () => import('#controllers/ikn_layers_controller')
const IknImagesController = () => import('#controllers/ikn_images_controller')
const MapController = () => import('#controllers/maps_controller')
const KomoditiNasionalController = () => import('#controllers/komoditi_nasionals_controller')
const HistoriesController = () => import('#controllers/histories_controller')
const GeoportalController = () => import('#controllers/geoportals_controller')

// DAERAH;
const AdminPeluangDaerahKelayakanController = () => import('#controllers/daerah/peluang_daerah_kelayakan_controller');
// PROVINSI
const AdminProvinsiDemografiController = () => import('#controllers/daerah/provinsi/demografi_controller');
const AdminProvinsiKantorController = () => import('#controllers/daerah/provinsi/kantor_controller');
const AdminProvinsiProfilController = () => import('#controllers/daerah/provinsi/profil_controller');
const AdminProvinsiEksporImporController = () => import('#controllers/daerah/provinsi/ekspor_impor_controller');
const AdminProvinsiUtilitasController = () => import('#controllers/daerah/provinsi/utilitas_controller');
const AdminProvinsiInvestasiController = () => import('#controllers/daerah/provinsi/investasi_controller');
// KABUPATEN
const AdminKabupatenDemografiController = () => import('#controllers/daerah/kabupaten/demografi_controller');
const AdminKabupatenUmrController = () => import('#controllers/daerah/kabupaten/umr_controller');
const AdminKabupatenKantorController = () => import('#controllers/daerah/kabupaten/kantor_controller');
const AdminKabupatenProfilController = () => import('#controllers/daerah/kabupaten/profil_controller');
const AdminKabupatenEksporImporController = () => import('#controllers/daerah/kabupaten/ekspor_impor_controller');
const AdminKabupatenUtilitasController = () => import('#controllers/daerah/kabupaten/utilitas_controller');
const AdminKabupatenInvestasiController = () => import('#controllers/daerah/kabupaten/investasi_controller');
// SEKTOR UNGGULAN DAERAH
const AdminSUDSektorDaerahController = () => import('#controllers/daerah/sektor_unggulan_daerah/sektor_daerah_controller');
const AdminSUDSubSektorDaerahController = () => import('#controllers/daerah/sektor_unggulan_daerah/sub_sektor_daerah_controller');
const AdminSUDSektorDaerahInsentifController = () => import('#controllers/daerah/sektor_unggulan_daerah/sektor_daerah_insentif_controller');
const AdminSUDKomoditiDaerahController = () => import('#controllers/daerah/sektor_unggulan_daerah/komoditi_daerah_controller');
// KAWASAN (KEK & KI)
const AdminKawasanIndustriController = () => import('#controllers/daerah/kawasan/kawasan_industri_controller')
const AdminKawasanPotensiInvestasiController = () => import('#controllers/daerah/kawasan/potensi_investasi_controller')
// KOMODITI
const AdminKomoditiProvinsiController = () => import('#controllers/daerah/komoditi/provinsi_controller')
const AdminKomoditiKabkotController = () => import('#controllers/daerah/komoditi/kabkot_controller')
// SARANA PRASARANA
const AdminSarprasBandaraController = () => import('#controllers/daerah/sarana_prasarana/bandara_controller')
const AdminSarprasPelabuhanController = () => import('#controllers/daerah/sarana_prasarana/pelabuhan_controller')
const AdminSarprasRumahSakitController = () => import('#controllers/daerah/sarana_prasarana/rumah_sakit_controller')
const AdminSarprasHotelController = () => import('#controllers/daerah/sarana_prasarana/hotel_controller')
const AdminSarprasPendidikanController = () => import('#controllers/daerah/sarana_prasarana/pendidikan_controller')
const AdminSarprasListrikController = () => import('#controllers/daerah/sarana_prasarana/listrik_controller')
const AdminSarprasGasController = () => import('#controllers/daerah/sarana_prasarana/gas_controller')
const AdminSarprasAirController = () => import('#controllers/daerah/sarana_prasarana/air_controller')
// PELUANG INVESTASI DAERAH
const AdminPeluangInvestasiDaerahController = () => import('#controllers/daerah/peluang_investasi_daerah_controller')

// INFORMASI
// ROADMAP 
const AdminRoadmapInputDataTableController = () => import('#controllers/informasi/roadmap/input_data_table_controller')
// KEBIJAKAN
const AdminKebijakanController = () => import('#controllers/informasi/kebijakan_controller')
// INSENTIF
const AdminInsentifController = () => import('#controllers/informasi/insentif_controller')

// ARTIKEL
const AdminArtikelInputDataController = () => import('#controllers/artikel/input_data_controller')

// MAP SERVICE
const AdminAdmProvinsiLayerSpasialController = () => import('#controllers/map_services/adm_provinsi_layer_spasial_controller');
const AdminAdmKabkotLayerSpasialController = () => import('#controllers/map_services/adm_kabkot_layer_spasial_controller');
const AdminKawasanLayersController = () => import('#controllers/map_services/kawasan_layers_controller');
const AdminKomoditiLayersController = () => import('#controllers/map_services/komoditi_layers_controller');
const AdminPeluangLayersController = () => import('#controllers/map_services/peluang_layers_controller');
const AdminPeluangDaerahLayerSpasialController = () => import('#controllers/map_services/peluang_daerah_layer_spasial_controller');

const AdminSektorDaerahInsentifController = () => import('#controllers/sektor_daerah_insentif_controller');
const AdminSektorNasionalInsentifController = () => import('#controllers/sektor_nasional_insentif_controller');
const AdminRumahSakitJenisController = () => import('#controllers/rumah_sakit_jenis_controller');
const AdminRumahSakitKategoriController = () => import('#controllers/rumah_sakit_kategori_controller');
const AdminBandaraKategoriController = () => import('#controllers/bandara_kategori_controller');
const AdminBandaraKelasController = () => import('#controllers/bandara_kelas_controller');
const AdminKomoditiSatuanController = () => import('#controllers/komoditi_satuan_controller');
const AdminPelabuhanKelasController = () => import('#controllers/pelabuhan_kelas_controller');
const AdminHotelKelasController = () => import('#controllers/hotel_kelas_controller');
const AdminPelabuhanFungsiController = () => import('#controllers/pelabuhan_fungsi_controller');
const AdminPendidikanKategoriController = () => import('#controllers/pendidikan_kategori_controller');
const AdminPendidikanJenjangController = () => import('#controllers/pendidikan_jenjang_controller');

// FILE SERVICE
const AdminSektorDaerahFileController = () => import('#controllers/files/sektor_daerah_file_controller');
const AdminSubSektorDaerahFileController = () => import('#controllers/files/sub_sektor_daerah_file_controller');
const AdminKomoditiDaerahFileController = () => import('#controllers/files/komoditi_daerah_file_controller');
const AdminSektorDaerahInsentifFileController = () => import('#controllers/files/sektor_daerah_insentif_file_controller');
const AdminKawasanIndustriFileController = () => import('#controllers/files/kawasan_industri_file_controller');
const AdminKawasanIndustriPeluangFileController = () => import('#controllers/files/kawasan_industri_peluang_file_controller');
const AdminPeluangDaerahFileController = () => import('#controllers/files/peluang_daerah_file_controller');

const AdminSektorNasionalValueController = () => import('#controllers/sektor_nasional_value_controller');
const AdminSubSektorNasionalValueController = () => import('#controllers/sub_sektor_nasional_value_controller');

// const AdminSUDSektorNasionalController = () => import('#controllers/sud_sektor_nasional');
// const AdminSUDSubSektorNasionalController = () => import('#controllers/sud_sub_sektor_nasional');

import AutoSwagger from 'adonis-autoswagger'
import swagger from '#config/swagger'
import MapsController from '#controllers/maps_controller';
// returns swagger in YAML
router.get("/swagger", async () => {
        return AutoSwagger.default.docs(router.toJSON(), { ...swagger, snakeCase: false });
});


// Renders Swagger-UI and passes YAML-output of /swagger
router.get("/docs", async () => {
        return AutoSwagger.default.ui("/swagger", swagger);
});

router.get('/', async ({ response }) => {
        return response.unauthorized({ message: 'Unauthorized Access' })

})



router.get('/csrf-token', async ({ response, session }: HttpContext) => {
  // Buat CSRF token baru
  const csrfSecret = crypto.randomBytes(16).toString('hex')
  
  // Simpan di session
  session.put('csrf-secret', csrfSecret)
  
  // Buat token yang akan dikirim ke client
  const csrfToken = crypto.createHash('sha256').update(csrfSecret).digest('hex')
  
  return response.json({
    success: true,
    csrfToken: csrfToken
  })
})

router.group(() => {
        router.get('slider/:bahasa?', [Home, 'slider']).as('home.slider')
        router.get('slider_background/', [Home, 'slider_background']).as('home.slider_background')
        router.get('sektor_nasonal_ref/:bahasa?', [Home, 'sektor_nasional_ref']).as('home.sektor_nasional_ref')
        router.get('peluang_sektor_ref/', [Home, 'peluang_sektor_ref']).as('home.peluang_sektor_ref')
        router.get('prov_ref/:bahasa', [Home, 'provinsi_ref']).as('home.prov_ref')
        router.get('sektor_unggulan_nasional/:bahasa?/:id_sektor', [Home, 'sektor_unggulan_nasional']).as('home.sektor_unggulan_nasional')
        router.get('sektor_nasional_detail/:bahasa?/:id_adm_provinsi', [Home, 'sektor_nasional_detail']).as('home.sektor_nasinal')
        router.get('sektor_unggulan_wilayah/:bahasa?/:id_adm_provinsi', [Home, 'sektor_unggulan_wilayah']).as('home.sektor_unggulan_wilayah')
        router.get('peluang_investasi_wilayah/:id_sektor', [Home, 'peluang_investasi_wilayah']).as('home.peluang_investasi_wilayah')
        router.get('umkm/', [Home, 'umkm']).as('home.umkm')
        router.get('ikn/', [Home, 'ikn']).as('home.ikn')
        router.get('link_terkait/', [Home, 'link_terkait']).as('home.link_terkait')
        router.get('news/', [Home, 'news']).as('home.news')
        router.get('vidio/', [Home, 'vidio']).as('home.vidio')
        router.get('search/:page?/:pageSize?/:search', [Home, 'search']).as('home.search')
        router.get('search_pid/:page?/:pageSize?/:search', [Home, 'searchPID']).as('home.search_pid')
        router.get('search_roadmap/:page?/:pageSize?/:search', [Home, 'searchRoadmap']).as('home.search_roadmap')
        router.get('search_kajian/:page?/:pageSize?/:search', [Home, 'searchKajian']).as('home.search_kajian')
        router.get('running_text/', [Home, 'running_text']).as('home.running_text')
        router.get('get_peluang_kategori_sektor/', [Home, 'get_peluang_kategori_sektor']).as('home.get_peluang_kategori_sektor')
        
}).prefix('home/')



router.group(() => {
        router.get('slider/', [PeluangInvestasi, 'slider']).as('peluang.slider')
        router.get('peluang_investasi_card/', [PeluangInvestasi, 'peluang_investasi_card']).as('peluang.peluang_investasi_card')
        router.get('peluang_investasi_sektor/', [PeluangInvestasi, 'peluang_investasi_sektor']).as('peluang.peluang_investasi_sektor')
        router.get('peluang_investasi_3sektor/', [PeluangInvestasi, 'peluang_investasi_3sektor']).as('peluang.peluang_investasi_3sektor')
        router.post('peluang_investasi_wilayah/', [PeluangInvestasi, 'peluang_investasi_wilayah']).as('peluang.peluang_investasi_wilayah')
        router.get('peluang_investasi_wilayah/', [PeluangInvestasi, 'get_peluang_investasi_wilayah']).as('peluang.get_peluang_investasi_wilayah')
        // router.post('peluang_investasi_wilayah_ipro/', [PeluangInvestasi, 'peluang_investasi_wilayah_ipro']).as('peluang.peluang_investasi_wilayah_ipro')
        router.post('count_viewer/', [PeluangInvestasi, 'count_viewer']).as('peluang.count_viewer')
        router.get('count_viewer/', [PeluangInvestasi, 'count_viewer_get']).as('peluang.count_viewer_get')
        router.post('ppi_like', [PeluangInvestasi, 'ppi_like']).as('peluang.ppi_like')
        router.get('ppi_like', [PeluangInvestasi, 'ppi_like_get']).as('peluang.ppi_like_get')
        router.get('get_ppi_like/:id_peluang_kabkot', [PeluangInvestasi, 'get_ppi_like']).as('peluang.get_ppi_like')
        router.get('is_ppi_like/:id_peluang_kabkot', [PeluangInvestasi, 'is_ppi_like']).as('peluang.is_ppi_like')
        router.post('pid_like', [PeluangInvestasi, 'pid_like']).as('peluang.pid_like')
        router.get('pid_like', [PeluangInvestasi, 'pid_like_get']).as('peluang.pid_like_get')
        router.get('get_pid_like/:id_peluang_kabkot', [PeluangInvestasi, 'get_pid_like']).as('peluang.get_pid_like')
        router.get('is_pid_like/:id_peluang_daerah', [PeluangInvestasi, 'is_pid_like']).as('peluang.is_pid_like')
        router.get('detail/:id_peluang_kabkot', [PeluangInvestasi, 'detail_peluang']).as('peluang.detail_peluang')
        router.get('detail_ipro/:id_peluang_kabkot', [PeluangInvestasi, 'detail_peluang']).as('peluang.detail_peluang_ipro')
        router.get('detail_pid/:id_peluang_kabkot', [PeluangInvestasi, 'detail_peluang_pid']).as('peluang.detail_peluang_pid')
        router.get('provinsi_ref', [PeluangInvestasi, 'provinsi_ref']).as('peluang.provinsi_ref')
        router.get('kabkot_ref/:id_adm_provinsi', [PeluangInvestasi, 'kabkot_ref']).as('peluang.kabkot_ref')
        router.get('sektor_peluang_ref', [PeluangInvestasi, 'sektor_peluang_ref']).as('peluang.sektor_peluang_ref')
        router.get('kategori_sektor_ref', [PeluangInvestasi, 'kategori_sektor_ref']).as('peluang.kategori_sektor_ref')
        router.get('get_tujuan_download', [PeluangInvestasi, 'get_tujuan_download']).as('peluang.get_tujuan_download')
        router.post('submit_unduh_dokumen', [PeluangInvestasi, 'submit_unduh_dokumen']).as('peluang.submit_unduh_dokumen')
}).prefix('peluang/')



router.group(() => {
        router.get('detail/', [Fe, 'get_ikn']).as('Fe.get_ikn')
        router.get('get_bandara', [Fe, 'get_bandara']).as('daerah.get_bandara')
        router.get('get_pelabuhan', [Fe, 'get_pelabuhan']).as('daerah.get_pelabuhan')
        router.get('get_hotel', [Fe, 'get_hotel']).as('daerah.get_hotel')
        router.get('get_pendidikan', [Fe, 'get_pendidikan']).as('daerah.get_pendidikan')
        router.get('get_rumah_sakit', [Fe, 'get_rumah_sakit']).as('daerah.get_rumah_sakit')
        router.get('get_layers', [Fe, 'get_layers']).as('daerah.get_layers')
        router.get('get_kawasan', [Fe, 'get_kawasan']).as('daerah.get_kawasan')
}).prefix('ikn')

router.group(() => {
        router.get('detail/:id_sektor', [Fe, 'detail_sektor_nasional']).as('Fe.detail_sektor_nasional')
}).prefix('sektor_unggulan_nasional')

router.group(() => {
        router.get('detail_provinsi/:id_adm_provinsi', [Daerah, 'detail_provinsi']).as('daerah.detail_provinsi')
        router.get('kawasan_provinsi/:id_adm_provinsi', [Daerah, 'kawasan_prov']).as('daerah.kawasanl_provinsi')
        router.get('detail_kabkot/:id_adm_kabkot', [Daerah, 'detail_kabkot']).as('daerah.detail_kabkot')
        router.get('kawasan_kabkot/:id_adm_kabkot', [Daerah, 'kawasan_kabkot']).as('daerah.kawasan_kabkot')
        router.get('get_komoditi_prov/:id_adm_provinsi/:id_sub_sektor/:tahun', [Daerah, 'detail_komoditi_prov']).as('daerah.get_komoditi_prov')
        router.get('get_komoditi_kabkot/:id_adm_kabkot/:id_sub_sektor/:tahun', [Daerah, 'detail_komoditi_kabkot']).as('daerah.get_komoditi_kabkot')
        router.get('detail_kawasan/:id_kawasan_industri/', [Daerah, 'detail_kawasan']).as('daerah.detail_kawsasan')
        router.get('get_zona_waktu', [Daerah, 'get_zona_waktu']).as('daerah.get_zona_waktu')
        router.get('get_prov/:id_zona_waktu?', [Daerah, 'get_prov']).as('daerah.get_prov')
        router.get('get_ppi_by_zonawaktu/:id_zona_waktu?', [Daerah, 'get_ppi_by_zonawaktu']).as('daerah.get_ppi_by_zonawaktu')
        router.get('get_ppi_by_prov/:id_adm_provinsi?', [Daerah, 'get_ppi_by_prov']).as('daerah.get_ppi_by_prov')
        router.get('get_ppi_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_ppi_by_kabkot']).as('daerah.get_ppi_by_kabkot')
        router.get('get_ipro_by_zonawaktu/:id_zona_waktu?', [Daerah, 'get_ipro_by_zonawaktu']).as('daerah.get_ipro_by_zonawaktu')
        router.get('get_ipro_by_prov/:id_adm_provinsi?', [Daerah, 'get_ipro_by_prov']).as('daerah.get_ipro_by_prov')
        router.get('get_ipro_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_ipro_by_kabkot']).as('daerah.get_ipro_by_kabkot')
        router.get('get_pid_by_zonawaktu/:id_zona_waktu?', [Daerah, 'get_pid_by_zonawaktu']).as('daerah.get_pid_by_zonawaktu')
        router.get('get_pid_by_prov/:id_adm_provinsi?', [Daerah, 'get_pid_by_prov']).as('daerah.get_pid_by_prov')
        router.get('get_pid_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_pid_by_kabkot']).as('daerah.get_pid_by_kabkot')
        router.get('get_kawasan_by_zonawaktu/:id_zona_waktu?', [Daerah, 'get_kawasan_by_zonawaktu']).as('daerah.get_kawasan_by_zonawaktu')
        router.get('get_kawasan_by_prov/:id_adm_provinsi?', [Daerah, 'get_kawasan_by_prov']).as('daerah.get_kawasan_by_prov')
        router.get('get_kawasan_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_kawasan_by_kabkot']).as('daerah.get_kawasan_by_kabkot')
        router.get('get_kek_by_zonawaktu/:id_zona_waktu?', [Daerah, 'get_kek_by_zonawaktu']).as('daerah.get_kek_by_zonawaktu')
        router.get('get_kek_by_prov/:id_adm_provinsi?', [Daerah, 'get_kek_by_prov']).as('daerah.get_kek_by_prov')
        router.get('get_kek_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_kek_by_kabkot']).as('daerah.get_kek_by_kabkot')
        // router.get('get_bandara_by_zonawaktu/:id_zona_waktu?', [Daerah, 'get_bandara_by_zonawaktu']).as('daerah.get_bandara_by_zonawaktu')
        router.get('get_bandara_by_prov/:id_adm_provinsi?', [Daerah, 'get_bandara_by_prov']).as('daerah.get_bandara_by_prov')
        router.get('get_bandara_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_bandara_by_kabkot']).as('daerah.get_bandara_by_kabkot')
        // router.get('get_pelabuhan_by_zonawaktu/:id_zona_waktu?', [Daerah, 'get_pelabuhan_by_zonawaktu']).as('daerah.get_pelabuhan_by_zonawaktu')
        router.get('get_pelabuhan_by_prov/:id_adm_provinsi?', [Daerah, 'get_pelabuhan_by_prov']).as('daerah.get_pelabuhan_by_prov')
        router.get('get_pelabuhan_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_pelabuhan_by_kabkot']).as('daerah.get_pelabuhan_by_kabkot')
        // router.get('get_hotel_by_zonawaktu/:id_zona_waktu?', [Daerah, 'get_hotel_by_zonawaktu']).as('daerah.get_hotel_by_zonawaktu')
        router.get('get_hotel_by_prov/:id_adm_provinsi?', [Daerah, 'get_hotel_by_prov']).as('daerah.get_hotel_by_prov')
        router.get('get_hotel_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_hotel_by_kabkot']).as('daerah.get_hotel_by_kabkot')
        // router.get('get_pendidikan_by_zonawaktu/:id_zona_waktu?', [Daerah, 'get_pendidikan_by_zonawaktu']).as('daerah.get_pendidikan_by_zonawaktu')
        router.get('get_pendidikan_by_prov/:id_adm_provinsi?', [Daerah, 'get_pendidikan_by_prov']).as('daerah.get_pendidikan_by_prov')
        router.get('get_pendidikan_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_pendidikan_by_kabkot']).as('daerah.get_pendidikan_by_kabkot')
        // router.get('get_rumah_sakit_by_zonawaktu/:id_zona_waktu?', [Daerah, 'get_rumah_sakit_by_zonawaktu']).as('daerah.get_rumah_sakit_by_zonawaktu')
        router.get('get_rumah_sakit_by_prov/:id_adm_provinsi?', [Daerah, 'get_rumah_sakit_by_prov']).as('daerah.get_rumah_sakit_by_prov')
        router.get('get_rumah_sakit_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_rumah_sakit_by_kabkot']).as('daerah.get_rumah_sakit_by_kabkot')
        router.get('get_komoditi_by_prov/:id_adm_provinsi?', [Daerah, 'get_komoditi_by_prov']).as('daerah.get_komoditi_by_prov')
        router.get('get_komoditi_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_komoditi_by_kabkot']).as('daerah.get_komoditi_by_kabkot')
        router.get('get_prov_by_zona_waktu/:id_adm_provinsi?', [Daerah, 'get_prov_by_zona_waktu']).as('daerah.get_prov_by_zona_waktu')
        router.get('get_prov_by_prov/:id_adm_provinsi?', [Daerah, 'get_prov_by_prov']).as('daerah.get_prov_by_prov')
        router.get('get_rtrw_by_prov/:id_adm_provinsi?', [Daerah, 'get_rtrw_by_prov']).as('daerah.get_rtrw_by_prov')
        router.get('get_rtrw_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_rtrw_by_kabkot']).as('daerah.get_rtrw_by_kabkot')
        router.get('get_rdtr_by_prov/:id_adm_provinsi?', [Daerah, 'get_rdtr_by_prov']).as('daerah.get_rdtr_by_prov')
        router.get('get_rdtr_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_rdtr_by_kabkot']).as('daerah.get_rdtr_by_kabkot')
        router.get('get_utilitas_by_prov/:id_adm_provinsi?', [Daerah, 'get_utilitas_by_prov']).as('daerah.get_utilitas_by_prov')
        router.get('get_utilitas_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_utilitas_by_kabkot']).as('daerah.get_utilitas_by_kabkot')
        router.get('/:search?', [Daerah, 'daerah']).as('daerah.daerah')
        router.get('get_listrik_by_prov/:id_adm_provinsi?', [Daerah, 'get_listrik_by_prov']).as('daerah.get_listrik_by_prov')
        router.get('get_listrik_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_listrik_by_kabkot']).as('daerah.get_listrik_by_kabkot')
        router.get('get_gas_by_prov/:id_adm_provinsi?', [Daerah, 'get_gas_by_prov']).as('daerah.get_gas_by_prov')
        router.get('get_gas_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_gas_by_kabkot']).as('daerah.get_gas_by_kabkot')
        router.get('get_air_by_prov/:id_adm_provinsi?', [Daerah, 'get_air_by_prov']).as('daerah.get_air_by_prov')
        router.get('get_air_by_kabkot/:id_adm_kabkot?', [Daerah, 'get_air_by_kabkot']).as('daerah.get_air_by_kabkot')
       
}).prefix('daerah')

router.group(() => {
        router.get('/get_icon_ppi', [MapController, 'get_icon_sektor_ppi']).as('map.get_icon_ppi')
        router.get('/get_icon_pid', [MapController, 'get_icon_sektor_pid']).as('map.get_icon_pid')
        router.get('/get_icon_komoditi', [MapController, 'get_icon_komoditi']).as('map.get_icon_komoditi')
}).prefix('map')

router.group(() => {
        router.get('umkm/list_sektor_umkm/', [UmkmController, 'list_sektor_umkm']).as('UmkmController.list_sektor_umkm')
        router.get('umk/list_sektor_umk/', [UmkmController, 'list_sektor_umk']).as('UmkmController.list_sektor_umk')
        router.post('umkm/list_umkm/', [UmkmController, 'list_umkm']).as('UmkmController.list_umkm')
        router.post('umk/list_umk/', [UmkmController, 'list_umk']).as('UmkmController.list_umk')
        router.get('insentif/get_insentif/', [Fe, 'get_insentif']).as('Fe.get_insentif')
        router.get('roadmap/get_roadmap/', [Fe, 'get_roadmap']).as('Fe.get_roadmap')
        router.get('kebijakan/get_kategori_kebijakan/', [Fe, 'get_kategori_kebijakan']).as('fe.get_kategori_kebijakan')
        router.get('kebijakan/get_kebijakan/:id_kategori/:page?/:pageSize?/:search?', [Fe, 'get_kebijakan']).as('fe.get_kebijakan')
        router.get('artikel/get_artikel/:search?', [Fe, 'get_artikel']).as('fe.get_artikel')
        router.get('artikel/get_kajian_potensi_prov/:id_adm_provinsi/:search?', [Fe, 'get_kajian_potensi_prov']).as('fe.get_kajian_potensi_prov')
        router.get('artikel/get_kajian_potensi_kabkot/:id_adm_kabkot/:search?', [Fe, 'get_kajian_potensi_kabkot']).as('fe.get_kajian_potensi_kabkot')
        router.get('artikel/get_kajian_hilirisasi/:id_komoditi', [Fe, 'get_kajian_hilirisasi']).as('fe.get_kajian_hilirisasi')
        router.get('faq/get_faq', [Fe, 'get_faq']).as('fe.get_faq')
        router.post('faq/add_faq', [Fe, 'add_faq']).as('fe.add_faq')
        router.get('infrastruktur/get_insfrastruktur/:page?/:pageSize?/:search?', [Fe, 'get_infrastruktur']).as('fe.get_infrastruktur')
        // router.get('infrastruktur/get_insfrastruktur2/:page?/:pageSize?/:search?', [Fe, 'get_infrastruktur2']).as('fe.get_infrastruktur2')
        // router.get('tes/:id', [Fe, 'tes']).as('fe.tes')
}).prefix('fe')

router.group(() => {
        router.post('flag_provinsi', [DashboardController, 'flag_provinsi']).as('DashboardController.flag_provinsi')
        router.delete('unflag_provinsi/:id', [DashboardController, 'destroy_mapped']).as('DashboardController.destroy_mapped')
        router.get('get_user_dashboard', [DashboardController, 'get_user_dashboard']).as('DashboardController.get_user_dashboard')
        router.get('get_dashboard/:startDate?/:endDate?', [DashboardController, 'get_dashboard']).as('DashboardController.get_dashboard')
        router.get('get_status_infrastruktur/:id_user?', [DashboardController, 'get_status_infrastruktur']).as('DashboardController.get_status_infrastruktur')
        router.get('get_pic_data/:id_user', [DashboardController, 'get_pic_data']).as('DashboardController.get_pic_data')
        router.get('get_pic_data_old/:id_user', [DashboardController, 'get_pic_data_old']).as('DashboardController.get_pic_data_old')
        router.get('get_status_data_prov/:id_user?', [DashboardController, 'get_status_data_prov']).as('DashboardController.get_status_data_prov')
        router.get('get_status_data_kabkot/:id_user?', [DashboardController, 'get_status_data_kabkot']).as('DashboardController.get_status_data_kabkot')
        router.get('get_pending_infrastruktur', [DashboardController, 'getPendingInfrastruktur']).as('DashboardController.get_pending_infrastruktur')
        router.get('get_pending_provinsi', [DashboardController, 'getPendingProvinsi']).as('DashboardController.get_pending_provinsi')
        router.get('get_pending_kabkot', [DashboardController, 'getPendingKabkot']).as('DashboardController.get_pending_kabkot')
        router.get('get_pending_kantor', [DashboardController, 'getPendingKantor']).as('DashboardController.get_pending_kantor')
        router.get('get_pending_nasional', [DashboardController, 'getPendingNasional']).as('DashboardController.get_pending_nasional')
        router.get('get_pending_daerah', [DashboardController, 'getPendingDaerah']).as('DashboardController.get_pending_daerah')
        router.get('get_detail_infrastruktur_update', [DashboardController, 'getDetailInfrastrukturUpdate']).as('DashboardController.get_detail_infrastruktur_update')
        router.get('get_detail_infrastruktur_belum', [DashboardController, 'getDetailInfrastrukturBelum']).as('DashboardController.get_detail_infrastruktur_belum')
        router.get('get_detail_satatus_data_tahun_prov', [DashboardController, 'getDetailDetailProv']).as('DashboardController.get_detail_satatus_data_tahun_prov')
        router.get('get_detail_satatus_data_tahun_kabkot', [DashboardController, 'getDetailDetailKabkot']).as('DashboardController.get_detail_satatus_data_tahun_kabkot')
}).prefix('dashboard/').use(middleware.auth())

router.group(() => {
        router.put('jenis_insentif/change_status/:id', [JenisInsentif, 'change_status']).as('JenisInsentif.change_status')
        router.get('jenis_insentif/datatable/:page?/:pageSize?/:search?', [JenisInsentif, 'get_paginate']).as('jenis_insentif.get_paginate')
        router.resource('jenis_insentif', JenisInsentif).except(['create', 'edit'])
})
router.group(() => {
        router.get('get_all_group', [HistoriesController, 'get_hitory']).as('history.get_hitory')
        router.get('get_home/datatable/:page?/:pageSize?/:search?', [HistoriesController, 'get_home_dt']).as('history.get_home_dt')
        router.get('get_peluang/datatable/:page?/:pageSize?/:search?', [HistoriesController, 'get_peluang_dt']).as('history.get_peluang_dt')
        router.get('get_kawasan_detail/datatable/:page?/:pageSize?/:search?', [HistoriesController, 'get_kawasan_detail_dt']).as('history.get_kawasan_detail_dt')
        router.get('get_kawasan/datatable/:page?/:pageSize?/:search?', [HistoriesController, 'get_kawasan_dt']).as('history.get_kawasan_dt')
        router.get('get_daerah/datatable/:page?/:pageSize?/:search?', [HistoriesController, 'get_daerah_dt']).as('history.get_daerah_dt')
        router.get('get_daerah_detail/datatable/:page?/:pageSize?/:search?', [HistoriesController, 'get_daerah_detail_dt']).as('history.get_daerah_detail_dt')
        router.get('get_pid/datatable/:page?/:pageSize?/:search?', [HistoriesController, 'get_pid_dt']).as('history.get_pid_dt')
        router.get('get_ppi/datatable/:page?/:pageSize?/:search?', [HistoriesController, 'get_ppi_dt']).as('history.get_ppi_dt')
        router.get('get_ipro/datatable/:page?/:pageSize?/:search?', [HistoriesController, 'get_ipro_dt']).as('history.get_ipro_dt')
        router.get('get_ppi', [HistoriesController, 'get_ppi']).as('history.get_ppi')
        router.get('get_ipro', [HistoriesController, 'get_ipro']).as('history.get_ipro')
        router.get('get_pid', [HistoriesController, 'get_pid']).as('history.get_pid')
        router.get('get_kajian/datatable/:page?/:pageSize?/:search?', [HistoriesController, 'get_kajian_dt']).as('history.get_kajian_dt')
        router.get('get_download_ppi/datatable/:page?/:pageSize?/:startDate?/:endDate?/:search?', [HistoriesController, 'get_download_ppi']).as('history.get_download_ppi')
        router.get('get_download_ipro/datatable/:page?/:pageSize?/:startDate?/:endDate?/:search?', [HistoriesController, 'get_download_ipro']).as('history.get_download_ipro')
        router.get('get_download_pid/datatable/:page?/:pageSize?/:startDate?/:endDate?/:search?', [HistoriesController, 'get_download_pid']).as('history.get_download_pid')
        router.get('get_download_kawasan/datatable/:page?/:pageSize?/:startDate?/:endDate?/:search?', [HistoriesController, 'get_download_kawasan']).as('history.get_download_kawasan')
}).prefix('history/')

router.group(() => {
        router.get('get_map/:layeruid?', [GeoportalController, 'get_map']).as('gp.get_map')
        router.get('get_login/', [GeoportalController, 'get_login']).as('gp.get_login')
}).prefix('gp/')
        router.group(() => {
        router.put('sektor_nasional/aprove/:id_sektor_nasional', [SektorNasional, 'aprove']).as('sektor_nasonal.aprove')
        router.get('sektor_nasional/datatable/:page?/:pageSize?/:search?', [SektorNasional, 'get_paginate']).as('sektor_nasonal.get_paginate')
        router.resource('sektor_nasional', SektorNasional).except(['create', 'edit'])

}).use(middleware.auth())

router.group(() => {
        router.get('sub_sektor_nasional/get_by_sektor/:id_sektor_nasional', [SubSektorNasional, 'get_by_sektor']).as('sub_sektor_nasonal.get_by_sektor')
        router.put('sub_sektor_nasional/aprove/:id_sub_sektor_nasional', [SubSektorNasional, 'aprove']).as('sub_sektor_nasonal.aprove')
        router.get('sub_sektor_nasional/datatable/:page?/:pageSize?/:search?', [SubSektorNasional, 'get_paginate']).as('sub_sektor_nasional.slider')
        router.get('sub_sektor_nasional/by_sektor_daerah/:id_sektor_daerah', [SubSektorNasional, 'bySektorDaerah']).as('sub_sektor_nasional.bySektorDaerah');
        router.get('sub_sektor_nasional/ref_by_sektor_daerah/:id_sektor_daerah', [SubSektorNasional, 'refSubSektorBySektorDaerah']).as('sub_sektor_nasional.RefbySektorDaerah');
        router.resource('sub_sektor_nasional', SubSektorNasional).except(['create', 'edit'])
}).use(middleware.auth())



router.group(() => {
  router.get('faq/datatable/:page?/:pageSize?/:search?',[Faq, 'get_paginate']).as('faq.slider')
  router.resource('faq',Faq).except(['create', 'edit'])
}).use(middleware.auth())


router.group(() => {
        router.get('slider_peluang/datatable/:page?/:pageSize?/:search?', [AppSLiderPeluang, 'get_paginate']).as('slider_peluang.slider')
        router.resource('slider_peluang', AppSLiderPeluang).except(['create', 'edit'])
}).use(middleware.auth())

router.group(() => {
        router.get('slider/datatable/:page?/:pageSize?/:search?', [AppSLider, 'get_paginate']).as('slider.get_all')
        router.resource('slider', AppSLider).except(['create', 'edit'])
}).use(middleware.auth())

router.group(() => {
        router.get('link_terkait/datatable/:page?/:pageSize?/:search?', [LinkTerkaitController, 'get_paginate']).as('link_terkait.get_all')
        router.resource('link_terkait', LinkTerkaitController).except(['create', 'edit'])
}).use(middleware.auth())

router.group(() => {
        router.get('running_text/datatable/:page?/:pageSize?/:search?', [RunningTextController, 'get_paginate']).as('running_text.get_all')
        router.resource('running_text', RunningTextController).except(['create', 'edit'])
}).use(middleware.auth())

router.group(() => {
        router.get('vidio_beranda/datatable/:page?/:pageSize?/:search?', [VidioBerandaController, 'get_paginate']).as('vidio_beranda.get_all')
        router.resource('vidio_beranda', VidioBerandaController).except(['create', 'edit'])

}).use(middleware.auth())
router.group(() => {
        router.put('pdb_sektor_nasional/aprove/:id_sektor_nasional_pdb', [PdbSNController, 'aprove']).as('pdb_sektor_nasional.aprove').use(middleware.auth())
        router.get('pdb_sektor_nasional/datatable/:page?/:pageSize?/:search?', [PdbSNController, 'get_paginate']).as('PdbSNController.slider')
        router.resource('pdb_sektor_nasional', PdbSNController).except(['create', 'edit'])
}).use(middleware.auth())

router.group(() => {
        router.put('pdrb_sektor_daerah/change_status/:id', [PdrbSDController, 'change_status']).as('pdrb_sektor_daerah.change_status').use(middleware.auth())
        router.get('pdrb_sektor_daerah/datatable/:page?/:pageSize?/:search?', [PdrbSDController, 'get_paginate']).as('PdrbSDController.slider')
        router.resource('pdrb_sektor_daerah', PdrbSDController).except(['create', 'edit'])
}).use(middleware.auth())

router.group(() => {
        router.get('sektor_nasional_insentif/datatable/:page?/:pageSize?/:search?', [InsentifSNController, 'get_paginate']).as('sektor_nasional_insentif.hdt')
        router.put('sektor_nasional_insentif/aprove/:id_sektor_nasional_insentif', [InsentifSNController, 'aprove']).as('sektor_nasonal_insentif.aprove').use(middleware.auth())
        router.resource('sektor_nasional_insentif', InsentifSNController).except(['create', 'edit'])


}).use(middleware.auth())

router.group(() => {
        router.put('umr_provinsi/change_status/:id', [UmrProvController, 'change_status']).as('UmrProvController.change_status').use(middleware.auth())
        router.get('umr_provinsi/datatable/:page?/:pageSize?/:search?', [UmrProvController, 'get_paginate']).as('UmrProvController.slider')
        router.resource('umr_provinsi', UmrProvController).except(['create', 'edit'])


}).use(middleware.auth())



router.group(() => {
        router.get('umkm/datatable/:page?/:pageSize?/:search?', [UmkmController, 'get_paginate']).as('UmkmController.dt')
        router.resource('umkm', UmkmController).except(['create', 'edit'])


}).use(middleware.auth())

router.group(() => {
        router.get('ref_sektor_nasional/datatable/:page?/:pageSize?/:search?', [RefSNController, 'get_paginate']).as('RefSNController.dt')
        router.resource('ref_sektor_nasional', RefSNController).except(['create', 'edit'])


}).use(middleware.auth())

router.group(() => {
        router.get('ref_sub_sektor_nasional/get_by_sektor/:id_sektor_nasional', [RefSSNController, 'get_by_sektor']).as('sub_sektor_nasonal.get_ref_by_sektor')
        router.get('ref_sub_sektor_nasional/datatable/:page?/:pageSize?/:search?', [RefSSNController, 'get_paginate']).as('RefSSNController.dt')
        router.resource('ref_sub_sektor_nasional', RefSSNController).except(['create', 'edit'])
}).use(middleware.auth())
router.group(() => {
        router.get('ref_komoditi_nasional/get_by_sub_sektor/:id_sub_sektor', [RefKNController, 'get_by_sub_sektor']).as('ref_komoditi_nasional.get_ref_by_sub_sektor')
        router.get('ref_komoditi_nasional/datatable/:page?/:pageSize?/:search?', [RefKNController, 'get_paginate']).as('RefKNController.dt')
        router.resource('ref_komoditi_nasional', RefKNController).except(['create', 'edit'])
}).use(middleware.auth())
router.group(() => {
        
        router.get('komoditi_nasional/get_by_sub_sektor/:id_sub_sektor', [KomoditiNasionalController, 'get_by_sub_sektor']).as('KomoditiNasionalController.get_by_sub_sektor')
        router.get('komoditi_nasional/datatable/:page?/:pageSize?/:search?', [KomoditiNasionalController, 'get_paginate']).as('KomoditiNasionalController.dt')
        router.put('komoditi_nasional/change_status/:id', [KomoditiNasionalController, 'change_status']).as('komoditi_nasional.change_status').use(middleware.auth())
        router.resource('komoditi_nasional', KomoditiNasionalController).except(['create', 'edit'])
}).use(middleware.auth())
router.group(() => {
        router.get('ref_peluang_sektor/datatable/:page?/:pageSize?/:search?', [RefPSController, 'get_paginate']).as('RefPSController.dt')
        router.resource('ref_peluang_sektor', RefPSController).except(['create', 'edit'])
}).use(middleware.auth())
router.group(() => {
        router.get('ref_peluang_kontak/datatable/:page?/:pageSize?/:search?', [RefPKController, 'get_paginate']).as('RefPKController.dt')
        router.resource('ref_peluang_kontak', RefPKController).except(['create', 'edit'])
}).use(middleware.auth())

router.group(() => {
        router.get('ref_sumber_data/datatable/:page?/:pageSize?/:search?', [RefSDController, 'get_paginate']).as('RefSDController.dt')
        router.resource('ref_sumber_data', RefSDController).except(['create', 'edit'])
}).use(middleware.auth())
router.group(() => {
        router.get('ref_sumber_data_instansi/datatable/:page?/:pageSize?/:search?', [RefSDIController, 'get_paginate']).as('RefSDIController.dt')
        router.resource('ref_sumber_data_instansi', RefSDIController).except(['create', 'edit'])
}).use(middleware.auth())

router.group(() => {
        router.post('ikn/update_headline', [IknController, 'update_ikn']).as('ikn.update_ikn')
        router.get('ikn/get_headline', [IknController, 'get_headline']).as('ikn.get_ikn')
        router.get('ikn/image/get_datatable/:page?/:pageSize?/:search?', [IknController, 'get_dt_image']).as('ikn.get_dt_image')
        router.get('ikn/peluang/get_datatable/:page?/:pageSize?/:search?', [IknController, 'get_dt_peluang']).as('ikn.get_dt_peluang')
        router.get('ikn/bandara/get_datatable/:page?/:pageSize?/:search?', [IknController, 'get_dt_bandara']).as('ikn.get_dt_bandara')
        router.get('ikn/hotel/get_datatable/:page?/:pageSize?/:search?', [IknController, 'get_dt_hotel']).as('ikn.get_dt_hotel')
        router.get('ikn/pelabuhan/get_datatable/:page?/:pageSize?/:search?', [IknController, 'get_dt_pelabuhan']).as('ikn.get_dt_pelabuhan')
        router.get('ikn/pendidikan/get_datatable/:page?/:pageSize?/:search?', [IknController, 'get_dt_pendidikan']).as('ikn.get_dt_pendidikan')
        router.get('ikn/rumah_sakit/get_datatable/:page?/:pageSize?/:search?', [IknController, 'get_dt_rumah_sakit']).as('ikn.get_dt_rumah_sakit')
        router.get('ikn/kawasan/get_datatable/:page?/:pageSize?/:search?', [IknController, 'get_dt_kawasan_industri']).as('ikn.get_dt_kawasan_industri')
        router.post('ikn/peluang/set_ikn/', [IknController, 'set_ikn_peluang']).as('ikn.set_ikn_peluang')
        router.post('ikn/bandara/set_ikn/', [IknController, 'set_ikn_bandara']).as('ikn.set_ikn_bandara')
        router.post('ikn/hotel/set_ikn/', [IknController, 'set_ikn_hotel']).as('ikn.set_ikn_hotel')
        router.post('ikn/pelabuhan/set_ikn/', [IknController, 'set_ikn_pelabuhan']).as('ikn.set_ikn_pelabuhan')
        router.post('ikn/pendidikan/set_ikn/', [IknController, 'set_ikn_pendidikan']).as('ikn.set_ikn_pendidikan')
        router.post('ikn/rumah_sakit/set_ikn/', [IknController, 'set_ikn_rumah_sakit']).as('ikn.set_ikn_rumah_sakit')
        router.post('ikn/kawasan/set_ikn/', [IknController, 'set_ikn_kawasan']).as('ikn.set_ikn_kawasan')
}).use(middleware.auth())

router.group(() => {
        router.put('ikn_data/change_status/:id', [IknDataController, 'change_status']).as('ikn_data.aprove')
        router.get('ikn_data/datatable/:page?/:pageSize?/:search?', [IknDataController, 'get_paginate']).as('ikn_data.get_paginate')
        router.resource('ikn_data', IknDataController).except(['create', 'edit'])
}).use(middleware.auth())
router.group(() => {
        router.get('ikn_layers/datatable/:page?/:pageSize?/:search?', [IknLayersController, 'get_paginate']).as('ikn_layers.get_paginate')
        router.put('ikn_layers/change_status/:id', [IknLayersController, 'change_status']).as('ikn_layers.aprove')
        router.resource('ikn_layers', IknLayersController).except(['create', 'edit'])
}).use(middleware.auth())
router.group(() => {
        router.put('ikn_images/change_status/:id', [IknImagesController, 'change_status']).as('ikn_images.aprove')
        router.get('ikn_images/datatable/:page?/:pageSize?/:search?', [IknImagesController, 'get_paginate']).as('ikn_images.get_paginate')
        router.resource('ikn_images', IknImagesController).except(['create', 'edit', 'index'])
})

router.group(() => {
        router.get('get_news_by_kategori/:id_news_kategori', [NewsController, 'getNewsByKategori']).as('news.getNewsByKategori')
        router.post('admin/create_and_update_kajian', [NewsController, 'createOrUpdateKajian']).as('news.createOrUpdateKajian')
        router.post('admin/toggle_status_kajian/:id', [NewsController, 'toggleStatusKajian']).as('news.toggleStatusKajian').use(middleware.auth())
        router.patch('admin/toggle_status_kajian/:id', [NewsController, 'toggleStatusKajian']).as('news.togglePatchStatusKajian');
        router.delete('admin/delete_kajian/:id', [NewsController, 'destroyKajian']).as('news.destroyKajian')
        router.resource('news', NewsController).except(['create', 'edit', 'getNewsByKategori', 'createOrUpdateKajian', 'toggleStatusKajian'])
}).use(middleware.auth())

router.group(() => {
        router.put('peluang_kabkot/aprove/:id_peluang_kabkot', [PeluangController, 'aprove']).as('tb_peluang_kabkot.aprove').use(middleware.auth())
        router.get('peluang_kabkot/datatable/:page?/:pageSize?/:search?', [PeluangController, 'get_paginate']).as('tb_peluang_kabkot.get_paginate')
        router.put('peluang_kabkot/change_status_proyek/:id_peluang_kabkot', [PeluangController, 'change_status_proyek']).as('tb_peluang_kabkot.change_status_proyek')
        router.resource('peluang_kabkot', PeluangController).except(['create', 'edit'])
}).use(middleware.auth())

router.group(() => {
        router.get('role/datatable/:page?/:pageSize?/:search?', [RoleController, 'get_paginate']).as('role.dt')

        // router.get('role/:page/:pageSize', [RoleController, 'get_paginate']).as('role.get_paginate')
        router.resource('role', RoleController).except(['create', 'edit'])
}).use(middleware.auth())




router.group(() => {
        router.get('menu/datatable/:page?/:pageSize?/:search?', [MenusController, 'get_paginate']).as('menu.dt')
        // router.get('menu/:page/:pageSize', [MenusController, 'get_paginate']).as('menu.get_paginate')
        router.get('get_role_menu/:id_role', [MenusController, 'get_role_menu']).as('menu.get_role_menu')
        router.get('menu/get_tree', [MenusController, 'get_tree']).as('menu.get_tree')
        router.resource('menu', MenusController).except(['create', 'edit'])
}).use(middleware.auth())

router.group(() => {
        router.get('user/datatable/:page?/:pageSize?/:search?', [AuthController, 'get_paginate']).as('AuthController.dt')
        router.get('user/me', [AuthController, 'me']).as('auth.me').use(middleware.auth())
        // router.get('user/:page/:pageSize', [AuthController, 'get_paginate']).as('auth.get_paginate')
        router.resource('user', AuthController).except(['create', 'edit'])
}).use(middleware.auth())

router.group(() => {
        router.get('role_menu/datatable/:page?/:pageSize?/:search?', [RoleMenusController, 'get_paginate']).as('role_menu.dt')
        // router.get('role_menu/:page/:pageSize', [RoleMenusController, 'get_paginate']).as('role_menu.get_paginate')
        router.resource('role_menu', RoleMenusController).except(['create', 'edit'])
}).use(middleware.auth())

router.group(() => {
        router.post('login', [AuthController, 'login']).as('auth.login').use(middleware.loginUpdate())
        router.delete('logout', [AuthController, 'logout']).as('auth.logout').use(middleware.auth())
        router.post('forgot_password', [AuthController, 'forgot_password']).as('auth.forgotPassword')
        router.post('reset_password', [AuthController, 'reset_password']).as('auth.resetPassword')
        router.post('update_password/:id', [AuthController, 'update_password']).as('auth.updatePassword')
})

router.group(() => {
        router.resource('config_upload', ConfigUploadsController).except(['create', 'edit'])

})

router.get('/uploads/*', async ({ params, response }) => {
        const array = params['*'];
        const param = array.join('/').replace(/%20/g, ' ');
        const filePath = path.join('uploads', param);
        if (fs.existsSync(filePath)) {
                return response.download(filePath);
        } else {
                return response.status(404).json({ message: 'File not found' });
        }
})

router.get('download/:id', [CustomController, 'download']).as('custom.download')
// router.post('tes_img', [CustomController, 'tes_img']).as('custom.tes_img')


router.group(() => {
        // router.post('insert', [CustomController, 'insertData']).as('custom.insert')
        // router.post('insert2', [CustomController, 'insertData2']).as('custom.insert2')
        // router.post('update', [CustomController, 'updateData']).as('custom.update')
        // router.post('delete', [CustomController, 'deleteData']).as('custom.delete')
        // router.post('getAll', [CustomController, 'getAllData']).as('custom.getAll')
        // router.post('getReferensi/:table', [CustomController, 'getReferensi']).as('custom.getReferensi')
        router.get('getReferensi/:table', [CustomController, 'getReferensi']).as('custom.getReferensiGet')
        // router.post('postAllFiles', [CustomController, 'postAllFiles']).as('custom.postAllFIles')
        
        // router.get('get_status2', [CustomController, 'get_status2']).as('custom.get_status')
}).prefix('global/')


router.group(() => {
        router.post('image', [CustomController, 'image']).as('upload.image')
        router.post('image_to_webp', [CustomController, 'image_to_webp']).as('upload.image_to_webp')
        router.post('vidio', [CustomController, 'vidio']).as('upload.vidio')
        router.post('doc', [CustomController, 'doc']).as('upload.doc')
}).prefix('upload/')




// PROVINSI
router.group(() => {
        router.get('/lists', [AdminProvinsiDemografiController, 'get']).as('admin.pd.get')
        router.get('/lists/:id', [AdminProvinsiDemografiController, 'getById']).as('admin.pd.get_by_id')
        router.post('/create', [AdminProvinsiDemografiController, 'createOrUpdate']).as('admin.pd.create')
        router.delete('/delete/:id', [AdminProvinsiDemografiController, 'deleteById']).as('admin.pd.delete')
        router.patch('/update/:id', [AdminProvinsiDemografiController, 'createOrUpdate']).as('admin.pd.update')
        router.patch('/toggle_status/:id', [AdminProvinsiDemografiController, 'toggleStatus']).as('admin.pd.toggle_status')
}).prefix('admin/provinsi_demografi').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminProvinsiKantorController, 'get']).as('admin.pk.get')
        router.get('/lists/:id', [AdminProvinsiKantorController, 'getById']).as('admin.pk.get_by_id')
        router.post('/create', [AdminProvinsiKantorController, 'createOrUpdate']).as('admin.pk.create')
        router.delete('/delete/:id', [AdminProvinsiKantorController, 'deleteById']).as('admin.pk.delete')
        router.patch('/update/:id', [AdminProvinsiKantorController, 'createOrUpdate']).as('admin.pk.update')
        router.patch('/toggle_status/:id', [AdminProvinsiKantorController, 'toggleStatus']).as('admin.pk.toggle_status')
}).prefix('admin/provinsi_kantor').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminProvinsiProfilController, 'get']).as('admin.pp.get')
        router.get('/lists/:id', [AdminProvinsiProfilController, 'getById']).as('admin.pp.get_by_id')
        router.post('/create', [AdminProvinsiProfilController, 'createOrUpdate']).as('admin.pp.create')
        router.delete('/delete/:id', [AdminProvinsiProfilController, 'deleteById']).as('admin.pp.delete')
        router.patch('/update/:id', [AdminProvinsiProfilController, 'createOrUpdate']).as('admin.pp.update')
        // router.patch('/toggle_status/:id', [AdminProvinsiProfilController, 'toggleStatus']).as('admin.pp.toggle_status')
}).prefix('admin/provinsi_profil').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminProvinsiEksporImporController, 'get']).as('admin.pei.get')
        router.get('/lists/:id', [AdminProvinsiEksporImporController, 'getById']).as('admin.pei.get_by_id')
        router.post('/create', [AdminProvinsiEksporImporController, 'createOrUpdate']).as('admin.pei.create')
        router.delete('/delete/:id', [AdminProvinsiEksporImporController, 'deleteById']).as('admin.pei.delete')
        router.patch('/update/:id', [AdminProvinsiEksporImporController, 'createOrUpdate']).as('admin.pei.update')
        router.patch('/toggle_status/:id', [AdminProvinsiEksporImporController, 'toggleStatus']).as('admin.pei.toggle_status')
}).prefix('admin/provinsi_ekspor_impor').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminProvinsiUtilitasController, 'get']).as('admin.pu.get')
        router.get('/lists/:id', [AdminProvinsiUtilitasController, 'getById']).as('admin.pu.get_by_id')
        router.post('/create', [AdminProvinsiUtilitasController, 'createOrUpdate']).as('admin.pu.create')
        router.delete('/delete/:id', [AdminProvinsiUtilitasController, 'deleteById']).as('admin.pu.delete')
        router.patch('/update/:id', [AdminProvinsiUtilitasController, 'createOrUpdate']).as('admin.pu.update')
        router.patch('/toggle_status/:id', [AdminProvinsiUtilitasController, 'toggleStatus']).as('admin.pu.toggle_status')
}).prefix('admin/provinsi_utilitas').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminProvinsiInvestasiController, 'get']).as('admin.pi.get')
        router.get('/lists/:id', [AdminProvinsiInvestasiController, 'getById']).as('admin.pi.get_by_id')
        router.post('/create', [AdminProvinsiInvestasiController, 'createOrUpdate']).as('admin.pi.create')
        router.delete('/delete/:id', [AdminProvinsiInvestasiController, 'deleteById']).as('admin.pi.delete')
        router.patch('/update/:id', [AdminProvinsiInvestasiController, 'createOrUpdate']).as('admin.pi.update')
        router.patch('/toggle_status/:id', [AdminProvinsiInvestasiController, 'toggleStatus']).as('admin.pi.toggle_status')
}).prefix('admin/provinsi_investasi').use(middleware.auth())

// KABUPATEN
router.group(() => {
        router.get('/lists', [AdminKabupatenDemografiController, 'get']).as('admin.kd.get')
        router.get('/lists/:id', [AdminKabupatenDemografiController, 'getById']).as('admin.kd.get_by_id')
        router.post('/create', [AdminKabupatenDemografiController, 'createOrUpdate']).as('admin.kd.create')
        router.delete('/delete/:id', [AdminKabupatenDemografiController, 'deleteById']).as('admin.kd.delete')
        router.patch('/update/:id', [AdminKabupatenDemografiController, 'createOrUpdate']).as('admin.kd.update')
        router.patch('/toggle_status/:id', [AdminKabupatenDemografiController, 'toggleStatus']).as('admin.kd.toggle_status')
}).prefix('admin/kabupaten_demografi').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminKabupatenUmrController, 'get']).as('admin.kbu.get')
        router.get('/lists/:id', [AdminKabupatenUmrController, 'getById']).as('admin.kbu.get_by_id')
        router.post('/create', [AdminKabupatenUmrController, 'createOrUpdate']).as('admin.kbu.create')
        router.delete('/delete/:id', [AdminKabupatenUmrController, 'deleteById']).as('admin.kbu.delete')
        router.patch('/update/:id', [AdminKabupatenUmrController, 'createOrUpdate']).as('admin.kbu.update')
        router.patch('/toggle_status/:id', [AdminKabupatenUmrController, 'toggleStatus']).as('admin.kbu.toggle_status')
}).prefix('admin/kabupaten_umr').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminKabupatenKantorController, 'get']).as('admin.kk.get')
        router.get('/lists/:id', [AdminKabupatenKantorController, 'getById']).as('admin.kk.get_by_id')
        router.post('/create', [AdminKabupatenKantorController, 'createOrUpdate']).as('admin.kk.create')
        router.delete('/delete/:id', [AdminKabupatenKantorController, 'deleteById']).as('admin.kk.delete')
        router.patch('/update/:id', [AdminKabupatenKantorController, 'createOrUpdate']).as('admin.kk.update')
        router.patch('/toggle_status/:id', [AdminKabupatenKantorController, 'toggleStatus']).as('admin.kk.toggle_status')
}).prefix('admin/kabupaten_kantor').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminKabupatenProfilController, 'get']).as('admin.kp.get')
        router.get('/lists/:id', [AdminKabupatenProfilController, 'getById']).as('admin.kp.get_by_id')
        router.post('/create', [AdminKabupatenProfilController, 'createOrUpdate']).as('admin.kp.create')
        router.delete('/delete/:id', [AdminKabupatenProfilController, 'deleteById']).as('admin.kp.delete')
        router.patch('/update/:id', [AdminKabupatenProfilController, 'createOrUpdate']).as('admin.kp.update')
        // router.patch('/toggle_status/:id', [AdminKabupatenProfilController, 'toggleStatus']).as('admin.kp.toggle_status')
}).prefix('admin/kabupaten_profil').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminKabupatenEksporImporController, 'get']).as('admin.kei.get')
        router.get('/lists/:id', [AdminKabupatenEksporImporController, 'getById']).as('admin.kei.get_by_id')
        router.post('/create', [AdminKabupatenEksporImporController, 'createOrUpdate']).as('admin.kei.create')
        router.delete('/delete/:id', [AdminKabupatenEksporImporController, 'deleteById']).as('admin.kei.delete')
        router.patch('/update/:id', [AdminKabupatenEksporImporController, 'createOrUpdate']).as('admin.kei.update')
        router.patch('/toggle_status/:id', [AdminKabupatenEksporImporController, 'toggleStatus']).as('admin.kei.toggle_status')
}).prefix('admin/kabupaten_ekspor_impor').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminKabupatenUtilitasController, 'get']).as('admin.ku.get')
        router.get('/lists/:id', [AdminKabupatenUtilitasController, 'getById']).as('admin.ku.get_by_id')
        router.post('/create', [AdminKabupatenUtilitasController, 'createOrUpdate']).as('admin.ku.create')
        router.delete('/delete/:id', [AdminKabupatenUtilitasController, 'deleteById']).as('admin.ku.delete')
        router.patch('/update/:id', [AdminKabupatenUtilitasController, 'createOrUpdate']).as('admin.ku.update')
        router.patch('/toggle_status/:id', [AdminKabupatenUtilitasController, 'toggleStatus']).as('admin.ku.toggle_status')
}).prefix('admin/kabupaten_utilitas').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminKabupatenInvestasiController, 'get']).as('admin.kbi.get')
        router.get('/lists/:id', [AdminKabupatenInvestasiController, 'getById']).as('admin.kbi.get_by_id')
        router.post('/create', [AdminKabupatenInvestasiController, 'createOrUpdate']).as('admin.kbi.create')
        router.delete('/delete/:id', [AdminKabupatenInvestasiController, 'deleteById']).as('admin.kbi.delete')
        router.patch('/update/:id', [AdminKabupatenInvestasiController, 'createOrUpdate']).as('admin.kbi.update')
        router.patch('/toggle_status/:id', [AdminKabupatenInvestasiController, 'toggleStatus']).as('admin.kbi.toggle_status')
}).prefix('admin/kabupaten_investasi').use(middleware.auth())

// router.group(() => {
//         router.get('/lists', [AdminSUDSektorNasionalController, 'get']).as('admin.sudsn.get')
//         router.get('/lists/:id', [AdminSUDSektorNasionalController, 'getById']).as('admin.sudsn.get_by_id')
//         router.post('/create', [AdminSUDSektorNasionalController, 'createOrUpdate']).as('admin.sudsn.create')
//         router.delete('/delete/:id', [AdminSUDSektorNasionalController, 'deleteById']).as('admin.sudsn.delete')
//         router.patch('/update/:id', [AdminSUDSektorNasionalController, 'createOrUpdate']).as('admin.sudsn.update')
//         router.get('/get_pdrb_lq', [AdminSUDSektorNasionalController, 'getPdrbLq']).as('admin.sudsn.get_pdrb_lq')
// }).prefix('admin/sud_sektor_nasional')

// router.group(() => {
//         router.get('/lists', [AdminSUDSubSektorNasionalController, 'get']).as('admin.sudssn.get')
//         router.get('/lists/:id', [AdminSUDSubSektorNasionalController, 'getById']).as('admin.sudssn.get_by_id')
//         router.post('/create', [AdminSUDSubSektorNasionalController, 'createOrUpdate']).as('admin.sudssn.create')
//         router.delete('/delete/:id', [AdminSUDSubSektorNasionalController, 'deleteById']).as('admin.sudssn.delete')
//         router.patch('/update/:id', [AdminSUDSubSektorNasionalController, 'createOrUpdate']).as('admin.sudssn.update')
// }).prefix('admin/sud_sub_sektor_nasional')

// SEKTOR UNGGULAN DAERAH
router.group(() => {
        router.get('/lists', [AdminSUDSektorDaerahController, 'get']).as('admin.sudsd.get')
        router.get('/lists/:id', [AdminSUDSektorDaerahController, 'getById']).as('admin.sudsd.get_by_id')
        router.post('/create', [AdminSUDSektorDaerahController, 'createOrUpdate']).as('admin.sudsd.create')
        router.delete('/delete/:id', [AdminSUDSektorDaerahController, 'deleteById']).as('admin.sudsd.delete')
        router.patch('/update/:id', [AdminSUDSektorDaerahController, 'createOrUpdate']).as('admin.sudsd.update')
        router.get('/get_pdrb_lq', [AdminSUDSektorDaerahController, 'getPdrbLq']).as('admin.sudsd.get_pdrb_lq')
        router.patch('/toggle_status/:id', [AdminSUDSektorDaerahController, 'toggleStatus']).as('admin.sudsd.toggle_status')
}).prefix('admin/sud_sektor_daerah').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminSUDSubSektorDaerahController, 'get']).as('admin.sudssd.get')
        router.get('/lists/:id', [AdminSUDSubSektorDaerahController, 'getById']).as('admin.sudssd.get_by_id')
        router.post('/create', [AdminSUDSubSektorDaerahController, 'createOrUpdate']).as('admin.sudssd.create')
        router.delete('/delete/:id', [AdminSUDSubSektorDaerahController, 'deleteById']).as('admin.sudssd.delete')
        router.patch('/update/:id', [AdminSUDSubSektorDaerahController, 'createOrUpdate']).as('admin.sudssd.update')
        router.patch('/toggle_status/:id', [AdminSUDSubSektorDaerahController, 'toggleStatus']).as('admin.sudssd.toggle_status')
}).prefix('admin/sud_sub_sektor_daerah').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminSUDSektorDaerahInsentifController, 'get']).as('admin.sudsdi.get')
        router.get('/lists/:id', [AdminSUDSektorDaerahInsentifController, 'getById']).as('admin.sudsdi.get_by_id')
        router.post('/create', [AdminSUDSektorDaerahInsentifController, 'createOrUpdate']).as('admin.sudsdi.create')
        router.delete('/delete/:id', [AdminSUDSektorDaerahInsentifController, 'deleteById']).as('admin.sudsdi.delete')
        router.patch('/update/:id', [AdminSUDSektorDaerahInsentifController, 'createOrUpdate']).as('admin.sudsdi.update')
        router.patch('/toggle_status/:id', [AdminSUDSektorDaerahInsentifController, 'toggleStatus']).as('admin.sudsdi.toggle_status')
}).prefix('admin/sud_sektor_daerah_insentif').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminSUDKomoditiDaerahController, 'get']).as('admin.sudkd.get')
        router.get('/lists/:id', [AdminSUDKomoditiDaerahController, 'getById']).as('admin.sudkd.get_by_id')
        router.post('/create', [AdminSUDKomoditiDaerahController, 'createOrUpdate']).as('admin.sudkd.create')
        router.delete('/delete/:id', [AdminSUDKomoditiDaerahController, 'deleteById']).as('admin.sudkd.delete')
        router.patch('/update/:id', [AdminSUDKomoditiDaerahController, 'createOrUpdate']).as('admin.sudkd.update')
        router.patch('/toggle_status/:id', [AdminSUDKomoditiDaerahController, 'toggleStatus']).as('admin.sudkd.toggle_status')
}).prefix('admin/sud_komoditi_daerah').use(middleware.auth())

// KAWASAN (KEK & KI) 
router.group(() => {
        router.get('/lists', [AdminKawasanIndustriController, 'get']).as('admin.ki.get')
        router.get('/lists/:id', [AdminKawasanIndustriController, 'getById']).as('admin.ki.get_by_id')
        router.post('/creates', [AdminKawasanIndustriController, 'createOrUpdate']).as('admin.ki.creates')
        router.post('/create', [AdminKawasanIndustriController, 'createOrUpdate']).as('admin.ki.create')
        router.delete('/deletes/:id', [AdminKawasanIndustriController, 'deleteById']).as('admin.ki.delete')
        router.patch('/update/:id', [AdminKawasanIndustriController, 'createOrUpdate']).as('admin.ki.update')
        router.patch('/toggle_status/:id', [AdminKawasanIndustriController, 'toggleStatus']).as('admin.ki.toggle_status')
}).prefix('admin/kawasan_industri').use(middleware.auth())


router.group(() => {
        router.get('/lists', [AdminKawasanPotensiInvestasiController, 'get']).as('admin.kpi.get')
        router.get('/lists/:id', [AdminKawasanPotensiInvestasiController, 'getById']).as('admin.kpi.get_by_id')
        router.post('/create', [AdminKawasanPotensiInvestasiController, 'createOrUpdate']).as('admin.kpi.create')
        router.delete('/delete/:id', [AdminKawasanPotensiInvestasiController, 'deleteById']).as('admin.kpi.delete')
        router.patch('/update/:id', [AdminKawasanPotensiInvestasiController, 'createOrUpdate']).as('admin.kpi.update')
}).prefix('admin/kawasan_potensi_investasi').use(middleware.auth())

// KOMODITI
router.group(() => {
        router.get('/lists', [AdminKomoditiProvinsiController, 'get']).as('admin.kop.get')
        router.get('/lists/:id', [AdminKomoditiProvinsiController, 'getById']).as('admin.kop.get_by_id')
        router.post('/create', [AdminKomoditiProvinsiController, 'createOrUpdate']).as('admin.kop.create')
        router.delete('/delete/:id', [AdminKomoditiProvinsiController, 'deleteById']).as('admin.kop.delete')
        router.patch('/update/:id', [AdminKomoditiProvinsiController, 'createOrUpdate']).as('admin.kop.update')
        router.patch('/toggle_status/:id', [AdminKomoditiProvinsiController, 'toggleStatus']).as('admin.kop.toggle_status')
}).prefix('admin/komoditi_provinsi').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminKomoditiKabkotController, 'get']).as('admin.kok.get')
        router.get('/lists/:id', [AdminKomoditiKabkotController, 'getById']).as('admin.kok.get_by_id')
        router.post('/create', [AdminKomoditiKabkotController, 'createOrUpdate']).as('admin.kok.create')
        router.delete('/delete/:id', [AdminKomoditiKabkotController, 'deleteById']).as('admin.kok.delete')
        router.patch('/update/:id', [AdminKomoditiKabkotController, 'createOrUpdate']).as('admin.kok.update')
        router.patch('/toggle_status/:id', [AdminKomoditiKabkotController, 'toggleStatus']).as('admin.kok.toggle_status')
}).prefix('admin/komoditi_kabkot').use(middleware.auth())

// SARANA PRASARANA 
router.group(() => {
        router.get('/lists', [AdminSarprasBandaraController, 'get']).as('admin.sb.get')
        router.get('/lists/:id', [AdminSarprasBandaraController, 'getById']).as('admin.sb.get_by_id')
        router.post('/create', [AdminSarprasBandaraController, 'createOrUpdate']).as('admin.sb.create')
        router.delete('/delete/:id', [AdminSarprasBandaraController, 'deleteById']).as('admin.sb.delete')
        router.patch('/update/:id', [AdminSarprasBandaraController, 'createOrUpdate']).as('admin.sb.update')
        router.patch('/toggle_status/:id', [AdminSarprasBandaraController, 'toggleStatus']).as('admin.sb.toggle_status')
}).prefix('admin/sarpras_bandara').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminSarprasPelabuhanController, 'get']).as('admin.sp.get')
        router.get('/lists/:id', [AdminSarprasPelabuhanController, 'getById']).as('admin.sp.get_by_id')
        router.post('/create', [AdminSarprasPelabuhanController, 'createOrUpdate']).as('admin.sp.create')
        router.delete('/delete/:id', [AdminSarprasPelabuhanController, 'deleteById']).as('admin.sp.delete')
        router.patch('/update/:id', [AdminSarprasPelabuhanController, 'createOrUpdate']).as('admin.sp.update')
        router.patch('/toggle_status/:id', [AdminSarprasPelabuhanController, 'toggleStatus']).as('admin.sp.toggle_status')
}).prefix('admin/sarpras_pelabuhan').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminSarprasRumahSakitController, 'get']).as('admin.srs.get')
        router.get('/lists/:id', [AdminSarprasRumahSakitController, 'getById']).as('admin.srs.get_by_id')
        router.post('/create', [AdminSarprasRumahSakitController, 'createOrUpdate']).as('admin.srs.create')
        router.delete('/delete/:id', [AdminSarprasRumahSakitController, 'deleteById']).as('admin.srs.delete')
        router.patch('/update/:id', [AdminSarprasRumahSakitController, 'createOrUpdate']).as('admin.srs.update')
        router.patch('/toggle_status/:id', [AdminSarprasRumahSakitController, 'toggleStatus']).as('admin.srs.toggle_status')
}).prefix('admin/sarpras_rumah_sakit').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminSarprasHotelController, 'get']).as('admin.ssh.get')
        router.get('/lists/:id', [AdminSarprasHotelController, 'getById']).as('admin.ssh.get_by_id')
        router.post('/create', [AdminSarprasHotelController, 'createOrUpdate']).as('admin.ssh.create')
        router.delete('/delete/:id', [AdminSarprasHotelController, 'deleteById']).as('admin.ssh.delete')
        router.patch('/update/:id', [AdminSarprasHotelController, 'createOrUpdate']).as('admin.ssh.update')
        router.patch('/toggle_status/:id', [AdminSarprasHotelController, 'toggleStatus']).as('admin.ssh.toggle_status')
}).prefix('admin/sarpras_hotel').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminSarprasPendidikanController, 'get']).as('admin.spp.get')
        router.get('/lists/:id', [AdminSarprasPendidikanController, 'getById']).as('admin.spp.get_by_id')
        router.post('/create', [AdminSarprasPendidikanController, 'createOrUpdate']).as('admin.spp.create')
        router.delete('/delete/:id', [AdminSarprasPendidikanController, 'deleteById']).as('admin.spp.delete')
        router.patch('/update/:id', [AdminSarprasPendidikanController, 'createOrUpdate']).as('admin.spp.update')
        router.patch('/toggle_status/:id', [AdminSarprasPendidikanController, 'toggleStatus']).as('admin.spp.toggle_status')
}).prefix('admin/sarpras_pendidikan').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminSarprasListrikController, 'get']).as('admin.sl.get')
        router.get('/lists/:id', [AdminSarprasListrikController, 'getById']).as('admin.sl.get_by_id')
        router.post('/create', [AdminSarprasListrikController, 'createOrUpdate']).as('admin.sl.create')
        router.delete('/delete/:id', [AdminSarprasListrikController, 'deleteById']).as('admin.sl.delete')
        router.patch('/update/:id', [AdminSarprasListrikController, 'createOrUpdate']).as('admin.sl.update')
        router.patch('/toggle_status/:id', [AdminSarprasListrikController, 'toggleStatus']).as('admin.sl.toggle_status')
}).prefix('admin/sarpras_listrik').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminSarprasGasController, 'get']).as('admin.sg.get')
        router.get('/lists/:id', [AdminSarprasGasController, 'getById']).as('admin.sg.get_by_id')
        router.post('/create', [AdminSarprasGasController, 'createOrUpdate']).as('admin.sg.create')
        router.delete('/delete/:id', [AdminSarprasGasController, 'deleteById']).as('admin.sg.delete')
        router.patch('/update/:id', [AdminSarprasGasController, 'createOrUpdate']).as('admin.sg.update')
        router.patch('/toggle_status/:id', [AdminSarprasGasController, 'toggleStatus']).as('admin.sg.toggle_status')
}).prefix('admin/sarpras_gas').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminSarprasAirController, 'get']).as('admin.sa.get')
        router.get('/lists/:id', [AdminSarprasAirController, 'getById']).as('admin.sa.get_by_id')
        router.post('/create', [AdminSarprasAirController, 'createOrUpdate']).as('admin.sa.create')
        router.delete('/delete/:id', [AdminSarprasAirController, 'deleteById']).as('admin.sa.delete')
        router.patch('/update/:id', [AdminSarprasAirController, 'createOrUpdate']).as('admin.sa.update')
        router.patch('/toggle_status/:id', [AdminSarprasAirController, 'toggleStatus']).as('admin.sa.toggle_status')
}).prefix('admin/sarpras_air').use(middleware.auth())


// PELUANG INVENTASI DAERAH
router.group(() => {
        router.get('/lists', [AdminPeluangInvestasiDaerahController, 'get']).as('admin.pid.get')
        router.get('/lists/:id', [AdminPeluangInvestasiDaerahController, 'getById']).as('admin.pid.get_by_id')
        router.post('/create', [AdminPeluangInvestasiDaerahController, 'createOrUpdate']).as('admin.pid.create')
        router.delete('/delete/:id', [AdminPeluangInvestasiDaerahController, 'deleteById']).as('admin.pid.delete')
        router.patch('/update/:id', [AdminPeluangInvestasiDaerahController, 'createOrUpdate']).as('admin.pid.update')
        router.get('/hitung_kelayakan', [AdminPeluangInvestasiDaerahController, 'hitungKelayakan']).as('admin.pid.get_hitung_kelayakan')
        router.patch('/toggle_status/:id', [AdminPeluangInvestasiDaerahController, 'toggleStatus']).as('admin.pid.toggle_status')
}).prefix('admin/peluang_investasi_daerah').use(middleware.auth())


// INFORMASI
// ROADMAP
router.group(() => {
        router.get('/lists', [AdminRoadmapInputDataTableController, 'get']).as('admin.ridt.get')
        router.get('/lists/:id', [AdminRoadmapInputDataTableController, 'getById']).as('admin.ridt.get_by_id')
        router.post('/create', [AdminRoadmapInputDataTableController, 'createOrUpdate']).as('admin.ridt.create')
        router.delete('/delete/:id', [AdminRoadmapInputDataTableController, 'deleteById']).as('admin.ridt.delete')
        router.patch('/update/:id', [AdminRoadmapInputDataTableController, 'createOrUpdate']).as('admin.ridt.update')
        router.patch('/toggle_status/:id', [AdminRoadmapInputDataTableController, 'toggleStatus']).as('admin.ridt.toggle_status')
}).prefix('admin/roadmap_input_data_table').use(middleware.auth())

// KEBIJAKAN
router.group(() => {
        router.get('/lists', [AdminKebijakanController, 'get']).as('admin.kebijakan.get')
        router.get('/lists/:id', [AdminKebijakanController, 'getById']).as('admin.kebijakan.get_by_id')
        router.post('/create', [AdminKebijakanController, 'createOrUpdate']).as('admin.kebijakan.create')
        router.delete('/delete/:id', [AdminKebijakanController, 'deleteById']).as('admin.kebijakan.delete')
        router.patch('/update/:id', [AdminKebijakanController, 'createOrUpdate']).as('admin.kebijakan.update')
        router.post('/toggle_status/:id', [AdminKebijakanController, 'toggleStatus']).as('admin.kebijakan.toggleStatus').use(middleware.auth())
        router.patch('/toggle_status/:id', [AdminKebijakanController, 'toggleStatus']).as('admin.patch.kebijakan.toggleStatus');
}).prefix('admin/kebijakan').use(middleware.auth())

// INSENTIF
router.group(() => {
        router.get('/lists', [AdminInsentifController, 'get']).as('admin.insentif.get')
        router.get('/lists/:id', [AdminInsentifController, 'getById']).as('admin.insentif.get_by_id')
        router.post('/create', [AdminInsentifController, 'createOrUpdate']).as('admin.insentif.create')
        router.delete('/delete/:id', [AdminInsentifController, 'deleteById']).as('admin.insentif.delete')
        router.patch('/update/:id', [AdminInsentifController, 'createOrUpdate']).as('admin.insentif.update')
}).prefix('admin/insentif').use(middleware.auth())
// ARTIKEL
// INPUTDATA
router.group(() => {
        router.get('/lists', [AdminArtikelInputDataController, 'get']).as('admin.aaid.get')
        router.get('/lists/:id', [AdminArtikelInputDataController, 'getById']).as('admin.aaid.get_by_id')
        router.post('/create', [AdminArtikelInputDataController, 'createOrUpdate']).as('admin.aaid.create')
        router.delete('/delete/:id', [AdminArtikelInputDataController, 'deleteById']).as('admin.aaid.delete')
        router.patch('/update/:id', [AdminArtikelInputDataController, 'createOrUpdate']).as('admin.aaid.update')
        router.post('/toggle_status/:id', [AdminArtikelInputDataController, 'toggleStatus']).as('admin.aaid.toggleStatus').use(middleware.auth())
        router.patch('/toggle_status/:id', [AdminArtikelInputDataController, 'toggleStatus']).as('admin.patch.aaid.toggleStatus')
}).prefix('admin/artikel_input_data').use(middleware.auth())

//MAPSERVICES
// Daerah > Provinsi
// tb_adm_provinsi_layer_spasial
router.group(() => {
        router.get('/lists', [AdminAdmProvinsiLayerSpasialController, 'get']).as('admin.adm_provinsi_layer_spasial.get')
        router.get('/lists/:id', [AdminAdmProvinsiLayerSpasialController, 'getById']).as('admin.adm_provinsi_layer_spasial.get_by_id')
        router.post('/create', [AdminAdmProvinsiLayerSpasialController, 'createOrUpdate']).as('admin.adm_provinsi_layer_spasial.create')
        router.delete('/delete/:id', [AdminAdmProvinsiLayerSpasialController, 'deleteById']).as('admin.adm_provinsi_layer_spasial.delete')
        router.patch('/update/:id', [AdminAdmProvinsiLayerSpasialController, 'createOrUpdate']).as('admin.adm_provinsi_layer_spasial.update')
}).prefix('admin/adm_provinsi_layer_spasial').use(middleware.auth())

// Daerah > Kabupaten/Kota
// tb_adm_kabkot_layer_spasial
router.group(() => {
        router.get('/lists', [AdminAdmKabkotLayerSpasialController, 'get']).as('admin.adm_kabkot_layer_spasial.get')
        router.get('/lists/:id', [AdminAdmKabkotLayerSpasialController, 'getById']).as('admin.adm_kabkot_layer_spasial.get_by_id')
        router.post('/create', [AdminAdmKabkotLayerSpasialController, 'createOrUpdate']).as('admin.adm_kabkot_layer_spasial.create')
        router.delete('/delete/:id', [AdminAdmKabkotLayerSpasialController, 'deleteById']).as('admin.adm_kabkot_layer_spasial.delete')
        router.patch('/update/:id', [AdminAdmKabkotLayerSpasialController, 'createOrUpdate']).as('admin.adm_kabkot_layer_spasial.update')
}).prefix('admin/adm_kabkot_layer_spasial').use(middleware.auth())

// Daerah > Kawasan KEK/KI
// tb_kawasan_layers
router.group(() => {
        router.get('/lists', [AdminKawasanLayersController, 'get']).as('admin.kawasan_layers.get')
        router.get('/lists/:id', [AdminKawasanLayersController, 'getById']).as('admin.kawasan_layers.get_by_id')
        router.post('/create', [AdminKawasanLayersController, 'createOrUpdate']).as('admin.kawasan_layers.create')
        router.delete('/delete/:id', [AdminKawasanLayersController, 'deleteById']).as('admin.kawasan_layers.delete')
        router.patch('/update/:id', [AdminKawasanLayersController, 'createOrUpdate']).as('admin.kawasan_layers.update')
}).prefix('admin/kawasan_layers').use(middleware.auth())

// Daerah > Kawasan Komoditi
// tb_komoditi_layers
router.group(() => {
        router.get('/lists', [AdminKomoditiLayersController, 'get']).as('admin.komoditi_layers.get')
        router.get('/lists/:id', [AdminKomoditiLayersController, 'getById']).as('admin.komoditi_layers.get_by_id')
        router.post('/create', [AdminKomoditiLayersController, 'createOrUpdate']).as('admin.komoditi_layers.create')
        router.delete('/delete/:id', [AdminKomoditiLayersController, 'deleteById']).as('admin.komoditi_layers.delete')
        router.patch('/update/:id', [AdminKomoditiLayersController, 'createOrUpdate']).as('admin.komoditi_layers.update')
}).prefix('admin/komoditi_layers').use(middleware.auth())

// Daerah > Peluang Investasi Daerah
// tb_peluang_daerah_layer_spasial
router.group(() => {
        router.get('/lists', [AdminPeluangDaerahLayerSpasialController, 'get']).as('admin.peluang_daerah_layer_spasial.get')
        router.get('/lists/:id', [AdminPeluangDaerahLayerSpasialController, 'getById']).as('admin.peluang_daerah_layer_spasial.get_by_id')
        router.post('/create', [AdminPeluangDaerahLayerSpasialController, 'createOrUpdate']).as('admin.peluang_daerah_layer_spasial.create')
        router.delete('/delete/:id', [AdminPeluangDaerahLayerSpasialController, 'deleteById']).as('admin.peluang_daerah_layer_spasial.delete')
        router.patch('/update/:id', [AdminPeluangDaerahLayerSpasialController, 'createOrUpdate']).as('admin.peluang_daerah_layer_spasial.update')
}).prefix('admin/peluang_daerah_layer_spasial').use(middleware.auth())

router.group(() => {
        router.get('/lists', [AdminPeluangLayersController, 'get']).as('admin.peluang_layers.get')
        router.get('/lists/:id', [AdminPeluangLayersController, 'getById']).as('admin.peluang_layers.get_by_id')
        router.post('/create', [AdminPeluangLayersController, 'createOrUpdate']).as('admin.peluang_layers.create')
        router.delete('/delete/:id', [AdminPeluangLayersController, 'deleteById']).as('admin.peluang_layers.delete')
        router.patch('/update/:id', [AdminPeluangLayersController, 'createOrUpdate']).as('admin.peluang_layers.update')
}).prefix('admin/peluang_layers').use(middleware.auth())

// tb_peluang_daerah_kelayakan
router.group(() => {
        router.get('/lists', [AdminPeluangDaerahKelayakanController, 'get']).as('admin.peluang_daerah_kelayakan.get')
        router.get('/lists/:id', [AdminPeluangDaerahKelayakanController, 'getById']).as('admin.peluang_daerah_kelayakan.get_by_id')
        router.post('/create', [AdminPeluangDaerahKelayakanController, 'createOrUpdate']).as('admin.peluang_daerah_kelayakan.create')
        router.delete('/delete/:id', [AdminPeluangDaerahKelayakanController, 'deleteById']).as('admin.peluang_daerah_kelayakan.delete')
        router.patch('/update/:id', [AdminPeluangDaerahKelayakanController, 'createOrUpdate']).as('admin.peluang_daerah_kelayakan.update')
}).prefix('admin/peluang_daerah_kelayakan').use(middleware.auth())

// tb_sektor_daerah_insentif
router.group(() => {
        router.get('/lists', [AdminSektorDaerahInsentifController, 'get']).as('admin.sektor_daerah_insentif.get')
        router.get('/by_sub_sektor_daerah', [AdminSektorDaerahInsentifController, 'getBySubSektorDaerah']).as('admin.sektor_daerah_insentif.get_by_sub_sektor_daerah')
        router.get('/lists/:id', [AdminSektorDaerahInsentifController, 'getById']).as('admin.sektor_daerah_insentif.get_by_id')
        router.post('/create', [AdminSektorDaerahInsentifController, 'createOrUpdate']).as('admin.sektor_daerah_insentif.create')
        router.delete('/delete/:id', [AdminSektorDaerahInsentifController, 'deleteById']).as('admin.sektor_daerah_insentif.delete')
        router.patch('/update/:id', [AdminSektorDaerahInsentifController, 'createOrUpdate']).as('admin.sektor_daerah_insentif.update')
}).prefix('admin/sektor_daerah_insentif').use(middleware.auth())

// tb_sektor_nasional_insentif
router.group(() => {
        router.get('/lists', [AdminSektorNasionalInsentifController, 'get']).as('admin.sektor_nasional_insentif.get')
        router.get('/by_sub_sektor_daerah', [AdminSektorNasionalInsentifController, 'getBySubSektorDaerah']).as('admin.sektor_nasional_insentif.get_by_sub_sektor_daerah')
        router.get('/lists/:id', [AdminSektorNasionalInsentifController, 'getById']).as('admin.sektor_nasional_insentif.get_by_id')
        router.post('/create', [AdminSektorNasionalInsentifController, 'createOrUpdate']).as('admin.sektor_nasional_insentif.create')
        router.delete('/delete/:id', [AdminSektorNasionalInsentifController, 'deleteById']).as('admin.sektor_nasional_insentif.delete')
        router.patch('/update/:id', [AdminSektorNasionalInsentifController, 'createOrUpdate']).as('admin.sektor_nasional_insentif.update')
}).prefix('admin/sektor_nasional_insentif').use(middleware.auth())

// tb_rumah_sakit_jenis
router.group(() => {
        router.get('/lists', [AdminRumahSakitJenisController, 'get']).as('admin.rumah_sakit_jenis.get')
        router.get('/lists/:id', [AdminRumahSakitJenisController, 'getById']).as('admin.rumah_sakit_jenis.get_by_id')
        router.post('/create', [AdminRumahSakitJenisController, 'createOrUpdate']).as('admin.rumah_sakit_jenis.create')
        router.delete('/delete/:id', [AdminRumahSakitJenisController, 'deleteById']).as('admin.rumah_sakit_jenis.delete')
        router.patch('/update/:id', [AdminRumahSakitJenisController, 'createOrUpdate']).as('admin.rumah_sakit_jenis.update')
}).prefix('admin/rumah_sakit_jenis').use(middleware.auth())

// tb_rumah_sakit_kategori
router.group(() => {
        router.get('/lists', [AdminRumahSakitKategoriController, 'get']).as('admin.rumah_sakit_kategori.get')
        router.get('/lists/:id', [AdminRumahSakitKategoriController, 'getById']).as('admin.rumah_sakit_kategori.get_by_id')
        router.post('/create', [AdminRumahSakitKategoriController, 'createOrUpdate']).as('admin.rumah_sakit_kategori.create')
        router.delete('/delete/:id', [AdminRumahSakitKategoriController, 'deleteById']).as('admin.rumah_sakit_kategori.delete')
        router.patch('/update/:id', [AdminRumahSakitKategoriController, 'createOrUpdate']).as('admin.rumah_sakit_kategori.update')
}).prefix('admin/rumah_sakit_kategori').use(middleware.auth())

// tb_bandara_kategori
router.group(() => {
        router.get('/lists', [AdminBandaraKategoriController, 'get']).as('admin.bandara_kategori.get')
        router.get('/lists/:id', [AdminBandaraKategoriController, 'getById']).as('admin.bandara_kategori.get_by_id')
        router.post('/create', [AdminBandaraKategoriController, 'createOrUpdate']).as('admin.bandara_kategori.create')
        router.delete('/delete/:id', [AdminBandaraKategoriController, 'deleteById']).as('admin.bandara_kategori.delete')
        router.patch('/update/:id', [AdminBandaraKategoriController, 'createOrUpdate']).as('admin.bandara_kategori.update')
}).prefix('admin/bandara_kategori').use(middleware.auth())

// tb_bandara_kelas
router.group(() => {
        router.get('/lists', [AdminBandaraKelasController, 'get']).as('admin.bandara_kelas.get')
        router.get('/lists/:id', [AdminBandaraKelasController, 'getById']).as('admin.bandara_kelas.get_by_id')
        router.post('/create', [AdminBandaraKelasController, 'createOrUpdate']).as('admin.bandara_kelas.create')
        router.delete('/delete/:id', [AdminBandaraKelasController, 'deleteById']).as('admin.bandara_kelas.delete')
        router.patch('/update/:id', [AdminBandaraKelasController, 'createOrUpdate']).as('admin.bandara_kelas.update')
}).prefix('admin/bandara_kelas').use(middleware.auth())

// tb_komoditi_satuan
router.group(() => {
        router.get('/lists', [AdminKomoditiSatuanController, 'get']).as('admin.komoditi_satuan.get')
        router.get('/lists/:id', [AdminKomoditiSatuanController, 'getById']).as('admin.komoditi_satuan.get_by_id')
        router.post('/create', [AdminKomoditiSatuanController, 'createOrUpdate']).as('admin.komoditi_satuan.create')
        router.delete('/delete/:id', [AdminKomoditiSatuanController, 'deleteById']).as('admin.komoditi_satuan.delete')
        router.patch('/update/:id', [AdminKomoditiSatuanController, 'createOrUpdate']).as('admin.komoditi_satuan.update')
}).prefix('admin/komoditi_satuan').use(middleware.auth())

// tb_pelabuhan_kelas
router.group(() => {
        router.get('/lists', [AdminPelabuhanKelasController, 'get']).as('admin.pelabuhan_kelas.get')
        router.get('/lists/:id', [AdminPelabuhanKelasController, 'getById']).as('admin.pelabuhan_kelas.get_by_id')
        router.post('/create', [AdminPelabuhanKelasController, 'createOrUpdate']).as('admin.pelabuhan_kelas.create')
        router.delete('/delete/:id', [AdminPelabuhanKelasController, 'deleteById']).as('admin.pelabuhan_kelas.delete')
        router.patch('/update/:id', [AdminPelabuhanKelasController, 'createOrUpdate']).as('admin.pelabuhan_kelas.update')
}).prefix('admin/pelabuhan_kelas').use(middleware.auth())

// tb_hotel_kelas
router.group(() => {
        router.get('/lists', [AdminHotelKelasController, 'get']).as('admin.hotel_kelas.get')
        router.get('/lists/:id', [AdminHotelKelasController, 'getById']).as('admin.hotel_kelas.get_by_id')
        router.post('/create', [AdminHotelKelasController, 'createOrUpdate']).as('admin.hotel_kelas.create')
        router.delete('/delete/:id', [AdminHotelKelasController, 'deleteById']).as('admin.hotel_kelas.delete')
        router.patch('/update/:id', [AdminHotelKelasController, 'createOrUpdate']).as('admin.hotel_kelas.update')
}).prefix('admin/hotel_kelas').use(middleware.auth())

// tb_pelabuhan_fungsi
router.group(() => {
        router.get('/lists', [AdminPelabuhanFungsiController, 'get']).as('admin.pelabuhan_fungsi.get')
        router.get('/lists/:id', [AdminPelabuhanFungsiController, 'getById']).as('admin.pelabuhan_fungsi.get_by_id')
        router.post('/create', [AdminPelabuhanFungsiController, 'createOrUpdate']).as('admin.pelabuhan_fungsi.create')
        router.delete('/delete/:id', [AdminPelabuhanFungsiController, 'deleteById']).as('admin.pelabuhan_fungsi.delete')
        router.patch('/update/:id', [AdminPelabuhanFungsiController, 'createOrUpdate']).as('admin.pelabuhan_fungsi.update')
}).prefix('admin/pelabuhan_fungsi').use(middleware.auth())

// tb_pendidikan_kategori
router.group(() => {
        router.get('/lists', [AdminPendidikanKategoriController, 'get']).as('admin.pendidikan_kategori.get')
        router.get('/lists/:id', [AdminPendidikanKategoriController, 'getById']).as('admin.pendidikan_kategori.get_by_id')
        router.post('/create', [AdminPendidikanKategoriController, 'createOrUpdate']).as('admin.pendidikan_kategori.create')
        router.delete('/delete/:id', [AdminPendidikanKategoriController, 'deleteById']).as('admin.pendidikan_kategori.delete')
        router.patch('/update/:id', [AdminPendidikanKategoriController, 'createOrUpdate']).as('admin.pendidikan_kategori.update')
}).prefix('admin/pendidikan_kategori').use(middleware.auth())

// tb_pendidikan_jenjang
router.group(() => {
        router.get('/lists', [AdminPendidikanJenjangController, 'get']).as('admin.pendidikan_jenjang.get')
        router.get('/lists/:id', [AdminPendidikanJenjangController, 'getById']).as('admin.pendidikan_jenjang.get_by_id')
        router.post('/create', [AdminPendidikanJenjangController, 'createOrUpdate']).as('admin.pendidikan_jenjang.create')
        router.delete('/delete/:id', [AdminPendidikanJenjangController, 'deleteById']).as('admin.pendidikan_jenjang.delete')
        router.patch('/update/:id', [AdminPendidikanJenjangController, 'createOrUpdate']).as('admin.pendidikan_jenjang.update')
}).prefix('admin/pendidikan_jenjang').use(middleware.auth())

// tb_sektor_daerah_file
router.group(() => {
        router.get('/lists', [AdminSektorDaerahFileController, 'get']).as('admin.sektor_daerah_file.get')
        router.get('/lists/:id', [AdminSektorDaerahFileController, 'getById']).as('admin.sektor_daerah_file.get_by_id')
        router.post('/create', [AdminSektorDaerahFileController, 'createOrUpdate']).as('admin.sektor_daerah_file.create')
        router.delete('/delete/:id', [AdminSektorDaerahFileController, 'deleteById']).as('admin.sektor_daerah_file.delete')
        router.patch('/update/:id', [AdminSektorDaerahFileController, 'createOrUpdate']).as('admin.sektor_daerah_file.update')
}).prefix('admin/sektor_daerah_file').use(middleware.auth())

// tb_sub_sektor_daerah_file
router.group(() => {
        router.get('/lists', [AdminSubSektorDaerahFileController, 'get']).as('admin.sub_sektor_daerah_file.get')
        router.get('/lists/:id', [AdminSubSektorDaerahFileController, 'getById']).as('admin.sub_sektor_daerah_file.get_by_id')
        router.post('/create', [AdminSubSektorDaerahFileController, 'createOrUpdate']).as('admin.sub_sektor_daerah_file.create')
        router.delete('/delete/:id', [AdminSubSektorDaerahFileController, 'deleteById']).as('admin.sub_sektor_daerah_file.delete')
        router.patch('/update/:id', [AdminSubSektorDaerahFileController, 'createOrUpdate']).as('admin.sub_sektor_daerah_file.update')
}).prefix('admin/sub_sektor_daerah_file').use(middleware.auth())

// tb_komoditi_daerah_file
router.group(() => {
        router.get('/lists', [AdminKomoditiDaerahFileController, 'get']).as('admin.komoditi_daerah_file.get')
        router.get('/lists/:id', [AdminKomoditiDaerahFileController, 'getById']).as('admin.komoditi_daerah_file.get_by_id')
        router.post('/create', [AdminKomoditiDaerahFileController, 'createOrUpdate']).as('admin.komoditi_daerah_file.create')
        router.delete('/delete/:id', [AdminKomoditiDaerahFileController, 'deleteById']).as('admin.komoditi_daerah_file.delete')
        router.patch('/update/:id', [AdminKomoditiDaerahFileController, 'createOrUpdate']).as('admin.komoditi_daerah_file.update')
}).prefix('admin/komoditi_daerah_file').use(middleware.auth())

// tb_sektor_daerah_insentif_file
router.group(() => {
        router.get('/lists', [AdminSektorDaerahInsentifFileController, 'get']).as('admin.sektor_daerah_insentif_file.get')
        router.get('/lists/:id', [AdminSektorDaerahInsentifFileController, 'getById']).as('admin.sektor_daerah_insentif_file.get_by_id')
        router.post('/create', [AdminSektorDaerahInsentifFileController, 'createOrUpdate']).as('admin.sektor_daerah_insentif_file.create')
        router.delete('/delete/:id', [AdminSektorDaerahInsentifFileController, 'deleteById']).as('admin.sektor_daerah_insentif_file.delete')
        router.patch('/update/:id', [AdminSektorDaerahInsentifFileController, 'createOrUpdate']).as('admin.sektor_daerah_insentif_file.update')
}).prefix('admin/sektor_daerah_insentif_file').use(middleware.auth())

// tb_kawasan_industri_file
router.group(() => {
        router.get('/lists', [AdminKawasanIndustriFileController, 'get']).as('admin.kawasan_industri_file.get')
        router.get('/lists/:id', [AdminKawasanIndustriFileController, 'getById']).as('admin.kawasan_industri_file.get_by_id')
        router.post('/create', [AdminKawasanIndustriFileController, 'createOrUpdate']).as('admin.kawasan_industri_file.create')
        router.delete('/delete/:id', [AdminKawasanIndustriFileController, 'deleteById']).as('admin.kawasan_industri_file.delete')
        router.patch('/update/:id', [AdminKawasanIndustriFileController, 'createOrUpdate']).as('admin.kawasan_industri_file.update')
}).prefix('admin/kawasan_industri_file').use(middleware.auth())

// tb_kawasan_industri_peluang_file
router.group(() => {
        router.get('/lists', [AdminKawasanIndustriPeluangFileController, 'get']).as('admin.kawasan_industri_peluang_file.get')
        router.get('/lists/:id', [AdminKawasanIndustriPeluangFileController, 'getById']).as('admin.kawasan_industri_peluang_file.get_by_id')
        router.post('/create', [AdminKawasanIndustriPeluangFileController, 'createOrUpdate']).as('admin.kawasan_industri_peluang_file.create')
        router.delete('/delete/:id', [AdminKawasanIndustriPeluangFileController, 'deleteById']).as('admin.kawasan_industri_peluang_file.delete')
        router.patch('/update/:id', [AdminKawasanIndustriPeluangFileController, 'createOrUpdate']).as('admin.kawasan_industri_peluang_file.update')
}).prefix('admin/kawasan_industri_peluang_file').use(middleware.auth())

// tb_peluang_daerah_file
router.group(() => {
        router.get('/lists', [AdminPeluangDaerahFileController, 'get']).as('admin.peluang_daerah_file.get')
        router.get('/lists/:id', [AdminPeluangDaerahFileController, 'getById']).as('admin.peluang_daerah_file.get_by_id')
        router.post('/create', [AdminPeluangDaerahFileController, 'createOrUpdate']).as('admin.peluang_daerah_file.create')
        router.delete('/delete/:id', [AdminPeluangDaerahFileController, 'deleteById']).as('admin.peluang_daerah_file.delete')
        router.patch('/update/:id', [AdminPeluangDaerahFileController, 'createOrUpdate']).as('admin.peluang_daerah_file.update')
}).prefix('admin/peluang_daerah_file').use(middleware.auth())

// tb_sektor_nasional_value
router.group(() => {
        router.get('/lists', [AdminSektorNasionalValueController, 'get']).as('admin.sektor_nasional_value.get')
        router.get('/lists/:id', [AdminSektorNasionalValueController, 'getById']).as('admin.sektor_nasional_value.get_by_id')
        router.post('/create', [AdminSektorNasionalValueController, 'createOrUpdate']).as('admin.sektor_nasional_value.create')
        router.delete('/delete/:id', [AdminSektorNasionalValueController, 'deleteById']).as('admin.sektor_nasional_value.delete')
        router.patch('/update/:id', [AdminSektorNasionalValueController, 'createOrUpdate']).as('admin.sektor_nasional_value.update')
}).prefix('admin/sektor_nasional_value').use(middleware.auth())

// tb_sub_sektor_nasional_value
router.group(() => {
        router.get('/lists', [AdminSubSektorNasionalValueController, 'get']).as('admin.sub_sektor_nasional_value.get')
        router.get('/lists/:id', [AdminSubSektorNasionalValueController, 'getById']).as('admin.sub_sektor_nasional_value.get_by_id')
        router.post('/create', [AdminSubSektorNasionalValueController, 'createOrUpdate']).as('admin.sub_sektor_nasional_value.create')
        router.delete('/delete/:id', [AdminSubSektorNasionalValueController, 'deleteById']).as('admin.sub_sektor_nasional_value.delete')
        router.patch('/update/:id', [AdminSubSektorNasionalValueController, 'createOrUpdate']).as('admin.sub_sektor_nasional_value.update')
}).prefix('admin/sub_sektor_nasional_value').use(middleware.auth())
