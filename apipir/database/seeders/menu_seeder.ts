import { BaseSeeder } from '@adonisjs/lucid/seeders'
import fs from 'fs'
import path from 'path'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient();

export default class restore_menu_seeder extends BaseSeeder {
    public async run() {
        const data = JSON.parse(fs.readFileSync(path.join('database', 'backup', 'menu.json'), 'utf-8'));
        await prisma.menu.createMany({ data, skipDuplicates: true });
    }
}