import { BaseSeeder } from '@adonisjs/lucid/seeders'
import fs from 'fs'
import path from 'path'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient();

export default class restore_users_seeder extends BaseSeeder {
    public async run() {
        const data = JSON.parse(fs.readFileSync(path.join('database', 'backup', 'users.json'), 'utf-8'));
        await prisma.users.createMany({ data, skipDuplicates: true });
    }
}