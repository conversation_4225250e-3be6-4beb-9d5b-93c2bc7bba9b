version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3333:3333"
    environment:
      TZ: Asia/Jakarta
      PORT: 3333
      HOST: 0.0.0.0
      LOG_LEVEL: info
      APP_KEY: MO0cngSrrXjjUhx9o5KE939XAhIIBlkg
      APP_URL: http://0.0.0.0:3333
      NODE_ENV: development
      DB_HOST: **************
      DB_PORT: 38732
      DB_USER: wgi
      DB_PASSWORD: pgBkpm
      DB_DATABASE: dbbkpm
      SMTP_HOST: smtp.gmail.com
      SMTP_PORT: 587
      SMTP_USERNAME: <EMAIL>
      SMTP_PASSWORD: jyof zhyj upnq oivb
      IMG_CONFIG: '{"size":"1mb","extnames":["jpg","png","jpeg"]}'
      DOC_CONFIG: '{"size":"5mb","extnames":["doc","pdf"]}'
      VIDIO_CONFIG: '{"size":"150mb","extnames":["mp4","mkv","mpeg"]}'
      DATABASE_URL: "********************************************/dbbkpm?schema=public&connection_limit=20"
      URL_LINK_EMAIL: "https://localhost:3333/reset_password"
    volumes:
      - .:/app
    command: ["node", "build/bin/server.js"]