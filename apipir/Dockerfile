# Base image
FROM node:23.5-alpine

# Install required dependencies including OpenSSL
RUN apk add --no-cache \
    build-base \
    gcc \
    autoconf \
    automake \
    libtool \
    nasm \
    vips-dev \
    python3 \
    openssl \
    openssl-dev

# Create app directory
WORKDIR /app

# Install dependencies first (for better caching)
COPY package*.json ./

# Remove existing sharp installation if any
RUN npm uninstall sharp

# Clean npm cache
RUN npm cache clean --force

# Install dependencies without sharp
RUN npm install --unsafe-perm --force

# Install sharp separately with specific flags
RUN npm install --ignore-scripts=false --foreground-scripts --platform=linuxmusl --arch=x64 sharp@0.32.6

# Copy prisma schema
COPY prisma ./prisma/

# Generate prisma client
RUN npx prisma generate

# Copy project files
COPY . .

# Build project
RUN node ace build --ignore-ts-errors

RUN cp .env build/

RUN ls -la build

# Expose port
EXPOSE 3333

# Start command
CMD ["node", "build/bin/server.js"]