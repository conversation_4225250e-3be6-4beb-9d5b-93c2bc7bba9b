{"name": "api", "version": "0.0.0", "private": true, "type": "module", "license": "UNLICENSED", "scripts": {"start": "node bin/server.js", "build": "node ace build", "dev": "node ace serve --hmr", "test": "node ace test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit", "prisma:generate": "node prisma/compilePrisma.js", "prisma:split": "node prisma/splitPrisma.js"}, "imports": {"#controllers/*": "./app/controllers/*.js", "#exceptions/*": "./app/exceptions/*.js", "#models/*": "./app/models/*.js", "#mails/*": "./app/mails/*.js", "#services/*": "./app/services/*.js", "#listeners/*": "./app/listeners/*.js", "#events/*": "./app/events/*.js", "#middleware/*": "./app/middleware/*.js", "#validators/*": "./app/validators/*.js", "#providers/*": "./providers/*.js", "#policies/*": "./app/policies/*.js", "#abilities/*": "./app/abilities/*.js", "#database/*": "./database/*.js", "#start/*": "./start/*.js", "#tests/*": "./tests/*.js", "#config/*": "./config/*.js"}, "devDependencies": {"@adonisjs/assembler": "^7.8.2", "@adonisjs/eslint-config": "^1.3.0", "@adonisjs/prettier-config": "^1.3.0", "@adonisjs/tsconfig": "^1.3.0", "@japa/api-client": "^2.0.3", "@japa/assert": "^3.0.0", "@japa/plugin-adonisjs": "^3.0.1", "@japa/runner": "^3.1.4", "@swc/core": "^1.6.5", "@types/archiver": "^6.0.3", "@types/luxon": "^3.4.2", "@types/node": "^20.14.9", "eslint": "^8.57.0", "hot-hook": "^0.2.6", "pino-pretty": "^11.2.1", "prettier": "^3.3.2", "prisma": "^5.22.0", "ts-node": "^10.9.2", "typescript": "~5.4"}, "dependencies": {"@adonisjs/auth": "^9.2.3", "@adonisjs/core": "^6.12.1", "@adonisjs/cors": "^2.2.1", "@adonisjs/lucid": "^21.1.0", "@adonisjs/mail": "^9.2.2", "@adonisjs/session": "^7.5.1", "@adonisjs/shield": "^8.1.2", "@google-cloud/translate": "^8.5.0", "@maximemrf/adonisjs-jwt": "^0.1.0", "@prisma/client": "^5.22.0", "@vinejs/vine": "^2.1.0", "adonis-autoswagger": "^3.59.0", "archiver": "^7.0.1", "csv-writer": "^1.6.0", "luxon": "^3.4.4", "nodemailer": "^6.9.14", "pg": "^8.12.0", "reflect-metadata": "^0.2.2", "request-ip": "^3.3.0", "sharp": "^0.33.5", "striptags": "^3.2.0"}, "hotHook": {"boundaries": ["./app/controllers/**/*.ts", "./app/middleware/*.ts"]}, "eslintConfig": {"extends": "@adonisjs/eslint-config/app"}, "prettier": "@adonisjs/prettier-config"}