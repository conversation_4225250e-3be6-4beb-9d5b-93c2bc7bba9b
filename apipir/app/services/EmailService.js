
import nodemailer from 'nodemailer';

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransport({
      host: 'smtp.gmail.com',
      port: 587,
      secure: false, // Gunakan true jika port 465
      auth: {
        user: '<EMAIL>',
        pass: 'jyof zhyj upnq oivb',
      },
    });
  }

  async sendMail({ to, subject, text }) {
    try {
      const info = await this.transporter.sendMail({
        from: '"Your Name" <<EMAIL>>',
        to,
        subject,
        text,
      });
      return info;
    } catch (error) {
      console.error('Error sending email:', error);
      throw error;
    }
  }
}


export default new EmailService();
