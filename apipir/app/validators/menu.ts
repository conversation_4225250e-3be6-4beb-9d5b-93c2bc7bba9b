import vine from '@vinejs/vine'
const url = vine.string().maxLength(100).minLength(0).optional()
const menu_name = vine.string().minLength(3).maxLength(100)
const icon = vine.string().minLength(3).maxLength(100).optional()
const order = vine.number()
const parent_id = vine.number().nullable().optional()


export const createMenuValidator = vine.compile(
    vine.object({
        url,
        menu_name,
        order,
        icon,
        parent_id :  vine.number().exists(async (db,value,ctx ) => {
            const match = await db.from('menu').select('id').where('id',value).first()
            return match
        }).optional()
    })
)

export const updateMenuValidator = vine.compile(
    vine.object({
        url : url.optional(),
        menu_name : menu_name.optional(),
        order :order.optional(),
        parent_id : vine.number().exists(async (db,value,ctx ) => {
            const match = await db.from('menu').select('id').where('id',value).first()
            return match

        }).optional(),
        icon
    })
)

