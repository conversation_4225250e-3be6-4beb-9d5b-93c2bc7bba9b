import vine from '@vinejs/vine'
import { normalStr } from '../helpers/global_helper.js'

const nama = vine.string().minLength(1).maxLength(100).optional()
const nama_tr = vine.string().minLength(1).maxLength(100).optional()
const keterangan = vine.string().minLength(1).maxLength(10000).optional()
const keterangan_tr = vine.string().minLength(1).maxLength(10000).optional()
const status = vine.string().minLength(1).maxLength(100).optional()
const nama_singkat = vine.string().minLength(1).maxLength(100).optional()
const nama_singkat_tr = vine.string().minLength(1).maxLength(100).optional()
const deskripsi_singkat = vine.string().minLength(1).maxLength(100).optional()
const deskripsi_singkat_tr = vine.string().minLength(1).maxLength(100).optional()
const deskripsi = vine.string().minLength(1).optional()
const deskripsi_tr = vine.string().minLength(1).optional()
const lokasi_kawasan = vine.string().minLength(1).maxLength(500).optional()
const lokasi_kawasan_tr = vine.string().minLength(1).maxLength(500).optional()
const project_status_enum = vine.string().minLength(1).maxLength(100).optional()
const kode_kbli = vine.string().minLength(1).maxLength(100).optional()
const kd_bahasa = vine.string().minLength(1).maxLength(3)
const id_adm_kabkot = vine.number()
const id_sektor = vine.number()
const tahun = vine.number()
const id_sumber_data = vine.number().optional()
const id_prioritas = vine.number()
const id_adm_kabkot_kantor = vine.number()
const id_kontak = vine.number().optional()
const zoom_peta_default = vine.number()
const nilai_investasi = vine.number().optional()
const nilai_irr = vine.number().optional()
const nilai_npv = vine.number().optional()
const nilai_pp = vine.number().optional()
const vidio = vine.string().optional()
const lon = vine.number().optional()
const lat = vine.number().optional()
const peluang_kontak =  vine.any().optional()
const peluang_insentif =  vine.any().optional()
const list_hapus_file =  vine.any().optional()
const list_hapus_file_tr =  vine.any().optional()
const is_ipro =  vine.boolean().optional()


export const createPeluangValidator = vine.compile(
    vine.object({
        nama,
        keterangan,
        status,
        nama_singkat,
        deskripsi_singkat,
        deskripsi,
        lokasi_kawasan,
        project_status_enum,
        kode_kbli,
        id_adm_kabkot,
        id_sektor,
        tahun,
        id_sumber_data,
        id_prioritas,
        id_adm_kabkot_kantor,
        id_kontak,
        zoom_peta_default,
        nilai_investasi,
        nilai_irr,
        nilai_npv,
        nilai_pp,
        lon,
        lat,
        vidio,
        kd_bahasa,
        nama_tr,
        keterangan_tr,
        nama_singkat_tr,
        deskripsi_singkat_tr,
        deskripsi_tr,
        lokasi_kawasan_tr,
        peluang_kontak,
        peluang_insentif,
        list_hapus_file,
        list_hapus_file_tr,
        is_ipro
    })
)
export const statusProyekValidator = vine.compile(
    vine.object({
        project_status_enum,
        email:vine.array(vine.string().email()).optional(),
        keterangan : vine.string().minLength(1).maxLength(1000).optional(),
        subjek : vine.number()
    })
)
