import vine from '@vinejs/vine'
const judul = vine.string().maxLength(1000).minLength(0).optional()
const deskripsi_singkat = vine.string().maxLength(10000).minLength(0).optional()
const deskripsi = vine.string().maxLength(10000).minLength(0).optional()
const judul_tr = vine.string().maxLength(1000).minLength(0).optional()
const deskripsi_singkat_tr = vine.string().maxLength(10000).minLength(0).optional()
const deskripsi_tr = vine.string().maxLength(10000).minLength(0).optional()
const id_peluang= vine.number().optional()


export const createNewsValidator = vine.compile(
    vine.object({
        judul,
        deskripsi,
        deskripsi_singkat,
        judul_tr,
        deskripsi_tr,
        deskripsi_singkat_tr,
        id_peluang,
    })
)
