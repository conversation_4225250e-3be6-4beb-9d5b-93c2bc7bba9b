import vine from '@vinejs/vine'
import { log } from 'console'

export const getPeluangValidator = vine.compile(
    vine.object({
        page  : vine.number().optional(),
        page_size  : vine.number().optional(),
        id_sektor  : vine.number().optional(),
        id_adm_provinsi : vine.number().optional(),
        id_adm_kabkot : vine.number().optional(),
        id_zona_waktu : vine.number().optional(),
        tahun : vine.number().optional(),
        search : vine.string().minLength(1).maxLength(100).optional(),
        sortir : vine.string().minLength(1).maxLength(100).optional(),
    })
)

export const countViewerValidator = vine.compile(
    vine.object({
        id_halaman_pengunjung  : vine.number().exists(async (db,value,ctx ) => {
            const match = await db.from('tb_halaman_pengunjung').select('id_halaman_pengunjung').where('id_halaman_pengunjung',value).first()
            return match
        }),
        id_konten  : vine.number().optional(),
    })
)
