import vine from '@vinejs/vine'

const id_adm_provinsi = vine.number().optional()
const id_adm_wilayah = vine.number().optional()
const kd_adm = vine.string().minLength(1).maxLength(255)
const nama = vine.string().minLength(1).maxLength(255)
const nama_ibukota = vine.string().minLength(1).maxLength(255)
const file_logo = vine.string().minLength(0).maxLength(255).optional()
const file_image = vine.string().minLength(0).maxLength(255).optional()
const deskripsi = vine.string().minLength(0).maxLength(255).optional()
const luas_wilayah = vine.number().optional()
const jumlah_penduduk = vine.number().optional()
const alamat = vine.string().minLength(0).maxLength(255).optional()
const no_telp = vine.string().minLength(0).maxLength(20).optional()
const no_fax = vine.string().minLength(0).maxLength(20).optional()
const url_web = vine.string().maxLength(255).optional()
const lon = vine.number().optional()
const lat = vine.number().optional()
const shape = vine.string().optional()

export const createProvinsiValidator = vine.compile(
  vine.object({
    id_adm_provinsi,
    id_adm_wilayah,
    kd_adm,
    nama,
    nama_ibukota,
    file_logo,
    file_image,
    deskripsi,
    luas_wilayah,
    jumlah_penduduk,
    alamat,
    no_telp,
    no_fax,
    url_web,
    lon,
    lat,
    shape
  })
)
