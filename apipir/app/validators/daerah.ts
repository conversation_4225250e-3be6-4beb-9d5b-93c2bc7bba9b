import vine from '@vinejs/vine'
import { PrismaClient } from '@prisma/client'
const prisma = new PrismaClient()


export const createKawasanfValidator = vine.compile(
    vine.object({
        nama : vine.string().minLength(3).unique(async (db,value) => {
             const match = await prisma.tb_kawasan_industri.findFirst({
                             where: {
                                 nama: value,
                             }
                         })
            return !match
        }),
    })
)