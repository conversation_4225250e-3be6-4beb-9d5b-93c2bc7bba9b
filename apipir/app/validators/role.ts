import vine from '@vinejs/vine'
import { PrismaClient } from '@prisma/client'
import { normalStr } from '../helpers/global_helper.js'
const prisma = new PrismaClient()

const role_name = vine.string().minLength(3).maxLength(100)


export const createRoleValidator = vine.compile(
    vine.object({
        role_name : vine.string().minLength(3).unique(async (db,value) => {
             const match = await prisma.role.findFirst({
                             where: {
                                 role_name: value,
                             }
                         })
            return !match
        }),
    })
)

export const updateRoleValidator = vine.compile(
    vine.object({
        role_name,
    })
)
