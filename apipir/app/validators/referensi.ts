import vine from '@vinejs/vine'
import { PrismaClient } from '@prisma/client'
const prisma = new PrismaClient()

export const createSNRefValidator = vine.compile(
    vine.object({
        nama : vine.string().minLength(1).maxLength(50) ,
        nama_tr :vine.string().minLength(1).maxLength(50),
        kd_bahasa :vine.string().minLength(1).maxLength(5),
        id_kategori_sektor : vine.number()

    })
)
export const createSSNRefValidator = vine.compile(
    vine.object({
        id_sektor : vine.number() ,
        nama : vine.string().minLength(3).unique(async (db,value) => {
             const match = await prisma.tb_sub_sektor_nasional_ref.findFirst({
                             where: {
                                 nama: value,
                             }
                         })
            return !match
        }),
        nama_tr :vine.string().minLength(1).maxLength(50),
        kd_bahasa :vine.string().minLength(1).maxLength(5),
    })
)
export const updateSNRefValidator = vine.compile(
    vine.object({
        nama : vine.string().minLength(3).maxLength(50),
        id_kategori_sektor : vine.number()
    })
)

export const updateSSNRefValidator = vine.compile(
    vine.object({
        id_sektor : vine.number(),
        nama : vine.string().minLength(1).maxLength(50) ,
    })
)

export const createKNRefValidator = vine.compile(
    vine.object({
        id_sub_sektor : vine.number() ,
        nama : vine.string().minLength(1).maxLength(50) ,
        nama_tr :vine.string().minLength(1).maxLength(50),
        kd_bahasa :vine.string().minLength(1).maxLength(5),
    })
)

export const updateKNRefValidator = vine.compile(
    vine.object({
        id_sub_sektor : vine.number(),
        nama : vine.string().minLength(1).maxLength(50) ,
    })
)


export const createPSRefValidator = vine.compile(
    vine.object({
        nama : vine.string().minLength(1).maxLength(50) ,
        keterangan : vine.string().minLength(1).maxLength(500).optional() ,
        nama_tr :vine.string().minLength(1).maxLength(50),
        keterangan_tr : vine.string().minLength(1).maxLength(500).optional() ,
        kd_bahasa :vine.string().minLength(1).maxLength(5),
        id_kategori_sektor : vine.number()

    })
)
export const updatePSRefValidator = vine.compile(
    vine.object({
        nama : vine.string().minLength(1).maxLength(50) ,
        keterangan : vine.string().minLength(1).maxLength(50) ,
        id_kategori_sektor : vine.number()

    })
)

export const createSDRefValidator = vine.compile(
    vine.object({
        id_sumber_data_instansi : vine.number(),
        tahun_rumus : vine.number(),
        keterangan : vine.string().minLength(1).maxLength(500).optional() ,
        keterangan_tr : vine.string().minLength(1).maxLength(500).optional() ,
        judul :vine.string().minLength(1).maxLength(50),
        judul_tr :vine.string().minLength(1).maxLength(50),
        kd_bahasa :vine.string().minLength(1).maxLength(5),
    })
)

export const updateSDRefValidator = vine.compile(
    vine.object({
        id_sumber_data_judul : vine.number(),
        tahun_rumus : vine.number(),
    })
)

export const createPKRefValidator = vine.compile(
    vine.object({
        nama : vine.string().minLength(1).maxLength(100).optional(),
        nama_tr : vine.string().minLength(1).maxLength(100).optional(),
        alamat : vine.string().minLength(1).maxLength(100).optional(),
        url_web : vine.string().minLength(1).maxLength(100).optional(),
        cp : vine.string().minLength(1).maxLength(100).optional(),
        jabatan_cp : vine.string().minLength(1).maxLength(100).optional(),
        email : vine.string().minLength(1).maxLength(100).optional(),
        no_telp : vine.string().minLength(1).maxLength(100).optional(),
        no_fax : vine.string().minLength(1).maxLength(100).optional()
    })
)

export const createSDIRefValidator = vine.compile(
    vine.object({
        id_adm_provinsi: vine.number().optional(),
        id_adm_kabkot: vine.number().optional(),
        nama : vine.string().minLength(1).maxLength(100).optional(),
        nama_tr : vine.string().minLength(1).maxLength(100).optional(),
        alamat : vine.string().minLength(1).maxLength(100).optional(),
        cp : vine.string().minLength(1).maxLength(100).optional(),
        url_web : vine.string().minLength(1).maxLength(100).optional(),
        email : vine.string().minLength(1).maxLength(100).optional(),
        no_telp : vine.string().minLength(1).maxLength(100).optional(),
        no_fax : vine.string().minLength(1).maxLength(100).optional()
    })
)

export const updateSDIRefValidator = vine.compile(
    vine.object({
        id_adm_provinsi: vine.number().optional(),
        id_adm_kabkot: vine.number().optional(),
        nama : vine.string().minLength(1).maxLength(100).optional(),
        nama_tr : vine.string().minLength(1).maxLength(100).optional(),
        alamat : vine.string().minLength(1).maxLength(100).optional(),
        cp : vine.string().minLength(1).maxLength(100).optional(),
        url_web : vine.string().minLength(1).maxLength(100).optional(),
        email : vine.string().minLength(1).maxLength(100).optional(),
        no_telp : vine.string().minLength(1).maxLength(100).optional(),
        no_fax : vine.string().minLength(1).maxLength(100).optional(),
        tr: vine.object({
            id_sumber_data_instansi:vine.number().optional(),
            nama:vine.string().minLength(1).maxLength(100).optional()
        }).optional()
    })
)
