import vine from '@vinejs/vine'
const url_link = vine.string().maxLength(100).minLength(0).optional()
const judul = vine.string().minLength(1).maxLength(100).optional()
const judul_tr = vine.string().minLength(1).maxLength(100).optional()
const deskripsi = vine.string().minLength(1).maxLength(2000).optional()
const deskripsi_tr = vine.string().minLength(1).maxLength(2000).optional()
const ordering = vine.number().optional()
const ordering_tr = vine.number().optional()
const id_app_slider_tr = vine.number().optional()
const is_roadmap = vine.boolean()


export const createAppSLiderPeluangValidator = vine.compile(
    vine.object({
        judul,
        deskripsi,
        url_link,
        ordering,
        judul_tr,
        deskripsi_tr,
        ordering_tr,
        id_app_slider_tr
    })
)

export const createAppSLiderValidator = vine.compile(
    vine.object({
        judul,
        deskripsi,
        url_link,
        ordering,
        judul_tr,
        deskripsi_tr,
        ordering_tr,
        id_app_slider_tr,
        id_komoditi : vine.string().optional().nullable().transform((value) => {
            if (value === 'null') return null
            if (typeof value === 'string' && /^\d+$/.test(value)) return parseInt(value, 10)
            throw new Error('The id_komoditi field must be a numeric string or null')
          }),
        is_roadmap
    })
)


