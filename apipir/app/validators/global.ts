import vine from '@vinejs/vine'
import { PrismaClient } from '@prisma/client';


const prisma = new PrismaClient();

export function getValidator(tableName : string) {
  switch (tableName) {
    case 'menu':
      return globalMenuValidator;
    case 'role':
      return globalRoleValidator;
    // Tambahkan validator untuk tabel lainnya se<PERSON><PERSON> k<PERSON>
    default:
      throw new Error('Invalid table name for validation');
  }
}



const menu_name = vine.string().minLength(1).maxLength(30).regex(/^[a-zA-Z0-9_]+$/)
const role_name = vine.string().minLength(1).maxLength(30).regex(/^[a-zA-Z0-9_]+$/)
const url = vine.string().minLength(1).maxLength(100)
const order = vine.number()


export const globalTableValidator = vine.compile(
    vine.object({
        table :  vine.string().minLength(1).maxLength(100).regex(/^[a-zA-Z0-9_]+$/).exists(async (db,value,ctx ) => {
           const match = await prisma.$queryRaw`
                    SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = ${value}
                    )
                `;
            return match[0].exists
        })
    })
)

export const globalMenuValidator = vine.compile(
    vine.object({
        menu_name,
        url,
        order,
    })
)

export const globalRoleValidator = vine.compile(
    vine.object({
        role_name,
    })
)