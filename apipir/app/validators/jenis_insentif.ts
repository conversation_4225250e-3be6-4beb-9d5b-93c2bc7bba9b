import vine from '@vinejs/vine'

export const createJenisInsentifValidator = vine.compile(
  vine.object({
    nama: vine.string().minLength(1).maxLength(300),    
    nama_tr: vine.string().minLength(1).maxLength(300),    
    keterangan: vine.string().minLength(1).maxLength(3000),    
    keterangan_tr: vine.string().minLength(1).maxLength(3000), 
    tipe :vine.number(),   
    urutan :vine.number(),   
    id_kategori_insentif :vine.number(),   
  })
);