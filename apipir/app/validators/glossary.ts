import vine from '@vinejs/vine'

const title = vine.string().minLength(1).maxLength(100)
const description = vine.string().minLength(1).maxLength(500)
const title_tr = vine.string().minLength(1).maxLength(100)
const description_tr = vine.string().minLength(1).maxLength(500)
const kode_bahasa = vine.string().minLength(2).maxLength(2).optional()

export const createGlossaryValidator = vine.compile(
    vine.object({
        title,
        description,
        title_tr,
        description_tr,
        kode_bahasa,
    })
)