import vine from '@vinejs/vine'


const id_adm_provinsi = vine.number().optional()
const id_adm_kabkot = vine.number().optional()
const lon = vine.number().optional()
const lat = vine.number().optional()
const nama = vine.string().minLength(3).maxLength(100)
const bidang_usaha = vine.string().minLength(3).maxLength(100)
const alamat = vine.string().minLength(3).maxLength(1000)
const nama_kabkot = vine.string().minLength(3).maxLength(100)
const nama_provinsi = vine.string().minLength(3).maxLength(100)

export const createUmkmValidator = vine.compile(
    vine.object({
        id_adm_provinsi,
        email : vine.string().email().unique(async (db,value) => {
            const match = await db.from('tb_umkm').select('id_umkm').where('email',value).first()
            return !match
        }),
        id_adm_kabkot,
        lon,
        lat,
        nama,
        bidang_usaha,
        alamat,
        nama_kabkot,
        nama_provinsi
    })
)
export const updateUmkmValidator = vine.compile(
    vine.object({
        id_adm_provinsi,
        email : vine.string().email(),
        id_adm_kabkot,
        lon,
        lat,
        nama,
        bidang_usaha,
        alamat,
        nama_kabkot,
        nama_provinsi
    })
)