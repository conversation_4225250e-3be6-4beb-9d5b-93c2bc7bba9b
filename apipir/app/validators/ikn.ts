import vine from '@vinejs/vine'


const nama = vine.string().minLength(1).maxLength(100)
const judul = vine.string().minLength(1).maxLength(100)
const nama_layer = vine.string().minLength(1).maxLength(100)
const deskripsi = vine.string().minLength(1).maxLength(50000)
const nama_tr = vine.string().minLength(1).maxLength(100)
const deskripsi_tr = vine.string().minLength(1).maxLength(50000)
const lon = vine.number()
const lat = vine.number()
const file_video = vine.string().minLength(1).maxLength(1000).optional()

export const updateIknValidator = vine.compile(
    vine.object({
        nama,
        deskripsi,
        nama_tr,
        deskripsi_tr,
        lon,
        lat,
        file_video
    })
)

export const IknLayersValidator = vine.compile(
    vine.object({
        nama_layer,
        judul,
        url_service : vine.string().minLength(1).maxLength(300).regex(/^\S+$/)
    })
)
export const createIknDataValidator = vine.compile(
    vine.object({
        nama,
        nama_tr,
        jenis : vine.number(),
        deskripsi,
        deskripsi_tr,
        is_fe : vine.boolean().optional(),
        fe_posisi : vine.number().optional()
    })
)
export const updateIknDataValidator = vine.compile(
    vine.object({
        nama,
        jenis : vine.number(),
        deskripsi,
        is_fe : vine.boolean().optional(),
        fe_posisi : vine.number().optional()
    })
)
export const IknImageValidator = vine.compile(
    vine.object({
        judul,
    })
)