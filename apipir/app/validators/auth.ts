import vine from '@vinejs/vine'

const password = vine.string().minLength(3).maxLength(50)
const login_name = vine.string().minLength(3).maxLength(50)
const full_name = vine.string().minLength(3).maxLength(100).optional()
const first_name = vine.string().minLength(1).maxLength(100).optional()
const middle_name = vine.string().minLength(0).maxLength(100).optional()
const last_name = vine.string().minLength(0).maxLength(100).optional()
const mobile_number = vine.string().minLength(3).maxLength(15).optional()
const phone_number = vine.string().minLength(3).maxLength(15).optional()
const role_id = vine.number()
const id = vine.number()
const page = vine.number()
const pageSize = vine.number()
const id_adm_provinsi = vine.number().optional()
const id_adm_kabkot = vine.number().optional()
const id_kawasan_industri = vine.number().optional()

export const registerValidator = vine.compile(
    
    vine.object({
        email : vine.string().email().unique(async (db,value) => {
            const match = await db.from('users').select('id').where('email',value).first()
            return !match
        }),
        login_name : vine.string().unique(async (db,value) => {
            const match = await db.from('users').select('id').where('login_name',value).first()
            return !match
        }),
        role_id: vine.number().exists(async (db,value,ctx ) => {
            const match = await db.from('role').select('id').where('id',value).first()
            return match

        }),
        password,
        first_name,
        middle_name,
        last_name,
        phone_number,
        mobile_number,
        id_adm_provinsi,
        id_kawasan_industri,
        id_adm_kabkot
    })
   
) 


export const loginValidator = vine.compile(
    vine.object({
        login_name,        
        password        
    })
)

export const updateUserValidator = vine.compile(
    vine.object({
        email : vine.string().email().normalizeEmail().unique(async (db, value, obj) => {
            const match = await db
                .from('users')
                .select('id')
                .where('email', value)
                .first();

            const currentId = obj.data.params.id ? parseInt(obj.data.params.id) : null;
            // Pastikan match ada sebelum mengakses match.id
            if (match && match.id !== currentId) {
                return false; // Email sudah dipakai oleh pengguna lain
            }

            return true; // Email valid dan unik
        }).optional(),
        login_name : vine.string().unique(async (db,value,obj) => {
            const match = await db.from('users').select('id').where('login_name',value).first()
            const currentId = obj.data.params.id ? parseInt(obj.data.params.id) : null;
            // Pastikan match ada sebelum mengakses match.id
            if (match && match.id !== currentId) {
                return false; // Email sudah dipakai oleh pengguna lain
            }

            return true; // Email valid dan unik
        }).optional(),
        password : password.optional(),       
        first_name,
        middle_name,
        last_name,
        phone_number,
        mobile_number,
        role_id : role_id.optional(),
        user_kabkot : vine.object({
            id_adm_kabkot:vine.number().optional(),
            id_adm_user:vine.number().optional()
        }).optional(),
        user_provinsi : vine.object({
            id_adm_provinsi:vine.number().optional(),
            id_adm_user:vine.number().optional()
        }).optional(),
        user_kawasan : vine.object({
            id_kawasan_industri:vine.number().optional(),
            id_kawasan_user:vine.number().optional()
        }).optional()
    })
)
export const updateUserPasswordValidator = vine.compile(
    vine.object({
        password : password,       
    })
)


export const paginateUserValidator = vine.compile(
    vine.object({
        page : page.optional(),
        pageSize : pageSize.optional()
    })
) 

export const forgotPasswordUserValidator = vine.compile(
    vine.object({
        email : vine.string().email()
    })
) 


export const resetPasswordValidator = vine.compile(
    vine.object({
        password,       
        token : vine.string(),       
    })
)


export const tesValidator = vine.compile(
    vine.object({
        token : vine.string(),       
 
    })
)

