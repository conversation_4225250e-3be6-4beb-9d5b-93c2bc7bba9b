import vine from '@vinejs/vine'

const jenis = vine.string().minLength(3).maxLength(10)
const size = vine.string().minLength(3).maxLength(10)
const extnames = vine.string().minLength(3).maxLength(200)
const id = vine.number()

export const createConfigUploadValidator = vine.compile(
    vine.object({
        jenis,
        size,
        extnames
    })
)

export const uploadConfigUploadValidator = vine.compile(
    vine.object({
        jenis,
        size,
        extnames,
    })
)