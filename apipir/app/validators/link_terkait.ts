import vine from '@vinejs/vine'
const nama = vine.string().minLength(1).maxLength(100)
const alamat = vine.string().minLength(1).maxLength(100)
const no_telpon = vine.string().minLength(1).maxLength(100)
const url_web = vine.string().minLength(1).maxLength(100)
const lon = vine.number().optional()
const lat = vine.number().optional()
const nama_pic = vine.string().minLength(1).maxLength(100)
const status = vine.number().optional()
const is_ikn = vine.number().optional()
const id_kategori = vine.number().optional()
const is_kontak = vine.number().optional()

const url = vine.string().minLength(1).maxLength(100).url()

export const createLinkTerkaitValidator = vine.compile(
    vine.object({
        nama,
        alamat,
        no_telpon,
        url_web,
        nama_pic,
        is_ikn,
        id_kategori,
        is_kontak,
    })
)

export const updateLinkTerkaitValidator = vine.compile(
    vine.object({
        nama:nama.optional(),
        alamat : alamat.optional(),
        no_telpon : no_telpon.optional(),
        url_web : url_web.optional(),
        nama_pic : nama_pic.optional(),
        is_ikn,
        id_kategori,
        is_kontak,
        status
    })
)

export const createVidioBerandaValidator = vine.compile(
    vine.object({
        url
    })
)

export const createRunningTextValidator = vine.compile(
    vine.object({
        keterangan : vine.string().minLength(1).maxLength(500),
        keterangan_tr : vine.string().minLength(1).maxLength(500).optional(),
        date : vine.string().minLength(1).maxLength(50)
    })
)

export const updateRunningTextValidator = vine.compile(
    vine.object({
        keterangan : vine.string().minLength(1).maxLength(500).optional(),
        keterangan_tr : vine.string().minLength(1).maxLength(500).optional(),
        date : vine.string().minLength(1).maxLength(50).optional()
    })
)

