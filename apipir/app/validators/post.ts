import vine from '@vinejs/vine'

const title = vine.string().minLength(3)
const desc = vine.string().minLength(3)
const id = vine.number()
const category_id = vine.number()


export const createPostFaqValidator = vine.compile(
    vine.object({
        nama : vine.string().minLength(1).maxLength(30) ,
        email : vine.string().minLength(1).maxLength(40).email(),
        bahasa : vine.string().minLength(2).maxLength(2).optional(),
        tujuan : vine.number()
    })
)

export const updatePostValidator = vine.compile(
    vine.object({
        title ,
        desc,
        category_id,
        id
    })
)

export const createPostDokumenValidator = vine.compile(
    vine.object({
        nama : vine.string().minLength(1).maxLength(30) ,
        email : vine.string().minLength(1).maxLength(40).email(),
        bahasa : vine.string().minLength(2).maxLength(2).optional(),
        asal_negara : vine.string().minLength(1).maxLength(30).optional(),
        id_jenis_konten : vine.string().minLength(1).maxLength(30).optional(),
        tujuan : vine.number(),
        id_peluang : vine.number()
    })
)
