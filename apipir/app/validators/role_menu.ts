import vine from '@vinejs/vine'
import { PrismaClient } from '@prisma/client'
const prisma = new PrismaClient()

    const role_id =  vine.number().exists(async (db,value,ctx ) => {
            const match = await prisma.role.findFirst({
                where:{
                    id:value
                }
            })
            return match
        })
    const menu_id = vine.number().exists(async (db,value,ctx ) => {
        const match = await prisma.menu.findFirst({
            where:{
                id:value
            }
        })
        return match
    })


export const createRoleMenuValidator = vine.compile(
    vine.object({
        role_id,
        menu_id 
    })
)

