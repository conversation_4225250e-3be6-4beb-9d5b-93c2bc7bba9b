import { Validator, RuleFn } from '@vinejs/vine'

export function exists(table: string, column: string): RuleFn {
  return async (value, _, ctx) => {
    const exists = await db.from(table).where(column, value).first()
    // if (!exists) {
    console.log(value);
    
      ctx.errorReporter.report(
        `The ${column} does not exist in the ${table} table`,
        ctx.field,
        'exists'
      )
    // }
  }
}
