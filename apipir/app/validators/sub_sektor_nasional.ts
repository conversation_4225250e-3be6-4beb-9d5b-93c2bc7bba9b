import vine from '@vinejs/vine'

const id_sektor_nasional = vine.number()
const tahun_pdb = vine.number()
const id_sumber_data = vine.number()
const jumlah_pdb = vine.number()
const status = vine.number().optional()
const nama = vine.string()
const deskripsi = vine.string()
const judul_file = vine.string()

const kd_bahasa = vine.string().optional()
const nama_tr = vine.string().optional()
const deskripsi_tr = vine.string().optional()

const deskripsi_singkat = vine.string()
const deskripsi_singkat_tr = vine.string()

const file_icon = vine.string().optional()
const file_image = vine.string().optional()
const id_sub_sektor = vine.number().optional()

const parameter_data = vine.array(
  vine.object({
    nama: vine.string(),         
    nama_tr: vine.string(),      
    satuan: vine.string(),       
    satuan_tr: vine.string(),    
    detail: vine.array(           
      vine.object({
        tahun: vine.string(),    
        angka: vine.string(),    
      })
    )
  })
).optional();

 
const kontak = vine.array(
  vine.object({
    nama_kontak: vine.string(),         
    nama_pic: vine.string(),      
    alamat: vine.string(),       
    no_telp: vine.string(),    
    no_fax: vine.string(),    
    url_web: vine.string(),    
    long: vine.string(),    
    lat: vine.string(),    
  })
).optional();

 

export const createPdbSNValidator = vine.compile(
    vine.object({
        id_sektor_nasional,
        tahun_pdb,
        id_sumber_data,
        jumlah_pdb,
        status,
    })
)

export const createSSNInsentifValidator = vine.compile(
    vine.object({
        id_sektor_nasional,
        nama,
        deskripsi,
        status,
        kd_bahasa,
        nama_tr,
        deskripsi_tr,
        judul_file,
    })
)
export const updateSSNInsentifValidator = vine.compile(
    vine.object({
        id_sektor_nasional,
        nama,
        deskripsi,
        status,
        kd_bahasa,
        nama_tr,
        deskripsi_tr,
        judul_file,
    })
)

export const createSubSektorNasionalValidator = vine.compile(
    vine.object({
        id_sektor_nasional,
        id_sub_sektor,
        deskripsi,
        deskripsi_tr,
        deskripsi_singkat,
        deskripsi_singkat_tr,
        status,
        file_icon,
        file_image,
        // id_sumber_data,

    })
)
export const updateSubSektorNasionalValidator = vine.compile(
    vine.object({
        id_sektor_nasional,
        id_sub_sektor,
        deskripsi,
        deskripsi_singkat,
        status,
        file_icon,
        file_image,

    })
)