import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../helpers/model_helper.js';
import uploadFile from '../helpers/file_uploader.js';

import prisma from '../lib/prisma.js';


// komoditi_layers_controller
export default class SubSektorNasionalValueController {
  private async schemaData() {
    const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
      id_sub_sektor_nasional_value: {column: 'id_sub_sektor_nasional_value', alias: 'id_sub_sektor_nasional_value', type: 'int'},
      id_sub_sektor_nasional: {column: 'id_sub_sektor_nasional', alias: 'id_sub_sektor_nasional', type: 'int'},
      nama: {column: 'nama', alias: 'nama', type: 'string'},
      tipe: {column: 'tipe', alias: 'tipe', type: 'string'},
      satuan: {column: 'satuan', alias: 'satuan', type: 'string'}
    };
  
    const joins = {};
  
    const where = {};
  
    return {
      columns: columnMappings,
      join: joins,
      where: where,
    };
  }

  public async get({ request, params, response }: HttpContext) {
    const query = request.qs(); // Ini akan mengembalikan objek query string

    console.log(query); // { id_kawasan_industri: '201' }
    let isAll = false;

    interface Options {
      skip: number; // Keep it non-optional
      take: number;
      select?: { [key: string]: boolean };
    }

    if (query['all'] || query['all'] == 'true') {
      isAll = true
      delete query['all'];
    }

    const page = parseInt(request.input('page', 1));
    const perPage = parseInt(request.input('per_page', 10));
    delete query['page'];
    delete query['per_page'];

    let options: Options = {
      skip: (page - 1) * perPage,
      take: perPage,
    };

    if (isAll) {
      delete options['skip'];
      delete options['take'];
    }

    if (query['select']) {
      options['select'] = modelSelect((await this.schemaData()).columns, query['select']);
      delete query['select'];
    }

    options['where'] = modelWhereAnd((await this.schemaData()).columns, query);

    let data = await prisma.tb_sub_sektor_nasional_value.findMany(options);

    if (isAll) {
      return response.status(200).json({
        success: true,
        data: data
      });      
    }

    const totalCount = await prisma.tb_sub_sektor_nasional_value.count();
    
    return response.status(200).json({
      success: true,
      data: data,
      pagination: {
        page: page,
        per_page: perPage,
        total_count: totalCount,
        total_pages: Math.ceil(totalCount / perPage),
      },
    });
  }

  public async getById({ request, params, response }: HttpContext) {
    interface Options {
      select?: { [key: string]: boolean };
      where?: { [key: string]: any; };
    }

    let options: Options = {};

    if (params['select']) {
      options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
      delete params['select'];
    }

    options['where'] = {
      'id_sub_sektor_nasional_value': parseInt(params.id)
    }

    const data = await prisma.tb_sub_sektor_nasional_value.findFirst(options);

    return response.status(200).json({
      success: true,
      data: data
    });      
  }

  public async createOrUpdate({ request, response }: HttpContext) {
    let params = request.body()

    try {
      const result = await prisma.$transaction(async (prisma) => {
        if (params.id_sub_sektor_nasional_value) {
          // Jika `id` ada, lakukan operasi update
          const update = await prisma.tb_sub_sektor_nasional_value.update({
            where: { id_sub_sektor_nasional_value: params.id_sub_sektor_nasional_value },
            data: params,
          })

          return {
            status: 'success',
            message: 'Successfully Updated Data',
            data: update,
          }
        } else {
          console.log("Sync tb_sub_sektor_nasional_value id sequence",
            await prisma.$executeRaw`
            SELECT setval((
                SELECT PG_GET_SERIAL_SEQUENCE('"tb_sub_sektor_nasional_value"', 'id_sub_sektor_nasional_value')),
                (SELECT (MAX("id_sub_sektor_nasional_value") + 1) FROM "tb_sub_sektor_nasional_value"),
                false) FROM "tb_sub_sektor_nasional_value";
            `);

            if (params && typeof params === 'object') {
              const model = "kawasan_potensi_investasi";
              for (const key in params) {
                if (params.hasOwnProperty(key)) {
                  let filename: string = "";
                  const value = params[key];
                  const file = request.file(`[${key}]file`);

                  value['id_sub_sektor_nasional_value'] = parseInt(value['id_sub_sektor_nasional_value']);
                  value['id_kawasan_industri'] = parseInt(value['id_kawasan_industri']);
                  value['tipe'] = parseInt(value['tipe']);

                  if (file) {
                    let uploadFileToServer = await uploadFile(file, model, value['tipe']);
                    let filenameFromServer = '';
                    if (uploadFileToServer) {
                            filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                    }
                    filename = filenameFromServer; // Set fileName if file exists
                    value['nama'] = filename;
                  }

                  if (value['id_sub_sektor_nasional_value'] > 1) {
                    const update = await prisma.tb_sub_sektor_nasional_value.update({
                      where: { id_sub_sektor_nasional_value: value['id_sub_sektor_nasional_value'] },
                      data: value,
                    })
                  } else {
                    delete value['id_sub_sektor_nasional_value'];
                    await prisma.tb_sub_sektor_nasional_value.create({
                      data: value,
                    });
                  }
                  // console.log(`Key: ${key}`);
                  // console.log(`Value:`, value);
                  // console.log(`Judul: ${value.judul}, Keterangan: ${value.keterangan}`);
                }
              }
  
              return {
                status: 'success',
                message: 'Successfully Upload Image',
                data: null,
              }
            } else if (Array.isArray(params)) {
              // for (const item of params) {
              //   console.log(item)
              //   await prisma.tb_sub_sektor_nasional_value.create({
              //     data: item,
              //   });
              // }
  
              return {
                status: 'success',
                message: 'Successfully Added Data',
                data: null,
              }
            } else {
              const save = await prisma.tb_sub_sektor_nasional_value.create({
                data: params,
              })
    
              return {
                status: 'success',
                message: 'Successfully Added Data',
                data: save,
              }
            }
        }
      })

      return response.json(result)

    } catch (error) {
      // Handle error jika ada kesalahan dalam transaksi
      return response.status(500).json({
        status: 'error',
        message: 'An error occurred while processing data',
        error: error.message,
      })
    }
  }

  public async deleteById({ request, params, response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_sub_sektor_nasional_value.delete({
        where: {
          id_sub_sektor_nasional_value: Id,
        },
      })

      return response.status(200).json({
        status: 'success',
        message: 'Success Delete Data',
        data: deletePost,
      })
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }  
  }
}