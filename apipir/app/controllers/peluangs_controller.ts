import { createMenuValidator, updateMenuValidator } from '#validators/menu';
import type { HttpContext } from '@adonisjs/core/http'
import { checkFileOrUrl, configUpload, get_paginate, img_to_webp, send_mail, upload, upload_img_to_webp } from '../helpers/global_helper.js';
import { createPeluangValidator, statusProyekValidator } from '#validators/peluang';
import { aproveValidator } from '#validators/aprove';
import env from '#start/env';
import striptags from 'striptags';
import prisma from '../lib/prisma.js'

export default class PeluangsController {
  /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const datas = await prisma.tb_peluang_kabkot.findMany({
        take: 100,
        include:{
            tb_adm_kabkot : true,
            tb_peluang_sektor:true,
            tb_sumber_data:{
              include:{
                tb_sumber_data_judul : true
              },
            },
        }
    });

    const data = datas.map(({ tb_sumber_data, tb_adm_kabkot, tb_peluang_sektor, ...item }) => {
      let prioritas = '';

      if (item.id_prioritas === 1) {
          prioritas = 'PRIORITAS ';
      } else if (item.id_prioritas === 2) {
          prioritas = 'DAERAH';
      } else if (item.id_prioritas === 3) {
          prioritas = 'DAERAH & IKN';
      }

        return {
            ...item,
            nama_kabkot: tb_adm_kabkot?.nama || '',
            nama_sektor: tb_peluang_sektor?.nama || '',
            judul: tb_sumber_data?.tb_sumber_data_judul?.judul || '',
            prioritas
        };
    });


    return {
        success : true,
        data : {data}
    }
  }

  /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response,request}:HttpContext) {
    const parameters =  request.qs()
    const order = parameters.order
    const by = ['asc','desc'].includes(parameters.by) ? parameters.by : 'asc'
    let orderBy={}
    const paramList = ['tahun','status','judul_peluang']
    if (order != undefined && paramList.includes(order)) {
      if (order == 'judul_peluang') {

        orderBy = {nama:by}
      } else {
        orderBy = {[order]:by}

      }
    }

    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = params.search != undefined ? params.search.replace('%20',' ') : ''; // Default to an empty string if search is not provided


    // Define the search conditions (optional)
    let searchCondition = {};
    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          { nama: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
          { keterangan: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
          { deskripsi: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
          { deskripsi_singkat: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
          {
            tb_peluang_sektor: {
              nama:  { contains: search, mode: 'insensitive' }, // Search in tb_app_slider_tr.deskripsi
            },
          },
          {
            tb_adm_kabkot: {
              nama:  { contains: search, mode: 'insensitive' }, // Search in tb_app_slider_tr.deskripsi
            },
          },
          {
            tb_adm_kabkot: {
              tb_adm_provinsi: {
                nama:  { contains: search, mode: 'insensitive' }, // Search in tb_app_slider_tr.deskripsi
              },
            },
          }
        ],
      };
    }
    if (parameters.kabkot) {
      searchCondition.id_adm_kabkot = parseInt(parameters.kabkot);
    } else if (parameters.id_prioritas) {
      searchCondition.id_prioritas = parseInt(parameters.id_prioritas);
    } else if (parameters.project_status_enum) {
      searchCondition.project_status_enum = parameters.project_status_enum;
    } else if (parameters.id_kategori_sektor) {
      searchCondition.tb_peluang_sektor = {kategori_sektor:{id_kategori_sektor:parseInt(parameters.id_kategori_sektor)}};
    } else if (parameters.status_ppi) {
      searchCondition.is_ipro = parameters.status_ppi == 'ipro' ? true : false;
    } else if (parameters.prov) {
        // Jika `prov` diisi, gunakan rentang berdasarkan `prov`
        const provNumber = parseInt(parameters.prov, 10); // Pastikan `prov` dalam bentuk angka
        searchCondition.id_adm_kabkot = {
            gte: provNumber * 100, // Awal rentang (contoh: 35 menjadi 3500)
            lt: (provNumber + 1) * 100, // Akhir rentang (contoh: 35 menjadi 3600)
        };
    }
    const req = request.qs()
    if (req.status) {
        (searchCondition.AND ??= []).push(
          { status: req.status }
  
        );
    }
    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_peluang_kabkot.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });


    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_peluang_kabkot.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      orderBy:orderBy,
      where: searchCondition, // Apply searchCondition only if it's not empty
          include:{
              tb_peluang_kabkot_file:true,
              tb_peluang_kabkot_insentif:true,
              tb_peluang_kabkot_kontak:true,
              tb_peluang_kabkot_file_tr:true,
              tb_peluang_kabkot_tr:true,
              tb_peluang_sektor:{
                include:{
                kategori_sektor:true
              },
            },
              tb_adm_kabkot:{
                include:{
                  tb_adm_provinsi:
                  {
                    include:{
                      tb_adm_wilayah:true
                    }
                  }
                }
              },
          }
    });

    const project_status = await prisma.tb_peluang_status.findMany()
    const data = datas.map((item) => {
      let prioritas = '';
      if (item.id_prioritas == 1) {
         prioritas = 'Prioritas'
      } else if (item.id_prioritas == 2) {
         prioritas = 'Daerah'
      } else {
         prioritas = 'Prioritas & IKN'
      }

      let status_proyek = '';
      if (item.project_status_enum !== '') {
        status_proyek = project_status.find((items) => items.id_peluang_status == parseInt(item.project_status_enum || '0'))?.nama || ''
      }

      return {
        id_peluang_kabkot :item.id_peluang_kabkot,
        id_adm_kabkot:item.id_adm_kabkot,
        judul_peluang : item.nama,
        lokasi_kawasan : item.lokasi_kawasan,
        deskripsi:item.deskripsi,
        deskripsi_singkat:item.deskripsi_singkat,
        pulau : item.tb_adm_kabkot.tb_adm_provinsi.tb_adm_wilayah.nama,
        provinsi : item.tb_adm_kabkot.tb_adm_provinsi.nama,
        kabkot : item.tb_adm_kabkot.nama,
        nama_Sektor : item.tb_peluang_sektor.nama,
        prioritas : prioritas,
        tahun : item.tahun,
        status : item.status,
        keterangan : striptags(item.keterangan || ''),
        projek_status_enum :item.project_status_enum,
        kategori_sektor:item?.tb_peluang_sektor?.kategori_sektor?.nama,
        status_ppi : item.is_ipro ? 'IPRO' : 'PPI',
        status_proyek:status_proyek
      }

    })


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };
  }


  /**git
   * Display form to create a new record
   */
  async create({}: HttpContext) {

  }

  /**
     * @store
     * @paramQuery id_adm_kabkot - Nama Menu - @type(number) @required
     * @paramQuery id_adm_kabkot - id_adm_kabkot - @type(number) @required
     * @paramQuery id_sektor - id_sektor - @type(number) @required
     * @paramQuery tahun - tahun - @type(number) @required
     * @paramQuery id_sumber_data - id_sumber_data - @type(number)
     * @paramQuery id_prioritas - id_prioritas - @type(number) @required
     * @paramQuery id_adm_kabkot_kantor - id_adm_kabkot_kantor - @type(number) @required
     * @paramQuery nama - nama - @type(string) @required
     * @paramQuery keterangan - keterangan - @type(string)
     * @paramQuery nilai_investasi - nilai_investasi - @type(number)
     * @paramQuery nilai_irr - nilai_irr - @type(number)
     * @paramQuery nilai_npv - nilai_npv - @type(number)
     * @paramQuery nilai_pp - nilai_pp - @type(number)
     * @paramQuery status - status - @type(string)
     * @paramQuery lon - lon - @type(number)
     * @paramQuery lat - lat - @type(number)
     * @paramQuery shape - shape - @type(string)
     * @paramQuery id_kontak - id_kontak - @type(number)
     * @paramQuery nama_singkat - nama_singkat - @type(string)
     * @paramQuery deskripsi_singkat - deskripsi_singkat - @type(string)
     * @paramQuery deskripsi - deskripsi - @type(string)
     * @paramQuery lokasi_kawasan - lokasi_kawasan - @type(string)
     * @paramQuery kode_kbli - kode_kbli - @type(string)
     * @paramQuery zoom_peta_default - zoom_peta_default - @type(number)
     * @paramQuery project_status_enum - project_status_enum - @type(string)
     */
    public async store({ request, response ,auth}: HttpContext) {


      let datas = await request.validateUsing(createPeluangValidator)
      // let peluang_kontak = []
      // let peluang_insentif = []
      let vidio = datas?.vidio
      delete datas.vidio
      let peluang_kontak = request.input('peluang_kontak')
      let peluang_insentif = request.input('peluang_insentif')
      // if (datas.peluang_kontak?.length  > 0) {
      //   peluang_kontak=datas.peluang_kontak
      //   delete datas.peluang_kontak
      // }
      // if (datas.peluang_insentif?.length > 0) {
      //     peluang_insentif=datas.peluang_insentif
      //     delete datas.peluang_insentif
      // }
      if (typeof peluang_kontak === 'string') {
        peluang_kontak = JSON.parse(peluang_kontak);
      }
      if (typeof peluang_insentif === 'string') {
        peluang_insentif = JSON.parse(peluang_insentif);
      }



      try {
        const result = await prisma.$transaction(async (prisma) => {
            const dataPost ={
              nama : datas.nama,
              keterangan : datas.keterangan,
              status : "0",
              nama_singkat : datas.nama_singkat,
              deskripsi_singkat : datas.deskripsi_singkat,
              deskripsi : datas.deskripsi,
              lokasi_kawasan : datas.lokasi_kawasan,
              project_status_enum : datas.project_status_enum,
              kode_kbli : datas.kode_kbli,
              id_adm_kabkot : datas.id_adm_kabkot,
              id_sektor : datas.id_sektor,
              tahun : datas.tahun,
              id_sumber_data : datas.id_sumber_data,
              id_prioritas : datas.id_prioritas,
              id_adm_kabkot_kantor : datas.id_adm_kabkot_kantor,
              id_kontak : datas.id_kontak,
              zoom_peta_default : datas.zoom_peta_default,
              nilai_investasi : datas.nilai_investasi,
              nilai_irr : datas.nilai_irr,
              nilai_npv : datas.nilai_npv,
              nilai_pp : datas.nilai_pp,
              lon : datas.lon,
              lat : datas.lat,
              is_ipro : datas.is_ipro,
            }
            const insert = await prisma.tb_peluang_kabkot.create({data:dataPost})
            const dataPostTr = {
                id_peluang_kabkot: insert.id_peluang_kabkot,
                kd_bahasa : datas.kd_bahasa,
                nama : datas.nama_tr,
                keterangan : datas.keterangan_tr,
                nama_singkat : datas.nama_singkat_tr,
                deskripsi_singkat : datas.deskripsi_singkat_tr,
                deskripsi : datas.deskripsi_tr,
                lokasi_kawasan : datas.lokasi_kawasan_tr,
              }

              const insertTr = await prisma.tb_peluang_kabkot_tr.create({data:dataPostTr})
              console.log(insertTr);

              if (Array.isArray(peluang_kontak) && peluang_kontak.length > 0) {
                peluang_kontak = peluang_kontak.map(Number);
                const kontak=peluang_kontak.map(numb => ({
                  id_peluang_kabkot:insert.id_peluang_kabkot,
                  id_peluang_kontak:numb
                }))
                const insertKontak = await prisma.tb_peluang_kabkot_kontak.createMany(
                  {
                    data:kontak
                  }
                )
            }

            if (Array.isArray(peluang_insentif) && peluang_insentif.length >0) {
                peluang_insentif = peluang_insentif.map(Number);
                const insentif=peluang_insentif.map(numb => ({
                  id_peluang_kabkot:insert.id_peluang_kabkot,
                  id_jenis_insentif:numb
                }))
                // return insentif
                const insertinsentif = await prisma.tb_peluang_kabkot_insentif.createMany(
                  {
                    data:insentif
                  }
                )
            }


              const configImg = await configUpload('img')
              const configDoc = await configUpload('doc')

              const dokumen = request.files('dokumen',configDoc);
              if (dokumen) {
                const uploadPromisess = dokumen.map(async (item) => {

                    const uploadPath = `uploads/peluang/${insert.id_adm_kabkot}/peluang/${insert.tahun}/${insert.id_prioritas}/`;
                    const upDoc = await upload(item, uploadPath);

                    const newFileEntry = await prisma.tb_peluang_kabkot_file.create({
                      data: {
                        id_peluang_kabkot: insert.id_peluang_kabkot,
                        nama: upDoc.data?.filename,
                        tipe: 3
                      }
                    });
                });
                await Promise.all(uploadPromisess);

              }
              const infografis = request.files('infografis',configImg);
              if (infografis) {
                // const uploadPromisess = infografis.map(async (item) => {

                //     const uploadPath = `uploads/peluang/${insert.id_adm_kabkot}/peluang/${insert.tahun}/${insert.id_prioritas}/`;
                //     const upDoc = await upload(item, uploadPath);

                //     const newFileEntry = await prisma.tb_peluang_kabkot_file.create({
                //       data: {
                //         id_peluang_kabkot: insert.id_peluang_kabkot,
                //         nama: upDoc.data?.filename,
                //         tipe: 4
                //       }
                //     });
                // });
                // await Promise.all(uploadPromisess);
                if (infografis) {
                  // Proses file secara berurutan untuk mempertahankan urutan
                  for (const item of infografis) {
                    const uploadPath = `uploads/peluang/${insert.id_adm_kabkot}/peluang/${insert.tahun}/${insert.id_prioritas}/`;
                    const upDoc = await upload(item, uploadPath);
                    
                    await prisma.tb_peluang_kabkot_file.create({
                      data: {
                        id_peluang_kabkot: insert.id_peluang_kabkot,
                        nama: upDoc.data?.filename,
                        tipe: 4
                      }
                    });
                  }
                }
              }
              const infografisTr = request.files('infografis_tr',configImg);
              if (infografisTr) {
                console.log("adaFileTr");
                // const uploadPromisess = infografisTr.map(async (item) => {
                //     const uploadPath = `uploads/peluang/${insert.id_adm_kabkot}/peluang/${insert.tahun}/${insert.id_prioritas}/`;
                //     const upDoc = await upload(item, uploadPath);
                //     const newFileEntry = await prisma.tb_peluang_kabkot_file_tr.create({
                //       data: {
                //         id_peluang_kabkot: insert.id_peluang_kabkot,
                //         nama: upDoc.data?.filename,
                //         kd_bahasa:datas.kd_bahasa,
                //         tipe: 4
                //       }
                //     });
                //     console.log(newFileEntry);

                // });
                // await Promise.all(uploadPromisess);
                if (infografisTr) {
                  // Proses file secara berurutan untuk mempertahankan urutan
                  for (const item of infografisTr) {
                    const uploadPath = `uploads/peluang/${insert.id_adm_kabkot}/peluang/${insert.tahun}/${insert.id_prioritas}/`;
                    const upDoc = await upload(item, uploadPath);
                    
                    await prisma.tb_peluang_kabkot_file_tr.create({
                      data: {
                        id_peluang_kabkot: insert.id_peluang_kabkot,
                        nama: upDoc.data?.filename,
                        kd_bahasa: datas.kd_bahasa,
                        tipe: 4
                      }
                    });
                  }
                }
              }

              let mapService = request.input('map_service')
              if (typeof mapService === 'string') {
                mapService = JSON.parse(mapService);
              }
              if (Array.isArray(mapService) && mapService.length > 0) {
                const insertPromises = mapService.map(async (item) => {
                    await prisma.tb_peluang_layers.create({
                        data: {
                            layeruid: item.layeruid,
                            keterangan: item.keterangan,
                            kategori: 1,
                            id_peluang: insert.id_peluang_kabkot,
                            is_active: true,
                        },
                    });
                });

                // Tunggu semua proses selesai
                await Promise.all(insertPromises);
                console.log("Data berhasil di-insert ke tabel peluang_layers.");
            }


              const images = request.files('images',configImg);
                if (images) {
                    // Create an array of promises
                    const uploadPromisesUpImg = images.map(async (item) => {
                        const uploadPath = `uploads/peluang/${insert.id_adm_kabkot}/peluang/${insert.tahun}/${insert.id_prioritas}/`;

                        // Wait for the image to upload
                        const upImg = await img_to_webp(item, uploadPath);

                        // After upload completes, insert data to the database
                        await prisma.tb_peluang_kabkot_file.create({
                            data: {
                                id_peluang_kabkot: insert.id_peluang_kabkot,
                                nama: upImg.data.filename,
                                tipe: 1,
                            },
                        });
                    });

                    // Wait for all uploads and database inserts to complete
                    await Promise.all(uploadPromisesUpImg);
                }

                if (vidio) {
                  await prisma.tb_peluang_kabkot_file.create({
                      data: {
                        id_peluang_kabkot: insert.id_peluang_kabkot,
                        nama:vidio,
                        tipe: 2,
                      },
                    });
                }
            response.status(201).json({ success: true,data:insert })
      })

    } catch (error) {
      response.status(500).json({ success: false, message: error.message })
      }
    }



  /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        let data = await prisma.tb_peluang_kabkot.findUnique({
          where: {
            id_peluang_kabkot: Id,
          },
          include:{
              tb_peluang_kabkot_file:true,
              tb_peluang_kabkot_file_tr:true,
              tb_peluang_kabkot_insentif:true,
              tb_peluang_kabkot_kontak:true,
              tb_peluang_kabkot_tr:true,
              tb_peluang_layers:true
          }
        })


         let files =
            {
              "images" : [],
              "infografis" : [],
              "document" : [],
              "video" : [],
            }
         let filesTr =
            {
              "images" : [],
              "infografis" : [],
              "document" : [],
              "video" : [],
            }
        if (data?.tb_peluang_kabkot_file) {

          for (const item of data.tb_peluang_kabkot_file) {
            if (item.tipe == 1) {
              let a = {
                ...item,
                path_file: `${env.get('APP_URL')}/uploads/peluang/${data.id_adm_kabkot}/peluang/${data.tahun}/${data.id_prioritas}/${item.nama}`,
              };
              files.images.push(a);
            } else if (item.tipe == 2) {
              const fileOrUrl = await checkFileOrUrl(item.nama);

              let a = {
                ...item,
                path_file:
                  fileOrUrl == "url"
                    ? item.nama
                    : `${env.get('APP_URL')}/uploads/peluang/${data.id_adm_kabkot}/peluang/${data.tahun}/${data.id_prioritas}/${item.nama}`,
              };

              files.video.push(a);
            } else if (item.tipe == 3) {
              let a = {
                ...item,
                path_file: `${env.get('APP_URL')}/uploads/peluang/${data.id_adm_kabkot}/peluang/${data.tahun}/${data.id_prioritas}/${item.nama}`,
              };
              files.document.push(a);
            } else if (item.tipe == 4) {
              let a = {
                ...item,
                path_file: `${env.get('APP_URL')}/uploads/peluang/${data.id_adm_kabkot}/peluang/${data.tahun}/${data.id_prioritas}/${item.nama}`,
              };
              files.infografis.push(a);
            }
          }


          data.tb_peluang_kabkot_file = files
          data.tb_peluang_kabkot_file_tr.map((item) =>{
            if (item.tipe == 1) {
              let a = {...item,
                path_file: `${env.get('APP_URL')}/uploads/peluang/${data.id_adm_kabkot}/peluang/${data.tahun}/${data.id_prioritas}/${item.nama}`,
              }
              filesTr.images.push(a)
            }else if (item.tipe == 2) {
              const filePattern = /^[\w,\s-]+\.[A-Za-z]{2,4}$/;
              if (filePattern.test(item.nama)){
                let a = {
                        ...item,
                        path_file:  item.nama?.startsWith('http://') || item.nama?.startsWith('https://') ? item.nama :`${env.get('APP_URL')}/uploads/peluang/${data.id_adm_kabkot}/peluang/${data.tahun}/${data.id_prioritas}/${item.nama}`,
                }
                filesTr.images.push(a)
              }else{

                filesTr.images.push(item)
              }

            }else if (item.tipe == 3) {
              let a = {...item,
                path_file: `${env.get('APP_URL')}/uploads/peluang/${data.id_adm_kabkot}/peluang/${data.tahun}/${data.id_prioritas}/${item.nama}`,
              }
              filesTr.document.push(a)
            }else if (item.tipe == 4) {
              let a = {...item,
                path_file: `${env.get('APP_URL')}/uploads/peluang/${data.id_adm_kabkot}/peluang/${data.tahun}/${data.id_prioritas}/${item.nama}`,
              }
              filesTr.infografis.push(a)
            }
          })
          data.tb_peluang_kabkot_file_tr = filesTr
        }

        if (data?.project_status_enum == 0)  {
          data.status_text = 'Draf'
        } else if (data?.project_status_enum == 1)  {
          data.status_text = 'Ready To Offer'
        }else if (data?.project_status_enum == 2)  {
          data.status_text = 'Diminati'
        }else if (data?.project_status_enum == 3)  {
          data.status_text = 'Penjajakan'
        }else if (data?.project_status_enum == 4)  {
          data.status_text = 'Perizinan'
        }
        return {
            success : true,
            data : data
        }
  }

  /**
   * Edit individual record
   */
  async edit({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_peluang_kabkot.findUnique({
          where: {
            id_peluang_kabkot: Id,
          },
        })

        return {
            success : true,
            data
        }
  }

  /**
     * @update
     * @paramQuery menu_name - Nama Menu - @type(string)
     * @paramQuery url - Url Menu - @type(string)
     * @paramQuery order - Urutan Menu - @type(number)
     * @paramQuery parent_id - ID Parent - @type(number)
     */
  async update({ params, request ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }
    let datas = await request.validateUsing(createPeluangValidator)
    let peluang_kontak = request.input('peluang_kontak')
    let peluang_insentif = request.input('peluang_insentif')
    let list_hapus_file = request.input('list_hapus_file')
    let list_hapus_file_tr = request.input('list_hapus_file_tr')
    if (typeof peluang_kontak === 'string') {
        peluang_kontak = JSON.parse(peluang_kontak);
    }
    if (typeof peluang_insentif === 'string') {
        peluang_insentif = JSON.parse(peluang_insentif);
    }
    if (typeof list_hapus_file === 'string') {
        list_hapus_file = JSON.parse(list_hapus_file);
    }
    if (typeof list_hapus_file === 'string') {
        list_hapus_file = JSON.parse(peluang_kontak);
    }
    if (typeof list_hapus_file_tr === 'string') {
        list_hapus_file_tr = JSON.parse(list_hapus_file_tr);
    }

    let vidio = datas?.vidio


      try {
        await prisma.$transaction(async (prisma) => {

          const dataPost ={
                nama : datas.nama,
                keterangan : datas.keterangan,
                status : "0",
                nama_singkat : datas.nama_singkat,
                deskripsi_singkat : datas.deskripsi_singkat,
                deskripsi : datas.deskripsi,
                lokasi_kawasan : datas.lokasi_kawasan,
                project_status_enum : datas.project_status_enum,
                kode_kbli : datas.kode_kbli,
                id_adm_kabkot : datas.id_adm_kabkot,
                id_sektor : datas.id_sektor,
                tahun : datas.tahun,
                id_sumber_data : datas.id_sumber_data,
                id_prioritas : datas.id_prioritas,
                id_adm_kabkot_kantor : datas.id_adm_kabkot_kantor,
                id_kontak : datas.id_kontak,
                zoom_peta_default : datas.zoom_peta_default,
                nilai_investasi : datas.nilai_investasi,
                nilai_irr : datas.nilai_irr,
                nilai_npv : datas.nilai_npv,
                nilai_pp : datas.nilai_pp,
                lon : datas.lon,
                lat : datas.lat,
                is_ipro : datas.is_ipro,
              }
          const update = await prisma.tb_peluang_kabkot.update({
            where: {
              id_peluang_kabkot: Id,
            },
            data: dataPost,
          })
          let tr = request.input('tr')
          if (typeof tr === 'string') {
              tr = JSON.parse(tr);
          }
          const id_peluang_kabkot_tr = parseInt(tr.id_peluang_kabkot_tr)
          delete tr.id_peluang_kabkot_tr
          console.log(tr);
          tr.id_peluang_kabkot = parseInt(tr.id_peluang_kabkot)
          if(id_peluang_kabkot_tr && !isNaN(id_peluang_kabkot_tr)){
            const updateTr = await prisma.tb_peluang_kabkot_tr.update({
              where: {
                id_peluang_kabkot_tr: id_peluang_kabkot_tr,
              },
              data: tr,
            })
          }else{
            tr.id_peluang_kabkot = update.id_peluang_kabkot
            const insertTr = await prisma.tb_peluang_kabkot_tr.create({data:tr})
          }
          if (Array.isArray(list_hapus_file) && list_hapus_file.length > 0) {
            const list_hapus_files = list_hapus_file.map(Number);

            await prisma.tb_peluang_kabkot_file.deleteMany({
              where: {
                // id_peluang_kabkot:update.id_peluang_kabkot                
                id_peluang_kabkot_file: {
                  in: list_hapus_files,
                },
              },
            });
          }
          if ( Array.isArray(list_hapus_file_tr) && list_hapus_file_tr.length > 0) {
            const list_hapus_files = list_hapus_file_tr.map(Number);

            await prisma.tb_peluang_kabkot_file_tr.deleteMany({
              where: {
                // id_peluang_kabkot:update.id_peluang_kabkot
                id_peluang_kabkot_file_tr: {
                  in: list_hapus_files,
                },
              },
            });
          }

          await prisma.tb_peluang_kabkot_file_tr.deleteMany({
            where: {
              id_peluang_kabkot:update.id_peluang_kabkot,
              tipe:4
            },
          });
          await prisma.tb_peluang_kabkot_file.deleteMany({
            where: {
              id_peluang_kabkot:update.id_peluang_kabkot,
              tipe:4                
            },
          });

          if (Array.isArray(peluang_kontak) && peluang_kontak.length > 0) {
            // Step 1: Ambil semua data yang ada di database
            peluang_kontak = peluang_kontak.map(Number)
            const existingKontak = await prisma.tb_peluang_kabkot_kontak.findMany({
                where: { id_peluang_kabkot: update.id_peluang_kabkot },
                select: { id_peluang_kontak: true },
            });
            // Step 2: Ambil ID dari existing data di database
            const existingKontakIds = existingKontak.map(kontak => kontak.id_peluang_kontak);
            // Step 3: Bandingkan array `peluang_kontak` dengan `existingKontakIds`
            const kontakToAdd = peluang_kontak.filter(id => !existingKontakIds.includes(id));
            const kontakToDelete = existingKontakIds.filter(id => !peluang_kontak.includes(id));

            // Step 4: Tambahkan kontak baru yang belum ada di database
            if (kontakToAdd.length > 0) {
                const newKontak = kontakToAdd.map(numb => ({
                    id_peluang_kabkot: update.id_peluang_kabkot,
                    id_peluang_kontak: numb,
                }));
                await prisma.tb_peluang_kabkot_kontak.createMany({ data: newKontak });
            }

            // Step 5: Hapus kontak yang ada di database tapi tidak ada di array
            if (kontakToDelete.length > 0) {
                await prisma.tb_peluang_kabkot_kontak.deleteMany({
                    where: {
                        id_peluang_kabkot: update.id_peluang_kabkot,
                        id_peluang_kontak: { in: kontakToDelete },
                    },
                });
            }
          }

          if (Array.isArray(peluang_insentif) && peluang_insentif.length > 0) {
            peluang_insentif = peluang_insentif.map(Number)

            // Step 1: Ambil semua data yang ada di database
            const existingInsentif = await prisma.tb_peluang_kabkot_insentif.findMany({
                where: { id_peluang_kabkot: update.id_peluang_kabkot },
                select: { id_jenis_insentif: true },
            });

            // Step 2: Ambil ID dari existing data di database
            const existingInsentifIds = existingInsentif.map(insentif => insentif.id_jenis_insentif);

            // Step 3: Bandingkan array `peluang_insentif` dengan `existingInsentifIds`
            const insentifToAdd = peluang_insentif.filter(id => !existingInsentifIds.includes(id));
            const insentifToDelete = existingInsentifIds.filter(id => !peluang_insentif.includes(id));

            // Step 4: Tambahkan insentif baru yang belum ada di database
            if (insentifToAdd.length > 0) {
                const newInsentif = insentifToAdd.map(numb => ({
                    id_peluang_kabkot: update.id_peluang_kabkot,
                    id_jenis_insentif: numb,
                }));
                await prisma.tb_peluang_kabkot_insentif.createMany({ data: newInsentif });
            }

            // Step 5: Hapus insentif yang ada di database tapi tidak ada di array
            if (insentifToDelete.length > 0) {
                await prisma.tb_peluang_kabkot_insentif.deleteMany({
                    where: {
                        id_peluang_kabkot: update.id_peluang_kabkot,
                        id_jenis_insentif: { in: insentifToDelete },
                    },
                });
            }
          }


          const configImg = await configUpload('img')
          const configDoc = await configUpload('doc')
          const dokumen = request.files('dokumen',configDoc);
          if (dokumen) {
            const uploadPromisess = dokumen.map(async (item) => {

                const uploadPath = `uploads/peluang/${update.id_adm_kabkot}/peluang/${update.tahun}/${update.id_prioritas}/`;
                const upDoc = await upload(item, uploadPath);

                const newFileEntry = await prisma.tb_peluang_kabkot_file.create({
                  data: {
                    id_peluang_kabkot: update.id_peluang_kabkot,
                    nama: upDoc.data?.filename,
                    tipe: 3
                  }
                });
            });
            await Promise.all(uploadPromisess);

          }

          const infografisTr = request.files('infografis_tr',configImg);
          if (infografisTr) {
            // const uploadPromisess = infografisTr.map(async (item) => {
            //     const uploadPath = `uploads/peluang/${update.id_adm_kabkot}/peluang/${update.tahun}/${update.id_prioritas}/`;
            //     const upDoc = await upload(item, uploadPath);
            //     const newFileEntry = await prisma.tb_peluang_kabkot_file_tr.create({
            //       data: {
            //         id_peluang_kabkot: update.id_peluang_kabkot,
            //         nama: upDoc.data?.filename,
            //         kd_bahasa:datas.kd_bahasa,
            //         tipe: 4
            //       }
            //     });
            //     console.log(newFileEntry);
            // });
            // await Promise.all(uploadPromisess);
            if (infografisTr) {
              // Proses file secara berurutan untuk mempertahankan urutan
              for (const item of infografisTr) {
                const uploadPath = `uploads/peluang/${update.id_adm_kabkot}/peluang/${update.tahun}/${update.id_prioritas}/`;
                const upDoc = await upload(item, uploadPath);
                
                await prisma.tb_peluang_kabkot_file_tr.create({
                  data: {
                    id_peluang_kabkot: update.id_peluang_kabkot,
                    nama: upDoc.data?.filename,
                    kd_bahasa: datas.kd_bahasa,
                    tipe: 4
                  }
                });
              }
            }
          }

          const infografis = request.files('infografis',configImg);
          if (infografis) {
            // const uploadPromisess = infografis.map(async (item) => {

            //     const uploadPath = `uploads/peluang/${update.id_adm_kabkot}/peluang/${update.tahun}/${update.id_prioritas}/`;
            //     const upDoc = await upload(item, uploadPath);

            //     const newFileEntry = await prisma.tb_peluang_kabkot_file.create({
            //       data: {
            //         id_peluang_kabkot: update.id_peluang_kabkot,
            //         nama: upDoc.data?.filename,
            //         tipe: 4
            //       }
            //     });
            // });
            // await Promise.all(uploadPromisess);
            if (infografis) {
              // Proses file secara berurutan untuk mempertahankan urutan
              for (const item of infografis) {
                const uploadPath = `uploads/peluang/${update.id_adm_kabkot}/peluang/${update.tahun}/${update.id_prioritas}/`;
                const upDoc = await upload(item, uploadPath);
                
                await prisma.tb_peluang_kabkot_file.create({
                  data: {
                    id_peluang_kabkot: update.id_peluang_kabkot,
                    nama: upDoc.data?.filename,
                    tipe: 4
                  }
                });
              }
            }
          }

          const images = request.files('images',configImg);
            if (images) {

                // Create an array of promises
              const uploadPromisesUpImg = images.map(async (item) => {
              const uploadPath = `uploads/peluang/${update.id_adm_kabkot}/peluang/${update.tahun}/${update.id_prioritas}/`;

              // Wait for the image to upload
              const upImg = await img_to_webp(item, uploadPath);

              // After upload completes, aupdate data to the database
              await prisma.tb_peluang_kabkot_file.create({
                  data: {
                    id_peluang_kabkot: update.id_peluang_kabkot,
                    nama: upImg.data.filename,
                    tipe: 1,
                  },
                });
              });

              // Wait for all uploads and database updates to complete
              await Promise.all(uploadPromisesUpImg);
            }

            if (vidio) {
              await prisma.tb_peluang_kabkot_file.deleteMany({
                where: {
                    id_peluang_kabkot: update.id_peluang_kabkot,
                    tipe: 2,
                },
              });
              await prisma.tb_peluang_kabkot_file.create({
                  data: {
                    id_peluang_kabkot: update.id_peluang_kabkot,
                    nama:vidio,
                    tipe: 2,
                  },
                });
            }

            let mapService = request.input('map_service')
            if (typeof mapService === 'string') {
              mapService = JSON.parse(mapService);
            }
            if (Array.isArray(mapService) && mapService.length > 0) {
              const existingRecords = await prisma.tb_peluang_layers.findMany({
                    where: { id_peluang: Id },
                });

                // Buat array id_pl dari data input
                const inputIds = mapService
                    .filter((data) => data.id_pl !== undefined) // Hanya data yang memiliki id_pl
                    .map((data) => parseInt(data.id_pl));

                // Hapus data yang ada di database tapi tidak ada di input
                const deletePromises = existingRecords
                    .filter((record) => !inputIds.includes(record.id_pl))
                    .map((record) =>
                        prisma.tb_peluang_layers.delete({
                            where: { id_pl: record.id_pl },
                        })
                    );

                // Loop untuk insert atau update data
                const upsertPromises = mapService.map((data) => {
                    if (data.id_pl) {
                        // Jika id_pl ada, lakukan upsert
                        return prisma.tb_peluang_layers.update({
                            where: { id_pl: parseInt(data.id_pl) },
                            data: {
                                layeruid: data.layeruid,
                                keterangan: data.keterangan,
                                is_active : typeof data.is_active === 'boolean'
                                            ? data.is_active
                                            : data.is_active === 'true'
                            },
                        });
                    } else {
                        // Jika id_pl tidak ada, lakukan insert
                        return prisma.tb_peluang_layers.create({
                            data: {
                                layeruid: data.layeruid,
                                keterangan: data.keterangan,
                                id_peluang: Id,
                                kategori: 1, // Tambahkan kategori default
                                is_active: true, // Status aktif default
                            },
                        });
                    }
                });

                // Jalankan semua operasi secara paralel
                await Promise.all([...deletePromises, ...upsertPromises]);
            }
            return response.status(200).json({ success: true, data: update })
          })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data' ,message:error.message})
    }
  }



  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_peluang_kabkot.delete({
        where: {
          id_peluang_kabkot: Id,
        },
      })
      return response.status(200).send({ success: true,data:deletePost})

    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }


  async  getMenuWithChildren(parentId = null) {
    const menus =  prisma.tb_peluang_kabkot.findMany({
      where: { parentId },
      include: { children: true },
      orderBy: { order: 'asc' },
    });

    return menus.map(menu => ({
          ...menu,
          children: getMenuWithChildren(menu.id),
        }));
  }

  /**
     * @aprove
     * @paramQuery status - status - @type(number) @required
     * @paramQuery keterangan - keterangan - @type(string)  @required
     */
    async aprove({ params, request, response ,auth}: HttpContext) {
        // Ambil dan parse ID dari parameter URL
        const Id = parseInt(params.id_peluang_kabkot, 10);
        // Validasi ID
        if (isNaN(Id)) {
            return response.status(400).json({ error: 'Invalid ID provided' });
        }
        try {
            // Validasi input dengan validator yang telah dibuat
            const dataPost = await request.validateUsing(aproveValidator);

            // Update data di tb_sektor_nasional
            const update = await prisma.tb_peluang_kabkot.update({
            where: {
                id_peluang_kabkot: Id
            },
                data: {
                    status: String(dataPost.status)
                },
            });
            // Insert ke tb_sektor_nasional_status
            const insert = await prisma.tb_peluang_kabkot_status.create({
                data: {
                    id_peluang_kabkot: Id,
                    status: dataPost.status,
                    status_proses: dataPost.status,
                    keterangan: dataPost.keterangan,
                    created_by:auth.user?.id,
                    updated_by:auth.user?.id
                },
            });
            // Berikan respon sukses dengan data update
            return response.status(201).json({ success: true, data: update });
        } catch (error) {
            // Tangani error dan kirimkan respon gagal
            return response.status(500).json({ success: false, message: error.message });
        }
    }

    /**
     * @aprove
     * @paramQuery status - status - @type(number) @required
     */
    async change_status_proyek({ params, request, response ,auth}: HttpContext) {
        // Ambil dan parse ID dari parameter URL
        const Id = parseInt(params.id_peluang_kabkot, 10);
        // Validasi ID
        if (isNaN(Id)) {
            return response.status(400).json({ error: 'Invalid ID provided' });
        }
        try {
            // Validasi input dengan validator yang telah dibuat
            const dataPost = await request.validateUsing(statusProyekValidator);

            // Update data di tb_sektor_nasional
            const update = await prisma.tb_peluang_kabkot.update({
            where: {
                id_peluang_kabkot: Id
            },
                data: {
                    project_status_enum: dataPost.project_status_enum
                },
            });

            for (const item of dataPost.email ?? []) {
              const insert = await prisma.tb_status_proyek_email.create({
                  data: {
                      id_peluang_kabkot: Id,
                      jenis: dataPost.subjek,
                      keterangan: dataPost.keterangan,
                      email_penerima: item, // Pastikan ini mengambil `item` dari email
                  },
              });
              const proyek = await prisma.tb_peluang_kabkot.findFirst({
                where:{
                  id_peluang_kabkot:Id
                }
              })
              const logo = `${env.get('APP_URL')}/uploads/logo.png`
              const subjek = insert.jenis == 1 ? 'Nota Dinas' : 'Informasi';

              const body = `
              <table style="border-collapse: collapse; width: 40%; margin: 20px auto; font-family: Arial, sans-serif; border: 1px solid black;">
                <thead>
                    <tr>
                        <th colspan="2" style="background-color: #f4f4f4; text-align: center; padding: 10px; border: 1px solid black;">
                            <img src="${logo}" alt="Header Image" style="width: 300px; height: auto; margin: 0 auto;">
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="font-weight: bold; padding: 10px; border: 1px solid black; width: 30%;">Subjek</td>
                        <td style="padding: 10px; border: 1px solid black; width: 70%;">${subjek}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold; padding: 10px; border: 1px solid black; width: 30%;">Proyek</td>
                        <td style="padding: 10px; border: 1px solid black; width: 70%;">${proyek?.nama}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold; padding: 10px; border: 1px solid black; width: 30%;">Status</td>
                        <td style="padding: 10px; border: 1px solid black; width: 70%;">SOLD</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold; padding: 10px; border: 1px solid black; width: 30%;">Keterangan</td>
                        <td style="padding: 10px; border: 1px solid black; width: 70%;">${insert.keterangan}</td>
                    </tr>
                </tbody>
            </table>
              `;

              await send_mail(insert.email_penerima, subjek, body);

              response.status(201).json({
                  success: true,
                  data: {
                      nama: insert.email_penerima,
                      subjek: subjek,
                  },
              });
          }




            // Berikan respon sukses dengan data update
            return response.status(201).json({ success: true, data: dataPost });
        } catch (error) {
            // Tangani error dan kirimkan respon gagal
            return response.status(500).json({ success: false, message: error.message });
        }
    }

}