import { createMenuValidator, updateMenuValidator } from '#validators/menu';
import type { HttpContext } from '@adonisjs/core/http'
import { configUpload, get_paginate, img_to_webp, upload } from '../helpers/global_helper.js';
import { createAppSLiderPeluangValidator } from '#validators/add_slider_peluang';
import env from '#start/env';
import prisma from '../lib/prisma.js'


export default class AppSLiderPeluangController {
  /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.tb_app_slider_peluang.findMany({

    });

      return {
          success : true,
          data : {data}
      }
  }

  /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          { judul: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
          { deskripsi: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
        ],
      };
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_app_slider_peluang.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const sliders = await prisma.tb_app_slider_peluang.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      include: {
        tr: true,
      },
    });
    const data = sliders.map((item =>{
        return {
          ...item,
          nama_file_image : `${env.get('APP_URL')}/uploads/slider/${item.nama_file_image}`,
           tb_app_slider_peluang_tr: item.tr.map((trItem) => ({
          ...trItem,
          nama_file_image: `${env.get('APP_URL')}/uploads/slider/${trItem.nama_file_image}`,
        })),
        }
    }))


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {

  }

  /**
     * @store
     * @paramQuery judul - Judul Slider - @type(string)
     * @paramQuery deskripsi - Deskripsi Slider - @type(string)
     * @paramQuery url_link - Url - @type(string)
     * @paramQuery ordering - Urutan - @type(number)
     * @paramQuery judul_tr - Judul Slider - @type(string)
     * @paramQuery deskripsi_tr - Deskripsi Slider - @type(string)
     * @paramQuery ordering_tr - Urutan - @type(number)
     * @paramQuery image - image - @type(file)
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createAppSLiderPeluangValidator)
        try {
            const configImg = await configUpload('img')
            const image = request.files('image',configImg);
            let nama_file_img = ''
              if (image) {
                const uploadPromisess = image.map(async (item) => {

                    const uploadPath = `uploads/slider`;
                    const upImg = await img_to_webp(image[0], uploadPath);
                    nama_file_img = upImg.data.filename
                });
                await Promise.all(uploadPromisess);

              }
           const dataPost = {
                judul:data.judul,
                deskripsi:data.deskripsi,
                nama_file_image : nama_file_img,
                url_link:data.url_link,
                ordering:data.ordering,
            }

            const insert = await prisma.tb_app_slider_peluang.create({data:dataPost})
            const dataPostTr = {
                id_app_slider:insert.id_app_slider,
                kd_bahasa : "en",
                judul:data.judul_tr,
                deskripsi:data.deskripsi_tr,
                nama_file_image : nama_file_img,
                ordering:data.ordering_tr,
            }

            const insertTr = await prisma.tb_app_slider_peluang_tr.create({data:dataPostTr})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        let data = await prisma.tb_app_slider_peluang.findUnique({
          where: {
            id_app_slider: Id,
          },
          include: {
            tr: true,
          },
        })
        if (data && data.nama_file_image) {
          data.nama_file_image = `${env.get('APP_URL')}/uploads/slider/${data.nama_file_image}`;
        }
        if (data && data.tr) {
          data.tr = data.tr.map((trItem) => ({
            ...trItem,
            nama_file_image: `${env.get('APP_URL')}/uploads/slider/${trItem.nama_file_image}`,
          }));
        }
        return {
            success : true,
            data : data
        }
  }

  /**
   * Edit individual record
   */
  async edit({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_app_slider_peluang.findUnique({
          where: {
            id_app_slider: Id,
          },
        })

        return {
            success : true,
            data
        }
  }

  /**
     * @update
     * @paramQuery judul - Judul Slider - @type(string)
     * @paramQuery deskripsi - Deskripsi Slider - @type(string)
     * @paramQuery url_link - Url - @type(string)
     * @paramQuery ordering - Urutan - @type(number)
     * @paramQuery judul_tr - Judul Slider - @type(string)
     * @paramQuery deskripsi_tr - Deskripsi Slider - @type(string)
     * @paramQuery ordering_tr - Urutan - @type(number)
     * @paramQuery image - image - @type(file)
     *
     */
  async update({ params, request ,response}: HttpContext) {

      const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }

      const data = await request.validateUsing(createAppSLiderPeluangValidator)
        try {
            const configImg = await configUpload('img')
            const image = request.files('image',configImg);
            let dataPost = {}
            let dataPostTr = {}
            dataPost = {
                      judul : data.judul,
                      deskripsi :data.deskripsi,
                      ordering:data.ordering,
                      url_link : data.url_link
                    }
            dataPostTr = {
                        kd_bahasa : "en",
                        judul:data.judul_tr,
                        deskripsi:data.deskripsi_tr,
                        ordering:data.ordering_tr,
                    }
              if (image) {
                const uploadPromisess = image.map(async (item) => {

                    const uploadPath = `uploads/slider`;
                    const upImg = await img_to_webp(image[0], uploadPath);
                    dataPost.nama_file_image = upImg.data.filename
                    dataPostTr.nama_file_image = upImg.data.filename
                });
                await Promise.all(uploadPromisess);
              }
            const insert = await prisma.tb_app_slider_peluang.update({
              data:dataPost,
              where:{
                id_app_slider:Id
              }
            })
            const tr = await prisma.tb_app_slider_peluang_tr.findFirst({
              where:{
                id_app_slider:Id
              },
              select :{
                id_app_slider_tr:true
              }
            })
            const updateTr = await prisma.tb_app_slider_peluang_tr.update({
              data:dataPostTr,
              where:{
                id_app_slider_tr:tr?.id_app_slider_tr,
              }
            })
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_app_slider_peluang.delete({
        where: {
          id_app_slider: Id,
        },
      })
      return response.status(200).send({ success: true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }


  async  getMenuWithChildren(parentId = null) {
    const menus =  prisma.tb_app_slider_peluang.findMany({
      where: { parentId },
      include: { children: true },
      orderBy: { order: 'asc' },
    });

    return menus.map(menu => ({
          ...menu,
          children: getMenuWithChildren(menu.id),
        }));
  }

}