import { createUmkmValidator, updateUmkmValidator } from '#validators/umkm'
import { createUmrProvValidator } from '#validators/umr'
import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'
import { configUpload, img_to_webp } from '../helpers/global_helper.js'
import env from '#start/env'
import { createSNRefValidator, updateSNRefValidator } from '#validators/referensi'
import { IknLayersValidator } from '#validators/ikn'
import { aproveValidator } from '#validators/aprove'

export default class IknLayersController {
    
  async index({}: HttpContext) {
    const data = await prisma.tb_sektor_nasional_ref.findMany({})
    return { 
        success : true,
        data : data
    }
  }
   /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          { nama_layer: { contains: search, mode: 'insensitive' } }, 
          { judul: { contains: search, mode: 'insensitive' } }, 
        ],
      };
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_ikn_layer.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_ikn_layer.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
     
    });
    
    

    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: datas, // Data for the current page
    };


  }
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
     * @store
     * @summary Insert Referensi Sektor Nasional Provinsi
     * @paramQuery nama_layer - nama layer - @type(string) @required
     * @paramQuery judul - judul - @type(string) @required
     * @paramQuery url_service - url service - @type(string) @required
  */
    public async store({ request, response ,auth}: HttpContext) {
      const data = await request.validateUsing(IknLayersValidator)
        try {
            const dataPost = {
              url_service: data.url_service,
              nama_layer : data.nama_layer,
              judul : data.judul,
              tipe : 2,
              id_ikn : 1,
              status : 0
            }
            const insert = await prisma.tb_ikn_layer.create({data:dataPost})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
    const data = await prisma.tb_ikn_layer.findUnique({
      where: {
        id_ikn_layer: Id,
      },
    })
//
    return { 
      success : true,
      data : data
    }
  }


  /**
     * @update
     * @summary Insert Referensi Sektor Nasional Provinsi
     * @paramQuery nama_layer - nama layer - @type(string) @required
     * @paramQuery judul - judul - @type(string) @required
     * @paramQuery url_service - url service - @type(string) @required
     */
  async update({ params, request ,response}: HttpContext) {

    
    const Id = parseInt(params.id, 10)
    
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }
    try {
      const data = await request.validateUsing(IknLayersValidator)
      const configImg = await configUpload('img');
     
      const dataPost = {
              url_service: data.url_service,
              nama_layer : data.nama_layer,
              judul : data.judul,
              tipe : 2,
              id_ikn : 1,
              status : 0
            }
     
      const update = await prisma.tb_ikn_layer.update({
          data:dataPost,
          where:{
            id_ikn_layer : Id,
          }
        })
      return response.status(200).json({ success: true, data: update })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data' })
    }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_ikn_layer.delete({
        where: {
          id_ikn_layer: Id,
        },
      })
      return response.status(200).send({success:true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }

  /**
     * @change_status
     * @paramQuery status - status - @type(number) @required
     * @paramQuery keterangan - keterangan - @type(string)  @required
     */
    async change_status({ params, request, response ,auth}: HttpContext) {
        // Ambil dan parse ID dari parameter URL
        const Id = parseInt(params.id, 10);
        // Validasi ID
        if (isNaN(Id)) {
            return response.status(400).json({ error: 'Invalid ID provided' });
        }
        try {
            // Validasi input dengan validator yang telah dibuat
            const dataPost = await request.validateUsing(aproveValidator);

            // Update data di tb_sektor_nasional
            const update = await prisma.tb_ikn_layer.update({
            where: {
                id_ikn_layer: Id
            },
                data: {
                    status: dataPost.status
                },
            });
            // Berikan respon sukses dengan data update
            return response.status(201).json({ success: true, data: update });
        } catch (error) {
            // Tangani error dan kirimkan respon gagal
            return response.status(500).json({ success: false, message: error.message });
        }
    }
}

