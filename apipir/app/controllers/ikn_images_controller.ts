import { createMenuValidator, updateMenuValidator } from '#validators/menu';
import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'
import { configUpload, get_paginate, img_to_webp, upload } from '../helpers/global_helper.js';
import { createAppSLiderValidator } from '#validators/add_slider_peluang';
import env from '#start/env';
import { IknImageValidator } from '#validators/ikn';
import { aproveValidator } from '../validators/aprove.js';



export default class IknImagesController {
  /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.tb_app_slider.findMany({
      include:{
        tb_app_slider_tr: true,
      }
    });

      return { 
          success : true,
          data : {data}
      }
  }

  /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          { judul: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
          { nama: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi          
        ],
      };
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_ikn_file.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const sliders = await prisma.tb_ikn_file.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
     
    });
    const data = sliders.map((item =>{
        return {
          ...item,
          image : `${env.get('APP_URL')}/uploads/ikn/${item.nama}`,
        }
    }))
    

    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {

  }

  /**
     * @store
     * @paramQuery judul - Judul Slider - @type(string) 
     * @paramQuery deskripsi - Deskripsi Slider - @type(string) 
     * @paramQuery url_link - Url - @type(string)
     * @paramQuery ordering - Urutan - @type(number) 
     * @paramQuery judul_tr - Judul Slider - @type(string) 
     * @paramQuery deskripsi_tr - Deskripsi Slider - @type(string) 
     * @paramQuery ordering_tr - Urutan - @type(number) 
     * @paramQuery image - image - @type(file) 
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async store({ request, response ,auth}: HttpContext) {

      const configImg = await configUpload('img')
      const data = await request.validateUsing(IknImageValidator)
        try {
            const image = request.files('image',configImg);
            let nama_file_img = ''
              if (image) {
                const uploadPromisess = image.map(async (item) => {
                  
                  const uploadPath = `uploads/ikn`;
                  const upImg = await img_to_webp(item, uploadPath);
                  nama_file_img = upImg.data.filename  
                  const dataPost = {
                      judul:data.judul,
                      nama : nama_file_img,
                      status:99,
                      id_ikn:1,
                      jenis:1,
                      tipe:1,
                  }
                        
                  const insert = await prisma.tb_ikn_file.create({data:dataPost})                      
                  response.status(201).json({ success: true,data:insert })
              });

                await Promise.all(uploadPromisess);

              }
           
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        let data = await prisma.tb_ikn_file.findUnique({
          where: {
            id_ikn_file: Id,
          },
        })
        if (data) {
            data['image'] = `${env.get('APP_URL')}/uploads/ikn/${data.nama}`
        }
        return { 
            success : true,
            data : {data}
        }
  }

  /**
   * Edit individual record
   */
  async edit({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_ikn_file.findUnique({
          where: {
            id_ikn_file: Id,
          },
        })

        return { 
            success : true,
            data
        }
  }

  /**
     * @update
     * @paramQuery judul - judul - @type(string) 
     * @paramQuery image - image - @type(file) 
     * 
     */
  async update({ params, request ,response}: HttpContext) {
  
      const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
      const data = await request.validateUsing(IknImageValidator)

        try{
          const configImg = await configUpload('img')
            const image = request.files('image',configImg);
            let nama_file_img = ''
              if (image.length > 0) {
                const uploadPromisess = image.map(async (item) => {
                  
                  const uploadPath = `uploads/ikn`;
                  const upImg = await img_to_webp(item, uploadPath);
                  nama_file_img = upImg.data.filename  
                  const dataPost = {
                    judul:data.judul,
                      nama : nama_file_img,
                      status:99,
                      id_ikn:1,
                      jenis:1,
                      tipe:1,
                    }
                    
                    const update = await prisma.tb_ikn_file.update({
                      where:{
                      id_ikn_file:Id
                    },
                    data:dataPost
                  })                  
                  response.status(201).json({ success: true,data:update })
                });
                await Promise.all(uploadPromisess);
                
              }else{
                const dataPost = {
                    judul:data.judul,
                    status:99,
                    id_ikn:1,
                    jenis:1,
                    tipe:1,
                }
                      
                const update = await prisma.tb_ikn_file.update({
                  where:{
                    id_ikn_file:Id
                  },
                  data:dataPost
                })    
                response.status(201).json({ success: true,data:update })
              }
           
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_ikn_file.delete({
        where: {
          id_ikn_file: Id,
        },
      })
      return response.status(200).send({ success: true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }

  /**
     * @change_status
     * @paramQuery status - status - @type(number) @required
     * @paramQuery keterangan - keterangan - @type(string)  @required
     */
    async change_status({ params, request, response ,auth}: HttpContext) {
        // Ambil dan parse ID dari parameter URL
        const Id = parseInt(params.id, 10);
        // Validasi ID
        if (isNaN(Id)) {
            return response.status(400).json({ error: 'Invalid ID provided' });
        }
        // try {
            // Validasi input dengan validator yang telah dibuat
            const dataPost = await request.validateUsing(aproveValidator);

            // Update data di tb_sektor_nasional
            const update = await prisma.tb_ikn_file.update({
            where: {
                id_ikn_file: Id
            },
                data: {
                    status: dataPost.status
                },
            });
            // Berikan respon sukses dengan data update
            return response.status(201).json({ success: true, data: update });
        // } catch (error) {
        //     // Tangani error dan kirimkan respon gagal
        //     return response.status(500).json({ success: false, message: error. });
        // }
    }


}