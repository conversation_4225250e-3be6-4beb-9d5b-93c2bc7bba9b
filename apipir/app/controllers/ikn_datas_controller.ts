import { createMenuValidator, updateMenuValidator } from '#validators/menu';
import type { HttpContext } from '@adonisjs/core/http'
import { configUpload, get_paginate, img_to_webp, upload } from '../helpers/global_helper.js';
import { createAppSLiderValidator } from '#validators/add_slider_peluang';
import env from '#start/env';
import { createIknDataValidator, updateIknDataValidator } from '#validators/ikn';
import { aproveValidator } from '../validators/aprove.js';
import striptags from 'striptags';
import prisma from '../lib/prisma.js'


export default class IknDatasController {
  /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.tb_ikn_data.findMany({

    });

      return {
          success : true,
          data : {data}
      }
  }

  /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',') {
      searchCondition = {
        OR: [
          { nama: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
          { deskripsi: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
        ],
      };
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_ikn_data.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_ikn_data.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty

    });

    const data = datas.map((item) => {
      return {
        id_ikn_data: item.id_ikn_data,
        nama : item.nama,
        jenis : item.jenis,
        is_fe : item.is_fe,
        deskripsi : striptags(item.deskripsi),
        fe_posisi: item.fe_posisi,
        status : item.status
      }
    })



    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {

  }

  /**
     * @store
     * @paramQuery judul - Judul Slider - @type(string)
     * @paramQuery image - image - @type(file)
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createIknDataValidator)
        try {
            const configImg = await configUpload('img')
            const configDoc = await configUpload('doc')

            const icon = request.files('icon',configImg);
            const file_ikn = request.files('file');
            const file_ikn_tr = request.files('file_tr');
            let nama_file_img =null

            if (icon) {
                const uploadPromisess = icon.map(async (item) => {
                const uploadPath = `uploads/ikn`;
                const upImg = await img_to_webp(icon[0], uploadPath);
                nama_file_img = upImg.data.filename
              });
              await Promise.all(uploadPromisess);

            }
            // await prisma.$transaction(async (prisma) => {

            const dataPost = {
              id_ikn:1,
              jenis:data.jenis,
                    deskripsi:data.deskripsi,
                    nama : data.nama,
                    is_fe:data.is_fe,
                    fe_posisi:data.fe_posisi ? data.fe_posisi : 0 ,
                    status:0,
                    file_image:nama_file_img
                  }

            const insert = await prisma.tb_ikn_data.create({data:dataPost})
            const dataPostTr = {
                id_ikn_data:insert.id_ikn_data,
                kd_bahasa : "en",
                deskripsi:data.deskripsi_tr,
                  nama : data.nama_tr,
            }

            const insertTr = await prisma.tb_ikn_data_tr.create({data:dataPostTr})

            let file_detail = request.input('file_detail')
            let file_detail_tr = request.input('file_detail_tr')
            if (typeof file_detail === 'string') {
                  file_detail = JSON.parse(file_detail);
            }
            if (typeof file_detail_tr === 'string') {
                  file_detail_tr = JSON.parse(file_detail_tr);
            }
            if (file_ikn.length > 0 && Array.isArray(file_detail) && file_ikn_tr && Array.isArray(file_detail_tr)) {
              if (file_ikn.length !== file_detail.length) {
                  return response.badRequest('Jumlah gambar dan detail tidak sesuai untuk file_ikn');
              }
              if (file_ikn_tr.length !== file_detail_tr.length) {
                  return response.badRequest('Jumlah gambar dan detail tidak sesuai untuk file_ikn_tr');
              }


              const imageExtensions = ['jpg', 'jpeg', 'png', 'webp','JPG','JPEG'];
              const documentExtensions = ['pdf', 'doc', 'docx'];
              const vidioExtensions = ['mp4', 'MP4', 'mov', 'MOV'];
              let fileIds = [];
              for (let index = 0; index < file_ikn.length; index++) {
                const item = file_ikn[index];
                const uploadPath = `uploads/ikn/`;
                let fileName;
                
                // Periksa tipe file
                const ext = item.extname.toLowerCase();
                const fileType = item.type;
                
                try {
                  if (imageExtensions.includes(ext)) {
                    const upImg = await img_to_webp(item, uploadPath);
                    fileName = upImg.data.filename;
                  } else if (documentExtensions.includes(ext)) {
                    const upDoc = await upload(item, uploadPath);
                    fileName = upDoc.data?.filename;
                  } else if (vidioExtensions.includes(ext) || fileType === 'video') {
                    const upDoc = await upload(item, uploadPath);
                    fileName = upDoc.data?.filename;
                  } else {
                    console.error(`Unsupported file extension: ${ext}, type: ${fileType}`);
                    continue; // Skip this file instead of throwing error
                  }
                  
                  // Detail dari file utama
                  const detail = file_detail[index];
                  
                  // Insert file utama ke database
                  const insertedFile = await prisma.tb_ikn_data_file.create({
                    data: {
                      id_ikn_data: insert.id_ikn_data,
                      tipe: parseInt(detail.tipe),
                      nama: fileName,
                      judul: detail.judul,
                    },
                  });
                  
                  fileIds.push(insertedFile.id_ikn_data_file);
                } catch (error) {
                  console.error(`Error processing file ${index}:`, error);
                  // Continue processing other files instead of throwing
                }
              }
        
              // Proses file terkait (file_ikn_tr) hanya jika ada fileIds
              if (fileIds.length > 0) {
                for (let index = 0; index < file_ikn_tr.length; index++) {
                  if (index >= fileIds.length) break; // Skip if no corresponding main file
                  
                  const item = file_ikn_tr[index];
                  const uploadPathTr = `uploads/ikn_tr/`;
                  let fileName;
                  
                  try {
                    // Periksa tipe file
                    const ext = item.extname.toLowerCase();
                    if (imageExtensions.includes(ext)) {
                      const upImgTr = await img_to_webp(item, uploadPathTr);
                      fileName = upImgTr.data.filename;
                    } else if (documentExtensions.includes(ext)) {
                      const upDocTr = await upload(item, uploadPathTr);
                      fileName = upDocTr.data?.filename;
                    } else if (vidioExtensions.includes(ext) || item.type === 'video') {
                      const upDocTr = await upload(item, uploadPathTr);
                      fileName = upDocTr.data?.filename;
                    } else {
                      console.error(`Unsupported file extension: ${ext}`);
                      continue; // Skip this file
                    }
                    
                    // Detail dari file terkait
                    const detailTr = file_detail_tr[index];
                    
                    // Insert file terkait ke database
                    await prisma.tb_ikn_data_file_tr.create({
                      data: {
                        id_ikn_data_file: fileIds[index],
                        kd_bahasa: detailTr.kd_bahasa || 'en',
                        nama: fileName,
                        judul: detailTr.judul,
                      },
                    });
                  } catch (error) {
                    console.error(`Error processing translation file ${index}:`, error);
                    // Continue processing other files
                  }
                }
              }
            }
            
              response.status(201).json({ success: true,data:insert })
          // })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        let data = await prisma.tb_ikn_data.findUnique({
          where: {
            id_ikn_data: Id,
          },
          include: {
            tb_ikn_data_file: {
              include:{
                tb_ikn_data_file_tr:true
              }
            },
            tb_ikn_data_tr:true
          },
        })
         if (data && data.file_image) {
          data.file_image = `${env.get('APP_URL')}/uploads/ikn/${data.file_image}`;
        }
        if (data && data.tb_ikn_data_file) {
          data.tb_ikn_data_file = data.tb_ikn_data_file.map((item) => ({
            ...item,
            nama: `${env.get('APP_URL')}/uploads/ikn/${item.nama}`,
            tb_ikn_data_file_tr : item.tb_ikn_data_file_tr.map((item2) => {
              return{
                ...item,
                nama: `${env.get('APP_URL')}/uploads/ikn/${item2.nama}`,
              }
            })
          }));
        }
        return {
            success : true,
            data : data
        }
  }

  /**
   * Edit individual record
   */
  async edit({ params,response }: HttpContext) {

  }
  async update({ params, request ,response}: HttpContext) {

      const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
      const data = await request.validateUsing(updateIknDataValidator)

        try {
            const configImg = await configUpload('img')
            const configDoc = await configUpload('doc')

            const icon = request.files('logo',configImg);
            const file_ikn = request.files('file');
            const file_ikn_tr = request.files('file_tr');
            let dataPost = {
              jenis:data.jenis,
              deskripsi:data.deskripsi,
              nama : data.nama,
              is_fe:data.is_fe,
              fe_posisi:data.fe_posisi ? data.fe_posisi : 0 ,
              status:0,
            }
            if (icon) {
                const uploadPromisess = icon.map(async (item) => {
                const uploadPath = `uploads/ikn`;
                const upImg = await img_to_webp(item, uploadPath);
                dataPost.file_image = upImg.data.filename
              });
              await Promise.all(uploadPromisess);

            }
            // await prisma.$transaction(async (prisma) => {


                  // return Id
            const update = await prisma.tb_ikn_data.update({
            where:{
              id_ikn_data : Id
            },
              data:dataPost
            })


            let tr = request.input('tr')
            let file_detail = request.input('file_detail')
            let file_detail_tr = request.input('file_detail_tr')
            if (typeof tr === 'string') {
                  tr = JSON.parse(tr);
            }


            const updateTr = await prisma.tb_ikn_data_tr.update({
              where:{
                id_ikn_data_tr : parseInt(tr.id_ikn_data_tr)
              },
              data:{
                nama:tr.nama,
                deskripsi:tr.deskripsi
              }
            })

            if (typeof file_detail === 'string') {
                  file_detail = JSON.parse(file_detail);
            }
            if (typeof file_detail_tr === 'string') {
                  file_detail_tr = JSON.parse(file_detail_tr);
            }
            if (file_ikn && Array.isArray(file_detail) && file_ikn_tr && Array.isArray(file_detail_tr)) {
                const imageExtensions = ['jpg', 'jpeg', 'png', 'webp'];
                const documentExtensions = ['pdf', 'doc', 'docx'];

                // Proses file utama (file_ikn)
                const existingFileIds = file_detail.map(detail => parseInt(detail.id_ikn_data_file)).filter(Boolean);
                const dbExistingFiles = await prisma.tb_ikn_data_file.findMany({
                  where: { id_ikn_data: Id },
                });

                // Hapus data yang tidak ada di input
                const idsToDelete = dbExistingFiles
                  .filter(file => !existingFileIds.includes(file.id_ikn_data_file))
                  .map(file => file.id_ikn_data_file);
                if (idsToDelete.length) {
                  await prisma.tb_ikn_data_file.deleteMany({
                    where: { id_ikn_data_file: { in: idsToDelete } },
                  });
                }

                // Proses unggahan atau pembaruan
                let idInserFile = []
                const uploadPromisesUpImg = file_detail.map(async (detail, index) => {
                  let fileName;
                  const isFileUploaded = file_ikn.find(file => file.clientName === detail.fileName);

                  if (isFileUploaded) {
                    const item = isFileUploaded;
                    const uploadPath = `uploads/ikn/`;

                    if (imageExtensions.includes(item.extname)) {
                      const upImg = await img_to_webp(item, uploadPath);
                      fileName = upImg.data.filename;
                    } else if (documentExtensions.includes(item.extname)) {
                      const upDoc = await upload(item, uploadPath);
                      fileName = upDoc.data?.filename;
                    } else {
                      throw new Error(`Jenis file tidak didukung: ${item.extname}`);
                    }
                  }

                  // Jika ID ada, lakukan pembaruan
                  if (detail.id_ikn_data_file) {

                    await prisma.tb_ikn_data_file.update({
                      where: { id_ikn_data_file: parseInt(detail.id_ikn_data_file) },
                      data: {
                        nama: fileName || undefined, // Hanya ubah nama jika ada file baru
                        judul: detail.judul,
                        tipe: parseInt(detail.tipe),
                      },
                    });
                  } else {
                    // Jika ID tidak ada, buat baru
                    const insertedFile = await prisma.tb_ikn_data_file.create({
                      data: {
                        id_ikn_data: Id,
                        nama: fileName,
                        judul: detail.judul,
                        tipe: parseInt(detail.tipe),
                      },
                    });
                    idInserFile.push(insertedFile.id_ikn_data_file);
                    return insertedFile.id_ikn_data_file;
                  }
                });

                // Tunggu semua file utama selesai diproses
                const fileIds = await Promise.all(uploadPromisesUpImg);

                // Proses file terkait (file_ikn_tr)
                const existingFileTrIds = file_detail_tr.map(detail => parseInt(detail.id_ikn_data_file_tr)).filter(Boolean);
                const dbExistingFileTrs = await prisma.tb_ikn_data_file_tr.findMany({
                  where: { id_ikn_data_file: { in: fileIds.map(id => Id) } },
                });

                // Hapus data terkait yang tidak ada di input
                const idsToDeleteTr = dbExistingFileTrs
                  .filter(fileTr => !existingFileTrIds.includes(fileTr.id_ikn_data_file_tr))
                  .map(fileTr => fileTr.id_ikn_data_file_tr);
                if (idsToDeleteTr.length) {
                  await prisma.tb_ikn_data_file_tr.deleteMany({
                    where: { id_ikn_data_file_tr: { in: idsToDeleteTr } },
                  });
                }

                // Proses unggahan atau pembaruan file terkait
                const uploadPromisesUpImgTr = file_detail_tr.map(async (detailTr, index) => {
                  let fileName;
                  const isFileUploaded = file_ikn_tr.find(file => file.clientName === detailTr.fileName);

                  // Jika ada file yang diunggah
                  if (isFileUploaded) {
                    const item = isFileUploaded;
                    const uploadPathTr = `uploads/ikn/`;

                    if (imageExtensions.includes(item.extname)) {
                      const upImgTr = await img_to_webp(item, uploadPathTr);
                      fileName = upImgTr.data.filename;
                    } else if (documentExtensions.includes(item.extname)) {
                      const upDocTr = await upload(item, uploadPathTr);
                      fileName = upDocTr.data?.filename;
                    } else {
                      throw new Error(`Jenis file tidak didukung: ${item.extname}`);
                    }
                  }

                  // Jika ID ada, lakukan pembaruan
                  if (detailTr.id_ikn_data_file_tr) {
                    await prisma.tb_ikn_data_file_tr.update({
                      where: { id_ikn_data_file_tr: parseInt(detailTr.id_ikn_data_file_tr) },
                      data: {
                        nama: fileName || undefined, // Hanya ubah nama jika ada file baru
                        judul: detailTr.judul,
                        kd_bahasa: detailTr.kd_bahasa || 'en',
                      },
                    });
                  } else {
                    // Jika ID tidak ada, buat baru
                    await prisma.tb_ikn_data_file_tr.create({
                      data: {
                        id_ikn_data_file:idInserFile[0],
                        id_ikn_data_file_tr: fileIds[index],
                        nama: fileName,
                        judul: detailTr.judul,
                        kd_bahasa: detailTr.kd_bahasa || 'en',
                      },
                    });
                    idInserFile = idInserFile.slice(1)
                  }
                });

                // Tunggu semua file terkait selesai diproses
                await Promise.all(uploadPromisesUpImgTr);
            }

              response.status(201).json({ success: true,data:update })
          // })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_ikn_data.delete({
        where: {
          id_ikn_data: Id,
        },
      })
      return response.status(200).send({ success: true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }

  /**
     * @change_status
     * @paramQuery status - status - @type(number) @required
     * @paramQuery keterangan - keterangan - @type(string)  @required
     */
    async change_status({ params, request, response ,auth}: HttpContext) {
        // Ambil dan parse ID dari parameter URL
        const Id = parseInt(params.id, 10);
        // Validasi ID
        if (isNaN(Id)) {
            return response.status(400).json({ error: 'Invalid ID provided' });
        }
        try {
            // Validasi input dengan validator yang telah dibuat
            const dataPost = await request.validateUsing(aproveValidator);

            // Update data di tb_sektor_nasional
            const update = await prisma.tb_ikn_data.update({
            where: {
                id_ikn_data: Id
            },
                data: {
                    status: dataPost.status
                },
            });
            // Berikan respon sukses dengan data update
            return response.status(201).json({ success: true, data: update });
        } catch (error) {
            // Tangani error dan kirimkan respon gagal
            return response.status(500).json({ success: false, message: error });
        }
    }



}
