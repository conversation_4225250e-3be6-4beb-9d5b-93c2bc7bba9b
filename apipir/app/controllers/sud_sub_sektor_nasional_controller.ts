import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../helpers/model_helper.js';

import prisma from '../lib/prisma.js';
import uploadFile from '../helpers/file_uploader.js';



export default class SUDSubSektorDaerahController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_sub_sektor_nasional: { column: 'id_sub_sektor_nasional', alias: 'id_sub_sektor_nasional', type: 'int' },
                        id_sektor_nasional: { column: 'id_sektor_nasional', alias: 'id_sektor_nasional', type: 'int' },
                        id_sub_sektor: { column: 'id_sub_sektor', alias: 'id_sub_sektor', type: 'int' },
                        deskripsi_singkat: { column: 'deskripsi_singkat', alias: 'deskripsi_singkat', type: 'string' },
                        deskripsi: { column: 'deskripsi', alias: 'deskripsi', type: 'string' },
                        file_icon: { column: 'file_icon', alias: 'file_icon', type: 'string' },
                        file_image: { column: 'file_image', alias: 'file_image', type: 'string' },
                        status: { column: 'status', alias: 'status', type: 'int' },
                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                if (request.input('is_simple') || request.input('is_simple') == 'true') {
                        const params = request.qs();
                        let idSektorDaerah = params['id_sektor_nasional'];

                        const data = await prisma.tb_sub_sektor_nasional.findMany({
                                where: {
                                        id_sektor_nasional: parseInt(idSektorDaerah)
                                },
                                select: {
                                        sektor: true
                                },
                        });

                        return response.status(200).json({
                                success: true,
                                message: 'Success Retrieve Data',
                                data: data,
                        })
                }

                let isAll = false;

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        include: {
                                tb_komoditi_daerah: true,
                                tb_sektor_daerah: true,
                                tb_sub_sektor_nasional: true,
                                tb_peluang_daerah: true,
                        }
                }


                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                options['where'] = modelWhereAnd((await this.schemaData()).columns, queryParams);

                try {
                        const data = await prisma.tb_sub_sektor_nasional.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_sub_sektor_nasional.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }

        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = { 
                        include: {
                                        tb_komoditi_daerah: true,
                                        tb_sektor_daerah: true,
                                        tb_sub_sektor_nasional: true,
                                        tb_peluang_daerah: true,
                                } 
                        };

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_sub_sektor_daerah': parseInt(params.id)
                }

                const data = await prisma.tb_sub_sektor_nasional.findFirst(options);

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }
}