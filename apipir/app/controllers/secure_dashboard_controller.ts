import type { HttpContext } from '@adonisjs/core/http'
import { SecureDatabaseHelper } from '#helpers/secure_database_helper'

export default class SecureDashboardController {

  /**
   * Secure method to get detail province data
   * Replaces the vulnerable getDetailDetailProv method
   */
  async getDetailDetailProv({ request, response }: HttpContext) {
    try {
      // Extract and validate parameters
      const year = request.input('year')
      const search = request.input('search', '')
      const pageSize = request.input('pageSize', 10)
      const page = request.input('page', 1)
      const sts = request.input('sts', '= 0')
      const table = request.input('table')
      const joinTable = request.input('join_table', 'tb_demografi_provinsi')

      // Validate required parameters
      if (!year) {
        return response.status(400).json({
          success: false,
          error: 'Year parameter is required'
        })
      }

      // Prepare parameters for secure helper
      const params = {
        year: parseInt(year, 10),
        search: search || '',
        pageSize: parseInt(pageSize, 10),
        page: parseInt(page, 10),
        sts: sts
      }

      // Parse table configuration if provided
      let tableConfig = null
      if (table) {
        try {
          tableConfig = typeof table === 'string' ? JSON.parse(table) : table
        } catch (error) {
          return response.status(400).json({
            success: false,
            error: 'Invalid table configuration'
          })
        }
      }

      // Use secure database helper
      const result = await SecureDatabaseHelper.getDetailDetailProv(
        params,
        tableConfig,
        joinTable
      )

      return response.json(result)

    } catch (error) {
      console.error('Error in getDetailDetailProv:', error)
      return response.status(500).json({
        success: false,
        error: 'Internal server error'
      })
    }
  }

  /**
   * Secure method to get detail kabkot data
   * Replaces the vulnerable getDetailDetailKabkot method
   */
  async getDetailDetailKabkot({ request, response }: HttpContext) {
    try {
      // Extract and validate parameters
      const year = request.input('year')
      const search = request.input('search', '')
      const pageSize = request.input('pageSize', 10)
      const page = request.input('page', 1)
      const sts = request.input('sts', '= 0')
      const table = request.input('table')
      const joinTable = request.input('join_table', 'tb_demografi_kabkot')

      // Validate required parameters
      if (!year) {
        return response.status(400).json({
          success: false,
          error: 'Year parameter is required'
        })
      }

      // Prepare parameters for secure helper
      const params = {
        year: parseInt(year, 10),
        search: search || '',
        pageSize: parseInt(pageSize, 10),
        page: parseInt(page, 10),
        sts: sts
      }

      // Parse table configuration if provided
      let tableConfig = null
      if (table) {
        try {
          tableConfig = typeof table === 'string' ? JSON.parse(table) : table
        } catch (error) {
          return response.status(400).json({
            success: false,
            error: 'Invalid table configuration'
          })
        }
      }

      // Use secure database helper
      const result = await SecureDatabaseHelper.getDetailDetailKabkot(
        params,
        tableConfig,
        joinTable
      )

      return response.json(result)

    } catch (error) {
      console.error('Error in getDetailDetailKabkot:', error)
      return response.status(500).json({
        success: false,
        error: 'Internal server error'
      })
    }
  }

  /**
   * Secure method to get infrastructure update data
   * Replaces the vulnerable getDetailInfrastrukturUpdate method
   */
  async getDetailInfrastrukturUpdate({ request, response }: HttpContext) {
    try {
      // Extract and validate parameters
      const search = request.input('search', '')
      const pageSize = request.input('pageSize', 10)
      const page = request.input('page', 1)
      const table = request.input('table')
      const currentYear = new Date().getFullYear()

      // Prepare parameters for secure helper
      const params = {
        year: currentYear,
        search: search || '',
        pageSize: parseInt(pageSize, 10),
        page: parseInt(page, 10),
        sts: '> 0' // Default status condition
      }

      // Parse table configuration if provided
      let tableConfig = null
      if (table) {
        try {
          tableConfig = typeof table === 'string' ? JSON.parse(table) : table
        } catch (error) {
          return response.status(400).json({
            success: false,
            error: 'Invalid table configuration'
          })
        }
      }

      // Use secure database helper
      const result = await SecureDatabaseHelper.getDetailInfrastrukturUpdate(
        params,
        tableConfig,
        currentYear
      )

      return response.json(result)

    } catch (error) {
      console.error('Error in getDetailInfrastrukturUpdate:', error)
      return response.status(500).json({
        success: false,
        error: 'Internal server error'
      })
    }
  }

  /**
   * Secure method for general dashboard statistics
   * Uses parameterized queries only
   */
  async getDashboardStats({ request, response }: HttpContext) {
    try {
      const year = request.input('year', new Date().getFullYear())
      
      // Validate year parameter
      const validatedYear = parseInt(year, 10)
      if (isNaN(validatedYear) || validatedYear < 2000 || validatedYear > 2100) {
        return response.status(400).json({
          success: false,
          error: 'Invalid year parameter'
        })
      }

      // Use only safe, parameterized queries for statistics
      // This is a placeholder - implement actual statistics queries using Prisma
      const stats = {
        totalProvinces: 34,
        totalKabkot: 514,
        year: validatedYear,
        lastUpdated: new Date().toISOString()
      }

      return response.json({
        success: true,
        data: stats
      })

    } catch (error) {
      console.error('Error in getDashboardStats:', error)
      return response.status(500).json({
        success: false,
        error: 'Internal server error'
      })
    }
  }
} 