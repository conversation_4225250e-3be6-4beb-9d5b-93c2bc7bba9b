import env from '#start/env';
import type { HttpContext } from '@adonisjs/core/http'
import { createCipheriv, createDecipheriv, randomBytes } from 'crypto';

export default class GeoportalsController {
  private async fetchLoginToken() {
    const payload = { username: env.get('USER_GP'), password: env.get('PASSWORD_GP') };
    const url_login = env.get('URL_LOGIN_GP') || 'https://';
    console.log(payload)
    console.log(url_login)
    try {
      const loginGP = await fetch(url_login, {
        method: 'POST',
        mode : 'cors',
        headers: { 'Content-Type': 'application/json', "accept": "application/json" },
        body: JSON.stringify(payload),
      });
      if (!loginGP.ok) throw new Error('Login failed');
      return await loginGP.json();
    } catch (error) {
      throw new Error(`Login error: ${error.message}`);
    }
  }
  private encryptToken(token: string) {
    const key = Buffer.from(env.get('ENCRYPTION_KEY'), 'hex'); // Kunci harus 32 byte untuk AES-256
    const iv = randomBytes(12); // IV harus 12 byte untuk AES-GCM
    const cipher = createCipheriv('aes-256-gcm', key, iv);

    let encrypted = cipher.update(token, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    const authTag = cipher.getAuthTag().toString('hex');

    // Gabungkan IV, authTag, dan data terenkripsi
    return `${iv.toString('hex')}:${authTag}:${encrypted}`;
  }
  public async get_login({ response }: HttpContext) {
    try {
      const loginResponse = await this.fetchLoginToken();
      if (loginResponse.status !== 'success') {
        return response.status(401).json(loginResponse);
      }

      // Enkripsi accessToken
      const encryptedToken = this.encryptToken(loginResponse.accessToken);
      return response.json({
        status: 'success',
        token : encryptedToken,
      });
    } catch (error) {
      return response.status(500).json({ status: 'error', message: error.message });
    }
  }

  public async get_map({ params, response }: HttpContext) {
    const layeruid = params.layeruid || 'xxxxxxxxxxxxxxxx';
    const url_map = env.get('URL_MAP_GP')?.replace('{layeruid}', layeruid) || 'https://';

    try {
      const loginResponse = await this.fetchLoginToken();
      if (loginResponse.status === 'success') {
        const mapGP = await fetch(url_map, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${loginResponse.accessToken}`,
          },
        });
        if (!mapGP.ok) throw new Error('Map fetch failed');
        const dataMap = await mapGP.json();
        return response.json(dataMap);
      }
      return response.status(401).json(loginResponse);
    } catch (error) {
      return response.status(500).json({ status: 'error', message: error.message });
    }
  }
}