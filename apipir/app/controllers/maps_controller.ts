import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'

import pLimit from 'p-limit';
import env from '#start/env';
export default class MapsController {

    public async get_icon_sektor_pid(params:HttpContext) {
        const datas = await prisma.tb_sektor_nasional_ref.findMany()
        const data = datas.map((item) => {
            return {
                id_sektor_nasional_ref: item.id_sektor_nasional_ref,
                icon: `${env.get('APP_URL')}/uploads/icon-web/${item.iconmap}`,
                nama_sektor: item.nama
            }
        })
        return {
            success : true,
            data : data
        }
    }
    public async get_icon_sektor_ppi(params:HttpContext) {
        const datas = await prisma.tb_peluang_sektor.findMany()
        const data = datas.map((item) => {
            return {
                id_peluang_sektor: item.id_peluang_sektor,
                icon: `${env.get('APP_URL')}/uploads/icon-web/${item.iconmap}`,
                nama_sektor: item.nama
            }
        })
        return {
            success : true,
            data : data
        }
    }
    public async get_icon_komoditi(params:HttpContext) {
        const datas = await prisma.tb_sub_sektor_nasional_ref.findMany()
        const data = datas.map((item) => {
            return {
                id_sub_sektor: item.id_sub_sektor,
                icon: `${env.get('APP_URL')}/uploads/icon-web/${item.iconmap}`,
                nama_sektor: item.nama
            }
        })
        return {
            success : true,
            data : data
        }
    }
  


}