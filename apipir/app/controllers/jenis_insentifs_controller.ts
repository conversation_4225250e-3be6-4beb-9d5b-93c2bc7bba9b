import type { HttpContext } from '@adonisjs/core/http'
import { configUpload, get_paginate, img_to_webp, upload, upload_img_to_webp } from '../helpers/global_helper.js';
import { createSektorNasionalValidator } from '#validators/sektor_nasional';
import { createJenisInsentifValidator } from '#validators/jenis_insentif';
import env from '#start/env';
import { aprovePdrbValidator, aproveValidator } from '../validators/aprove.js';
import striptags from 'striptags';
import prisma from '../lib/prisma.js'

export default class JenisInsentifsController {

  /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response,request}:HttpContext) {
    const parameters =  request.qs()
    let order = parameters.order
    const by = ['asc','desc'].includes(parameters.by) ? parameters.by : 'asc'
    let orderBy={}
    const paramList = ['status']
    if (order != undefined && paramList.includes(order)) {
            orderBy = {[order]:by}
    }
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {

        };

        if (search !== ',' && search !== '') {
        searchCondition = {
            OR: [
            { keterangan: { contains: search, mode: 'insensitive' } }, // Search in keterangan
            { nama: { contains: search, mode: 'insensitive' } }, // Search in nama
            {
                tb_jenis_insentif_tr: {
                some: {
                    OR: [
                    { nama: { contains: search, mode: 'insensitive' } }, // Search in tb_jenis_insentif_tr.nama
                    { keterangan: { contains: search, mode: 'insensitive' } }, // Search in tb_jenis_insentif_tr.keterangan
                    ],
                },
                },
            },
            ],
        };
        }
    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_jenis_insentif.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });


    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_jenis_insentif.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      orderBy:orderBy,
      where: searchCondition, // Apply searchCondition only if it's not empty
          include:{
              tb_jenis_insentif_file:true,
              tb_jenis_insentif_file_tr:true,
              tb_jenis_insentif_tr:true,
          }
    });
    const data = datas.map((item) => {

      return {
        id_jenis_insetntif:item.id_jenis_insentif,
        nama :item.nama,
        keterangan : striptags(item.keterangan),
        nama_tr : item.tb_jenis_insentif_tr[0]?.nama,
        keterangan_tr:striptags(item.tb_jenis_insentif_tr[0]?.keterangan),
        status: item.status
      }
    })


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };
  }


    /**
     * @store
     * @paramQuery id_adm_kabkot - id_adm_kabkot - @type(number) @required
     * @paramQuery id_adm_provinsi - id_adm_provinsi - @type(number) @required
     * @paramQuery nama - id_sektor - @type(string)  @required
     * @paramQuery bidang_usaha - tahun - @type(string) @required
     * @paramQuery email - email - @type(string) @required
     * @paramQuery alamat - alamat - @type(string) @required
     * @paramQuery nama_kabkot - nama kabkot - @type(string)
     * @paramQuery nama_provinsi - nama provinsi - @type(string)
     * @paramQuery image - image - @type(file)
     * @paramQuery lon - lon - @type(number)
     * @paramQuery lat - lat - @type(number)
     */

    public async store({ request, response ,auth}: HttpContext) {

      const data = request.all();


      let datas = await request.validateUsing(createJenisInsentifValidator)
      let image_detail = request.input('image_detail')
      let image_detail_tr = request.input('image_detail_tr')

        try {
            const configImg = await configUpload('img');
            const image = request.files('image', configImg);
            const image_tr = request.files('image_tr', configImg);
            // Jalankan semua operasi database dalam transaction Prisma
            await prisma.$transaction(async (prisma) => {
                // Insert pertama untuk tb_sektor_nasional
                const dataPost ={
                    nama : datas.nama,
                    keterangan : datas.keterangan,
                    tipe : datas.tipe,
                    urutan : datas.urutan,
                    status : 0,
                    id_kategori_insentif : datas.id_kategori_insentif
                }
                const insert = await prisma.tb_jenis_insentif.create({ data: dataPost });
                const dataPostTr ={
                    id_jenis_insentif : insert.id_jenis_insentif,
                    kd_bahasa :'en',
                    nama : datas.nama_tr,
                    keterangan : datas.keterangan_tr
                }

                const insertTr = await prisma.tb_jenis_insentif_tr.create({ data: dataPostTr });

                if (typeof image_detail === 'string') {
                    image_detail = JSON.parse(image_detail);
                }
                if (image && Array.isArray(image_detail)) {

                    // Jika panjang array JSON dan file gambar tidak sesuai, kembalikan error
                    if (image.length !== image_detail.length) {
                        return response.badRequest('Jumlah gambar dan detail tidak sesuai');
                    }

                    const uploadPromisesUpImg = image.map(async (item, index) => {
                        const uploadPath = `uploads/jenis_insentif/${insert.id_jenis_insentif}`;
                        const upImg = await img_to_webp(item, uploadPath); // Mengupload gambar ke WebP
                        const fileName = upImg.data.filename; // Mendapatkan nama file gambar

                        // Menghubungkan gambar yang diupload dengan detail dari JSON
                        const detail = image_detail[index]; // Mendapatkan detail JSON berdasarkan index

                        // Lakukan proses insert ke database atau logika apapun yang kamu inginkan
                        await prisma.tb_jenis_insentif_file.create({
                            data: {
                                id_jenis_insentif : insert.id_jenis_insentif,
                                tipe:1,
                                nama: fileName, // Nama dari JSON
                                judul: detail.judul, // Judul dari JSON
                                keterangan : '-',
                            },
                        });
                    });
                    await Promise.all(uploadPromisesUpImg); // Menjalankan semua upload dan insert secara paralel
                }

                if (typeof image_detail_tr === 'string') {
                    image_detail_tr = JSON.parse(image_detail_tr);
                }
                if (image_tr && Array.isArray(image_detail_tr)) {
                    // Jika panjang array JSON dan file gambar tidak sesuai, kembalikan error
                    if (image_tr.length !== image_detail_tr.length) {
                        return response.badRequest('Jumlah gambar dan detail tidak sesuai');
                    }
                        const uploadPromisesUpImg = image_tr.map(async (item, index) => {
                        const uploadPath = `uploads/jenis_insentif/${insert.id_jenis_insentif}`;
                        const upImg = await img_to_webp(item, uploadPath); // Mengupload gambar ke WebP
                        const fileName = upImg.data.filename; // Mendapatkan nama file gambar

                        // Menghubungkan gambar yang diupload dengan detail dari JSON
                        const detail = image_detail_tr[index]; // Mendapatkan detail JSON berdasarkan index

                        // Lakukan proses insert ke database atau logika apapun yang kamu inginkan
                        await prisma.tb_jenis_insentif_file_tr.create({
                            data: {
                                id_jenis_insentif : insert.id_jenis_insentif,
                                tipe:1,
                                kd_bahasa:'en',
                                nama: fileName, // Nama dari JSON
                                judul: detail.judul, // Judul dari JSON
                                keterangan : '-',
                            },
                        });
                    });
                    await Promise.all(uploadPromisesUpImg); // Menjalankan semua upload dan insert secara paralel
                }

                response.status(201).json({ success: true,data:insert })
            });
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

     async show({ params,response }: HttpContext) {
        const { id } = params
        const Id = parseInt(id, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ success:false,error: 'Invalid ID provided' })
        }
        let datas = await prisma.tb_jenis_insentif.findUnique({
            where: {
                id_jenis_insentif: Id,
            },
            include:{
              tb_jenis_insentif_file:true,
              tb_jenis_insentif_file_tr:true,
              tb_jenis_insentif_tr:true,
          }
        })

        if(datas){
            if (datas?.tb_jenis_insentif_file.length > 0) {
                const file = datas?.tb_jenis_insentif_file.map((item) => {
                    return {
                        ...item,
                        nama: `${env.get('APP_URL')}/uploads/jenis_insentif/${datas?.id_jenis_insentif}/${item?.nama}`,
                    }
                })
                datas.tb_jenis_insentif_file = file
            }

            if (datas?.tb_jenis_insentif_file_tr.length > 0) {
                const file = datas?.tb_jenis_insentif_file_tr.map((item) => {
                    return {
                        ...item,
                        nama: `${env.get('APP_URL')}/uploads/jenis_insentif/${datas?.id_jenis_insentif}/${item?.nama}`,
                    }
                })
                datas.tb_jenis_insentif_file_tr = file
            }
        }

        return {
            success : true,
            data : datas
        }
    }

    public async update({ params,request, response ,auth}: HttpContext) {
        const Id = parseInt(params.id, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        let datas = await request.validateUsing(createJenisInsentifValidator)
        let image_detail = request.input('image_detail')
        let image_detail_tr = request.input('image_detail_tr')
        if (typeof image_detail_tr === 'string') {
            image_detail_tr = JSON.parse(image_detail_tr)
        }

        try {
            const configImg = await configUpload('img');
            const image = request.files('image', configImg);
            const image_tr = request.files('image_tr', configImg);
            // Jalankan semua operasi database dalam transaction Prisma
            await prisma.$transaction(async (prisma) => {
                const dataPost ={
                    nama : datas.nama,
                    keterangan : datas.keterangan,
                    tipe : datas.tipe,
                    urutan : datas.urutan,
                    status : 0,
                    id_kategori_insentif : datas.id_kategori_insentif
                }

                const update = await prisma.tb_jenis_insentif.update(
                    {
                        data: dataPost,
                        where:{
                            id_jenis_insentif : Id
                        }
                     });
                let tr = request.input('tr')
                if (typeof tr === 'string') {
                    tr = JSON.parse(tr);
                }
                const id_jenis_insentif_tr = parseInt(tr.id_jenis_insentif_tr)
                delete tr.id_jenis_insentif_tr
                const updateTr = await prisma.tb_jenis_insentif_tr.update({
                    where: {
                        id_jenis_insentif_tr: id_jenis_insentif_tr,
                    },
                    data: tr,
                })

                if (typeof image_detail === 'string') {
                    image_detail = JSON.parse(image_detail);
                }

                // return response.status(201).json({ success: true,data:potensi })
                if (image_detail && Array.isArray(image_detail)) {
                    // Ambil semua file terkait `id_jenis_insentif` dari database
                    const existingFiles = await prisma.tb_jenis_insentif_file.findMany({
                        where: { id_jenis_insentif: Id },
                    });

                    // ID file yang ada di database
                    const existingFileIds = existingFiles.map(file => file.id_jenis_insentif_file);

                    // ID file dari detail yang diberikan
                    const currentFileIds = image_detail
                        .filter(detail => parseInt(detail.id_jenis_insentif_file))
                        .map(detail => parseInt(detail.id_jenis_insentif_file));

                    // Cari file yang harus dihapus
                    const filesToDelete = existingFiles.filter(file => !currentFileIds.includes(file.id_jenis_insentif_file));
                    if (filesToDelete.length > 0) {
                        await prisma.tb_jenis_insentif_file.deleteMany({
                            where: {
                                id_jenis_insentif_file: { in: filesToDelete.map(file => file.id_jenis_insentif_file) },
                            },
                        });
                    }
                    // Proses setiap `image_detail`
                    for (const detail of image_detail) {
                        const matchingImage = image.find(im => im.clientName === detail.fileName);

                        if (matchingImage) {
                            // Jika ada file gambar yang cocok
                            const uploadPath = `uploads/jenis_insentif/${Id}`;
                            const upImg = await img_to_webp(matchingImage, uploadPath);

                            if (detail.id_jenis_insentif_file) {
                                // Update file yang sudah ada
                                await prisma.tb_jenis_insentif_file.update({
                                    where: { id_jenis_insentif_file: parseInt(detail.id_jenis_insentif_file) },
                                    data: {
                                        id_jenis_insentif: Id,
                                        tipe: 1,
                                        nama: upImg.data.filename,
                                        judul: detail.judul,
                                    },
                                });
                            } else {
                                // Tambahkan file baru
                                await prisma.tb_jenis_insentif_file.create({
                                    data: {
                                        id_jenis_insentif: Id,
                                        tipe: 1,
                                        nama: upImg.data.filename,
                                        judul: detail.judul,
                                        keterangan:'-'
                                    },
                                });
                            }
                        } else {
                            // Jika tidak ada file gambar yang cocok, hanya update tanpa upload
                            if (detail.id_jenis_insentif_file) {
                                const fileExists = existingFileIds.includes(parseInt(detail.id_jenis_insentif_file));

                                if (!fileExists) {
                                    throw new Error(`id_jenis_insentif_file ${detail.id_jenis_insentif_file} not found`);
                                }

                                await prisma.tb_jenis_insentif_file.update({
                                    where: { id_jenis_insentif_file: parseInt(detail.id_jenis_insentif_file) },
                                    data: {
                                        id_jenis_insentif: Id,
                                        tipe: 1,
                                        judul: detail.judul,
                                        keterangan: detail.keterangan,
                                    },
                                });
                            } else {
                                // Jika tidak ada id_jenis_insentif_file, file harus diunggah
                                return response.status(500).json({
                                    success: false,
                                    message: 'File Gambar Potensi Required!',
                                    data: detail,
                                });
                            }
                        }
                    }
                }


                if (typeof image_detail_tr === 'string') {
                    image_detail_tr = JSON.parse(image_detail_tr);
                }
                if (image_tr && Array.isArray(image_detail_tr)) {
                    // Ambil semua file terkait `id_jenis_insentif` dari database
                    const existingFiles = await prisma.tb_jenis_insentif_file_tr.findMany({
                        where: { id_jenis_insentif: Id },
                    });

                    // ID file yang ada di database
                    const existingFileIds = existingFiles.map(file => file.id_jenis_insentif_file_tr);

                    // ID file dari detail yang diberikan
                    const currentFileIds = image_detail_tr
                        .filter(detail => parseInt(detail.id_jenis_insentif_file_tr))
                        .map(detail => parseInt(detail.id_jenis_insentif_file_tr));

                    // Cari file yang harus dihapus
                    const filesToDelete = existingFileIds.filter(id => !currentFileIds.includes(id));
                    if (filesToDelete.length > 0) {

                        await prisma.tb_jenis_insentif_file_tr.deleteMany({
                            where: {
                                id_jenis_insentif_file_tr: { in: filesToDelete },
                            },
                        });
                    }


                    // Proses setiap `image_detail_tr`
                    for (const detail of image_detail_tr) {
                        const matchingImage = image_tr.find(im => im.clientName === detail.fileName);

                        if (matchingImage) {
                            // Jika ada file gambar yang cocok
                            const uploadPath = `uploads/jenis_insentif/${Id}`;
                            const upImg = await img_to_webp(matchingImage, uploadPath);

                            if (detail.id_jenis_insentif_file_tr) {
                                // Update file yang sudah ada
                                await prisma.tb_jenis_insentif_file_tr.update({
                                    where: { id_jenis_insentif_file_tr: parseInt(detail.id_jenis_insentif_file_tr) },
                                    data: {
                                        id_jenis_insentif: Id,
                                        tipe: 1,
                                        kd_bahasa: 'en',
                                        nama: upImg.data.filename,
                                        judul: detail.judul,
                                        keterangan: '-',
                                    },
                                });
                            } else {
                                // Tambahkan file baru
                                await prisma.tb_jenis_insentif_file_tr.create({
                                    data: {
                                        id_jenis_insentif: Id,
                                        tipe: 1,
                                        kd_bahasa: 'en',
                                        nama: upImg.data.filename,
                                        judul: detail.judul,
                                        keterangan: '-',
                                    },
                                });
                            }
                        } else {
                            // Jika tidak ada file gambar yang cocok, hanya update tanpa upload
                            if (detail.id_jenis_insentif_file_tr) {
                                const record = await prisma.tb_jenis_insentif_file_tr.findUnique({
                                    where: { id_jenis_insentif_file_tr: parseInt(detail.id_jenis_insentif_file_tr) },
                                    });

                                    if (!record) {
                                    throw new Error(`Record with id_jenis_insentif_file_tr ${detail.id_jenis_insentif_file_tr} not found`);
                                    }

                                await prisma.tb_jenis_insentif_file_tr.update({
                                    where: { id_jenis_insentif_file_tr: parseInt(detail.id_jenis_insentif_file_tr) },
                                    data: {
                                        id_jenis_insentif: Id,
                                        tipe: 1,
                                        kd_bahasa: 'en',
                                        judul: detail.judul,
                                        keterangan: detail.keterangan || '-',
                                    },
                                });
                            } else {
                                // Jika tidak ada id_jenis_insentif_file_tr, file harus diunggah
                                return response.status(500).json({
                                    success: false,
                                    message: 'File Gambar Potensi Required!',
                                    data: detail,
                                });
                            }
                        }
                    }
                }


                response.status(201).json({ success: true,data:update })
            });
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }


    async destroy({ params,response }: HttpContext) {
        const Id = parseInt(params.id, 10)
        if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
        }
        try {
        const deletePost = await prisma.tb_jenis_insentif.delete({
            where: {
                id_jenis_insentif: Id,
            },
        })
        return response.status(200).send({ success: true,data:deletePost})
        } catch (error) {
        return response.status(500).send({ error: 'Error deleting data' })
        }
    }

     /**
     * @change_status
     * @paramQuery status - status - @type(number) @required
     * @paramQuery keterangan - keterangan - @type(string)  @required
     */
    async change_status({ params, request, response ,auth}: HttpContext) {
        // Ambil dan parse ID dari parameter URL
        const Id = parseInt(params.id, 10);

        // Validasi ID
        if (isNaN(Id)) {
            return response.status(400).json({ error: 'Invalid ID provided' });
        }
        try {
            // Validasi input dengan validator yang telah dibuat
            const dataPost = await request.validateUsing(aproveValidator);

            // Update data di tb_sektor_nasional
            const update = await prisma.tb_jenis_insentif.update({
            where: {
                id_jenis_insentif: Id
            },
                data: {
                    status: parseInt(dataPost.status)
                },
            });
            await auth.check()

            // Insert ke tb_sektor_nasional_status
            const insert = await prisma.tb_jenis_insentif_status.create({
                data: {
                    id_jenis_insentif: Id,
                    status: dataPost.status,
                    status_proses: dataPost.status,
                    keterangan: dataPost.keterangan,
                    created_by:auth.user?.id,
                    updated_by:auth.user?.id,
                    created_date:new Date()
                },
            });
            // Berikan respon sukses dengan data update
            return response.status(201).json({ success: true, data: update });
        } catch (error) {
            // Tangani error dan kirimkan respon gagal
            return response.status(500).json({ success: false, message: error.message });
        }
    }
}

