import env from '#start/env';
import { tesValidator } from '#validators/auth';
import type { HttpContext } from '@adonisjs/core/http'
import { numberFormat, numberFormatLengkap, numberFormatLengkapEn, numberFormatRpLengkap, numberFormatRpSingkatan, rupiah } from '../helpers/global_helper.js';
import { resourceUsage } from 'process';
import { createPostFaqValidator } from '#validators/post';
import { log } from 'console';
import striptags from 'striptags';
import prisma from '../lib/prisma.js'
export default class FeController {

    public async get_ikn({ response,request }:HttpContext) {
        const parameters = request.qs()
        const ikns = await prisma.tb_ikn.findMany({
            include:{
                tb_ikn_tr:true
            }
        })
        const iknData = await prisma.tb_ikn_data.findMany({
            include:{
                tb_ikn_data_file:{
                    include:{
                        tb_ikn_data_file_tr:true
                    }
                },
                tb_ikn_data_tr:true
            }
        })
        const peluangs = await prisma.tb_peluang_kabkot.findMany({
            select:{
                id_adm_kabkot:true,
                id_peluang_kabkot:true,
                nama:true,
                nilai_investasi:true,
                nilai_irr:true,
                nilai_npv:true,
                nilai_pp:true,
                project_status_enum:true,
                is_ipro:true,
                tahun:true,
                tb_peluang_sektor:{
                    select:{
                        kategori_sektor:{
                            select:{
                                nama:true,
                                id_kategori_sektor:true
                            }
                        },
                        nama:true,
                        icon:true
                    }
                },
                tb_peluang_kabkot_file:{
                    select:{
                        nama:true
                    },
                    where:{
                        tipe:1
                    }
                },
                tb_adm_kabkot:{
                    select:{
                        nama:true,
                        tb_adm_provinsi:{
                            select:{
                                nama:true,
                                tb_adm_provinsi_tr:{
                                    select:{
                                        nama:true
                                    }
                                }
                            }
                        },
                        tb_adm_kabkot_tr:{
                            select:{
                                nama:true
                            }
                        }
                    }
                },
                tb_peluang_kabkot_tr:{
                    select:{
                        nama:true
                    }
                }
            },
            where: {
                    status:"99",
                    id_prioritas:1,
                    is_ikn:true ,
            },
            orderBy:{
                nilai_investasi:'desc'
            }
        })
        const peluang = peluangs.map((item) => {
            const status = {
                    2: 'DIMINATI',
                    7: 'SOLD',
                };

                let status_proyek = '';

                if (item.project_status_enum !== '') {
                status_proyek = status[item.project_status_enum] ;
            }

            let kabkot = item.tb_adm_kabkot.nama
            let prov = item.tb_adm_kabkot.tb_adm_provinsi.nama
            let nama = item.nama
            if (parameters.en) {
                kabkot = item.tb_adm_kabkot.tb_adm_kabkot_tr[0]?.nama || item.tb_adm_kabkot.nama
                prov = item.tb_adm_kabkot.tb_adm_provinsi.tb_adm_provinsi_tr[0].nama || item.tb_adm_kabkot.tb_adm_provinsi.nama
                nama = item.tb_peluang_kabkot_tr[0]?.nama || item.nama
            }

            return {
                id_peluang: item.id_peluang_kabkot,
                id_adm_provinsi: parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                id_adm_kabkot: item.id_adm_kabkot,
                nama_kabkot: kabkot,
                nama_provinsi: prov,
                nama: nama,
                nilai_irr: `${item.nilai_irr}%`,
                nilai_investasi: numberFormatRpSingkatan(item.nilai_investasi),
                nilai_npv: numberFormatRpSingkatan(item.nilai_npv),
                nilai_pp: item.nilai_pp,
                id_kategori_sektor: item.tb_peluang_sektor.kategori_sektor?.id_kategori_sektor,
                nama_sektor: item.tb_peluang_sektor.kategori_sektor?.nama,
                nama_sektor_peluang: item.tb_peluang_sektor.nama,
                project_status_enum: item.project_status_enum,
                status_proyek, // Properti status_proyek ditambahkan (opsional, jika dibutuhkan)
                status: item.is_ipro ? 'IPRO' : 'PPI',
                icon: `${env.get('APP_URL')}/uploads/icon-web/${item.tb_peluang_sektor.icon}`,
                image: `${env.get('APP_URL')}/uploads/peluang/${item.id_adm_kabkot}/peluang/${item.tahun}/1/${item.tb_peluang_kabkot_file[0]?.nama || 'default.jpg'}`, // Fallback untuk nama file
            };
        })

        let ikn
        if (!parameters.en) {
            ikn=  {
                nama:ikns[0].nama,
                lon:ikns[0].lon,
                lat:ikns[0].lat,
                deskripsi:ikns[0].deskripsi,
                logo:`${env.get('APP_URL')}/uploads/ikn/${ikns[0].file_logo}`,
                image:`${env.get('APP_URL')}/uploads/ikn/${ikns[0].file_image}`,
                vidio:`${env.get('APP_URL')}/uploads/ikn/${ikns[0].file_video}`,
                lokasi : 'Penajam Paser Utara, Kalimantan Timur',
                icon_lokasi : `${env.get('APP_URL')}/uploads/icon-web/Lokasi_56_putih.svg`
            }
        } else {
            ikn=  {
                nama:ikns[0]?.tb_ikn_tr[0]?.nama || ikns[0]?.nama,
                lon:ikns[0].lon,
                lat:ikns[0].lat,
                deskripsi:ikns[0].tb_ikn_tr[0]?.deskripsi || ikns[0]?.nama,
                logo:`${env.get('APP_URL')}/uploads/ikn/${ikns[0].file_logo}`,
                image:`${env.get('APP_URL')}/uploads/ikn/${ikns[0].file_image}`,
                vidio:`${env.get('APP_URL')}/uploads/ikn/${ikns[0].file_video}`,
                lokasi : 'Penajam Paser Utara, Kalimantan Timur',
                icon_lokasi : `${env.get('APP_URL')}/uploads/icon-web/Lokasi_56_putih.svg`
            }
        }


        const galery = await prisma.tb_ikn_file.findMany({})
        const galeri = galery.map((item) => {
            return {
                image:`${env.get('APP_URL')}/uploads/ikn/${item.nama}`
            }
        })
        let peraturan = []
        let profil = []
        let indikator = []
        iknData.map((item) => {
            if (item.jenis == 1) {
                if (!parameters.en) {
                    profil.push({
                        nama :item.nama,
                        deskripsi:item.deskripsi
                    })
                } else {
                    profil.push({
                        nama :item.tb_ikn_data_tr[0]?.nama || item.nama,
                        deskripsi:
                        item.tb_ikn_data_tr[0]?.deskripsi !== '<p><br></p>' &&
                        item.tb_ikn_data_tr[0]?.deskripsi !== undefined
                            ? item.tb_ikn_data_tr[0]?.deskripsi
                            : item.deskripsi,
                    })
                }
            } else if(item.jenis == 2) {
                if (!parameters.en) {

                    peraturan.push({
                        nama:item.nama,
                        file:`${env.get('APP_URL')}/uploads/ikn/${item.tb_ikn_data_file[0]?.nama}`
                    })
                }else{
                    peraturan.push({
                        nama: item.tb_ikn_data_tr[0]?.nama || item.nama,
                        file: item.tb_ikn_data_file[0]?.tb_ikn_data_file_tr[0]?.nama
                            ? `${env.get('APP_URL')}/uploads/ikn/${item.tb_ikn_data_file[0]?.tb_ikn_data_file_tr[0]?.nama}`
                            : `${env.get('APP_URL')}/uploads/ikn/${item.tb_ikn_data_file[0]?.nama}`,
                    });
                }
            }else{
                if (!parameters.en) {
                    indikator.push({
                        nama:item.nama,
                        deskripsi:item.deskripsi,
                    })
                }else{
                    indikator.push({
                        nama:item.tb_ikn_data_tr[0]?.nama || item.nama,
                        deskripsi:item.tb_ikn_data_tr[0]?.deskripsi !== '<p><br></p>' &&
                        item.tb_ikn_data_tr[0]?.deskripsi !== undefined
                            ? item.tb_ikn_data_tr[0]?.deskripsi
                            : item.deskripsi,
                    })
                }
            }
        })
        return {
            success :true,
            data:{
                    ikn,
                    indikator,
                    peluang,
                    peraturan,
                    profil,
                    galeri
            }
        }
    }

    async detail_sektor_nasional({params,response,request}: HttpContext) {
        const Id = parseInt(params.id_sektor, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }
        const parameters = request.qs()
        const data = await prisma.$queryRaw`
            WITH RankedData AS (
                SELECT
                nas.id_sektor,
                nas.nama AS nama_sektor,
                nast.nama AS nama_sektor_tr,
                nv.nama AS nama_detail,
                nvt.nama AS nama_detail_tr,
                nvd.tahun,
                nvd.numeric_value,
                nv.satuan,
                ROW_NUMBER() OVER (PARTITION BY nv.nama ORDER BY nvd.tahun DESC) AS rn
                FROM tb_sektor_nasional_ref nas
                JOIN tb_sektor_nasional_value nv
                ON nv.id_sektor_nasional = nas.id_sektor
                JOIN tb_sektor_nasional_value_tr nvt
                ON nv.id_sektor_nasional_value = nvt.id_sektor_nasional_value
                JOIN tb_sektor_nasional_value_detail nvd
                ON nvd.id_sektor_nasional_value = nv.id_sektor_nasional_value
                JOIN tb_sektor_nasional_ref_tr nast
                ON nas.id_sektor = nast.id_sektor
                WHERE nas.id_sektor = ${Id}
            )
            SELECT
                id_sektor,
                nama_sektor,
                nama_sektor_tr,
                nama_detail,
                nama_detail_tr,
                tahun,
                numeric_value,
                satuan
            FROM RankedData
            WHERE rn = 1;
            `;


        const nilai = data.map(item => {
            let nilai = numberFormat(item.numeric_value)
            if(item.satuan == 'USD'){
                nilai = numberFormatLengkap(item.numeric_value)
            }else if(item.satuan == 'rupiah'){
                nilai = numberFormatLengkap(item.numeric_value)

            }else if(item.satuan == '%'){
                nilai = item.numeric_value
            }

            return {
                id_sektor:  item.id_sektor,
                nama_sektor: parameters.en ? item.nama_sektor_tr : item.nama_sektor_tr,
                nama_detail: parameters.en ? item.nama_detail_tr : item.nama_detail,
                tahun: item.tahun,
                numeric_value: nilai,
                satuan: item.satuan
            }
        })


        const sektors = await prisma.tb_sektor_nasional.findFirst({
            where: {
                id_sektor_nasional: Id
            },
            include:{
                sektor:{
                    include:{
                        sub_sektor_nasional:{
                            include:{
                                komoditi_nasional: {
                                    where:{
                                        status:99
                                    },
                                    include:{
                                        komoditi_nasional_ref:{
                                            include:{
                                                tb_komoditi_nasional_ref_tr:true,
                                                tb_komoditi_daerah:{
                                                    include:{
                                                        tb_komoditi_daerah_tr:true
                                                    }
                                                }
                                            }
                                        },
                                        komoditi_nasional_file:{
                                            include:{
                                                komoditi_nasional_file_tr:true
                                            }
                                        },
                                        komoditi_nasional_value:{
                                            include:{
                                                komoditi_nasional_value_detail:{
                                                    orderBy:{
                                                        tahun:'desc'
                                                    },
                                                    take:1
                                                },
                                                komoditi_nasional_value_tr:true
                                            }
                                        },
                                        komoditi_nasional_tr:true,
                                        
                                    }
                                },
                                sub_sektor_ref:{
                                    include:{
                                        sub_sektor_nasional_ref_tr:true
                                    }
                                },
                                sub_sektor_nasional_file:{
                                    include:{
                                        sub_sektor_nasional_file_tr:true
                                    }
                                },
                                sub_sektor_nasional_tr:true
                            }
                        },
                        sektor_nasional_tr:true
                    }
                },
                sektor_nasional_file:{
                    include:{
                        sektor_nasional_file_tr:true
                    }
                },
                sektor_nasional_insentif:
                {
                    include:{
                        sektor_nasional_insentif_file:{
                            include:{
                                tb_sektor_nasional_insentif_file_tr:true
                            }
                        },
                        sektor_nasional_insentif_tr:true
                    }
                },
                sektor_nasional_tr:true
            }
        })
        let detail
        let potensi
        let insentif
        let sub_sektor
        let diagram
        if(sektors){
            // return sektors?.sektor.sub_sektor_nasional
            detail = {
                nama_sektor : parameters.en ? sektors.sektor.sektor_nasional_tr?.nama : sektors.sektor.nama,
                deskripsi : parameters.en ? striptags(sektors.sektor_nasional_tr[0]?.deskripsi).replaceAll('&nbsp;',' ') : striptags(sektors?.deskripsi).replaceAll('&nbsp;',' '),
                file_icon : `${env.get('APP_URL')}/uploads/icon-web/${sektors?.sektor.icon}` ,
                image :  parameters.en ?
                             `${env.get('APP_URL')}/uploads/sektor/${sektors?.sektor_nasional_tr[0]?.file_image}`
                                : `${env.get('APP_URL')}/uploads/sektor/${sektors?.file_image}` ,
            }

            if (!parameters.en) {
                potensi = {
                    judul : sektors.sektor_nasional_file[0]?.judul,
                    deskripsi: striptags(sektors.potensi_pasar).replaceAll('&nbsp;',' '),
                    image : `${env.get('APP_URL')}/uploads/sektor/${sektors?.sektor_nasional_file[0]?.nama}` ,
                }
            } else {
                potensi = {
                    deskripsi: striptags(sektors.sektor_nasional_tr[0].potensi_pasar).replaceAll('&nbsp;',' ') || striptags(sektors.potensi_pasar).replaceAll('&nbsp;',' '),
                    judul : sektors.sektor_nasional_file[0]?.sektor_nasional_file_tr[0].judul ?
                                sektors.sektor_nasional_file[0]?.sektor_nasional_file_tr[0].judul
                                :sektors.sektor_nasional_file[0]?.judul,
                    image : sektors.sektor_nasional_file[0]?.sektor_nasional_file_tr[0].nama ?
                                 `${env.get('APP_URL')}/uploads/sektor/${sektors?.sektor_nasional_file[0]?.sektor_nasional_file_tr[0].nama}`
                                : `${env.get('APP_URL')}/uploads/sektor/${sektors?.sektor_nasional_file[0]?.nama}` ,
                }
            }


            insentif = sektors.sektor_nasional_insentif.map((item) => {
                if (!parameters.en) {
                    if (item.sektor_nasional_insentif_file.length > 0) {

                        return {
                            judul:item.sektor_nasional_insentif_file[0]?.judul,
                            nama:item.nama,
                            file:`${env.get('APP_URL')}/uploads/sektor/${item.sektor_nasional_insentif_file[0]?.nama}` ,
                        }
                    }else{
                        return null
                    }
                } else {
                    if (item.sektor_nasional_insentif_file.length > 0) {
                        return {
                            judul: item.sektor_nasional_insentif_file[0]?.tb_sektor_nasional_insentif_file_tr[0]?.judul ?
                                    item.sektor_nasional_insentif_file[0]?.tb_sektor_nasional_insentif_file_tr[0]?.judul
                                    :item.sektor_nasional_insentif_file[0]?.judul,
                            nama: item.sektor_nasional_insentif_tr[0]?.nama|| item.nama,
                            file:item.sektor_nasional_insentif_file[0]?.tb_sektor_nasional_insentif_file_tr[0]?.nama ?
                                `${env.get('APP_URL')}/uploads/sektor/${item.sektor_nasional_insentif_file[0]?.tb_sektor_nasional_insentif_file_tr[0]?.nama}`
                                : `${env.get('APP_URL')}/uploads/sektor/${item.sektor_nasional_insentif_file[0]?.nama}` ,
                        }
                    }else{
                        return null
                    }
                }

            }).filter((item) => item !== null);

            // return sektors.sektor.sub_sektor_nasional[0]


            diagram = sektors.sektor.sub_sektor_nasional.map((item) => {
                return item.komoditi_nasional.map((item) => {
                    if (!parameters.en) {
                        const images = item.komoditi_nasional_file
                            .filter((file) => file.nama)
                            .map((file) => `${env.get('APP_URL')}/uploads/sektor/${file.nama}` ) ;

                        if (images.length > 0) {
                            return {
                                nama_sektor: item.komoditi_nasional_ref.nama,
                                image: images[0], // Mengubah array menjadi string tunggal
                            };
                        }
                    } else {
                        const images = item.komoditi_nasional_file
                            .filter((file) => file.komoditi_nasional_file_tr[0]?.nama)
                            .map((file) => `${env.get('APP_URL')}/uploads/sektor/${file.komoditi_nasional_file_tr[0]?.nama}` ) ;

                        if (images.length > 0) {
                            return {
                                nama_sektor: item.komoditi_nasional_ref.tb_komoditi_nasional_ref_tr?.nama || item.komoditi_nasional_ref.nama,
                                image: images[0], // Mengubah array menjadi string tunggal
                            };
                        }
                    }

                }).filter(Boolean); // Hapus item yang undefined
            }).flat(); // Ratakan array untuk menghilangkan array kosong

            sub_sektor = sektors.sektor.sub_sektor_nasional.map((item) => {
                return {
                    deskripsi : parameters.en ? item.sub_sektor_nasional_tr[0]?.deskripsi :  item.deskripsi,
                    nama_sektor : parameters.en ?  item.sub_sektor_ref.sub_sektor_nasional_ref_tr?.nama : item.sub_sektor_ref.nama,
                    file_icon : `${env.get('APP_URL')}/uploads/sektor/${item.file_icon}` ,
                    file_image :  `${env.get('APP_URL')}/uploads/sektor/${item.file_image}`  ,
                    image : item.sub_sektor_nasional_file.map((item) => {
                        if (!parameters.en) {
                            return `${env.get('APP_URL')}/uploads/sektor/${item.nama}`
                        } else {
                            return item.sub_sektor_nasional_file_tr[0]?.nama ?
                                    `${env.get('APP_URL')}/uploads/sektor/${item.sub_sektor_nasional_file_tr[0]?.nama}`
                                    : `${env.get('APP_URL')}/uploads/sektor/${item.nama}`
                        }
                    }),
                    //     return {
                    //         id_komoditi_nasional:item.id_sub_sektor,
                    komoditi: item.komoditi_nasional.map((item) =>{
                                let ret= {
                                    id_komoditi_nasional:item.id_komoditi_nasional,
                                    id_komoditi:item.id_komoditi,
                                    nama_komoditi : parameters.en ? item.komoditi_nasional_ref.tb_komoditi_nasional_ref_tr?.nama:  item.komoditi_nasional_ref.nama,
                                    icon : `${env.get('APP_URL')}/uploads/sektor/${item.file_icon}`  ,
                                    detail: item.komoditi_nasional_value.map((item) => {
                                        return {
                                            nama :parameters.en ? item.komoditi_nasional_value_tr[0]?.nama: item.nama,
                                            value: item.komoditi_nasional_value_detail[0]?.numeric_value 
                                            ? parameters.en 
                                                ? `${numberFormatLengkapEn(item.komoditi_nasional_value_detail[0]?.numeric_value,2)} ${item.satuan}`
                                                : `${numberFormatLengkap(item.komoditi_nasional_value_detail[0]?.numeric_value,2)} ${item.satuan}`
                                            : ` ${item.satuan} 0`,
                                            tahun : item.komoditi_nasional_value_detail[0]?.tahun
                                        }
                                    })
                                }

                                ret.detail.push({
                                    nama : 'Sentra Produksi',
                                    value : parameters.en ? item.komoditi_nasional_ref.tb_komoditi_daerah[0]?.tb_komoditi_daerah_tr[0]?.sentra_produksi || '': item.komoditi_nasional_ref.tb_komoditi_daerah[0]?.sentra_produksi ||  '',
                                    tahun : null
                                })
                                return ret
                        }),
                    //     }
                    // })
                }
            })
        }

        return {
            success : true,
            data : {
                        detail,
                        nilai,
                        potensi,
                        insentif,
                        diagram,
                        sub_sektor
                    }
        }
    }

     public async get_insentif({ response,request }:HttpContext) {
            const datas = await prisma.tb_jenis_insentif.findMany({
                where:{
                    status:99
                },
                include:{
                    tb_kategori_insentif:true,
                    tb_jenis_insentif_file:true,
                    tb_jenis_insentif_file_tr:true,
                    tb_jenis_insentif_tr:true
                }
            })
            const parameters = request.qs()
            const groupedData = datas
            .filter((item) => item.tb_kategori_insentif?.nama !== undefined) // Filter hanya yang tidak undefined
            .map((item) => {
                if (!parameters.en) {
                    return {
                        id_jenis_insentif: item.id_jenis_insentif,
                        jenis_insentif: item.tb_kategori_insentif?.nama,
                        nama: item.nama,
                        keterangan: striptags(item.keterangan).replaceAll('&nbsp;',' '),
                        image: item.tb_jenis_insentif_file.map((items) =>
                            `${env.get('APP_URL')}/uploads/jenis_insentif/${items.id_jenis_insentif}/${items.nama}`
                        ),
                    }
                } else {
                    return {
                        id_jenis_insentif: item.id_jenis_insentif,
                        jenis_insentif: item.tb_kategori_insentif?.nama,
                        nama: item.tb_jenis_insentif_tr[0]?.nama || item.nama,
                        keterangan: striptags(item.tb_jenis_insentif_tr[0]?.keterangan).replaceAll('&nbsp;',' ') || striptags(item.keterangan).replaceAll('&nbsp;',' '),
                        image: item.tb_jenis_insentif_file_tr.map((items) =>
                            `${env.get('APP_URL')}/uploads/jenis_insentif/${items.id_jenis_insentif}/${items.nama}`
                        ),
                    }
                }

            })
            .reduce((acc, current) => {
                const { jenis_insentif } = current;

                if (!acc[jenis_insentif]) {
                acc[jenis_insentif] = [];
                }

                acc[jenis_insentif].push(current);

                return acc;
            }, {});

            const data = Object.entries(groupedData).map(([jenis_insentif, items]) => ({
            jenis_insentif,
            items,
            }));
            return {
                success:true,
                data : data
            }
     }
     public async get_roadmap({ response,request }:HttpContext) {
            const datas = await prisma.tb_roadmap.findMany({
                include:{
                    tb_komoditi_nasional_ref:{
                        include:{
                            tb_komoditi_daerah:true,
                            tb_komoditi_nasional_ref_tr:true
                        }
                    },
                    tb_roadmap_file:true,
                    tb_roadmap_tr:true,
                    tb_roadmap_file_tr:true,
                },
                where:{
                    status:99
                }
            })
            const parameters = request.qs()

            const data = datas
                .map((item) => {
                    let nama_komoditi = item.tb_komoditi_nasional_ref.nama
                    let image_file = item.tb_roadmap_file.map((items) =>
                                        `${env.get('APP_URL')}/uploads/roadmap/${items.nama}`
                                    )
                    if (parameters.en) {
                        nama_komoditi = item.tb_komoditi_nasional_ref.tb_komoditi_nasional_ref_tr?.nama || nama_komoditi
                        if (item.tb_roadmap_file_tr.length > 0) {
                        image_file = item.tb_roadmap_file_tr.map((items) =>
                                        `${env.get('APP_URL')}/uploads/roadmap/${items.nama}`
                                    )
                        }
                    }
                    

                    console.log(item.tb_komoditi_nasional_ref.tb_komoditi_nasional_ref_tr);

                    return {

                        id_komoditi:item.id_komoditi,
                        deskripsi:item.deskripsi,
                        id_roadmap: item.id_roadmap,
                        image_komoditi : `${env.get('APP_URL')}/uploads/sektor/${item.tb_komoditi_nasional_ref.tb_komoditi_daerah[0]?.file_icon}`,
                        nama_komoditi : nama_komoditi,
                        judul: item.judul,
                        image: image_file,
                    }
                })
                 .reduce((acc, current) => {
                    const existingKomoditi = acc.find(
                    (item) => item.nama_komoditi === current.nama_komoditi
                    );

                    if (existingKomoditi) {
                    // Tambahkan id_roadmap, judul, dan image ke array existingKomoditi
                    existingKomoditi.roadmaps.push({
                        id_roadmap: current.id_roadmap,
                        judul: current.judul,
                        deskripsi:current.deskripsi,
                        image: current.image[0], // gunakan hanya satu image jika diperlukan
                    });
                    } else {
                    // Jika komoditi belum ada, tambahkan objek baru ke accumulator
                    acc.push({
                        id_komoditi: current.id_komoditi,
                        image_komoditi: current.image_komoditi,
                        deskripsi:current.deskripsi,
                        nama_komoditi: current.nama_komoditi,
                        roadmaps: [
                        {
                            id_roadmap: current.id_roadmap,
                            judul: current.judul,
                            deskripsi: current.deskripsi,
                            image: current.image, // gunakan hanya satu image jika diperlukan
                        },
                        ],
                    });
                    }

                    return acc;
                }, []);
            return {
                success:true,
                data : data
            }
     }

    public async get_kategori_kebijakan({ request, params }: HttpContext) {
        const data = await prisma.tb_kebijakan_kategori.findMany({
            orderBy:{
                id_kategori:'asc'
            }
        })
        return {
            success:true,
            data : data
        }
    }
     public async get_kebijakan({ request, params }: HttpContext) {
        const search = params.search;

        // Ambil parameter page dan pageSize dari query string
        const id_kategori = parseInt(params.id_kategori) || 1; // Default halaman pertama
        const page = parseInt(params.page) || 1; // Default halaman pertama
        const pageSize = parseInt(params.pageSize) || 10; // Default 5 item per halaman
        const isNumber = !isNaN(Number(search));
        let searchCondition = {
             AND: [ // Gunakan AND untuk menggabungkan kondisi lain dengan OR
                {
                    status:99,
                    id_kategori: id_kategori
                 },
            ]
        };
        if (search !== ',' && search !== '') {
            searchCondition = {
                AND: [ // Gunakan AND untuk menggabungkan kondisi lain dengan OR
                    {
                        OR: [
                            { judul: { contains: search, mode: 'insensitive' } },
                        ],
                    }
                ]
            };
        }

        const totalData = await prisma.tb_kebijakan.count({
            where: searchCondition,
        });

        const dataKebijakan = await prisma.tb_kebijakan.findMany({
            where: searchCondition,
            include:{
                tb_kebijakan_kategori:true
            },
            skip: (page - 1) * pageSize, // Mulai dari item ke berapa
            take: pageSize, // Ambil jumlah item sesuai pageSize
        });


        const mappedKebijakan = dataKebijakan.map(item => ({
            id_kebijakan : item.id_kebijakan,
            judul : item.judul,
            deskripsi : item.deskripsi,
            file: `${env.get('APP_URL')}/uploads/kebijakan/${item?.nama_file }`,
            id_kategori : item.id_kategori,
            kategori : item.tb_kebijakan_kategori?.nama,
        }))


        return {
            success: true,
            totalRecords: totalData,
            totalPage:Math.ceil(totalData / pageSize),
            page,
            pageSize,
            data: mappedKebijakan
            };
    }

     public async get_artikel({ request, params }: HttpContext) {
        const search = params.search;
        const isNumber = !isNaN(Number(search));
        let searchCondition = {
             AND: [ // Gunakan AND untuk menggabungkan kondisi lain dengan OR
                {
                    status: 99,
                    id_news_kategori :{
                        in :[2,3,4]
                    }
                 },

            ]
        };
        if (search !== ',' && search !== '') {
            searchCondition = {
                AND: [
                    {
                        status: 99,
                        id_news_kategori: {
                        in: [2, 3, 4],
                        },
                    },
                    {
                        OR: [
                            { judul: { contains: search, mode: 'insensitive' } },
                            { deskripsi: { contains: search, mode: 'insensitive' } },
                            { deskripsi_singkat: { contains: search, mode: 'insensitive' } },
                        ],
                    }
                ]
            };
        }


        const artikel = await prisma.tb_news.findMany({
            where: searchCondition,
            include:{
                translations:true,
                files:true,
                tb_news_kategori:true
            },
        });


        const mappedArtikel = artikel.map(item => ({
            id_artikel : item.id,
            judul : item.judul,
            deskripsi : item.deskripsi,
            deskripsi_singkat : item.deskripsi_singkat,
            cover: `${env.get('APP_URL')}/uploads/berita/${item.id}/${item?.file_cover }`,
            id_kategori : item.id_news_kategori,
            kategori : item.tb_news_kategori?.nama,
            files : item.files.map((items) => {
                return {
                    nama: items.nama,
                    url: `${env.get('APP_URL')}/uploads/berita/${item.id}/${items.nama}`,
                }
            })
        }))
        .reduce((acc, current) => {
            const { id_kategori, kategori } = current;

            // Only proceed if `kategori` and `id_kategori` are defined
            if (!kategori || !id_kategori) return acc;

            // Find existing category group
            const existingCategory = acc.find(group => group.id_kategori === id_kategori);

            if (existingCategory) {
                // Add to existing category's items if the group is already there
                existingCategory.items.push(current);
            } else {
                // Create a new group if it doesn't exist
                acc.push({
                    id_kategori,
                    kategori,
                    items: [current],
                });
            }

            return acc;
        }, []);
        return {
            success: true,
            data: mappedArtikel
            };
    }
     public async get_kajian_potensi_prov({ request, params ,response}: HttpContext) {
        const search = params.search;
        const isNumber = !isNaN(Number(search));
         const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }
        let searchCondition = {
             AND: [ // Gunakan AND untuk menggabungkan kondisi lain dengan OR
                {
                    id_adm_provinsi :Id,
                    status: 99,
                    id_news_kategori :{
                        in :[1]
                    }
                 },

            ]
        };
        if (search !== ',' && search !== '') {
            searchCondition = {
                AND: [
                    {
                        id_adm_provinsi :Id,
                        status: 99,
                        id_news_kategori: {
                        in: [1],
                        },
                    },
                    {
                        OR: [
                            { judul: { contains: search, mode: 'insensitive' } },
                            { deskripsi: { contains: search, mode: 'insensitive' } },
                            { deskripsi_singkat: { contains: search, mode: 'insensitive' } },
                        ],
                    }
                ]
            };
        }


        const artikel = await prisma.tb_news.findMany({
            where: searchCondition,
            include:{
                translations:true,
                files:true,
                tb_news_kategori:true
            },
        });


        const mappedArtikel = artikel.map(item => ({
            id_kajian : item.id,
            judul : item.judul,
            deskripsi : item.deskripsi,
            deskripsi_singkat : item.deskripsi_singkat,
            cover: `${env.get('APP_URL')}/uploads/berita/${item.id}/${item?.file_cover }`,
            id_kategori : item.id_news_kategori,
            kategori : item.tb_news_kategori?.nama,
            files : item.files.map((items) => {
                return {
                    nama: items.nama,
                    url: `${env.get('APP_URL')}/uploads/berita/${item.id}/${items.nama}`,
                }
            })
        }))
        .reduce((acc, current) => {
            const { id_kategori, kategori } = current;

            // Only proceed if `kategori` and `id_kategori` are defined
            if (!kategori || !id_kategori) return acc;

            // Find existing category group
            const existingCategory = acc.find(group => group.id_kategori === id_kategori);

            if (existingCategory) {
                // Add to existing category's items if the group is already there
                existingCategory.items.push(current);
            } else {
                // Create a new group if it doesn't exist
                acc.push({
                    id_kategori,
                    kategori,
                    items: [current],
                });
            }

            return acc;
        }, []);

        return {
            success: true,
            data: mappedArtikel[0]?.items ? mappedArtikel[0]?.items  : null
            };
    }
     public async get_kajian_potensi_kabkot({ request, params ,response}: HttpContext) {
        const search = params.search;
        const isNumber = !isNaN(Number(search));
         const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }
        let searchCondition = {
             AND: [ // Gunakan AND untuk menggabungkan kondisi lain dengan OR
                {
                    id_adm_kabkot :Id,
                    status: 99,
                    id_news_kategori :{
                        in :[1]
                    }
                 },

            ]
        };
        if (search !== ',' && search !== '') {
            searchCondition = {
                AND: [
                    {
                        id_adm_kabkot :Id,
                        status: 99,
                        id_news_kategori: {
                        in: [1],
                        },
                    },
                    {
                        OR: [
                            { judul: { contains: search, mode: 'insensitive' } },
                            { deskripsi: { contains: search, mode: 'insensitive' } },
                            { deskripsi_singkat: { contains: search, mode: 'insensitive' } },
                        ],
                    }
                ]
            };
        }


        const artikel = await prisma.tb_news.findMany({
            where: searchCondition,
            include:{
                translations:true,
                files:true,
                tb_news_kategori:true
            },
        });


        const mappedArtikel = artikel.map(item => ({
            id_kajian : item.id,
            judul : item.judul,
            deskripsi : item.deskripsi,
            deskripsi_singkat : item.deskripsi_singkat,
            cover: `${env.get('APP_URL')}/uploads/berita/${item.id}/${item?.file_cover }`,
            id_kategori : item.id_news_kategori,
            kategori : item.tb_news_kategori?.nama,
            files : item.files.map((items) => {
                return {
                    nama: items.nama,
                    url: `${env.get('APP_URL')}/uploads/berita/${item.id}/${items.nama}`,
                }
            })
        }))
        .reduce((acc, current) => {
            const { id_kategori, kategori } = current;

            // Only proceed if `kategori` and `id_kategori` are defined
            if (!kategori || !id_kategori) return acc;

            // Find existing category group
            const existingCategory = acc.find(group => group.id_kategori === id_kategori);

            if (existingCategory) {
                // Add to existing category's items if the group is already there
                existingCategory.items.push(current);
            } else {
                // Create a new group if it doesn't exist
                acc.push({
                    id_kategori,
                    kategori,
                    items: [current],
                });
            }

            return acc;
        }, []);

        return {
            success: true,
            data: mappedArtikel[0]?.items ? mappedArtikel[0]?.items  : null
            };
    }
     public async get_kajian_hilirisasi({ request, params,response }: HttpContext) {
        const search = params.search;
         const Id = parseInt(params.id_komoditi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const artikel = await prisma.tb_news.findMany({
            where: {
                status: 99,
                    id_news_kategori :{
                        in :[5]
                    },
                    id_komoditi:Id
            },
            include:{
                translations:true,
                files:true,
                files_tr:true,
                tb_news_kategori:{
                    
                }
            },
        });

        const parameters = request.qs()
        let mappedArtikel = []
        if (!parameters.en) {
            mappedArtikel = artikel.map(item => ({
                id_artikel : item.id,
                judul : item.judul,
                deskripsi : item.deskripsi,
                deskripsi_singkat : item.deskripsi_singkat,
                cover: `${env.get('APP_URL')}/uploads/kajian/cover/${item?.file_cover }`,
                id_kategori : item.id_news_kategori,
                kategori : item.tb_news_kategori?.nama,
                files : item.files.map((items) => {
                    return {
                        nama: items.nama,
                        url: `${env.get('APP_URL')}/uploads/kajian/3/${items.nama}`,
                    }
                })
            }))
            .reduce((acc, current) => {
                const { id_kategori, kategori } = current;
    
                // Only proceed if `kategori` and `id_kategori` are defined
                if (!kategori || !id_kategori) return acc;
    
                // Find existing category group
                const existingCategory = acc.find(group => group.id_kategori === id_kategori);
    
                if (existingCategory) {
                    // Add to existing category's items if the group is already there
                    existingCategory.items.push(current);
                } else {
                    // Create a new group if it doesn't exist
                    acc.push({
                        id_kategori,
                        kategori,
                        items: [current],
                    });
                }
    
                return acc;
            }, []);
            
        } else {
            mappedArtikel = artikel.map(item => ({
                id_artikel : item.id,
                judul : item.translations?.[0]?.judul || item.judul,
                deskripsi : item.translations?.[0]?.deskripsi || item.deskripsi,
                deskripsi_singkat : item.translations?.[0]?.deskripsi_singkat || item.deskripsi_singkat,
                cover: `${env.get('APP_URL')}/uploads/kajian/cover/${item?.file_cover }`,
                id_kategori : item.id_news_kategori,
                kategori : item.tb_news_kategori?.nama,
                files : item.files_tr.map((items) => {
                    return {
                        nama: items.nama,
                        url: `${env.get('APP_URL')}/uploads/kajian/3/${items.nama}`,
                    }
                })
            }))
            .reduce((acc, current) => {
                const { id_kategori, kategori } = current;
    
                // Only proceed if `kategori` and `id_kategori` are defined
                if (!kategori || !id_kategori) return acc;
    
                // Find existing category group
                const existingCategory = acc.find(group => group.id_kategori === id_kategori);
    
                if (existingCategory) {
                    // Add to existing category's items if the group is already there
                    existingCategory.items.push(current);
                } else {
                    // Create a new group if it doesn't exist
                    acc.push({
                        id_kategori,
                        kategori,
                        items: [current],
                    });
                }
    
                return acc;
            }, []);
            
        }
        return {
            success: true,
            data: mappedArtikel[0]?.items? mappedArtikel[0]?.items :null
            };
    }

    public async get_faq({ request, params }: HttpContext) {
        const parameters = request.qs()

        const faqs = await prisma.tb_glossary.findMany({
            where:{
                glossary_type:"FAQ"
            },
            include:{
                tb_glossary_tr:true
            },
        });


        const mappedFaq = faqs.map(item => ({
            id_faq : item.id_glossary,
            question : !parameters.en ? item.title : item.tb_glossary_tr[0]?.title,
            answer : !parameters.en ? striptags(item.description) : striptags(item.tb_glossary_tr[0]?.description),
        }))

        return {
            success: true,
            data: mappedFaq
        };
    }

    /**
     * @add_faq
     * @paramQuery nama - Nama  - @type(string) @required
     * @paramQuery email - email - @type(string)
     * @paramQuery bahasa - bahasa (id,en) - @type(string)
     * @paramQuery tujuan - tujuan (1. Bertanya, 2. Minat Investasi) - @type(number) @required
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async add_faq({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createPostFaqValidator)
        try {
           const dataPost = {
                nama : data.nama,
                email : data.email,
                tujuan : data.tujuan,
                token : this.generateRandomString(),
                ip_pengunjung : request.ip(),
                is_active : false,
                lang : data.bahasa? data.bahasa : 'id',
            }

            const insert = await prisma.tb_email_subscription.create({data:dataPost})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }


    public generateRandomString(length = 10) {
        const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            const randomIndex = Math.floor(Math.random() * characters.length);
            result += characters[randomIndex];
        }
        return result;
    }

    public async get_infrastruktur({ request, params }: HttpContext) {
        // Ambil parameter pencarian dan pagination
            const search = params.search?.toLowerCase() || '';
            const page = parseInt(params.page) || 1;
            const pageSize = parseInt(params.pageSize) || 10;
            // return search
            // Daftar tabel dengan urutan
            const tableConfigs = [
                { table: 'tb_bandara', label: 'Bandara' },
                { table: 'tb_hotel', label: 'Hotel' },
                { table: 'tb_pelabuhan', label: 'Pelabuhan' },
                { table: 'tb_pendidikan', label: 'Pendidikan' },
                { table: 'tb_rumah_sakit', label: 'Rumah Sakit' },
            ];

            // Fungsi untuk menghitung total records per tabel
            const countRecords = async () => {
                return Promise.all(
                    tableConfigs.map(async (config) => {
                        const where = search
                            ? {
                                OR: [
                                    { nama: { contains: search, mode: 'insensitive' } },
                                    { alamat: { contains: search, mode: 'insensitive' } },
                                    { no_telp: { contains: search, mode: 'insensitive' } },
                                    { no_fax: { contains: search, mode: 'insensitive' } },
                                    { url_web: { contains: search, mode: 'insensitive' } },
                                ],
                            }
                            : {};
                        const count = await prisma[config.table].count({ where });
                        return { table: config.table, label: config.label, count };
                    })
                );
            };

            // Hitung total data per tabel
            const tableCounts = await countRecords();
            const totalRecords = tableCounts.reduce((sum, item) => sum + item.count, 0);

            // Hitung indeks awal dan akhir untuk halaman tertentu
            const startIndex = (page - 1) * pageSize;
            const endIndex = startIndex + pageSize;

            // Cari tabel yang relevan untuk indeks ini
            let currentStartIndex = 0;
            const paginatedDataPromises = [];

            for (const { table, label, count } of tableCounts) {
                const currentEndIndex = currentStartIndex + count;

                // Jika ada overlap dengan indeks yang dicari
                if (startIndex < currentEndIndex && endIndex > currentStartIndex) {
                    const tableStartIndex = Math.max(0, startIndex - currentStartIndex);
                    const tableEndIndex = Math.min(count, endIndex - currentStartIndex);

                    // Ambil data yang sesuai
                    const where = search
                        ? {
                            OR: [
                                { nama: { contains: search, mode: 'insensitive' } },
                                { alamat: { contains: search, mode: 'insensitive' } },
                                { no_telp: { contains: search, mode: 'insensitive' } },
                                { no_fax: { contains: search, mode: 'insensitive' } },
                                { url_web: { contains: search, mode: 'insensitive' } },
                            ],
                        }
                        : {};

                    paginatedDataPromises.push(
                        prisma[table].findMany({
                            where,
                            skip: tableStartIndex,
                            take: tableEndIndex - tableStartIndex,
                            select: {
                                nama: true,
                                alamat: true,
                                no_telp: true,
                                no_fax: true,
                                url_web: true,
                            },
                        }).then((data) =>
                            data.map((item) => ({
                                ...item,
                                keterangan: label,
                            }))
                        )
                    );
                }

                currentStartIndex = currentEndIndex;
            }

            // Tunggu semua data diambil
            const paginatedData = (await Promise.all(paginatedDataPromises)).flat();

            // Hitung total halaman
            const totalPage = Math.ceil(totalRecords / pageSize);

            // Return hasil paginasi
            return {
                success: true,
                totalRecords,
                totalPage,
                page,
                pageSize,
                data: paginatedData,
            };


    }
    public async get_infrastruktur2({ request, params }: HttpContext) {
        // Mendapatkan semua data dan gabungkan
        const selectFields = {
            nama: true,
            alamat: true,
            no_telp: true,
            no_fax: true,
            url_web: true,
        };
        const [bandara, hotel, pelabuhan, pendidikan, rumah_sakit] = await Promise.all([
            prisma.tb_bandara.findMany({
                select:selectFields
            }),
            prisma.tb_hotel.findMany({
                select:selectFields
            }),
            prisma.tb_pelabuhan.findMany({
                select:selectFields
            }),
            prisma.tb_pendidikan.findMany({
                select:selectFields
            }),
            prisma.tb_rumah_sakit.findMany({
                select:selectFields
            }),
        ]);

        // Mapping data dengan keterangan masing-masing
        const combinedData = [
        ...bandara.map(item => ({
            nama: item.nama,
            alamat: item.alamat,
            no_telp: item.no_telp,
            no_fax: item.no_fax,
            url_web: item.url_web,
            keterangan: 'Bandara',
        })),
        ...hotel.map(item => ({
            nama: item.nama,
            alamat: item.alamat,
            no_telp: item.no_telp,
            no_fax: item.no_fax,
            url_web: item.url_web,
            keterangan: 'Hotel',
        })),
        ...pelabuhan.map(item => ({
            nama: item.nama,
            alamat: item.alamat,
            no_telp: item.no_telp,
            no_fax: item.no_fax,
            url_web: item.url_web,
            keterangan: 'Pelabuhan',
        })),
        ...pendidikan.map(item => ({
            nama: item.nama,
            alamat: item.alamat,
            no_telp: item.no_telp,
            no_fax: item.no_fax,
            url_web: item.url_web,
            keterangan: 'Pendidikan',
        })),
        ...rumah_sakit.map(item => ({
            nama: item.nama,
            alamat: item.alamat,
            no_telp: item.no_telp,
            no_fax: item.no_fax,
            url_web: item.url_web,
            keterangan: 'Rumah Sakit',
        })),
        ];
        // return combinedData
            let search = ''
            let filteredData = combinedData
            if (params.search != ',' && params.search != '' ) {
                search = params.search?.toLowerCase() || ''; // String pencarian (case insensitive)
                filteredData = combinedData.filter(item =>
                    Object.values(item).some(value =>
                        typeof value === 'string' && value.toLowerCase().includes(search)
                    )
                );
            }

            // Paginasi secara manual
            const page = parseInt(params.page ) || 1; // Nomor halaman
            const pageSize = parseInt(params.pageSize ) || 10; // Jumlah data per halaman
            const totalRecords = filteredData.length; // Total jumlah data setelah pencarian
            const totalPage = Math.ceil(totalRecords / pageSize); // Total halaman
            const paginatedData = filteredData.slice((page - 1) * pageSize, page * pageSize);

            return {
                success: true,
                totalRecords, // Total number of records after filtering
                totalPage, // Total number of pages
                page,
                pageSize,
                data: paginatedData, // Data for the current page
            };
    }

    // public async tes({ request, params,response }: HttpContext) {
    //     // const Id = parseInt(params.id, 10)
    //     // if (isNaN(Id)) {
    //     //     return response.status(400).send({ error: 'Invalid ID provided' })
    //     // }
    //     const data = await prisma.tb_sektor_daerah.findMany({
    //         include:{
    //             tb_sektor_daerah_sumber_data:{
    //                 include:{
    //                     tb_sumber_data:{
    //                         include:{
    //                             tb_sumber_data_judul:{
    //                                 include:{
    //                                     tb_sumber_data_instansi:true
    //                                 }
    //                             }
    //                         }
    //                     }
    //                 }
    //             },
    //         },take:10
    //     })

    //     const datas = data.map((item) => {
    //         return {
    //             ...item,
    //             tb_sumber_data: item.tb_sektor_daerah_sumber_data.map((sumber) => {
    //                 return {
    //                     id_sumber_data: sumber.id_sumber_data,
    //                     judul: sumber.tb_sumber_data.tb_sumber_data_judul.judul,
    //                     instansi: sumber.tb_sumber_data.tb_sumber_data_judul.tb_sumber_data_instansi?.nama,
    //                 }
    //             })
    //         }
    //     })
    //     return data

    // }

    public async get_bandara({params ,response}:HttpContext) {

        const datas = await prisma.tb_bandara.findMany({
            where: {
                status: 99,
                is_ikn:true
            }
        });
        const data = datas.map((item) => {
            return {
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }

        })

        return {
            success :true,
            data :data
        }
    }

    public async get_hotel({params ,response}:HttpContext) {

        const datas = await prisma.tb_hotel.findMany({
            where: {
                status: 99,
                is_ikn:true
            }
        });
        const data = datas.map((item) => {
            return {
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }

        })

        return {
            success :true,
            data :data
        }
    }
    public async get_pelabuhan({params ,response}:HttpContext) {

        const datas = await prisma.tb_pelabuhan.findMany({
            where: {
                status: 99,
                is_ikn:true
            }
        });
        const data = datas.map((item) => {
            return {
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }

        })

        return {
            success :true,
            data :data
        }
    }
    public async get_pendidikan({params ,response}:HttpContext) {

        const datas = await prisma.tb_pendidikan.findMany({
            where: {
                status: 99,
                is_ikn:true
            }
        });
        const data = datas.map((item) => {
            return {
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }

        })

        return {
            success :true,
            data :data
        }
    }
    public async get_rumah_sakit({params ,response}:HttpContext) {

        const datas = await prisma.tb_rumah_sakit.findMany({
            where: {
                status: 99,
                is_ikn:true
            }
        });
        const data = datas.map((item) => {
            return {
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }

        })

        return {
            success :true,
            data :data
        }
    }


    public async get_layers({params ,response}:HttpContext) {

        const datas = await prisma.tb_ikn_layer.findMany({
            where: {
                status: 99,
            }
        });
        const data = datas.map((item) => {
            return {
                nama : item.judul,
                url_service:item.url_service,
            }

        })

        return {
            success :true,
            data :data
        }
    }
    public async get_kawasan({params ,response}:HttpContext) {

        const datas = await prisma.tb_kawasan_industri.findMany({
            where: {
                status: '99',
                is_ikn:true
            }
        });
        const data = datas.map((item) => {
            return {
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }

        })

        return {
            success :true,
            data :data
        }
    }


}