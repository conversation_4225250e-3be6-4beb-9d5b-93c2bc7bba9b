import { createPKRefValida<PERSON>, createSDIRefValidator, updateSDIRefValidator } from '#validators/referensi'
import { createRoleValidator, updateRoleValidator } from '#validators/role'
import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'
export default class RefSumberdataInstansiController {

  async index({}: HttpContext) {
    const data = await prisma.tb_sumber_data_instansi.findMany({})
    return {
        success : true,
        data : {data}
    }
  }
    /**
     * @get_paginate
     * @summary Get a list of role with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          {
            nama: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
          },
          {
            alamat: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
          },
          {
            cp: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
          },
          {
            email: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
          },
          {
            tb_adm_kabkot:{
              nama: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
            }
          },
          {
            tb_adm_provinsi:{
              nama: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
            }
          }
        ],
      };


    }
    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_sumber_data_instansi.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_sumber_data_instansi.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      include:{
        tb_sumber_data_instansi_tr:true,
        tb_adm_kabkot:true,
        tb_adm_provinsi:true
      }
    });

    const data = datas.map((item)=>{
      return {
          id_sumber_data_instansi : item.id_sumber_data_instansi,
          nama:item.nama,
          alamat:item.alamat,
          nama_kabkot:item.tb_adm_kabkot?.nama,
          nama_Provinsi:item.tb_adm_provinsi?.nama,
          cp :item.cp,
          url_web :item.url_web,
          email:item.email,
          no_telp:item.no_telp,
          no_fax:item.no_fax,
      }
    })

    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };
  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
     * @store
     * @summary Insert peluang kontak
     * @paramQuery id_adm_kabkot -  id_adm_kabkot - @type(number)
     * @paramQuery id_adm_provinsi -  id_adm_provinsi - @type(number)
     * @paramQuery nama -  Nama - @type(string)
     * @paramQuery nama_tr -  Nama TR- @type(string)
     * @paramQuery alamat -  alamat - @type(string)
     * @paramQuery cp -  cp - @type(string)
     * @paramQuery email -  email - @type(string)
     * @paramQuery no_telp -  no_telp - @type(string)
     * @paramQuery no_fax -  no_fax - @type(string)
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createSDIRefValidator)
        try {
           const dataPost = {
              id_adm_provinsi: data.id_adm_provinsi,
              id_adm_kabkot: data.id_adm_kabkot,
              nama: data.nama,
              alamat: data.alamat,
              cp: data.cp,
              url_web: data.url_web,
              email:data.email,
              no_telp:data.no_telp,
              no_fax:data.no_fax,
            }
            const insert = await prisma.tb_sumber_data_instansi.create({data:dataPost})
           const dataPostTr = {
              id_sumber_data_instansi:insert.id_sumber_data_instansi,
              nama: data.nama_tr,
              kd_bahasa:'en',
            }
            const insertTr = await prisma.tb_sumber_data_instansi_tr.create({data:dataPostTr})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
    const data = await prisma.tb_sumber_data_instansi.findUnique({
      where: {
        id_sumber_data_instansi: Id,
      },
      include:{
        tb_sumber_data_instansi_tr:true
      }
    })

    return {
      success : true,
      data : data
    }
  }


  async edit({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    const data = await prisma.tb_sumber_data_instansi.findUnique({
      where: {
        id_sumber_data_instansi: Id,
      },
    })

    return {
      success : true,
      data : {data}
    }
  }

  /**
     * @update
     * @summary Insert peluang kontak
     * @paramQuery id_adm_kabkot -  id_adm_kabkot - @type(number)
     * @paramQuery id_adm_provinsi -  id_adm_provinsi - @type(number)
     * @paramQuery nama -  Nama - @type(string)
     * @paramQuery nama_tr -  Nama TR- @type(string)
     * @paramQuery alamat -  alamat - @type(string)
     * @paramQuery cp -  cp - @type(string)
     * @paramQuery email -  email - @type(string)
     * @paramQuery no_telp -  no_telp - @type(string)
     * @paramQuery no_fax -  no_fax - @type(string)
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
  async update({ params, request ,response}: HttpContext) {
    const data = await request.validateUsing(updateSDIRefValidator)

    const Id = parseInt(params.id, 10)

    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    // try {

      const updatePost = await prisma.tb_sumber_data_instansi.update({
        where: {
          id_sumber_data_instansi: Id,
        },
        data: {
          id_adm_provinsi: data.id_adm_provinsi,
          id_adm_kabkot: data.id_adm_kabkot,
          nama: data.nama,
          alamat: data.alamat,
          cp: data.cp,
          url_web: data.url_web,
          email:data.email,
          no_telp:data.no_telp,
          no_fax:data.no_fax,
        },
      })
      if (data.tr) {

        await prisma.tb_sumber_data_instansi_tr.update({
          where: {
            id_sumber_data_instansi_tr: data?.tr.id_sumber_data_instansi,
          },
          data: {
            nama: data.tr.nama,
          },
        })
      }
      return response.status(200).json({ success: true, data: updatePost })
    // } catch (error) {
    //   return response.status(500).send({ error: 'Error updating data' })
    // }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_sumber_data_instansi.delete({
        where: {
          id_sumber_data_instansi: Id,
        },
      })
      return response.status(200).send({success:true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }
}