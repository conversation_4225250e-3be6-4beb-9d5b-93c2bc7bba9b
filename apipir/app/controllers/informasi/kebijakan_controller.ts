import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../helpers/model_helper.js';

import uploadFile from '../../helpers/file_uploader.js';
import env from '#start/env';

import prisma from '../../lib/prisma.js'

export default class RoadmapKebijakanController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_kebijakan: { column: 'id_kebijakan', alias: 'id_kebijakan', type: 'int' },
                        judul: { column: 'judul', alias: 'judul', type: 'string' },
                        nama_file: { column: 'nama_file', alias: 'nama_file', type: 'string' },
                        id_kategori: { column: 'id_kategori', alias: 'id_kategori', type: 'int' },
                        deskripsi: { column: 'deskripsi', alias: 'deskripsi', type: 'string' }

                };

                const joins = {};

                const where = {};


                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;
                let q = '';

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }
                if (request.input('q')) {
                        q = request.input('q');
                }
                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };


                let whereDefault = {}

                if (q) {
                        whereDefault = {
                                OR: [
                                        {
                                                judul: {
                                                        contains: q,
                                                        mode: 'insensitive', // Mengabaikan besar kecil huruf
                                                },

                                        },
                                        {
                                                deskripsi: {
                                                        contains: q,
                                                        mode: 'insensitive', // Mengabaikan besar kecil huruf
                                                },

                                        },
                                        {
                                                tb_kebijakan_kategori: {
                                                        nama: {
                                                                contains: q,
                                                                mode: 'insensitive', // Mengabaikan besar kecil huruf
                                                        },
                                                }
                                        },
                                        {
                                                tb_kebijakan_file: {
                                                        some:{
                                                                nama: {
                                                                        contains: q,
                                                                        mode: 'insensitive', // Mengabaikan besar kecil huruf
                                                                        },
                                                        },
                                                }
                                        },
                                ],
                            };
                }

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        where: whereDefault,
                        orderBy:{},
                        include: {
                                tb_kebijakan_kategori: true
                        }
                }

                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                let where = modelWhereAnd((await this.schemaData()).columns, queryParams);
                if (Object.keys(where).length !== 0) {
                        options['where'] = {
                            AND: [
                                whereDefault, // Kondisi pencarian `OR`
                                where,        // Kondisi tambahan dari `modelWhereAnd`
                            ],
                        };
                }

                try {
                        let order = queryParams.order
                        const by = ['asc','desc'].includes(queryParams.by) ? queryParams.by : 'asc'
                        const paramList = ['status','judul','kategori']
                        if (order != undefined && paramList.includes(order)) {
                                if (order == 'kategori') {
                                        options.orderBy = {tb_kebijakan_kategori: {nama:by}}

                                //} else if (order == 'nama_sub_sektor') {
                                //         orderBy = {sub_sektor:{sub_sektor_ref:{nama:by}}}
                                // }else if (order == 'nama_komoditi') {
                                //         orderBy = {komoditi_nasional_ref:{nama:by}}
                                }else{
                                        options.orderBy = {[order]:by}
                                }
                        }
                        const data = await prisma.tb_kebijakan.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(200).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_kebijakan.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                // const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        // status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }

        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {
                        include: {
                                tb_kebijakan_kategori: true,
                                tb_kebijakan_file:true,
                                tb_kebijakan_tr:true,
                                tb_kebijakan_file_tr:true
                        }
                };

                options['where'] = {
                        'id_kebijakan': parseInt(params.id)
                }

                const data = await prisma.tb_kebijakan.findFirst(options);
                if (data) {
                        data.tb_kebijakan_file = data.tb_kebijakan_file.map(file => ({
                            ...file,
                            path: `${env.get('APP_URL')}/uploads/kebijakan/${file.nama}`
                        }));
                        data.tb_kebijakan_file_tr = data.tb_kebijakan_file_tr.map(file => ({
                                ...file,
                                path: `${env.get('APP_URL')}/uploads/kebijakan/${file.nama}`
                            }));
                    }
                return response.status(200).json({
                        success: true,
                        data: data
                });
        }

        public async createOrUpdate({ request, response, params,auth }: HttpContext) {
                const model = "informasi_kebijakan";
                const paramID = parseInt(params.id);

                console.log(paramID);

                try {
                        const result = await prisma.$transaction(async (prisma) => {
                                let fileName: string | undefined;
                                let fileNameTr: string | undefined;

                                // Handle file upload if provided
                                const file = request.file("nama_file");
                                if (file) {
                                        await uploadFile(file, model);
                                        fileName = file.fileName; // Set fileName if file exists
                                }
                                const fileTr = request.file("nama_file_tr");
                                if (fileTr) {
                                        await uploadFile(fileTr, model);
                                        fileNameTr = fileTr.fileName; // Set fileName if file exists
                                }

                                if (!paramID) {
                                        console.log(
                                                "Sync tb_kebijakan id sequence",
                                                await prisma.$executeRaw`
                                                        SELECT setval((SELECT PG_GET_SERIAL_SEQUENCE('"tb_kebijakan"', 'id_kebijakan')),
                                                        (SELECT (MAX("id_kebijakan") + 1) FROM "tb_kebijakan"),
                                                        false) FROM "tb_kebijakan";
                                                `);

                                        const res = await prisma.tb_kebijakan.create({
                                                data: {
                                                        judul: request.input("judul") || "",
                                                        nama_file: fileName || "",
                                                        id_kategori: parseInt(request.input("id_kategori")) || 0,
                                                        deskripsi: request.input("deskripsi") || "",
                                                },
                                        });
                                        const resTr = await prisma.tb_kebijakan_tr.create({
                                                data: {
                                                        id_kebijakan: res.id_kebijakan,
                                                        judul: request.input("judul_tr") || "",
                                                        nama_file: fileName || "",
                                                        deskripsi: request.input("deskripsi_tr") || "",
                                                },
                                        });
                                        await auth.check()
                                        const insert = await prisma.tb_kebijakan_status.create({
                                                data: {
                                                id_kebijakan: res.id_kebijakan,
                                                status: 0,
                                                status_proses: 0,
                                                keterangan: 'Dokumen ada perubahan',
                                                created_by:auth.user?.id,
                                                updated_by:auth.user?.id,
                                                created_date:new Date()
                                                },
                                        });
                                        const dataFile: any = {
                                                id_kebijakan: res.id_kebijakan,
                                                nama: fileName || "",
                                        };
                                        const dataFileTr: any = {
                                                id_kebijakan: res.id_kebijakan,
                                                nama: fileNameTr || "",
                                        };

                                        console.log(
                                                "Sync tb_kebijakan_file id sequence",
                                                await prisma.$executeRaw`
                                                        SELECT setval((SELECT PG_GET_SERIAL_SEQUENCE('"tb_kebijakan_file"', 'id_kebijakan_file')),
                                                        (SELECT (MAX("id_kebijakan_file") + 1) FROM "tb_kebijakan_file"),
                                                        false) FROM "tb_kebijakan_file";
                                                `);

                                        const kebijakanFile = await prisma.tb_kebijakan_file.create({
                                                data: dataFile,
                                        });
                                        const kebijakanFileTr = await prisma.tb_kebijakan_file_tr.create({
                                                data: dataFileTr,
                                        });
                                        return response.status(200).json({
                                                status: "success",
                                                message: "Successfully Added Data",
                                                data: res,
                                        });
                                } else {
                                        const updateData: Record<string, any> = {
                                                judul: request.input("judul") || "",
                                                id_kategori: parseInt(request.input("id_kategori")) || 0,
                                                deskripsi: request.input("deskripsi") || "",
                                        };

                                        const updateDataTr: Record<string, any> = {
                                                judul: request.input("judul_tr") || "",
                                                deskripsi: request.input("deskripsi_tr") || "",
                                        };

                                        const idTr = parseInt(request.input("id_kebijakan_tr"));

                                        if (fileName) {
                                                updateData.nama_file = fileName || "";
                                        }
                                        if (fileNameTr) {
                                                updateDataTr.nama_file = fileName || "";
                                        }

                                        Object.keys(updateData).forEach((key) => {
                                                if (updateData[key] === undefined) {
                                                        delete updateData[key];
                                                }
                                        });

                                        Object.keys(updateDataTr).forEach((key) => {
                                                if (updateDataTr[key] === undefined) {
                                                        delete updateDataTr[key];
                                                }
                                        });

                                        const update = await prisma.tb_kebijakan.update({
                                                where: { id_kebijakan: paramID },
                                                data: updateData,
                                        });
                                        const updateTr = await prisma.tb_kebijakan_tr.update({
                                                where: { id_kebijakan_tr: idTr },
                                                data: updateDataTr,
                                        });
                                        await auth.check()
                                        const insert = await prisma.tb_kebijakan_status.create({
                                                data: {
                                                id_kebijakan: paramID,
                                                status: 0,
                                                status_proses: 0,
                                                keterangan: 'Dokumen ada perubahan',
                                                created_by:auth.user?.id,
                                                updated_by:auth.user?.id,
                                                created_date:new Date()
                                                },
                                        });

                                        if (fileName) {
                                                const dataFile = {
                                                        id_kebijakan: update.id_kebijakan,
                                                        nama: fileName || "",
                                                };

                                                const existingID = await prisma.tb_kebijakan_file.findFirst({
                                                        where: {
                                                                id_kebijakan: paramID,
                                                        },
                                                });

                                                let updateFile;
                                                if (existingID) {
                                                        updateFile = await prisma.tb_kebijakan_file.update({
                                                                where: { id_kebijakan_file: existingID.id_kebijakan_file },
                                                                data: dataFile,
                                                        });
                                                }
                                        }

                                        if (fileNameTr) {
                                                const dataFile = {
                                                        nama: fileName || "",
                                                };

                                                const existingID = await prisma.tb_kebijakan_file_tr.findFirst({
                                                        where: {
                                                                id_kebijakan: paramID,
                                                        },
                                                });

                                                let updateFile;
                                                if (existingID) {
                                                        updateFile = await prisma.tb_kebijakan_file_tr.update({
                                                                where: { id_kebijakan_file_tr: existingID.id_kebijakan_file_tr },
                                                                data: dataFile,
                                                        });
                                                }
                                        }

                                        return response.status(200).json({
                                                status: "success",
                                                message: "Successfully Updated Data",
                                                data: update,
                                        });
                                }

                        });
                } catch (error) {
                        // Handle error jika ada kesalahan dalam transaksi
                        console.log(error);
                        return response.status(500).json({
                                status: "error",
                                message: "An error occurred while processing data",
                                error: error.message,
                        });
                }
        }


        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_kebijakan.delete({
                                where: { id_kebijakan: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response ,auth}: HttpContext) {
                        const Id = parseInt(params.id, 10)
                        const statusId = parseInt(request.input('status') ?? 0);

                        if (isNaN(Id)) {
                                return response.status(400).send({ error: 'Invalid ID provided' })
                        }

                        if (isNaN(statusId)) {
                                return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                        }

                        try {
                                const toggleStatus = await prisma.tb_kebijakan.update({
                                        where: {
                                                id_kebijakan: Id,
                                        },
                                        data: {
                                                status: parseInt(request.input('status')) ?? 0
                                        },
                                })

                                // await auth.check()
                                // const insert = await prisma.tb_kebijakan_status.create({
                                //         data: {
                                //             id_kebijakan: Id,
                                //             status: parseInt(request.input('status')),
                                //             status_proses: parseInt(request.input('status')),
                                //             keterangan: request.input('keterangan'),
                                //             created_by:auth.user?.id,
                                //             updated_by:auth.user?.id,
                                //             created_date:new Date()
                                //         },
                                //     });

                                return response.status(200).json({
                                        status: 'success',
                                        message: 'Success Toggle Status',
                                        data: toggleStatus,
                                })
                        } catch (error) {
                                console.log(error);
                                return response.status(500).send({ error: 'Error Toggle Status' ,message : error.message})
                        }
                }

}