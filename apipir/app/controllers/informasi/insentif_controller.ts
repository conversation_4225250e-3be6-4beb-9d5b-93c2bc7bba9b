import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../helpers/model_helper.js';

import uploadFile from '../../helpers/file_uploader.js';

import prisma from '../../lib/prisma.js'

export default class RoadmapInsentifController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_jenis_insentif: { column: 'id_jenis_insentif', alias: 'id_jenis_insentif', type: 'int' },
                        nama: { column: 'nama', alias: 'nama', type: 'string' },
                        keterangan: { column: 'keterangan', alias: 'keterangan', type: 'string' },
                        status: { column: 'status', alias: 'status', type: 'int' },
                        tipe: { column: 'tipe', alias: 'tipe', type: 'int' },
                        urutan: { column: 'urutan', alias: 'urutan', type: 'int' },
                        id_adm_provinsi: { column: 'id_adm_provinsi', alias: 'id_adm_provinsi', type: 'int' },
                        id_adm_kabkot: { column: 'id_adm_kabkot', alias: 'id_adm_kabkot', type: 'int' },
                        id_kategori_insentif: { column: 'id_kategori_insentif', alias: 'id_kategori_insentif', type: 'int' }

                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                }


                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                options['where'] = modelWhereAnd((await this.schemaData()).columns, queryParams);

                try {

                        const data = await prisma.tb_jenis_insentif.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_jenis_insentif.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }
        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {};

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_jenis_insentif': parseInt(params.id)
                }

                const data = await prisma.tb_jenis_insentif.findFirst(options);

                const status_text = data?.status === 99 ? 'Approve' : 'Delete';

                return response.status(200).json({
                        success: true,
                        data: {
                                ...data,
                                status_text
                        }
                });
        }

        public async createOrUpdate({ request, response, params }: HttpContext) {
                const paramID = parseInt(params.id);
                const model = "informasi_insentif";

                let fileName1: string = "";
                let fileName2: string = "";

                let resID: number | undefined;

                const file1 = request.file("file");
                const file2 = request.file("file_tr_file");

                try {
                        const result = await prisma.$transaction(async (prisma) => {


                                if (!paramID) {
                                        console.log("Sync tb_jenis_insentif id sequence",
                                                await prisma.$executeRaw`
                                            SELECT setval((
                                            SELECT PG_GET_SERIAL_SEQUENCE('"tb_jenis_insentif"', 'id_jenis_insentif')),
                                            (SELECT (MAX("id_jenis_insentif") + 1) FROM "tb_jenis_insentif"),
                                            false) FROM "tb_jenis_insentif";
                                        `);

                                        const res = await prisma.tb_jenis_insentif.create({
                                                data: {
                                                        nama: request.input("nama") || "",
                                                        keterangan: request.input("keterangan") || "",
                                                        status: parseInt(request.input("status")) || 0,
                                                        tipe: parseInt(request.input("tipe")) || 0,
                                                        urutan: parseInt(request.input("urutan")) || 0,
                                                        id_adm_provinsi: parseInt(request.input("id_adm_provinsi")) || 0,
                                                        id_adm_kabkot: parseInt(request.input("id_adm_kabkot")) || 0,
                                                        id_kategori_insentif: parseInt(request.input("id_kategori_insentif")) || 0,
                                                }
                                        });

                                        resID = res.id_jenis_insentif;

                                        if (file1) {
                                                await uploadFile(file1, model, resID);
                                                fileName1 = file1.fileName ?? ""; // Set fileName if file exists
                                        }

                                        if (file2) {
                                                await uploadFile(file2, model, resID);
                                                fileName2 = file2.fileName ?? ""; // Set fileName if file exists
                                        }

                                        console.log("Sync tb_jenis_insentif_file id sequence",
                                                await prisma.$executeRaw`
                                            SELECT setval((
                                            SELECT PG_GET_SERIAL_SEQUENCE('"tb_jenis_insentif_file"', 'id_jenis_insentif_file')),
                                            (SELECT (MAX("id_jenis_insentif_file") + 1) FROM "tb_jenis_insentif_file"),
                                            false) FROM "tb_jenis_insentif_file";
                                        `);

                                        const data_jenis_insentif_file = {
                                                id_jenis_insentif: res.id_jenis_insentif || 0,
                                                tipe: parseInt(request.input("file_tipe")) || 0,
                                                nama: fileName1 || "",
                                                judul: request.input("file_judul") || "",
                                                keterangan: request.input("file_keterangan") || "",
                                        }

                                        const res_data_jenis_insentif_file = await prisma.tb_jenis_insentif_file.create({ data: data_jenis_insentif_file });

                                        const data_jenis_insentif_file_tr = {
                                                id_jenis_insentif: res.id_jenis_insentif || 0,
                                                kd_bahasa: request.input("kd_bahasa") || "",
                                                judul: request.input("file_tr_judul") || "",
                                                keterangan: request.input("file_tr_keterangan") || "",
                                                nama: fileName2 || "",
                                                tipe: parseInt(request.input("file_tr_tipe")) || 0,
                                        }

                                        const data_jenis_insentif_kbli = {
                                                id_jenis_insentif: res.id_jenis_insentif ? res.id_jenis_insentif : 0,
                                                id_kbli: parseInt(request.input("id_kbli")) || 0,
                                        }

                                        const data_jenis_insentif_status = {
                                                id_jenis_insentif: res.id_jenis_insentif ? res.id_jenis_insentif : 0,
                                                status: res.status || 0,
                                                status_proses: parseInt(request.input("st_status_proses")) || 0,
                                                keterangan: request.input("st_keterangan") || "",
                                                created_by: parseInt(request.input("created_by")) || 0,
                                                created_date: new Date(request.input("created_date")) || new Date(),
                                                updated_by: request.input("updated_by") ? parseInt(request.input("updated_by")) : null,
                                                updated_date: new Date(request.input("updated_date")) || new Date(),
                                        }

                                        const res_data_jenis_insentif_file_tr = await prisma.tb_jenis_insentif_file_tr.create({ data: data_jenis_insentif_file_tr });
                                        const res_data_jenis_insentif_kbli = await prisma.tb_jenis_insentif_kbli.create({ data: data_jenis_insentif_kbli });
                                        const res_data_jenis_insentif_status = await prisma.tb_jenis_insentif_status.create({ data: data_jenis_insentif_status });

                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: res,
                                                data_jenis_insentif_file: res_data_jenis_insentif_file,
                                                data_jenis_insentif_file_tr: res_data_jenis_insentif_file_tr,
                                                data_jenis_insentif_kbli: res_data_jenis_insentif_kbli,
                                                data_jenis_insentif_status: res_data_jenis_insentif_status,
                                        };
                                } else {
                                        console.log(paramID);

                                        const update = await prisma.tb_jenis_insentif.update({
                                                where: { id_jenis_insentif: paramID },
                                                data: {
                                                        nama: request.input("nama") || undefined,  // Update only if value exists
                                                        keterangan: request.input("keterangan") || undefined, // Update only if value exists
                                                        status: request.input("status") ? parseInt(request.input("status")) : undefined, // Parse to number if exists
                                                        tipe: request.input("tipe") ? parseInt(request.input("tipe")) : undefined,
                                                        urutan: request.input("urutan") ? parseInt(request.input("urutan")) : undefined,
                                                        id_adm_provinsi: request.input("id_adm_provinsi") ? parseInt(request.input("id_adm_provinsi")) : undefined,
                                                        id_adm_kabkot: request.input("id_adm_kabkot") ? parseInt(request.input("id_adm_kabkot")) : undefined,
                                                        id_kategori_insentif: request.input("id_kategori_insentif") ? parseInt(request.input("id_kategori_insentif")) : undefined,
                                                }
                                        });

                                        // File data update logic
                                        const data_jenis_insentif_file = {
                                                id_jenis_insentif: update.id_jenis_insentif,
                                                tipe: request.input("file_tipe") ? parseInt(request.input("file_tipe")) : undefined,
                                                nama: fileName1 || undefined,
                                                judul: request.input("judul") || undefined,
                                                keterangan: request.input("file_keterangan") || undefined,
                                        };

                                        const existing_data_jenis_insentif_file = await prisma.tb_jenis_insentif_file.findFirst({
                                                where: { id_jenis_insentif: update.id_jenis_insentif }
                                        });

                                        let res_data_jenis_insentif_file;
                                        if (existing_data_jenis_insentif_file) {
                                                res_data_jenis_insentif_file = await prisma.tb_jenis_insentif_file.update({
                                                        where: { id_jenis_insentif_file: existing_data_jenis_insentif_file.id_jenis_insentif_file },
                                                        data: data_jenis_insentif_file,
                                                });
                                        }

                                        // Similar checks for other related data
                                        const data_jenis_insentif_file_tr = {
                                                id_jenis_insentif: update.id_jenis_insentif,
                                                kd_bahasa: request.input("kd_bahasa") || undefined,
                                                judul: request.input("tr_judul") || undefined,
                                                keterangan: request.input("tr_keterangan") || undefined,
                                                nama: fileName2 || undefined,
                                                tipe: request.input("tr_tipe") ? parseInt(request.input("tr_tipe")) : undefined,
                                        };

                                        const existing_data_jenis_insentif_file_tr = await prisma.tb_jenis_insentif_file_tr.findFirst({
                                                where: { id_jenis_insentif: paramID }
                                        });

                                        let res_data_jenis_insentif_file_tr;

                                        if (existing_data_jenis_insentif_file_tr) {
                                                res_data_jenis_insentif_file_tr = await prisma.tb_jenis_insentif_file_tr.update({
                                                        where: { id_jenis_insentif_file_tr: existing_data_jenis_insentif_file_tr.id_jenis_insentif_file_tr },
                                                        data: data_jenis_insentif_file_tr,
                                                });
                                        }

                                        // Update tb_jenis_insentif_kbli
                                        const data_jenis_insentif_kbli = {
                                                id_jenis_insentif: update.id_jenis_insentif,
                                                id_kbli: request.input("id_kbli") ? parseInt(request.input("id_kbli")) : undefined,
                                        };

                                        const existing_data_jenis_insentif_kbli = await prisma.tb_jenis_insentif_kbli.findFirst({
                                                where: { id_jenis_insentif: update.id_jenis_insentif }
                                        });

                                        let res_data_jenis_insentif_kbli;
                                        if (existing_data_jenis_insentif_kbli) {
                                                res_data_jenis_insentif_kbli = await prisma.tb_jenis_insentif_kbli.update({
                                                        where: { id_jenis_insentif: existing_data_jenis_insentif_kbli.id_jenis_insentif },
                                                        data: data_jenis_insentif_kbli,
                                                });
                                        }

                                        // Status update
                                        const data_jenis_insentif_status = {
                                                id_jenis_insentif: update.id_jenis_insentif,
                                                status: update.status || 0,
                                                status_proses: request.input("st_status_proses") ? parseInt(request.input("st_status_proses")) : undefined,
                                                keterangan: request.input("st_keterangan") || undefined,
                                                created_by: request.input("created_by") ? parseInt(request.input("created_by")) : undefined,
                                                created_date: request.input("created_date") ? new Date(request.input("created_date")) : undefined,
                                                updated_by: request.input("updated_by") ? parseInt(request.input("updated_by")) : undefined,
                                                updated_date: request.input("updated_date") ? new Date(request.input("updated_date")) : undefined,
                                        };

                                        const existing_data_jenis_insentif_status = await prisma.tb_jenis_insentif_status.findFirst({
                                                where: { id_jenis_insentif: update.id_jenis_insentif }
                                        });

                                        let res_data_jenis_insentif_status;
                                        if (existing_data_jenis_insentif_status) {
                                                res_data_jenis_insentif_status = await prisma.tb_jenis_insentif_status.update({
                                                        where: { id_jenis_insentif_status: existing_data_jenis_insentif_status.id_jenis_insentif_status },
                                                        data: data_jenis_insentif_status,
                                                });
                                        }

                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update,
                                                data_jenis_insentif_file: res_data_jenis_insentif_file,
                                                data_jenis_insentif_file_tr: res_data_jenis_insentif_file_tr,
                                                data_jenis_insentif_kbli: res_data_jenis_insentif_kbli,
                                                data_jenis_insentif_status: res_data_jenis_insentif_status,
                                        };

                                }

                        });

                        return response.json(result);
                } catch (error) {
                        console.log(error);
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error,
                        });
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_jenis_insentif.delete({
                                where: { id_jenis_insentif: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }
}