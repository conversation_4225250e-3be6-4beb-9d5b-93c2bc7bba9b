import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';

import prisma from '../../../lib/prisma.js';
import uploadFile from '../../../helpers/file_uploader.js';



export default class RoadmapInputDataTableController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_roadmap: { column: 'id_roadmap', alias: 'id_roadmap', type: 'int' },
                        id_komoditi: { column: 'id_komoditi', alias: 'id_komoditi', type: 'int' },
                        status: { column: 'status', alias: 'status', type: 'int' },
                        judul: { column: 'judul', alias: 'judul', type: 'string' },
                        deskripsi: { column: 'deskripsi', alias: 'deskripsi', type: 'string' }

                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;

                let q = request.input('q')?.trim() || '';
                console.log(q)
                let whereDefault = {
                    OR: [
                        {
                            judul: {
                                contains: q,
                                mode: 'insensitive',
                            },
                        },
                        {
                            tb_komoditi_nasional_ref: {
                                nama: {
                                    contains: q,
                                    mode: 'insensitive',
                                },
                            },
                        }
                    ],
                };

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        orderBy?: { [key: string]: string };
                        where?: any;
                        include?: any;
                }

                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        include: {
                                tb_komoditi_nasional_ref: true
                        },
                        where: {
                                ...(q ? whereDefault : {}),
                        }
                }
                let order = queryParams.order 
                const by = ['asc','desc'].includes(queryParams.by) ? queryParams.by : 'asc'
                const paramList = ['status','komoditi']
                if (order != undefined && paramList.includes(order)) {
                        if (order == 'komoditi') {
                                
                                options.orderBy = { tb_komoditi_nasional_ref :{nama:by}}
                        }else{
                                
                                options.orderBy = {[order]:by}
                        }
                }
                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                let where = modelWhereAnd((await this.schemaData()).columns, queryParams);

                if (Object.keys(where).length !== 0) {
                        options['where'] = {
                            AND: [
                                whereDefault, // Kondisi pencarian `OR`
                                where,        // Kondisi tambahan dari `modelWhereAnd`
                            ],
                        };
                }

                try {
                        const data = await prisma.tb_roadmap.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_roadmap.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                // const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        // status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }

        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {
                        include: {
                                tb_komoditi_nasional_ref: true,
                                tb_roadmap_tr:true
                        }
                };

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_roadmap': parseInt(params.id)
                }

                const data = await prisma.tb_roadmap.findFirst(options);
                let dataFiles = await prisma.tb_roadmap_file.findMany({
                        where: {
                                id_roadmap: parseInt(params.id)
                        }
                });
                let dataFilesTr = await prisma.tb_roadmap_file_tr.findMany({
                        where: {
                                id_roadmap: parseInt(params.id)
                        }
                });
                dataFiles = dataFiles.map((item) => {
                        return {
                                ...item, // Spread item untuk mempertahankan properti yang ada
                                path: `${process.env.APP_URL}/uploads/roadmap/${item.nama}`,
                        };
                });

                dataFilesTr = dataFilesTr.map((item) => {
                        return {
                                ...item, // Spread item untuk mempertahankan properti yang ada
                                path: `${process.env.APP_URL}/uploads/roadmap/${item.nama}`,
                        };
                });

                data['files'] = dataFiles;
                data['files_tr'] = dataFilesTr;

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }

        public async createOrUpdate({ request, response, params }: HttpContext) {
                const paramID = parseInt(params.id) || request.input('id_roadmap');
                const model = "roadmap_input_data_table";

                try {
                        const result = await prisma.$transaction(async (prisma) => {
                                let resID: number | undefined;

                                if (!paramID) {
                                        console.log("Sync tb_roadmap id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_roadmap"', 'id_roadmap')),
                                                (SELECT (MAX("id_roadmap") + 1) FROM "tb_roadmap"),
                                                false) FROM "tb_roadmap";
                                         `);

                                        const res = await prisma.tb_roadmap.create({
                                                data: {
                                                        id_komoditi: parseInt(request.input("id_komoditi")) || 0,
                                                        judul: request.input("judul") || "",
                                                        deskripsi: request.input("deskripsi") || "",
                                                        status: 0,
                                                }
                                        });
                                        resID = res.id_roadmap;

                                        const resEn = await prisma.tb_roadmap_tr.create({
                                                data: {
                                                        id_roadmap:resID,
                                                        judul: request.input("judul_tr") || "",
                                                        deskripsi: request.input("deskripsi_tr") || "",
                                                }
                                        });


                                        console.log("Sync tb_roadmap_file id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_roadmap_file"', 'id_roadmap_file')),
                                                (SELECT (MAX("id_roadmap_file") + 1) FROM "tb_roadmap_file"),
                                                false) FROM "tb_roadmap_file";
                                         `);
                                        
                                        for (let n in request.files('nama_file')) {
                                                let fileParam : any = {};
                                                let file = request.files('nama_file')[n];
                                                if (file) {
                                                        let uploadFileToServer = await uploadFile(file, model, undefined);
                                                        let filenameFromServer = '';
                                                        if (uploadFileToServer) {
                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                        }
                                                        let filename = filenameFromServer; // Set fileName if file exists
                                                        fileParam['nama'] = filename;
                                                        fileParam['id_roadmap'] = resID;

                                                        await prisma.tb_roadmap_file.create({
                                                                data: fileParam
                                                        });
                                                }
                                        }

                                        for (let n in request.files('nama_file_tr')) {
                                                let fileParam : any = {};
                                                let file = request.files('nama_file_tr')[n];
                                                if (file) {
                                                        let uploadFileToServer = await uploadFile(file, model, undefined);
                                                        let filenameFromServer = '';
                                                        if (uploadFileToServer) {
                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                        }
                                                        let filename = filenameFromServer; // Set fileName if file exists
                                                        fileParam['nama'] = filename;
                                                        fileParam['id_roadmap'] = resID;

                                                        await prisma.tb_roadmap_file_tr.create({
                                                                data: fileParam
                                                        });
                                                }
                                        }

                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: res
                                        };


                                } else {
                                        const update = await prisma.tb_roadmap.update({
                                                where: { id_roadmap: parseInt(paramID) },
                                                data: {
                                                        id_komoditi: parseInt(request.input("id_komoditi")) || 0,
                                                        judul: request.input("judul") || "",
                                                        deskripsi: request.input("deskripsi") || "",
                                                        status: 0,
                                                }
                                        });
                                        const idTr =  parseInt(request.input("id_roadmap_tr")) || 0

                                        const updateEn = await prisma.tb_roadmap_tr.update({
                                                where: { id_roadmap_tr:idTr },
                                                data: {
                                                        judul: request.input("judul_tr") || "",
                                                        deskripsi: request.input("deskripsi_tr") || "",
                                                }
                                        });

                                        let roadmapFileIds: number[] = [];

                                        for (let x in request.input('id_roadmap_file')) {
                                                let idRoadmapFile = parseInt(request.input('id_roadmap_file')[x]);
                                                roadmapFileIds.push(idRoadmapFile);
                                        }

                                        for (let n in request.files('nama_file')) {
                                                let fileParam : any = {};
                                                let file = request.files('nama_file')[n];

                                                const fieldName = file.fieldName; // Mendapatkan nama field, contoh: "nama_file[2]"
                                                const match = fieldName.match(/\[(\d+)\]/); // Ekstrak indeks dari nama field
                                                const index = match ? parseInt(match[1]) : 0; // Jika ada indeks, gunakan; jika tidak, gunakan 0
                                                let idRoadmapFile = 0;
                                                const element = Array.isArray(request.input('id_roadmap_file')) 
                                                              ? request.input('id_roadmap_file')[index] 
                                                              : undefined;
                                                if (element) {
                                                  idRoadmapFile = parseInt(request.input('id_roadmap_file')[index]);
                                                }

                                                if (file) {
                                                        let uploadFileToServer = await uploadFile(file, model, undefined);
                                                        let filenameFromServer = '';
                                                        if (uploadFileToServer) {
                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                        }
                                                        let filename = filenameFromServer; // Set fileName if file exists
                                                        fileParam['nama'] = filename;
                                                        fileParam['id_roadmap'] = parseInt(paramID);

                                                        if (idRoadmapFile) {
                                                                await prisma.tb_roadmap_file.update({
                                                                        where: {
                                                                                id_roadmap_file: idRoadmapFile
                                                                        },
                                                                        data: fileParam
                                                                });
                                                        } else {
                                                                let saveFile = await prisma.tb_roadmap_file.create({
                                                                        data: fileParam
                                                                });

                                                                roadmapFileIds.push(saveFile.id_roadmap_file);
                                                        }
                                                }
                                        }

                                        await prisma.tb_roadmap_file.deleteMany({
                                                where: { 
                                                        id_roadmap: parseInt(paramID),
                                                        id_roadmap_file: {
                                                                notIn: roadmapFileIds
                                                        }
                                                }
                                        });

                                        let roadmapFileTrIds: number[] = [];
                                        for (let x in request.input('id_roadmap_file_tr')) {
                                                let idRoadmapFileTr = parseInt(request.input('id_roadmap_file_tr')[x]);
                                                roadmapFileTrIds.push(idRoadmapFileTr);
                                        }

                                        for (let n in request.files('nama_file_tr')) {
                                                let fileParam : any = {};
                                                let file = request.files('nama_file_tr')[n];

                                                const fieldName = file.fieldName; // Mendapatkan nama field, contoh: "nama_file_tr[2]"
                                                const match = fieldName.match(/\[(\d+)\]/); // Ekstrak indeks dari nama field
                                                const index = match ? parseInt(match[1]) : 0; // Jika ada indeks, gunakan; jika tidak, gunakan 0
                                                let idRoadmapFileTr = 0;
                                                const element = Array.isArray(request.input('id_roadmap_file_tr')) 
                                                              ? request.input('id_roadmap_file_tr')[index] 
                                                              : undefined;
                                                if (element) {
                                                  idRoadmapFileTr = parseInt(request.input('id_roadmap_file_tr')[index]);
                                                }

                                                if (file) {
                                                        let uploadFileToServer = await uploadFile(file, model, undefined);
                                                        let filenameFromServer = '';
                                                        if (uploadFileToServer) {
                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                        }
                                                        let filename = filenameFromServer; // Set fileName if file exists
                                                        fileParam['nama'] = filename;
                                                        fileParam['id_roadmap'] = parseInt(paramID);

                                                        if (idRoadmapFileTr) {
                                                                await prisma.tb_roadmap_file_tr.update({
                                                                        where: {
                                                                                id_roadmap_file_tr: idRoadmapFileTr
                                                                        },
                                                                        data: fileParam
                                                                });
                                                        } else {
                                                                let saveFile = await prisma.tb_roadmap_file_tr.create({
                                                                        data: fileParam
                                                                });

                                                                roadmapFileTrIds.push(saveFile.id_roadmap_file_tr);
                                                        }
                                                }
                                        }

                                        await prisma.tb_roadmap_file_tr.deleteMany({
                                                where: { 
                                                        id_roadmap: parseInt(paramID),
                                                        id_roadmap_file_tr: {
                                                                notIn: roadmapFileTrIds
                                                        }
                                                }
                                        });

                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update
                                        };
                                }
                        });

                        return response.json(result);
                } catch (error) {
                        console.log(error);
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error.message,
                        });
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_roadmap.delete({
                                where: { id_roadmap: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response , auth}: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }
                
                try {
                        const toggleStatus = await prisma.tb_roadmap.update({
                                where: {
                                        id_roadmap: Id,
                                },
                                data: {
                                        status: parseInt(request.input('status')) ?? 0
                                },
                        })
                        
                        // await auth.check()
                        const insert = await prisma.tb_roadmap_status.create({
                                data: {
                                    id_roadmap: Id,
                                    status: parseInt(request.input('status')),
                                    status_proses: parseInt(request.input('status')),
                                    keterangan: request.input('keterangan'),
                                    created_by: 1,
                                    updated_by: 1,
                                    created_date:new Date()
                                },
                            });

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        console.log(error);
                        return response.status(500).send({ error: 'Error Toggle Status' ,message : error.message})
                }
        }
}