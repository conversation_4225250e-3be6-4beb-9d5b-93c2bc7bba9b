import type { HttpContext } from '@adonisjs/core/http'
import env from '#start/env'
import prisma from '../lib/prisma.js'
import { checkFileOrUrl, get_detail_ip, numberFormat, numberFormatRpLengkap, numberFormatRpSingkatan, rupiah, send_mail } from '../helpers/global_helper.js'
import { Application } from '@adonisjs/core/app'
import { Env } from '@adonisjs/core/env'
import { isTemplateExpression } from 'typescript'
import { getPeluangValidator } from '#validators/peluang_investasi'
import { createPostDokumenValidator, createPostFaqValidator } from '#validators/post'
import fs from 'fs';
import path from 'path';
import archiver from 'archiver';
import { base64 } from '@adonisjs/core/helpers'
import { countViewerValidator } from '../validators/peluang_investasi.js'
import requestIp from 'request-ip';
import striptags from 'striptags'


export default class PeluangInvestasisController {

    async slider({request}: HttpContext) {
        const datas = await prisma.tb_app_slider_peluang.findMany({
            orderBy:{
                ordering: 'asc'
            },
            include:{
                tr:true
            }
        });
        let data
        const parameter = request.qs()
        if (!parameter.en) {
            data = datas.map(({ tr,...item }) => ({
                ...item,
                nama_file_image: `${env.get('APP_URL')}/uploads/slider/${item.nama_file_image}`
            }));
        } else {
            data = datas.map(({ tr,...item }) => ({
                ...item,
                nama_file_image: tr[0]?.nama_file_image != undefined ?  `${env.get('APP_URL')}/uploads/slider/${tr[0]?.nama_file_image}` : `${env.get('APP_URL')}/uploads/slider/${item.nama_file_image}`,
                judul : tr[0]?.judul || item.judul,
                deskripsi : tr[0]?.deskripsi || item.deskripsi,
            }));
        }

        return {
            success : true,
            data : {data}
        }
    }

    async peluang_investasi_card({params,response,request}: HttpContext) {
        const parameters = request.qs()
        // const [ppi, pid] = await Promise.all([
        //  prisma.tb_peluang_kabkot.findMany({
        //     select:{
        //         nama:true,
        //         nilai_investasi:true,
        //         nilai_irr:true,
        //         nilai_npv:true,
        //         nilai_pp:true,
        //         project_status_enum:true,
        //         is_ipro:true,
        //         tb_peluang_sektor:{
        //             select:{
        //                 kategori_sektor:{
        //                     select:{
        //                         nama:true,
        //                     }
        //                 },
        //                 tb_peluang_sektor_tr:{
        //                     select:{
        //                         nama:true
        //                     }
        //                 }
        //             }
        //         },
        //         tb_adm_kabkot:{
        //             select:{
        //                 nama:true,
        //                 tb_adm_kabkot_tr:{
        //                     select:{
        //                         nama:true
        //                     }
        //                 },
        //                 tb_adm_provinsi:{
        //                     select:{
        //                         nama:true,
        //                         tb_adm_provinsi_tr:{
        //                             select:{
        //                                 nama:true
        //                             }
        //                         }
        //                     }
        //                 },
        //             }
        //         },
        //         tb_peluang_kabkot_tr:{
        //             select:{
        //                 nama:true
        //             }
        //         }
        //     },
        //     where: {
        //             status:"99",
        //             id_prioritas:1,
        //     },
        //     orderBy:{
        //         nilai_investasi:'desc'
        //     }
        // }),
        // prisma.$queryRaw`
        //     SELECT
        //         p.judul as nama,
        //         k.initial_invesment as nilai_investasi,
        //         k.irr as nilai_irr,
        //         k.npv as nilai_npv,
        //         k.pp as nilai_pp,
        //         skn.nama as nama_sektor,
        //         ak.nama as nama_kabkot,
        //         ap.nama as nama_provinsi,
        //         pt.judul as nama_tr
        //     FROM tb_peluang_daerah p
        //     LEFT JOIN tb_peluang_daerah_tr pt
        //     on pt.id_peluang_daerah=p.id_peluang_daerah
        //     LEFT JOIN tb_peluang_daerah_kelayakan k
        //         ON p.id_peluang_daerah = k.id_peluang_daerah
        //     LEFT JOIN tb_sub_sektor_daerah ssd
        //         ON p.id_sub_sektor_daerah = ssd.id_sub_sektor_daerah
        //     LEFT JOIN tb_sub_sektor_nasional ssn
        //         ON ssd.id_sub_sektor_nasional = ssn.id_sub_sektor_nasional
        //     LEFT JOIN tb_sektor_nasional sn
        //         ON sn.id_sektor_nasional = ssn.id_sektor_nasional
        //     LEFT JOIN tb_sektor_nasional_ref snf
        //         ON sn.id_sektor_nasional = snf.id_sektor
        //     LEFT JOIN tb_kategori_sektor skn
        //         ON skn.id_kategori_sektor = snf.id_kategori_sektor
        //     LEFT JOIN tb_adm_kabkot ak
        //         on ak.id_adm_kabkot = p.id_adm_kabkot
        //     LEFT JOIN tb_adm_provinsi ap
        //         on ap.id_adm_provinsi = p.id_adm_provinsi
        //     WHERE p.status = 99
        //     ORDER BY k.initial_invesment DESC;
        // `
        // ]);

        // // Mapping data dengan keterangan masing-masing
        // const proyeks = [
        // ...ppi.map(item => {
        //     if (!parameters.en) {
        //         return {
        //             nama:item.nama,
        //             nama_kabkot:item.tb_adm_kabkot.nama,
        //             nama_provinsi:item.tb_adm_kabkot.tb_adm_provinsi.nama,
        //             nilai_investasi:item.nilai_investasi,
        //             nilai_irr:item.nilai_irr,
        //             nilai_npv:item.nilai_npv,
        //             nilai_pp:item.nilai_pp,
        //             nama_sektor : item.tb_peluang_sektor.kategori_sektor?.nama,
        //             project_status_enum:item.project_status_enum,
        //             status: item.is_ipro == true ? 'IPRO': 'PPI',
        //         }
        //     } else {
        //         return {
        //             nama:item.tb_peluang_kabkot_tr[0]?.nama || item.nama,
        //             nama_kabkot: item.tb_adm_kabkot.tb_adm_kabkot_tr[0]?.nama || item.tb_adm_kabkot.nama,
        //             nama_provinsi:item.tb_adm_kabkot.tb_adm_provinsi.tb_adm_provinsi_tr[0]?.nama ||item.tb_adm_kabkot.tb_adm_provinsi.nama,
        //             nilai_investasi:item.nilai_investasi,
        //             nilai_irr:item.nilai_irr,
        //             nilai_npv:item.nilai_npv,
        //             nilai_pp:item.nilai_pp,
        //             nama_sektor : item.tb_peluang_sektor.kategori_sektor?.nama,
        //             project_status_enum:item.project_status_enum,
        //             status: item.is_ipro == true ? 'IPRO': 'PPI',
        //         }
        //     }

        // }),
        // ...pid.map(item => {
        //     if (!parameters.en) {
        //         return {
        //             nama:item.nama,
        //             nilai_investasi:item.nilai_investasi,
        //             nilai_irr:item.nilai_irr,
        //             nilai_npv:item.nilai_npv,
        //             nilai_pp:item.nilai_pp,
        //             nama_sektor:item.nama_sektor,
        //             project_status_enum:null,
        //             status: 'PID',
        //         }
        //     }else{
        //         return {
        //             nama:item.nama_tr || item.nama,
        //             nilai_investasi:item.nilai_investasi,
        //             nilai_irr:item.nilai_irr,
        //             nilai_npv:item.nilai_npv,
        //             nilai_pp:item.nilai_pp,
        //             nama_sektor:item.nama_sektor,
        //             project_status_enum:null,
        //             status: 'PID',
        //         }
        //     }

        // }),
        // ];
        // // hitung total proyek
        // const total_proyeks = proyeks.map(item => {
        //     return {
        //         nama : item.nama,
        //         nilai:item.nilai_investasi
        //     }
        // })

        // const count = proyeks.reduce(
        //     (acc, curr) => {
        //         acc.proyek += 1;
        //         acc.nilai += curr?.nilai_investasi;
        //         return acc;
        //     },
        //     { proyek: 0, nilai: 0 }
        // );
        // const label_total_proyek = parameters.en ? 'Project' : 'Peluang Investasi'
        // const label_desc_poyek = parameters.en ? `with ${numberFormatRpLengkap(count.nilai)} of total investment` : `dengan total Investasi ${numberFormatRpLengkap(count.nilai)}`
        // const proyek = {
        //     total :`${count.proyek} ${label_total_proyek}` ,
        //     deskripsi : label_desc_poyek,
        //     detail: total_proyeks
        // }


        // Mengambil data dengan Prisma
        const [ppi] = await Promise.all([
            prisma.tb_peluang_kabkot.findMany({
            select: {
                nama: true,
                nilai_investasi: true,
                nilai_irr: true,
                nilai_npv: true,
                nilai_pp: true,
                project_status_enum: true,
                is_ipro: true,
                tb_peluang_sektor: {
                select: {
                    kategori_sektor: {
                    select: {
                        nama: true,
                    },
                    },
                    tb_peluang_sektor_tr: {
                    select: {
                        nama: true,
                    },
                    },
                },
                },
                tb_adm_kabkot: {
                select: {
                    nama: true,
                    tb_adm_kabkot_tr: {
                    select: {
                        nama: true,
                    },
                    },
                    tb_adm_provinsi: {
                    select: {
                        nama: true,
                        tb_adm_provinsi_tr: {
                        select: {
                            nama: true,
                        },
                        },
                    },
                    },
                },
                },
                tb_peluang_kabkot_tr: {
                select: {
                    nama: true,
                },
                },
            },
            where: {
                status: "99",
                id_prioritas: 1,
                project_status_enum:{
                    not:'-1'
                }
            },
            orderBy: {
                nilai_investasi: "desc",
            },
            }),
        ]);

        // Mengambil data dengan raw query
        // const pid = await prisma.$queryRaw`
        //     SELECT
        //         p.judul as nama,
        //         k.initial_invesment as nilai_investasi,
        //         k.irr as nilai_irr,
        //         k.npv as nilai_npv,
        //         k.pp as nilai_pp,
        //         skn.nama as nama_sektor,
        //         ak.nama as nama_kabkot,
        //         ap.nama as nama_provinsi,
        //         pt.judul as nama_tr
        //     FROM tb_peluang_daerah p
        //     LEFT JOIN tb_peluang_daerah_tr pt ON pt.id_peluang_daerah = p.id_peluang_daerah
        //     LEFT JOIN tb_peluang_daerah_kelayakan k ON p.id_peluang_daerah = k.id_peluang_daerah
        //     LEFT JOIN tb_sub_sektor_daerah ssd ON p.id_sub_sektor_daerah = ssd.id_sub_sektor_daerah
        //     LEFT JOIN tb_sub_sektor_nasional ssn ON ssd.id_sub_sektor_nasional = ssn.id_sub_sektor_nasional
        //     LEFT JOIN tb_sektor_nasional sn ON sn.id_sektor_nasional = ssn.id_sektor_nasional
        //     LEFT JOIN tb_sektor_nasional_ref snf ON sn.id_sektor_nasional = snf.id_sektor
        //     LEFT JOIN tb_kategori_sektor skn ON skn.id_kategori_sektor = snf.id_kategori_sektor
        //     LEFT JOIN tb_adm_kabkot ak ON ak.id_adm_kabkot = p.id_adm_kabkot
        //     LEFT JOIN tb_adm_provinsi ap ON ap.id_adm_provinsi = p.id_adm_provinsi
        //     WHERE p.status = 99
        //     ORDER BY k.initial_invesment DESC;
        // `;

        // Gabungkan hasil data dan mapping
        const proyeks = [
            ...ppi.map((item) => ({
            nama: parameters.en ? item.tb_peluang_kabkot_tr[0]?.nama || item.nama : item.nama,
            nama_kabkot: parameters.en ? item.tb_adm_kabkot.tb_adm_kabkot_tr[0]?.nama || item.tb_adm_kabkot.nama : item.tb_adm_kabkot.nama,
            nama_provinsi: parameters.en
                ? item.tb_adm_kabkot.tb_adm_provinsi.tb_adm_provinsi_tr[0]?.nama || item.tb_adm_kabkot.tb_adm_provinsi.nama
                : item.tb_adm_kabkot.tb_adm_provinsi.nama,
            nilai_investasi: item.nilai_investasi,
            nilai_irr: item.nilai_irr,
            nilai_npv: item.nilai_npv,
            nilai_pp: item.nilai_pp,
            nama_sektor: item.tb_peluang_sektor.kategori_sektor?.nama,
            project_status_enum:item.project_status_enum,
            status: item.is_ipro ? "IPRO" : "PPI",
            })),
            // ...pid.map((item) => ({
            //     nama: parameters.en ? item.nama_tr || item.nama : item.nama,
            //     nama_kabkot: item.nama_kabkot,
            //     nama_provinsi: item.nama_provinsi,
            //     nilai_investasi: item.nilai_investasi,
            //     nilai_irr: item.nilai_irr,
            //     nilai_npv: item.nilai_npv,
            //     nilai_pp: item.nilai_pp,
            //     nama_sektor: item.nama_sektor,
            //     project_status_enum:item.project_status_enum,
            //     status: "PID",
            // })),
        ];


        // Menghitung total proyek
        const totalNilai = proyeks.reduce((acc, curr) => acc + (curr.nilai_investasi || 0), 0);
        const labelTotalProyek = parameters.en ? "Project" : "Peluang Investasi";
        const labelDescProyek = parameters.en
            ? `with ${numberFormatRpLengkap(totalNilai)} of total investment`
            : `dengan total Investasi ${numberFormatRpLengkap(totalNilai)}`;

        const proyek = {
            total: `${proyeks.length} ${labelTotalProyek}`,
            deskripsi: labelDescProyek,
            detail: proyeks.map(({ nama, nilai_investasi }) => ({ nama, nilai: nilai_investasi })),
        };
        //grouping sektor
        const groupedSektor = proyeks.reduce((acc, curr) => {
            if (!acc[curr.nama_sektor]) {
                acc[curr.nama_sektor] = { nama: curr.nama_sektor, nilai: 0 };
            }
            acc[curr.nama_sektor].nilai += curr.nilai_investasi;

            return acc;
        }, {});
        const sektorGroup = Object.values(groupedSektor);
        const label_total_sektor = parameters.en ? ` ${sektorGroup.length} Sector in` : ` ${sektorGroup.length} Sektor`
        const label_desc_sektor = parameters.en ? ` Provience of Indonesia` : ` di 34 Provinsi Indonesia`

        const sektor = {
            total : label_total_sektor,
            deskripsi : label_desc_sektor,
            detail : sektorGroup
        }
        // pengunjung
        const totalPengunjung = await prisma.tb_halaman_pengunjung_det.count({
            where: {
                id_halaman_pengunjung: {
                    in: [6,7,8],
                },
            },
        });

        //     const detailPengunjungs = await prisma.$queryRaw`
        //         SELECT
        //             nama,
        //             SUM(jumlah) AS jumlah
        //         FROM (
        //             SELECT
        //                 tks.nama AS nama,
        //                 COUNT(*) AS jumlah
        //             FROM tb_halaman_pengunjung_det thpd
        //             JOIN tb_peluang_kabkot tpk ON thpd.id_konten = tpk.id_peluang_kabkot
        //             JOIN tb_peluang_sektor tps ON tps.id_peluang_sektor = tpk.id_sektor
        //             JOIN tb_kategori_sektor tks ON tks.id_kategori_sektor = tps.id_kategori_sektor
        //             WHERE thpd.id_halaman_pengunjung IN (6, 7)
        //             GROUP BY tks.nama

        //             UNION ALL

        //             SELECT
        //                 skn.nama AS nama,
        //                 COUNT(*) AS jumlah
        //             FROM tb_halaman_pengunjung_det thpd
        //             LEFT JOIN tb_peluang_daerah p ON p.id_peluang_daerah = thpd.id_konten
        //             LEFT JOIN tb_peluang_daerah_kelayakan k
        //                 ON p.id_peluang_daerah = k.id_peluang_daerah
        //             LEFT JOIN tb_sub_sektor_daerah ssd
        //                 ON p.id_sub_sektor_daerah = ssd.id_sub_sektor_daerah
        //             LEFT JOIN tb_sub_sektor_nasional ssn
        //                 ON ssd.id_sub_sektor_nasional = ssn.id_sub_sektor_nasional
        //             LEFT JOIN tb_sektor_nasional sn
        //                 ON sn.id_sektor_nasional = ssn.id_sektor_nasional
        //             LEFT JOIN tb_sektor_nasional_ref snf
        //                 ON sn.id_sektor_nasional = snf.id_sektor
        //             LEFT JOIN tb_kategori_sektor skn
        //                 ON skn.id_kategori_sektor = snf.id_kategori_sektor
        //             WHERE thpd.id_halaman_pengunjung IN (8)
        //             AND skn.nama IS NOT NULL
        //             GROUP BY skn.nama
        //         ) AS gabungan
        //         GROUP BY nama
        //         ORDER BY jumlah DESC;
        //     `;

        // const detailPengunjung = detailPengunjungs.map(item => {
        //     return {
        //         nama:item.nama,
        //         nilai :parseInt(item.jumlah),
        //     }
        // })
        // const label_desc_pengunjung = parameters.en ? ` Visitors` : `pengunjung`

        // const pengunjung = {
        //     total : `${numberFormat(totalPengunjung)} Total`,
        //     deskripsi : label_desc_pengunjung,
        //     detail : detailPengunjung
        // }

        // Query raw untuk mendapatkan data pengunjung
        const detailPengunjung = await prisma.$queryRaw`
        SELECT
        nama,
        SUM(jumlah) AS jumlah
        FROM (
        SELECT tks.nama, COUNT(*) AS jumlah
        FROM tb_halaman_pengunjung_det thpd
        JOIN tb_peluang_kabkot tpk ON thpd.id_konten = tpk.id_peluang_kabkot
        JOIN tb_peluang_sektor tps ON tps.id_peluang_sektor = tpk.id_sektor
        JOIN tb_kategori_sektor tks ON tks.id_kategori_sektor = tps.id_kategori_sektor
        WHERE thpd.id_halaman_pengunjung IN (6, 7)
        GROUP BY tks.nama

        UNION ALL

        SELECT skn.nama, COUNT(*) AS jumlah
        FROM tb_halaman_pengunjung_det thpd
        LEFT JOIN tb_peluang_daerah p ON p.id_peluang_daerah = thpd.id_konten
        LEFT JOIN tb_peluang_daerah_kelayakan k ON p.id_peluang_daerah = k.id_peluang_daerah
        LEFT JOIN tb_sub_sektor_daerah ssd ON p.id_sub_sektor_daerah = ssd.id_sub_sektor_daerah
        LEFT JOIN tb_sub_sektor_nasional ssn ON ssd.id_sub_sektor_nasional = ssn.id_sub_sektor_nasional
        LEFT JOIN tb_sektor_nasional sn ON sn.id_sektor_nasional = ssn.id_sektor_nasional
        LEFT JOIN tb_sektor_nasional_ref snf ON sn.id_sektor_nasional = snf.id_sektor
        LEFT JOIN tb_kategori_sektor skn ON skn.id_kategori_sektor = snf.id_kategori_sektor
        WHERE thpd.id_halaman_pengunjung IN (8) AND skn.nama IS NOT NULL
        GROUP BY skn.nama
        ) AS gabungan
        GROUP BY nama
        ORDER BY jumlah DESC;
        `;

        // Mengubah data hasil query menjadi format yang diinginkan
        const pengunjung = {
        total: `${numberFormat(totalPengunjung)} Total`,
        deskripsi: parameters.en ? 'Visitors' : 'pengunjung',
        detail: detailPengunjung.map(({ nama, jumlah }) => ({
                nama,
                nilai: parseInt(jumlah)
            }))
        };


        // diminati
        const totalDiminati = proyeks.filter(item => item.project_status_enum === "2").length;
        const detailDiminati = proyeks.filter(item => item.project_status_enum === "2").map( item => {
            let tahun = 'Tahun'
            if (parameters.en) {
                tahun = 'Year'
            }
            return {
                nama: item.nama,
                irr: item.nilai_irr,
                npv: numberFormatRpLengkap(item.nilai_npv),
                pp: `${item.nilai_pp} ${tahun}`,
                status : 'diminati'
            }
        })
        // const totaltakDiminati = proyeks.filter(item => item.project_status_enum != "2").length;
        // const detail = proyeks.filter(item => item.project_status_enum != "2").map( item => {
        //     let status = ""
        //     if (item.project_status_enum == "2") {
        //         status = "Diminati"
        //     }else if(item.project_status_enum == "3"){
        //         status = "Penjajakan"
        //     }else {
        //         status = "Perizinan"
        //     }

        //     let tahun = 'Tahun'
        //     if (parameters.en) {
        //         tahun = 'Year'
        //     }
        //     return {
        //         nama: item.nama,
        //         irr: item.nilai_irr,
        //         npv: item.nilai_npv,
        //         npv_text: numberFormatRpLengkap(item.nilai_npv),
        //         pp: item.nilai_pp,
        //         pp_text: `${item.nilai_pp} ${tahun}`,
        //         status : status
        //     }
        // })

        const status_proyek ={
            total : ` ${totalDiminati} Total`,
            deskripsi : parameters.en ? ` on demand Projects` : `Proyek yang diminati`,
            detail : detailDiminati
        }



        return {
            success : true,
            data :{
                proyek,
                sektor,
                pengunjung,
                status_proyek,
            }
        }
    }

    async peluang_investasi_sektor({params,response,request}: HttpContext) {
        const parameters = request.qs()
        const proyeks = await prisma.tb_peluang_kabkot.findMany({
            where: {
                    status:"99",
                    id_prioritas:1,
                    // project_status_enum:{
                    //     not:'-1'
                    // }
            },
            include:{
                tb_peluang_sektor:{
                    include:{
                        kategori_sektor:true,
                        tb_peluang_sektor_tr:true
                    }
                }
            },
            orderBy:{
                nilai_investasi:'desc'
            }
        });


        const data = proyeks.reduce((acc, item) => {
            let nama_sektor = item.tb_peluang_sektor.nama
            let proyek = 'Proyek'
            if (parameters.en) {
                nama_sektor = item.tb_peluang_sektor.tb_peluang_sektor_tr[0]?.nama || nama_sektor
                proyek = 'Project'

            }
            if (!acc[item.tb_peluang_sektor.nama]) {
                const img = item.tb_peluang_sektor.image == undefined ? 'uploads/no_image.png' : item.tb_peluang_sektor.image
                acc[item.tb_peluang_sektor.nama] = {
                    id_sektor: item.id_sektor,
                    nama: nama_sektor,
                    proyek: 0,
                    jumlah_proyek : '',
                    icon: `${env.get('APP_URL')}/uploads/icon-web/${item.tb_peluang_sektor.icon}`,
                    image: `${env.get('APP_URL')}/uploads/sektor/img/${item.tb_peluang_sektor.id_peluang_sektor}/${img}` ,
                    kategori_sektor : item.tb_peluang_sektor.kategori_sektor?.nama
                }
            }
            acc[item.tb_peluang_sektor.nama].proyek += 1;
            acc[item.tb_peluang_sektor.nama].jumlah_proyek = `${acc[item.tb_peluang_sektor.nama].proyek} ${proyek}`;

            return acc;
        }, {})
        // const data = groupedSektor.map(item =>{
        //     return {
        //         nama_sektor:item.nama,
        //         jumlah_proyek:`${item.proyek} Proyek`
        //     }
        // });

        return {
            success : true,
            data : data
        }
    }
    async peluang_investasi_3sektor({params,response,request}: HttpContext) {
        const proyeks = await prisma.tb_peluang_kabkot.findMany({
            where: {
                    status:"99",
                    id_prioritas:1,
                    project_status_enum:{
                        not:'-1'
                    }
            },
            include:{
                tb_peluang_sektor:{
                    include:{
                        kategori_sektor:{
                            include:{
                                tb_kategori_sektor_tr:true
                            }
                        }
                    }
                }
            },
            orderBy:{
                nilai_investasi:'desc'
            }
        });
        const parameters = request.qs()

        const data = proyeks.reduce((acc, item) => {
            let proyek = 'Proyek'
            let nama_kategori = item.tb_peluang_sektor.kategori_sektor?.nama
            if (parameters.en) {
                proyek = 'Project'
                nama_kategori = item.tb_peluang_sektor.kategori_sektor?.tb_kategori_sektor_tr[0]?.nama || nama_kategori
            }

            if (!acc[item.tb_peluang_sektor.kategori_sektor?.nama]) {
                const img = item.tb_peluang_sektor.image == undefined ? 'uploads/no_image.png' : item.tb_peluang_sektor.image?.replace('upload','uploads')
                acc[item.tb_peluang_sektor.kategori_sektor?.nama] = {
                    id_sektor: item.tb_peluang_sektor.kategori_sektor?.id_kategori_sektor,
                    nama: nama_kategori,
                    proyek: 0,
                    jumlah_proyek : '',
                    icon: `${env.get('APP_URL')}/uploads/icon-web/${item.tb_peluang_sektor.icon}`,
                    image: `${env.get('APP_URL')}/${img}` ,
                    kategori_sektor : nama_kategori
                }
            }
            acc[item.tb_peluang_sektor.kategori_sektor?.nama].proyek += 1;
            acc[item.tb_peluang_sektor.kategori_sektor?.nama].jumlah_proyek = `${acc[item.tb_peluang_sektor.kategori_sektor?.nama].proyek} ${proyek}`;

            return acc;
        }, {})



        return {
            success : true,
            data : data
        }
    }

    /**
     * @peluang_investasi_wilayah
     * @paramQuery page - Kalaman ke - @type(number)
     * @paramQuery page_size - data per halaman  - @type(number)
     * @paramQuery search - ketik text pencarian - @type(string)
     * @paramQuery id_zona_waktu - Masukan Id Zona Waktu - @type(number)
     * @paramQuery id_sektor - Masukan Id Sektor - @type(number)
     * @paramQuery id_adm_provinsi - Masukan Id Provinsi - @type(number)
     * @paramQuery id_adm_kabkot - Masukan Id Kabkot - @type(number)
     * @paramQuery id_sektor - Masukan Id Sektor - @type(number)
     */
    async peluang_investasi_wilayah({request, response}: HttpContext) {
        const dataPost = await request.validateUsing(getPeluangValidator);
        const provinsiIds = [];
        if (dataPost.id_zona_waktu) {
            const prov = await prisma.tb_adm_provinsi.findMany({
                where:{
                    id_zona_waktu : dataPost.id_zona_waktu
                },
                orderBy:{
                    id_adm_provinsi:'asc'
                }
            })
            if (prov.length > 0) {
                prov.map((item) => {
                    provinsiIds.push(item.id_adm_provinsi);
                })

            }
        }
        const [ppi, pid] = await Promise.all([
        prisma.tb_peluang_kabkot.findMany({
            select:{
                id_adm_kabkot:true,
                id_peluang_kabkot:true,
                nama:true,
                deskripsi:true,
                nilai_investasi:true,
                nilai_irr:true,
                nilai_npv:true,
                nilai_pp:true,
                id_prioritas:true,
                project_status_enum:true,
                is_ipro:true,
                tahun:true,
                tb_peluang_sektor:{
                    select:{
                        kategori_sektor:{
                            select:{
                                nama:true,
                                id_kategori_sektor:true
                            }
                        },
                        nama:true,
                        icon:true
                    }
                },
                tb_peluang_kabkot_file:{
                    select:{
                        nama:true
                    },
                    where:{
                        tipe:1
                    }
                },
                tb_adm_kabkot:{
                    select:{
                        nama:true,
                        tb_adm_provinsi:{
                            select:{
                                nama:true,
                                tb_adm_provinsi_tr:{
                                    select:{
                                        nama:true
                                    }
                                }
                            }
                        },
                        tb_adm_kabkot_tr:{
                            select:{
                                nama:true
                            }
                        }
                    }
                },
                tb_peluang_kabkot_tr:{
                    select:{
                        nama:true
                    }
                }
            },
            where: {
                    status:"99",
                    id_prioritas:1,
                    // project_status_enum:{
                    //     not:'-1'
                    // }
            },
            orderBy:{
                nilai_investasi:'desc'
            }
        }),
        prisma.$queryRaw`
            SELECT
                            p.id_peluang_daerah,
                            p.id_adm_provinsi,
                            p.id_adm_kabkot,
                            p.keterangan,
                            p.judul as nama,
                            p.tahun as tahun,
                            pt.judul as nama_tr,
                            k.initial_invesment as nilai_investasi,
                            k.irr as nilai_irr,
                            k.npv as nilai_npv,
                            k.pp as nilai_pp,
                            skn.id_kategori_sektor as id_kategori_sektor,
                            skn.nama as nama_sektor,
                            snf.nama as nama_sektor_peluang,
                            snf.icon as icon,
                            pdf.nama as images,
                            p.status,
                            ak.nama as nama_kabkot,
                            akt.nama as nama_kabkot_tr,
                            ap.nama as nama_provinsi,
                            apt.nama as nama_provinsi_tr,
                            p.project_status_enum,
                            ps.nama as status_proyek
                        FROM tb_peluang_daerah p
                        LEFT JOIN tb_peluang_daerah_tr pt
                            ON p.id_peluang_daerah = pt.id_peluang_daerah
                        LEFT JOIN tb_peluang_daerah_kelayakan k
                            ON p.id_peluang_daerah = k.id_peluang_daerah
                        LEFT JOIN tb_sub_sektor_daerah ssd
                            ON p.id_sub_sektor_daerah = ssd.id_sub_sektor_daerah
                        LEFT JOIN tb_sub_sektor_nasional ssn
                            ON ssd.id_sub_sektor_nasional = ssn.id_sub_sektor_nasional
                        LEFT JOIN tb_sektor_nasional sn
                            ON sn.id_sektor_nasional = ssn.id_sektor_nasional
                        LEFT JOIN tb_sektor_nasional_ref snf
                            ON sn.id_sektor_nasional = snf.id_sektor
                        LEFT JOIN tb_kategori_sektor skn
                            ON skn.id_kategori_sektor = snf.id_kategori_sektor
                        LEFT JOIN tb_peluang_daerah_file pdf
                            ON pdf.id_peluang_daerah = p.id_peluang_daerah AND pdf.tipe = 1
                        LEFT JOIN tb_adm_kabkot ak
                            ON ak.id_adm_kabkot = p.id_adm_kabkot
                        LEFT JOIN tb_adm_kabkot_tr akt
                            ON ak.id_adm_kabkot = akt.id_adm_kabkot
                        LEFT JOIN tb_adm_provinsi ap
                            ON ap.id_adm_provinsi = p.id_adm_provinsi
                        LEFT JOIN tb_adm_provinsi_tr apt
                            ON apt.id_adm_provinsi = ap.id_adm_provinsi
                        LEFT JOIN tb_peluang_status ps
                            ON ps.id_peluang_status = p.project_status_enum
                        WHERE p.status = 99
                        and p.project_status_enum != -1
            ORDER BY k.initial_invesment desc;
        `
        ]);

        // Mapping data dengan keterangan masing-masing
        const parameters = request.qs()
        const peluang_status = await prisma.tb_peluang_status.findMany()

        const proyeks = [

        ...ppi.map(item => {
                const status = {
                    2: 'DIMINATI',
                    7: 'SOLD',
                };

                
                let status_proyek = '';
                
                if (item.project_status_enum !== '') {
                    status_proyek = peluang_status.find((items) => items.id_peluang_status == parseInt(item.project_status_enum || '0'))?.nama || ''
                    // status_proyek = status[item.project_status_enum];
                }
                let kabkot = item.tb_adm_kabkot.nama
                let prov = item.tb_adm_kabkot.tb_adm_provinsi.nama
                let nama = item.nama
                if (parameters.en) {
                    kabkot = item.tb_adm_kabkot.tb_adm_kabkot_tr[0]?.nama || item.tb_adm_kabkot.nama
                    prov = item.tb_adm_kabkot.tb_adm_provinsi.tb_adm_provinsi_tr[0]?.nama || item.tb_adm_kabkot.tb_adm_provinsi.nama
                    nama = item.tb_peluang_kabkot_tr[0]?.nama || item.nama
                }
                let sts = 'PPI'
                if(item.is_ipro){
                    sts = 'IPRO'
                }else if(item.id_prioritas ==2){
                    sts = 'PID'
                }
                return {
                    id_peluang: item.id_peluang_kabkot,
                    id_adm_provinsi: parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_adm_kabkot: item.id_adm_kabkot,
                    nama_kabkot: kabkot,
                    nama_provinsi: prov,
                    nama: nama,
                    tahun: item.tahun,
                    deskripsi: item.deskripsi,
                    nilai_irr: `${item.nilai_irr}%`,
                    nilai_investasi: numberFormatRpSingkatan(item.nilai_investasi),
                    nilai_npv: numberFormatRpSingkatan(item.nilai_npv),
                    nilai_pp: item.nilai_pp,
                    id_kategori_sektor: item.tb_peluang_sektor.kategori_sektor?.id_kategori_sektor,
                    nama_sektor: item.tb_peluang_sektor.kategori_sektor?.nama,
                    nama_sektor_peluang: item.tb_peluang_sektor.nama,
                    project_status_enum: item.project_status_enum,
                    status_proyek, // Properti status_proyek ditambahkan (opsional, jika dibutuhkan)
                    // status: sts,
                    status: item.is_ipro ? 'IPRO' : 'PPI',
                    icon: `${env.get('APP_URL')}/uploads/icon-web/${item.tb_peluang_sektor.icon}`,
                    image: `${env.get('APP_URL')}/uploads/peluang/${item.id_adm_kabkot}/peluang/${item.tahun}/${item.id_prioritas}/${item.tb_peluang_kabkot_file[0]?.nama || 'default.jpg'}`, // Fallback untuk nama file
                };
            }),
        ...pid.map(item => {
                let kabkot = item.nama_kabkot
                let prov = item.nama_provinsi
                let nama = item.nama
                if (parameters.en) {
                    kabkot = item.nama_kabkot_tr || kabkot
                    prov =item.nama_provinsi_tr || prov
                    nama =item.nama_tr || nama
                }

            return {
                id_peluang:item.id_peluang_daerah,
                id_adm_provinsi:item.id_adm_provinsi,
                id_adm_kabkot:item.id_adm_kabkot,
                nama_kabkot:kabkot,
                nama_provinsi:prov,
                nama:nama,
                tahun:item.tahun,
                deskripsi:item.keterangangit,
                nilai_irr: `${item.nilai_irr}%`,
                nilai_investasi: numberFormatRpSingkatan(item.nilai_investasi),
                nilai_npv: numberFormatRpSingkatan(item.nilai_npv),
                nilai_pp:item.nilai_pp,
                id_kategori_sektor:item.id_kategori_sektor,
                nama_sektor:item.nama_sektor,
                nama_sektor_peluang:item.nama_sektor_peluang,
                project_status_enum:item.project_status_enum == null ? 0 : item.project_status_enum,
                status_proyek:item.status_proyek,
                status: 'PID',
                icon: `${env.get('APP_URL')}/uploads/icon-web/${item.icon}`,
                image:`${env.get('APP_URL')}/uploads/peluang_daerah/${item.id_peluang_daerah}/${item.images}`
                }
        })
    ]

    // return proyeks
    let search = ''
    let filteredData = proyeks
    if (dataPost.search != ',' && dataPost.search != '' && dataPost.search != undefined ) {

        search = dataPost.search?.toLowerCase() || ''; // String pencarian (case insensitive)

        filteredData = proyeks.filter(item =>
            Object.values(item).some(value =>
                typeof value === 'string' && value.toLowerCase().includes(search)
            )
        );
    }

    if (provinsiIds.length >0) {

        filteredData = filteredData.filter((item) => provinsiIds.includes(item.id_adm_prov));

    }
    if (dataPost.id_adm_provinsi) {
        filteredData = filteredData.filter((item) => {
            if (item.id_adm_provinsi === dataPost.id_adm_provinsi) return true;
                // Filter untuk id_adm_kabkot berdasarkan 2 angka pertama
                const kabkotPrefix = Math.floor(item.id_adm_kabkot / 100);
            if (kabkotPrefix === dataPost.id_adm_provinsi) return true;
            return false;
        });
    }

    if (dataPost.id_sektor) {
        filteredData = filteredData.filter((item) => {
            if (item.id_kategori_sektor === dataPost.id_sektor) return true;
            return false;
        });
    }
    if (dataPost.id_adm_kabkot) {
        filteredData = filteredData.filter((item) => {
            if (item.id_adm_kabkot === dataPost.id_adm_kabkot) return true;
            return false;
        });
    }

    // Paginasi secara manual
    const page = dataPost.page || 1; // Default to page 1 if not provided
    const pageSize = dataPost.page_size || 9; // Default to 10 items per page if not provided
    const skip = (page - 1) * pageSize;
    const take = pageSize;
    const totalRecords = filteredData.length; // Total jumlah data setelah pencarian
    const totalPage = Math.ceil(totalRecords / pageSize); // Total halaman
    const paginatedData = filteredData.slice((page - 1) * pageSize, page * pageSize);

    return {
        success: true,
        totalRecords, // Total number of records after filtering
        totalPage, // Total number of pages
        page,
        pageSize,
        data: paginatedData, // Data for the current page
    };
    return proyeks
    // const dataPost = await request.validateUsing(getPeluangValidator);

    // const whereConditions = {
    //     status: "99",
    //     id_prioritas: 1,
    // };

    // Dynamically add conditions based on dataPost
    // if (dataPost.id_sektor !== undefined) {
    //     whereConditions.id_sektor = dataPost.id_sektor;
    // }

    // if (dataPost.id_adm_provinsi !== undefined) {
    //     const id_adm_prov = String(dataPost.id_adm_provinsi).slice(0, 2);
    //     const id_adm_prov_range_start = parseInt(`${id_adm_prov}00`, 10); // e.g., 6300
    //     const id_adm_prov_range_end = parseInt(`${id_adm_prov}99`, 10); // e.g., 6399

    //     whereConditions.id_adm_kabkot = {
    //         gte: id_adm_prov_range_start,
    //         lt: id_adm_prov_range_end + 1 // Add 1 to include the upper limit
    //     };
    // }
    // if (dataPost.id_adm_kabkot !== undefined) {
    //     whereConditions.id_adm_kabkot = dataPost.id_adm_kabkot;
    // }

    // if (dataPost.tahun !== undefined) {
    //     whereConditions.tahun = dataPost.tahun;
    // }

    // Optionally add a search condition if `search` is defined
    // if (dataPost.search) {
    //     whereConditions.OR = [
    //         { nama: { contains: dataPost.search, mode: 'insensitive' } },
    //         { nama_singkat: { contains: dataPost.search, mode: 'insensitive' } },
    //         { deskripsi_singkat: { contains: dataPost.search, mode: 'insensitive' } },
    //         { deskripsi: { contains: dataPost.search, mode: 'insensitive' } }
    //     ];
    // }

    // let orderByConditions = [];
    // if (dataPost.sortir) {
    //     const sortOrder = dataPost.sortir.split(' - ');
    //     const column = sortOrder[0]; // Either "judul" or "tahun"
    //     const direction = sortOrder[1] === 'asc' ? 'asc' : 'desc'; // Default to 'desc' if not 'asc'

    //     // Only add valid columns to orderBy
    //     if (column === 'tahun') {
    //         orderByConditions.push({ [column]: direction });
    //     }
    //     if (column === 'judul') {
    //         orderByConditions.push({ ['nama']: direction });
    //     }
    // }

    // Pagination
    // const page = dataPost.page || 1; // Default to page 1 if not provided
    // const pageSize = dataPost.page_size || 9; // Default to 10 items per page if not provided
    // const skip = (page - 1) * pageSize;
    // const take = pageSize;

    // Query with pagination
    // const datas = await prisma.tb_peluang_kabkot.findMany({
    //     where: whereConditions,
    //     include: {
    //         tb_peluang_sektor: true,
    //         tb_adm_kabkot: {
    //             include: {
    //                 tb_adm_provinsi: true,
    //             }
    //         },
    //         tb_peluang_kabkot_file:{
    //             where:{
    //                 tipe:1
    //             }
    //         }
    //     },
    //     // orderBy: orderByConditions.length > 0 ? orderByConditions : undefined,
    //     orderBy: {
    //         id_peluang_kabkot:'desc'
    //     },
    //     skip, // Skip the number of items based on current page
    //     take, // Limit the number of items per page
    // });

    // Get total count for pagination metadata
    // const totalRecords = await prisma.tb_peluang_kabkot.count({
    //     where: whereConditions,
    // });

    // Format data
    // const data = await Promise.all(datas.map(async ({tb_peluang_sektor, tb_adm_kabkot,tb_peluang_kabkot_file, ...data}) => ({
    //     id_pelluang_kabkot : data.id_peluang_kabkot,
    //     id_sektor: data.id_sektor,
    //     judul: data.nama,
    //     nama_kabkot: tb_adm_kabkot.nama,
    //     nama_provinsi: tb_adm_kabkot.tb_adm_provinsi.nama,
    //     nilai_irr: `${data.nilai_irr}%`,
    //     nilai_investasi: numberFormatRpSingkatan(data.nilai_investasi),
    //     nilai_npv: numberFormatRpSingkatan(data.nilai_npv),
    //     nama_sektor: tb_peluang_sektor.nama,
    //     icon: `${env.get('APP_URL')}/uploads/icon-web/${tb_peluang_sektor.icon}`,
    //     image : `${env.get('APP_URL')}/uploads/peluang/${data.id_adm_kabkot}/peluang/${data.tahun}/1/${tb_peluang_kabkot_file[0].nama}`,

    // })));

    // Pagination response
        // return {
        //     success: true,
        //     page: page,
        //     pageSize: pageSize,
        //     totalRecords: totalRecords,
        //     totalPage: Math.ceil(totalRecords / pageSize),
        //     data: { data },

        // };
    }
    /**
     * @peluang_investasi_wilayah_ipro
     * @paramQuery page - Kalaman ke - @type(number)
     * @paramQuery page_size - data per halaman  - @type(number)
     * @paramQuery search - ketik text pencarian - @type(string)
     * @paramQuery id_zona_waktu - Masukan Id Zona Waktu - @type(number)
     * @paramQuery id_sektor - Masukan Id Sektor - @type(number)
     * @paramQuery id_adm_provinsi - Masukan Id Provinsi - @type(number)
     * @paramQuery id_adm_kabkot - Masukan Id Kabkot - @type(number)
     * @paramQuery id_sektor - Masukan Id Sektor - @type(number)
     */
    async peluang_investasi_wilayah_ipro({request, response}: HttpContext) {
        const dataPost = await request.validateUsing(getPeluangValidator);

        const whereConditions = {
            status: "99",
            is_ipro:true
        };

        // Dynamically add conditions based on dataPost
        if (dataPost.id_sektor !== undefined) {
            whereConditions.tb_peluang_sektor =
            {
                kategori_sektor:{
                    id_kategori_sektor : dataPost.id_sektor
                }
            }
        }
        if (dataPost.id_zona_waktu !== undefined) {
            const prov = await prisma.tb_adm_provinsi.findMany({
                where:{
                    id_zona_waktu : dataPost.id_zona_waktu
                },
                orderBy:{
                    id_adm_provinsi:'asc'
                }
            })
            const provinsiIds = [];
            if (prov.length > 0) {
                prov.map((item) => {
                    provinsiIds.push(item.id_adm_provinsi);
                })

            }
                const id_adm_kabkotConditions = provinsiIds.map((id_adm_prov) => {
                const id_adm_prov_range_start = parseInt(`${id_adm_prov}00`, 10); // Contoh: 2200
                const id_adm_prov_range_end = parseInt(`${id_adm_prov}99`, 10);   // Contoh: 2299
                return {
                    gte: id_adm_prov_range_start,
                    lte: id_adm_prov_range_end,
                };
                });

                // Gabungkan kondisi `id_adm_kabkot` dengan `whereConditions`
                if (!whereConditions.OR) {
                whereConditions.OR = []; // Pastikan `OR` array ada
                }

                whereConditions.OR.push(
                ...id_adm_kabkotConditions.map((range) => ({
                    id_adm_kabkot: range,
                }))
                );
        }

        if (dataPost.id_adm_provinsi !== undefined) {
            const id_adm_prov = String(dataPost.id_adm_provinsi).slice(0, 2);
            const id_adm_prov_range_start = parseInt(`${id_adm_prov}00`, 10); // e.g., 6300
            const id_adm_prov_range_end = parseInt(`${id_adm_prov}99`, 10); // e.g., 6399

            whereConditions.id_adm_kabkot = {
                gte: id_adm_prov_range_start,
                lt: id_adm_prov_range_end + 1 // Add 1 to include the upper limit
            };
        }
        if (dataPost.id_adm_kabkot !== undefined) {
            whereConditions.id_adm_kabkot = dataPost.id_adm_kabkot;
        }

        // if (dataPost.tahun !== undefined) {
        //     whereConditions.tahun = dataPost.tahun;
        // }

        // Optionally add a search condition if `search` is defined
        if (dataPost.search) {
            whereConditions.OR = [
                { nama: { contains: dataPost.search, mode: 'insensitive' } },
                { nama_singkat: { contains: dataPost.search, mode: 'insensitive' } },
                { deskripsi_singkat: { contains: dataPost.search, mode: 'insensitive' } },
                { deskripsi: { contains: dataPost.search, mode: 'insensitive' } }
            ];
        }

        // let orderByConditions = [];
        // if (dataPost.sortir) {
        //     const sortOrder = dataPost.sortir.split(' - ');
        //     const column = sortOrder[0]; // Either "judul" or "tahun"
        //     const direction = sortOrder[1] === 'asc' ? 'asc' : 'desc'; // Default to 'desc' if not 'asc'

        //     // Only add valid columns to orderBy
        //     if (column === 'tahun') {
        //         orderByConditions.push({ [column]: direction });
        //     }
        //     if (column === 'judul') {
        //         orderByConditions.push({ ['nama']: direction });
        //     }
        // }

        // Pagination
        const page = dataPost.page || 1; // Default to page 1 if not provided
        const pageSize = dataPost.page_size || 9; // Default to 10 items per page if not provided
        const skip = (page - 1) * pageSize;
        const take = pageSize;

        // Query with pagination
        const datas = await prisma.tb_peluang_kabkot.findMany({
            where: whereConditions,
            include: {
                tb_peluang_sektor: {
                    include:{
                        kategori_sektor:true
                    }
                },
                tb_adm_kabkot: {
                    include: {
                        tb_adm_provinsi: true,
                    }
                },
                tb_peluang_kabkot_file:{
                    where:{
                        tipe:1
                    }
                },
            },
            // orderBy: orderByConditions.length > 0 ? orderByConditions : undefined,
            skip, // Skip the number of items based on current page
            take, // Limit the number of items per page
        });

        // Get total count for pagination metadata
        const totalRecords = await prisma.tb_peluang_kabkot.count({
            where: whereConditions,
        });

        // Format data
        const data = await Promise.all(datas.map(async ({tb_peluang_sektor, tb_adm_kabkot,tb_peluang_kabkot_file, ...data}) => ({
            id_adm_prov:parseInt(data.id_adm_kabkot.toString().slice(0, 2)),
            id_adm_kabkot:data.id_adm_kabkot,
            id_pelluang_kabkot : data.id_peluang_kabkot,
            id_sektor: data.id_sektor,
            id_kategori_sektor: tb_peluang_sektor.kategori_sektor?.id_kategori_sektor,
            nama_kategori_sektor: tb_peluang_sektor.kategori_sektor?.nama,
            judul: data.nama,
            nama_kabkot: tb_adm_kabkot.nama,
            nama_provinsi: tb_adm_kabkot.tb_adm_provinsi.nama,
            nilai_irr: `${data.nilai_irr}%`,
            nilai_investasi: numberFormatRpSingkatan(data.nilai_investasi),
            nilai_npv: numberFormatRpSingkatan(data.nilai_npv),
            nama_sektor: tb_peluang_sektor.nama,
            statu: 'IPRO',
            icon: `${env.get('APP_URL')}/uploads/icon-web/${tb_peluang_sektor.icon}`,
            image : `${env.get('APP_URL')}/uploads/peluang/${data.id_adm_kabkot}/peluang/${data.tahun}/${data.id_prioritas}/${tb_peluang_kabkot_file[0].nama}`,
        })));

        // Pagination response
            return {
                success: true,
                page: page,
                pageSize: pageSize,
                totalRecords: totalRecords,
                totalPage: Math.ceil(totalRecords / pageSize),
                data: { data },

            };
    }

    async get_peluang_investasi_wilayah({request, response}: HttpContext) {
        let dataPost = request.qs()

        if (dataPost.id_zona_waktu) {
            dataPost.id_zona_waktu = parseInt(dataPost.id_zona_waktu)
        }
        if (dataPost.id_sektor) {
            dataPost.id_sektor = parseInt(dataPost.id_sektor)
        }
        if (dataPost.id_kategori_sektor) {
            dataPost.id_kategori_sektor = parseInt(dataPost.id_kategori_sektor)
        }
        if (dataPost.id_adm_provinsi) {
            dataPost.id_adm_provinsi = parseInt(dataPost.id_adm_provinsi)
        }
        if (dataPost.id_adm_kabkot) {
            dataPost.id_adm_kabkot = parseInt(dataPost.id_adm_kabkot)
        }
        if (dataPost.page) {
            dataPost.page = parseInt(dataPost.page)
        }
        if (dataPost.page_size) {
            dataPost.page_size = parseInt(dataPost.page_size)
        }
        // return dataPost
        // const dataPost = await request.validateUsing(getPeluangValidator);
        const provinsiIds = [];
        if (dataPost.id_zona_waktu) {
            const prov = await prisma.tb_adm_provinsi.findMany({
                where:{
                    id_zona_waktu : dataPost.id_zona_waktu
                },
                orderBy:{
                    id_adm_provinsi:'asc'
                }
            })
            if (prov.length > 0) {
                prov.map((item) => {
                    provinsiIds.push(item.id_adm_provinsi);
                })

            }
        }

        const [ppi, pid] = await Promise.all([
        prisma.tb_peluang_kabkot.findMany({
            select:{
                id_adm_kabkot:true,
                id_peluang_kabkot:true,
                nama:true,
                deskripsi:true,
                nilai_investasi:true,
                nilai_irr:true,
                nilai_npv:true,
                nilai_pp:true,
                id_sektor:true,
                id_prioritas:true,
                project_status_enum:true,
                is_ipro:true,
                tahun:true,
                tb_peluang_sektor:{
                    select:{
                        kategori_sektor:{
                            select:{
                                nama:true,
                                id_kategori_sektor:true
                            }
                        },
                        nama:true,
                        icon:true
                    }
                },
                tb_peluang_kabkot_file:{
                    select:{
                        nama:true
                    },
                    where:{
                        tipe:1
                    }
                },
                tb_adm_kabkot:{
                    select:{
                        nama:true,
                        tb_adm_provinsi:{
                            select:{
                                nama:true,
                                tb_adm_provinsi_tr:{
                                    select:{
                                        nama:true
                                    }
                                }
                            }
                        },
                        tb_adm_kabkot_tr:{
                            select:{
                                nama:true
                            }
                        }
                    }
                },
                tb_peluang_kabkot_tr:{
                    select:{
                        nama:true
                    }
                }
            },
            where: {
                    status:"99",
                    id_prioritas:1,
                    // project_status_enum:{
                    //     not:'-1'
                    // }
            },
            orderBy:{
                nilai_investasi:'desc'
            }
        }),
        prisma.$queryRaw`
            SELECT
                            p.id_peluang_daerah,
                            p.id_adm_provinsi,
                            p.id_adm_kabkot,
                            p.keterangan,
                            0 as id_sektor,
                            p.judul as nama,
                            p.tahun as tahun,
                            pt.judul as nama_tr,
                            k.initial_invesment as nilai_investasi,
                            k.irr as nilai_irr,
                            k.npv as nilai_npv,
                            k.pp as nilai_pp,
                            skn.id_kategori_sektor as id_kategori_sektor,
                            skn.nama as nama_sektor,
                            snf.nama as nama_sektor_peluang,
                            snf.icon as icon,
                            pdf.nama as images,
                            p.status,
                            ak.nama as nama_kabkot,
                            akt.nama as nama_kabkot_tr,
                            ap.nama as nama_provinsi,
                            apt.nama as nama_provinsi_tr,
                            p.project_status_enum,
                            ps.nama as status_proyek
                        FROM tb_peluang_daerah p
                        LEFT JOIN tb_peluang_daerah_tr pt
                            ON p.id_peluang_daerah = pt.id_peluang_daerah
                        LEFT JOIN tb_peluang_daerah_kelayakan k
                            ON p.id_peluang_daerah = k.id_peluang_daerah
                        LEFT JOIN tb_sub_sektor_daerah ssd
                            ON p.id_sub_sektor_daerah = ssd.id_sub_sektor_daerah
                        LEFT JOIN tb_sub_sektor_nasional ssn
                            ON ssd.id_sub_sektor_nasional = ssn.id_sub_sektor_nasional
                        LEFT JOIN tb_sektor_nasional sn
                            ON sn.id_sektor_nasional = ssn.id_sektor_nasional
                        LEFT JOIN tb_sektor_nasional_ref snf
                            ON sn.id_sektor_nasional = snf.id_sektor
                        LEFT JOIN tb_kategori_sektor skn
                            ON skn.id_kategori_sektor = snf.id_kategori_sektor
                        LEFT JOIN tb_peluang_daerah_file pdf
                            ON pdf.id_peluang_daerah = p.id_peluang_daerah AND pdf.tipe = 1
                        LEFT JOIN tb_adm_kabkot ak
                            ON ak.id_adm_kabkot = p.id_adm_kabkot
                        LEFT JOIN tb_adm_kabkot_tr akt
                            ON ak.id_adm_kabkot = akt.id_adm_kabkot
                        LEFT JOIN tb_adm_provinsi ap
                            ON ap.id_adm_provinsi = p.id_adm_provinsi
                        LEFT JOIN tb_adm_provinsi_tr apt
                            ON apt.id_adm_provinsi = ap.id_adm_provinsi
                        LEFT JOIN tb_peluang_status ps
                            ON ps.id_Peluang_status = p.project_status_enum
                        WHERE p.status = 99
            ORDER BY k.initial_invesment desc;
        `
        ]);

        // Mapping data dengan keterangan masing-masing
        const parameters = request.qs()
        const peluang_status = await prisma.tb_peluang_status.findMany()

        const proyeks = [

        ...ppi.map(item => {
                const status = {
                    2: 'DIMINATI',
                    7: 'SOLD',
                };

                
                let status_proyek = '';
                
                if (item.project_status_enum !== '') {
                    status_proyek = peluang_status.find((items) => items.id_peluang_status == parseInt(item.project_status_enum || '0'))?.nama || ''
                    // status_proyek = status[item.project_status_enum];
                }
                let kabkot = item.tb_adm_kabkot.nama
                let prov = item.tb_adm_kabkot.tb_adm_provinsi.nama
                let nama = item.nama
                if (parameters.en) {
                    kabkot = item.tb_adm_kabkot.tb_adm_kabkot_tr[0]?.nama || item.tb_adm_kabkot.nama
                    prov = item.tb_adm_kabkot.tb_adm_provinsi.tb_adm_provinsi_tr[0]?.nama || item.tb_adm_kabkot.tb_adm_provinsi.nama
                    nama = item.tb_peluang_kabkot_tr[0]?.nama || item.nama
                }
                let sts = 'PPI'
                if(item.is_ipro){
                    sts = 'IPRO'
                }else if(item.id_prioritas ==2){
                    sts = 'PID'
                }
                return {
                    id_peluang: item.id_peluang_kabkot,
                    id_adm_provinsi: parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_adm_kabkot: item.id_adm_kabkot,
                    id_sektor: item.id_sektor,
                    nama_kabkot: kabkot,
                    nama_provinsi: prov,
                    nama: nama,
                    tahun: item.tahun,
                    deskripsi: item.deskripsi,
                    nilai_irr: `${item.nilai_irr}%`,
                    nilai_investasi: numberFormatRpSingkatan(item.nilai_investasi),
                    nilai_npv: numberFormatRpSingkatan(item.nilai_npv),
                    nilai_pp: item.nilai_pp,
                    id_kategori_sektor: item.tb_peluang_sektor.kategori_sektor?.id_kategori_sektor,
                    nama_sektor: item.tb_peluang_sektor.kategori_sektor?.nama,
                    nama_sektor_peluang: item.tb_peluang_sektor.nama,
                    project_status_enum: item.project_status_enum,
                    status_proyek, // Properti status_proyek ditambahkan (opsional, jika dibutuhkan)
                    // status: sts,
                    status: item.is_ipro ? 'IPRO' : 'PPI',
                    icon: `${env.get('APP_URL')}/uploads/icon-web/${item.tb_peluang_sektor.icon}`,
                    image: `${env.get('APP_URL')}/uploads/peluang/${item.id_adm_kabkot}/peluang/${item.tahun}/${item.id_prioritas}/${item.tb_peluang_kabkot_file[0]?.nama || 'default.jpg'}`, // Fallback untuk nama file
                };
            }),
        ...pid.map(item => {
                let kabkot = item.nama_kabkot
                let prov = item.nama_provinsi
                let nama = item.nama
                if (parameters.en) {
                    kabkot = item.nama_kabkot_tr || kabkot
                    prov =item.nama_provinsi_tr || prov
                    nama =item.nama_tr || nama
                }

            return {
                id_peluang:item.id_peluang_daerah,
                id_adm_provinsi:item.id_adm_provinsi,
                id_adm_kabkot:item.id_adm_kabkot,
                id_sektor:item.id_sektor,
                nama_kabkot:kabkot,
                nama_provinsi:prov,
                nama:nama,
                tahun:item.tahun,
                deskripsi:item.keterangangit,
                nilai_irr: `${item.nilai_irr}%`,
                nilai_investasi: numberFormatRpSingkatan(item.nilai_investasi),
                nilai_npv: numberFormatRpSingkatan(item.nilai_npv),
                nilai_pp:item.nilai_pp,
                id_kategori_sektor:item.id_kategori_sektor,
                nama_sektor:item.nama_sektor,
                nama_sektor_peluang:item.nama_sektor_peluang,
                project_status_enum:item.project_status_enum == null ? 0 : item.project_status_enum,
                status_proyek:item.status_proyek,
                status: 'PID',
                icon: `${env.get('APP_URL')}/uploads/icon-web/${item.icon}`,
                image:`${env.get('APP_URL')}/uploads/peluang_daerah/${item.id_peluang_daerah}/${item.images}`
                }
        })
    ]

    // return proyeks
    let search = ''
    let filteredData = proyeks
    if (dataPost.search != ',' && dataPost.search != '' && dataPost.search != undefined ) {

        search = dataPost.search?.toLowerCase() || ''; // String pencarian (case insensitive)

         filteredData = proyeks.filter(item =>
            Object.values(item).some(value =>
                typeof value === 'string' && value.toLowerCase().includes(search)
            )
        );
    }

    if (provinsiIds.length >0) {
        filteredData = filteredData.filter((item) => provinsiIds.includes(parseInt(item.id_adm_provinsi)));
    }
    if (dataPost.id_adm_provinsi && dataPost.id_adm_provinsi != "") {
        filteredData = filteredData.filter((item) => {
            if (item.id_adm_provinsi === dataPost.id_adm_provinsi) return true;
                // Filter untuk id_adm_kabkot berdasarkan 2 angka pertama
                const kabkotPrefix = Math.floor(item.id_adm_kabkot / 100);
            if (kabkotPrefix === dataPost.id_adm_provinsi) return true;
            return false;
        });
    }

    if (dataPost.id_kategori_sektor && dataPost.id_kategori_sektor != "") {
        filteredData = filteredData.filter((item) => {
            if (item.id_kategori_sektor === dataPost.id_kategori_sektor) return true;
            return false;
        });
    }

    if (dataPost.id_sektor && dataPost.id_sektor != "") {
        filteredData = filteredData.filter((item) => {
            if (item.id_sektor === dataPost.id_sektor) return true;
            return false;
        });
    }
    if (dataPost.id_adm_kabkot && dataPost.id_adm_kabkot != "") {
        filteredData = filteredData.filter((item) => {
            if (item.id_adm_kabkot === dataPost.id_adm_kabkot) return true;
            return false;
        });
    }

    if (dataPost.status && dataPost.status != "") {
        filteredData = filteredData.filter((item) => {
            if (item.status === dataPost.status.toUpperCase()) return true;
            return false;
        });
    }

    // Paginasi secara manual
    const page = dataPost.page || 1; // Default to page 1 if not provided
    const pageSize = dataPost.page_size || 9; // Default to 10 items per page if not provided
    const skip = (page - 1) * pageSize;
    const take = pageSize;
    const totalRecords = filteredData.length; // Total jumlah data setelah pencarian
    const totalPage = Math.ceil(totalRecords / pageSize); // Total halaman
    const paginatedData = filteredData.slice((page - 1) * pageSize, page * pageSize);

    return {
        success: true,
        totalRecords, // Total number of records after filtering
        totalPage, // Total number of pages
        page,
        pageSize,
        data: paginatedData, // Data for the current page
    };

    }
    /**
     * @count_viewer
     * @paramQuery id_halaman_pengunjung - id_halaman_pengunjung - @type(number) @required
     * @paramQuery id_konten - id konten - @type(number)
     */
    async count_viewer({ response,request }: HttpContext) {
        const data = await request.validateUsing(countViewerValidator);
        const clientIp = requestIp.getClientIp(request.request);
            // Cara 3: Menggunakan header X-Forwarded-For jika menggunakan proxy



            const dataPost = {
                    id_halaman_pengunjung:data.id_halaman_pengunjung,
                    ip_pengunjung : clientIp,
                    id_konten : data.id_konten || 0,
                    created_date: new Date(1735969000873),
                    id_peluang_daerah:null,
                    id_peluang_kabkot:null
            }
            const insert = await prisma.tb_halaman_pengunjung_det.create({
                data:dataPost
            })

            return {
                success : true,
                data : insert
            }
    }
    async count_viewer_get({ response,request }: HttpContext) {
        // const data = await request.validateUsing(countViewerValidator);
        const clientIp = requestIp.getClientIp(request.request) == '::1'?  '*************' : requestIp.getClientIp(request.request);
        const data = request.qs()
        if (data.id_halaman_pengunjung) {
            data.id_halaman_pengunjung = parseInt(data.id_halaman_pengunjung)
        }
        if (data.id_konten) {
            data.id_konten = parseInt(data.id_konten)
        }
        try {
            // Gunakan template literal yang benar
            const countryName = await get_detail_ip(clientIp);
            // Lanjutkan dengan kode yang ada
            const checkUnique = await prisma.tb_halaman_pengunjung_det.count({
                where: {
                    id_halaman_pengunjung: data.id_halaman_pengunjung || undefined,
                    id_konten: data.id_konten || undefined,
                    ip_pengunjung: clientIp,
                    created_date: {
                        gte: new Date(new Date().getTime() - 30 * 60 * 1000) // 30 menit terakhir
                    }
                }
            });
            
            if (checkUnique > 0) {
                return {
                    status: 'error',
                    message: 'Data Duplikat! IP Sudah Pernah Mengakses Halaman!',
                };
            }
    
            const dataPost = {
                id_halaman_pengunjung: data.id_halaman_pengunjung || 0,
                ip_pengunjung: clientIp,
                id_konten: data.id_konten || 0,
                created_date: new Date().toISOString(),
                asal_negara: countryName,
                id_peluang_daerah: null,
                id_peluang_kabkot: null
            };
            
            const insert = await prisma.tb_halaman_pengunjung_det.create({
                data: dataPost
            });

            const update = await prisma.tb_halaman_pengunjung.update({
                where: {
                    id_halaman_pengunjung: data.id_halaman_pengunjung || 0,
                },
                data: {
                    total_pengunjung: {
                        increment: 1
                    }
                }
            });
    
            return {
                success: true,
                data: insert,
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
        
    }
    /**
     * @ppi_like
     * @paramQuery id_peluang_kabkot - id peluang daerah - @type(number) @required
     */
    async ppi_like({ params,response,request }: HttpContext) {
        const id  = request.input("id_peluang_kabkot")
        const Id = parseInt(id, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ success:false,error: 'Invalid ID provided' })
        }
            const dataPost = {
                    id_peluang_kabkot:Id,
                    ip_pengunjung : request.ip(),
            }
            const insert = await prisma.tb_peluang_kabkot_likes_counter.create({
            data:dataPost
            })

            return {
                success : true,
                data : {insert}
            }
    }
    async ppi_like_get({ params,response,request }: HttpContext) {
        const parameters = request.qs()

        const id  = parameters.id_peluang_kabkot
        const Id = parseInt(id, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ success:false,error: 'Invalid ID provided' })
        }
            const dataPost = {
                    id_peluang_kabkot:Id,
                    ip_pengunjung : request.ip(),
            }
            const insert = await prisma.tb_peluang_kabkot_likes_counter.create({
            data:dataPost
            })

            return {
                success : true,
                data : {insert}
            }
    }
    async get_ppi_like({ params,response,request }: HttpContext) {
        const id  = params.id_peluang_kabkot
        const Id = parseInt(id, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ success:false,error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_peluang_kabkot_likes_counter.findMany({
                where:{
                    id_peluang_kabkot:Id,
                }
        })

        return {
            success : true,
            data : {
                id_peluang_kabkot : Id,
                jumlah : datas.length
            }
        }
    }
    async is_ppi_like({ params,response,request }: HttpContext) {
        const id  = params.id_peluang_kabkot
        const Id = parseInt(id, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ success:false,error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_peluang_kabkot_likes_counter.findMany({
                where:{
                    id_peluang_kabkot:Id,
                    ip_pengunjung:request.ip()
                }
        })
        const sts = datas.length > 0 ? true : false;

        return {
            success : true,
            data : {
                is_like : sts,
            }
        }
    }
    /**
     * @pid_like
     * @paramQuery id_peluang_daerah - id peluang daerah - @type(number) @required
     */
    async pid_like({ params,response,request }: HttpContext) {
        const parameters = request.qs()

        const id  = parameters.id_peluang_kabkot
        const Id = parseInt(id, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ success:false,error: 'Invalid ID provided' })
        }
            const dataPost = {
                    id_peluang_daerah:Id,
                    ip_pengunjung : request.ip(),
            }
            const insert = await prisma.tb_peluang_daerah_likes_counter.create({
            data:dataPost
            })

            return {
                success : true,
                data : {insert}
            }
    }
    async pid_like_get({ params,response,request }: HttpContext) {
        const id  = request.input("id_peluang_daerah")
        const Id = parseInt(id, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ success:false,error: 'Invalid ID provided' })
        }
            const dataPost = {
                    id_peluang_daerah:Id,
                    ip_pengunjung : request.ip(),
            }
            const insert = await prisma.tb_peluang_daerah_likes_counter.create({
            data:dataPost
            })

            return {
                success : true,
                data : {insert}
            }
    }
    async is_pid_like({ params,response,request }: HttpContext) {
        const id  = params.id_peluang_daerah
        const Id = parseInt(id, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ success:false,error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_peluang_daerah_likes_counter.findMany({
                where:{
                    id_peluang_daerah:Id,
                    ip_pengunjung:request.ip()
                }
        })
        const sts = datas.length > 0 ? true : false;

        return {
            success : true,
            data : {
                is_like : sts,
            }
        }
    }
    async get_pid_like({ params,response,request }: HttpContext) {
        const id  = params.id_peluang_kabkot
        const Id = parseInt(id, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ success:false,error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_peluang_daerah_likes_counter.findMany({
                where:{
                    id_peluang_daerah:Id,
                }
        })

        return {
            success : true,
            data : {
                id_peluang_kabkot : Id,
                jumlah : datas.length
            }
        }
    }

    async detail_peluang({params,response,request}: HttpContext) {
        const id  = params.id_peluang_kabkot
        const Id = parseInt(id, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ success:false,error: 'Invalid ID provided' })
        }

        const parameters = request.qs()
        const proyeks = await prisma.tb_peluang_kabkot.findFirst({
            where : {
                id_peluang_kabkot : Id
            },
            include:{
                tb_peluang_sektor:{
                    include:{
                        tb_peluang_sektor_tr:true
                    }
                },
                tb_adm_kabkot:{
                    include:{
                        tb_adm_provinsi:{
                            include:{
                                tb_adm_provinsi_tr:true
                            }
                        },
                        tb_adm_kabkot_tr:true
                    }
                },
                tb_peluang_kabkot_file:{
                    where: {
                        tipe:{
                           in: [1,2],
                        }
                    },
                },
                tb_peluang_kabkot_insentif : {
                    include : {
                        tb_jenis_insentif:{
                            include:{
                                tb_jenis_insentif_tr:true
                            }
                        },
                    },
                },
                tb_halaman_pengunjung_det:true,
                tb_peluang_kabkot_tr:true
            }
        })



        let total_pengunjung=0
        const pengunjung = await prisma.tb_halaman_pengunjung_det.findMany({
            where:{
                id_halaman_pengunjung:7,
                id_konten:Id
            }
        })

        if (pengunjung.length > 0) {

            const uniqueIPs = new Set(); // Store unique IPs

            pengunjung.forEach(item => {
                uniqueIPs.add(item.ip_pengunjung);
            });
            total_pengunjung = uniqueIPs.size;
        }



        let image ='';
        let vidio ='';
        for (let i = 0; i < proyeks?.tb_peluang_kabkot_file.length; i++) {
            if (proyeks?.tb_peluang_kabkot_file[i].tipe === 1) {
                image = `${env.get('APP_URL')}/uploads/peluang/${proyeks?.id_adm_kabkot}/peluang/${proyeks?.tahun}/${proyeks?.id_prioritas}/${proyeks?.tb_peluang_kabkot_file[i].nama}`
                break; // Exit the loop when the element is 3
            }
        }
        for (let i = 0; i < proyeks?.tb_peluang_kabkot_file.length; i++) {
            if (proyeks?.tb_peluang_kabkot_file[i].tipe === 2) {
                vidio = `${env.get('APP_URL')}/uploads/peluang/${proyeks?.id_adm_kabkot}/peluang/${proyeks?.tahun}/${proyeks?.id_prioritas}/${proyeks?.tb_peluang_kabkot_file[i].nama}`
                break; // Exit the loop when the element is 3
            }
        }

        const peluang_status = await prisma.tb_peluang_status.findMany()

            const status = {
                2: 'DIMINATI',
                7: 'SOLD',
            };

            let status_proyek = '';

            if (proyeks?.project_status_enum !== '') {
                    status_proyek = peluang_status.find((items) => items.id_peluang_status == parseInt(proyeks?.project_status_enum || '0'))?.nama || ''
                    // status_proyek = status[item.project_status_enum];
                }
                
            let judul = proyeks?.nama
            let deskripsi = proyeks?.deskripsi
            let keterangan = striptags(proyeks?.keterangan || '')
            let deskripsi_singkat = proyeks?.deskripsi_singkat
            let kabkot = proyeks?.tb_adm_kabkot?.nama
            let provinsi =  proyeks?.tb_adm_kabkot.tb_adm_provinsi.nama
            let sektor = proyeks?.tb_peluang_sektor.nama
            if (parameters.en) {
                judul = proyeks?.tb_peluang_kabkot_tr[0].nama || judul
                deskripsi = proyeks?.tb_peluang_kabkot_tr[0].deskripsi || deskripsi
                keterangan = striptags(proyeks?.tb_peluang_kabkot_tr[0].keterangan || '') || keterangan
                deskripsi_singkat = striptags(proyeks?.tb_peluang_kabkot_tr[0].deskripsi_singkat || '') || deskripsi_singkat
                provinsi = striptags(proyeks?.tb_adm_kabkot.tb_adm_provinsi.tb_adm_provinsi_tr[0]?.nama || '') || provinsi
                kabkot = proyeks?.tb_adm_kabkot?.tb_adm_kabkot_tr[0]?.nama || kabkot
                sektor = proyeks?.tb_peluang_sektor.tb_peluang_sektor_tr[0]?.nama || sektor
            }
            // return kabkot
            let tahun = 'Tahun'
            if (parameters.en) {
                tahun = 'Year'
            }
            const detail = {
                    id_sektor: proyeks?.id_sektor,
                    judul: judul,
                    nama_kabkot: kabkot,
                    nama_provinsi: provinsi,
                    lokasi_kawasan : proyeks?.lokasi_kawasan,
                    nama_sektor: sektor,
                    image :image,
                    vidio :vidio,
                    kode_kbli : proyeks?.kode_kbli,
                    nilai_investasi: numberFormatRpSingkatan(proyeks?.nilai_investasi),
                    tahun:proyeks?.tahun,
                    nilai_irr: `${proyeks?.nilai_irr == undefined ? 0 : proyeks?.nilai_irr}%`,
                    nilai_npv: numberFormatRpSingkatan(proyeks?.nilai_npv),
                    payback_period : ` ${proyeks?.nilai_pp == undefined ? 0 : proyeks?.nilai_pp} ${tahun} `,
                    longitude :proyeks?.lon,
                    latitude : proyeks?.lat,
                    deskripsi : keterangan,
                    deskripsi_singkat : deskripsi_singkat,
                    status_proyek,
                    total_pengunjung,
                }

            const insentif = proyeks?.tb_peluang_kabkot_insentif.map((item) => {
                if (!parameters.en) {

                    return {
                        nama : item.tb_jenis_insentif.nama,
                        keterangan : item.tb_jenis_insentif.keterangan
                    }
                }else{
                    return {
                        nama : item.tb_jenis_insentif.tb_jenis_insentif_tr[0].nama || item.tb_jenis_insentif.nama,
                        keterangan : item.tb_jenis_insentif.tb_jenis_insentif_tr[0].keterangan || item.tb_jenis_insentif.keterangan
                    }
                }
            })

            let infos
            if (!parameters.en) {
                infos = await prisma.tb_peluang_kabkot_file.findMany({
                    where : {
                        id_peluang_kabkot : Id,
                        tipe:4,
                    },
                })
            } else {
                infos = await prisma.tb_peluang_kabkot_file_tr.findMany({
                    where : {
                        id_peluang_kabkot : Id,
                        tipe:4,
                    },
                })
            }

            const info = infos.map((item) => {
                return {
                    ...item,
                    nama : `${env.get('APP_URL')}/uploads/peluang/${proyeks?.id_adm_kabkot}/peluang/${proyeks?.tahun}/${proyeks?.id_prioritas}/${item.nama}`,
                }
            })
            const galery = await prisma.tb_peluang_kabkot_file.findMany({
                where : {
                    id_peluang_kabkot : Id,
                    tipe:1,
                }
            })

            const galeri = galery.map((item) => {
                return {
                    image : `${env.get('APP_URL')}/uploads/peluang/${proyeks?.id_adm_kabkot}/peluang/${proyeks?.tahun}/${proyeks?.id_prioritas}/${item.nama}`,
                }
            })



            const proyekTerkait = await prisma.tb_peluang_kabkot.findMany({
                where:{
                    id_sektor: proyeks?.id_sektor,
                    status:"99",
                    id_prioritas:1
                },
                include: {
                    tb_peluang_sektor: true,
                    tb_adm_kabkot: {
                        include: {
                            tb_adm_provinsi: true,
                        }
                    },
                    tb_peluang_kabkot_file:{
                        where:{
                            tipe:1
                        }
                    }
                },
            })

            const proyek_terkait = await Promise.all(proyekTerkait.map(async ({tb_peluang_sektor, tb_adm_kabkot, ...data}) => {
                const status = {
                    2: 'DIMINATI',
                    7: 'SOLD',
                };
                const peluang_status = await prisma.tb_peluang_status.findMany()

                let status_proyek = '';
                
                if (data?.project_status_enum !== '') {
                    status_proyek = peluang_status.find((items) => items.id_peluang_status == parseInt(data?.project_status_enum || '0'))?.nama || ''
                    // status_proyek = status[item.project_status_enum];
                }
                let sts = 'PPI'
                if(data.is_ipro){
                    sts = 'IPRO'
                }else if(data.id_prioritas ==2){
                    sts = 'PID'
                }
                return {    id_peluang_kabkot :data.id_peluang_kabkot,
                            id_sektor: data.id_sektor,
                            judul: data.nama,
                            nama_kabkot: tb_adm_kabkot.nama,
                            nama_provinsi: tb_adm_kabkot.tb_adm_provinsi.nama,
                            nilai_irr: `${data.nilai_irr}%`,
                            nilai_investasi: numberFormatRpSingkatan(data?.nilai_investasi || 0),
                            nilai_npv: numberFormatRpSingkatan(data.nilai_npv || 0),
                            status:sts,
                            status_proyek,
                            nama_sektor: tb_peluang_sektor.nama,
                            icon: `${env.get('APP_URL')}/uploads/icon-web/${tb_peluang_sektor.icon}`,
                            image : `${env.get('APP_URL')}/uploads/peluang/${data.id_adm_kabkot}/peluang/${data.tahun}/${data.id_prioritas}/${data.tb_peluang_kabkot_file[0]?.nama}`,
                }
            }));

            const kontaks = await prisma.tb_peluang_kabkot_kontak.findMany({
                where : {
                    id_peluang_kabkot : Id,
                },
                include:{
                    tb_peluang_kontak:true
                }
            })
            let kontak

            if(kontaks.length > 0){

                kontak =  kontaks.map((i) => {
                    return {
                        Kontak : i.tb_peluang_kontak.nama,
                        Alamat : i.tb_peluang_kontak.alamat,
                        Telepon : i.tb_peluang_kontak.no_telp,
                        Email : i.tb_peluang_kontak.email,
                        Website : i.tb_peluang_kontak.url_web,
                    }
                })
            }else{

                kontak = [{
                    Kontak:	"Kementerian Investasi /BKPM Direktorat Perencanaan Sumber Daya Alam",
                    Alamat:	"Jl. Gatot Subroto No. 44, Jakarta 12190, Indonesia",
                    Telepon:"(021) 5225837",
                    Email:	"<EMAIL>",
                    Website: "https://regionalinvestment.bkpm.go.id/"
                }]
            }



            const layer = await prisma.tb_peluang_layers.findMany({
                where:{
                    id_peluang:Id
                },
                select:{
                    id_peluang:true,
                    id_pl:true,
                    layeruid:true
                }
            })

         return {
                success : true,
                data : {
                    detail,
                    insentif,
                    info,
                    galeri,
                    kontak : kontak[0],
                    proyek_terkait,
                    layer

                }
            }
    }

    async detail_peluang_pid({params,response,request}: HttpContext) {
        const id  = params.id_peluang_kabkot
        const Id = parseInt(id, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ success:false,error: 'Invalid ID provided' })
        }
        const parameters = request.qs()
        const proyeks = await prisma.tb_peluang_daerah.findFirst({
            where:{
                id_peluang_daerah:Id,
            },
            include:{
                tb_peluang_daerah_tr:true,
                tb_adm_kabkot: {
                    include: {
                        tb_adm_kabkot_tr:true,
                        tb_adm_provinsi: {
                            include : {
                                tb_adm_provinsi_tr:true
                            }
                        },

                    }
                },
                tb_peluang_daerah_file:{
                    where:{
                        tipe:{
                            in:[1,2]
                        }
                    },
                    take:2
                },
                tb_peluang_daerah_kelayakan:true,
                tb_adm_provinsi:{
                    include:{
                        tb_adm_provinsi_tr:true
                    }
                },
                tb_sub_sektor_daerah:{
                    include:{
                        tb_sub_sektor_nasional :{
                            include:{
                                sektor:{
                                    include:{
                                        sektor_nasional_tr:true
                                    }
                                }
                            }
                        }
                    }
                },
                tb_peluang_daerah_komoditi:{
                    include:{
                        tb_komoditi_daerah:{
                            include:{
                                tb_komoditi_nasional_ref:{
                                    include:{
                                        tb_komoditi_nasional_ref_tr:true
                                    }
                                }
                            }
                        }
                    }
                },
                tb_peluang_status:true,
                tb_halaman_pengunjung_det:true
            }

        })
        let total_pengunjung=0
        const pengunjung = await prisma.tb_halaman_pengunjung_det.findMany({
            where:{
                id_halaman_pengunjung:8,
                id_konten:Id
            }
        })

        if (pengunjung.length > 0) {

            const uniqueIPs = new Set(); // Store unique IPs

            pengunjung.forEach(item => {
                uniqueIPs.add(item.ip_pengunjung);
            });
            total_pengunjung = uniqueIPs.size;
        }




        let komoditi = ''
        if (proyeks?.tb_peluang_daerah_komoditi) {
            if (!parameters.en) {
                komoditi =  proyeks?.tb_peluang_daerah_komoditi.
                map((item)=>`${item.tb_komoditi_daerah?.tb_komoditi_nasional_ref?.nama}`)
                .join(', ')
            }else{
                komoditi =  proyeks?.tb_peluang_daerah_komoditi.
                map((item)=>`${item.tb_komoditi_daerah?.tb_komoditi_nasional_ref?.tb_komoditi_nasional_ref_tr[0]?.nama}`)
                .join(', ')
            }

        }

        let image ='';
        let vidio ='';
        for (let i = 0; i < proyeks?.tb_peluang_daerah_file.length; i++) {
            if (proyeks?.tb_peluang_daerah_file[i].tipe === 1) {
                image = `${env.get('APP_URL')}/uploads/peluang_daerah/${proyeks?.id_peluang_daerah}/${proyeks?.tb_peluang_daerah_file[i].nama}`
                break; // Exit the loop when the element is 3
            }
        }
        for (let i = 0; i < proyeks?.tb_peluang_daerah_file.length; i++) {
            if (proyeks?.tb_peluang_daerah_file[i].tipe === 2) {
                vidio = `${env.get('APP_URL')}/uploads/peluang_daerah/${proyeks?.id_peluang_daerah}/${proyeks?.tb_peluang_daerah_file[i].nama}`
                break; // Exit the loop when the element is 3
            }
        }

        let judul = proyeks?.judul
        let kabkot = proyeks?.tb_adm_kabkot?.nama
        let provinsi = proyeks?.tb_adm_provinsi?.nama ? proyeks?.tb_adm_provinsi.nama : proyeks?.tb_adm_kabkot?.tb_adm_provinsi.nama
        if (parameters.en) {
            judul = proyeks?.tb_peluang_daerah_tr[0]?.judul ||proyeks?.judul
            provinsi = proyeks?.tb_adm_provinsi?.nama ? proyeks?.tb_adm_provinsi.tb_adm_provinsi_tr[0]?.nama || proyeks?.tb_adm_provinsi.nama : proyeks?.tb_adm_kabkot?.tb_adm_provinsi.nama
            kabkot = proyeks?.tb_adm_kabkot?.tb_adm_kabkot_tr[0]?.nama || proyeks?.tb_adm_kabkot?.nama

        }

        const detail = {
                    id_sektor: proyeks?.tb_sub_sektor_daerah?.tb_sub_sektor_nasional.sektor.id_sektor,
                    judul: judul,
                    nama_kabkot: kabkot,
                    nama_provinsi: provinsi,
                    lokasi_kawasan : proyeks?.lokasi,
                    nama_sektor: proyeks?.tb_sub_sektor_daerah?.tb_sub_sektor_nasional.sektor.nama,
                    image :image,
                    vidio :vidio,
                    kode_kbli : proyeks?.kode_kbli,
                    nilai_investasi: numberFormatRpSingkatan(proyeks?.tb_peluang_daerah_kelayakan[0]?.initial_invesment),
                    tahun:proyeks?.tahun,
                    nilai_irr: `${proyeks?.tb_peluang_daerah_kelayakan[0]?.irr == undefined ? 0 : proyeks?.tb_peluang_daerah_kelayakan[0]?.irr}%`,
                    payback_period: `${proyeks?.tb_peluang_daerah_kelayakan[0]?.pp == undefined ? '' : proyeks?.tb_peluang_daerah_kelayakan[0]?.pp} Tahun`,
                    nilai_npv: numberFormatRpSingkatan(proyeks?.tb_peluang_daerah_kelayakan[0]?.npv),
                    longitude :proyeks?.lon,
                    latitude : proyeks?.lat,
                    komoditi,
                    status_proyek:proyeks?.tb_peluang_status?.nama == null ? 'DRAF' : proyeks?.tb_peluang_status?.nama,
                    total_pengunjung
                }

            const infos = await prisma.tb_peluang_daerah_file.findMany({
                where : {
                    id_peluang_daerah : Id,
                    tipe:4,
                },
                // include:{
                //     tb_peluang_daerah_file_tr : true
                // }
            })
            const info = infos.map((item) => {
                return {
                    ...item,
                    nama : `${env.get('APP_URL')}/uploads/peluang_daerah/${proyeks?.id_peluang_daerah}/${item.nama}`,
                }
            })
            const galery = await prisma.tb_peluang_daerah_file.findMany({
                where : {
                    id_peluang_daerah : Id,
                    tipe:1,
                }
            })

            const galeri = galery.map((item) => {
                return {
                    image : `${env.get('APP_URL')}/uploads/peluang_daerah/${proyeks?.id_peluang_daerah}/${item.nama}`,
                }
            })

            const kontaks = await prisma.tb_peluang_daerah_kontak.findMany({
                where : {
                    id_peluang_daerah : Id,
                },
                include:{
                    tb_peluang_kontak:true
                }
            })
            let kontak

            if(kontaks.length > 0){

                kontak =  kontaks.map((i) => {
                    return {
                        Kontak : i.tb_peluang_kontak.nama,
                        Alamat : i.tb_peluang_kontak.alamat,
                        Telepon : i.tb_peluang_kontak.no_telp,
                        Email : i.tb_peluang_kontak.email,
                        Website : i.tb_peluang_kontak.url_web,
                    }
                })
            }else{
                kontak = [{
                    Kontak:	"Kementerian Investasi /BKPM Direktorat Perencanaan Sumber Daya Alam",
                    Alamat:	"Jl. Gatot Subroto No. 44, Jakarta 12190, Indonesia",
                    Telepon:"(021) 5225837",
                    Email:	"<EMAIL>",
                    Website: "https://regionalinvestment.bkpm.go.id/"
                }]
            }



            const proyekTerkait = await prisma.tb_peluang_daerah.findMany({
                where:{
                    status:99,
                    // id_prioritas:1
                },
                include: {
                    tb_sub_sektor_daerah:{
                        include:{
                            tb_sektor_daerah:{
                                include:{
                                    sektor_nasional:{
                                        include:{
                                            sektor:{
                                                include:{
                                                    sektor_nasional_tr:true
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    tb_adm_kabkot: {
                        include: {
                            tb_adm_provinsi: {
                                include:{
                                    tb_adm_provinsi_tr:true
                                }
                            },
                            tb_adm_kabkot_tr:true
                        }
                    },
                    tb_adm_provinsi:{
                        include:{
                            tb_adm_provinsi_tr:true
                        }
                    },
                    tb_peluang_daerah_file:{
                        where:{
                            tipe:1
                        }
                    },
                    tb_peluang_daerah_tr:true,
                    tb_peluang_daerah_kelayakan:true
                },
                take:10
            })

            const proyek_terkait = await Promise.all(proyekTerkait.map(async ({tb_sub_sektor_daerah, tb_adm_kabkot,tb_adm_provinsi, ...data}) => {
                let kabkot = tb_adm_kabkot?.nama
                let provinsi = tb_adm_kabkot?.tb_adm_provinsi.nama || tb_adm_provinsi?.nama
                let judul = data.judul
                let sektor : string| undefined  = tb_sub_sektor_daerah?.tb_sektor_daerah?.sektor_nasional?.sektor?.nama
                if (parameters.en) {
                    kabkot = tb_adm_kabkot?.tb_adm_kabkot_tr[0]?.nama || kabkot
                    provinsi = tb_adm_kabkot?.tb_adm_provinsi.tb_adm_provinsi_tr[0]?.nama || provinsi
                    judul = data.tb_peluang_daerah_tr[0].judul || judul
                    sektor = tb_sub_sektor_daerah?.tb_sektor_daerah?.sektor_nasional?.sektor?.sektor_nasional_tr?.nama
                }
                return {
                    id_peluang_daerah :data.id_peluang_daerah,
                    id_sektor: tb_sub_sektor_daerah?.tb_sektor_daerah?.sektor_nasional?.id_sektor_nasional,
                    judul: judul,
                    nama_kabkot: kabkot,
                    nama_provinsi: provinsi,
                    nilai_irr: `${data.tb_peluang_daerah_kelayakan[0]?.irr}%`,
                    nilai_investasi: numberFormatRpSingkatan(data?.tb_peluang_daerah_kelayakan[0]?.initial_invesment || 0),
                    nilai_npv: numberFormatRpSingkatan(data?.tb_peluang_daerah_kelayakan[0]?.npv || 0),
                    nama_sektor: sektor,
                    status:'PID',
                    icon: `${env.get('APP_URL')}/uploads/icon-web/${tb_sub_sektor_daerah?.tb_sektor_daerah?.sektor_nasional?.sektor?.icon}`,
                    image : `${env.get('APP_URL')}/uploads/peluang_daerah/${data?.id_peluang_daerah}/${data.tb_peluang_daerah_file[0]?.nama}`,
                }
            }));

            const layers = await prisma.tb_peluang_daerah_layer_spasial.findMany({
                where:{
                    id_peluang_daerah:Id
                },
                select:{
                    id_peluang_daerah_layer_spasial:true,
                    id_peluang_daerah:true,
                    url_service:true
                }
            })
            const layer = layers.map((item) => {
                return{
                    id_peluang_daerah_layer_spasial:item.id_peluang_daerah_layer_spasial,
                    id_peluang_daerah:item.id_peluang_daerah,
                    layeruid:item.url_service
                }
            })

        return {
            success:true,
            data: {
                detail,
                deskripsi: parameters.en
                            ? proyeks?.tb_peluang_daerah_tr[0]?.keterangan
                            : proyeks?.keterangan
                            ,
                aspek_teknis :  parameters.en
                                ? proyeks?.tb_peluang_daerah_tr[0]?.aspek_teknis
                                : proyeks?.aspek_teknis,
                aspek_pasar : parameters.en
                            ? proyeks?.tb_peluang_daerah_tr[0]?.aspek_teknis
                            : proyeks?.aspek_teknis,
                infografis : info,
                galeri : galeri,
                kontak,
                proyek_terkait,
                layer
                }
        }
    }

    async provinsi_ref({request}: HttpContext) {
        const datas = await prisma.tb_adm_provinsi.findMany({
        select:{
            id_adm_provinsi:true,
            nama:true,
            tb_adm_provinsi_tr:{
                select:{
                    nama:true
                }
            }
        },
        orderBy:{
            id_adm_provinsi:'asc'
        }
        });
        const parameters = request.qs()
        const data = datas.map((item) =>{

            return {
                id_adm_provinsi:item.id_adm_provinsi,
                nama : parameters.en ? item.tb_adm_provinsi_tr[0]?.nama : item.nama
            }
        })

      return {
          success : true,
          data : {data}
      }
    }

     async kabkot_ref({params,response,request}: HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ success:false,error: 'Invalid ID provided' })
        }
        const datas = await prisma.tb_adm_kabkot.findMany({
            where:{
                id_adm_provinsi:Id
            },
            select:{
                id_adm_provinsi:true,
                id_adm_kabkot:true,
                nama:true,
                tb_adm_kabkot_tr:{
                    select:{
                        nama:true
                    }
                }
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const parameters = request.qs()
        const data = datas.map((item) =>{
            return {
                id_adm_provinsi:item.id_adm_provinsi,
                id_adm_kabkot:item.id_adm_kabkot,
                nama : parameters.en ? item.tb_adm_kabkot_tr[0]?.nama : item.nama
            }
        })


      return {
          success : true,
          data : {data}
      }
    }

     async sektor_peluang_ref({response}: HttpContext) {

        const data = await prisma.tb_sektor_nasional_ref.findMany({
        select:{
            id_sektor:true,
            nama:true
        },
        orderBy:{
            id_sektor:'asc'
        }
        });

      return {
          success : true,
          data : {data}
      }
    }

    async kategori_sektor_ref({response,request}: HttpContext) {

        const datas = await prisma.tb_kategori_sektor.findMany({
            select:{
                id_kategori_sektor:true,
                nama:true,
                tb_kategori_sektor_tr:{
                    select:{
                        nama:true
                    }
                }
            },
            orderBy:{
                id_kategori_sektor:'asc'
            }
        });

        const parameters = request.qs()
        const data = datas.map((item) =>{
            return {
                id_kategori_sektor:item.id_kategori_sektor,
                nama : parameters.en ? item.tb_kategori_sektor_tr[0]?.nama : item.nama
            }
        })

      return {
          success : true,
          data : {data}
      }
    }

     /**
     * @submit_unduh_dokumen
     * @paramQuery nama - nama - @type(string)
     * @paramQuery tujuan - tujuan - @type(number)
     * @paramQuery email - email  - @type(string)
     * @paramQuery asal_negara - asal negara - @type(string)
     * @paramQuery id_peluang - id peluang - @type(number)
     */
    public async submit_unduh_dokumen({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createPostDokumenValidator)
        try {
           const dataPost = {
                nama : data.nama,
                email : data.email,
                id_unduh_data_tujuan : data.tujuan,
                asal_negara : data.asal_negara,
                id_konten : data.id_peluang,
                ip_pengunjung : request.ip(),
                id_jenis_konten:data.id_jenis_konten,
                no_telp:'-'
            }

            const insert = await prisma.tb_unduh_data.create({data:dataPost})
            if (insert) {
                const peluang = await prisma.tb_peluang_kabkot.findUnique({
                    where:{
                        id_peluang_kabkot:data.id_peluang
                    },
                    include:{
                        tb_peluang_kabkot_file:true,
                        tb_peluang_kabkot_file_tr:{
                            where:{
                                tipe:4,
                            },
                        }
                    },

                })

                let files: String[] = [];
                let download_list = ''
                if (peluang) {
                    const headTable = `
                                        <table style="width: 100%; border: none; margin-bottom: 20px;">
                                            <tr>
                                                <th align="center" style="padding: 8px;">File Name</th>
                                                <th align="center" style="padding: 8px;">Action</th>
                                            </tr>
                                    `
                    const footTable = '</table></br></br>'
                        download_list += '<h2 align="left">Image</h2>'
                        download_list += headTable
                        peluang.tb_peluang_kabkot_file.map(async(item) => {
                            if (item.tipe == 1) {
                                if (item.nama != null) {
                                    const idUrl = base64.encode(`peluang/${peluang.id_adm_kabkot}/peluang/${peluang.tahun}/${peluang.id_prioritas}/${item.nama}`)
                                    const url = `${env.get('APP_URL')}/download/${idUrl}`
                                    const str= `
                                                    <tr>
                                                        <td align="left">${item.nama}</td>
                                                        <td><a href="${url}" >Download</a></td>
                                                    </tr>
                                                `
                                    download_list += str
                                }
                            }
                        })
                        download_list += footTable
                        const hasTipe2 = peluang.tb_peluang_kabkot_file.some(item => item.tipe === 2);
                        if (hasTipe2) {

                            download_list += '<h2 align="left">Vidio</h2>'
                            download_list += headTable
                            const promises = peluang.tb_peluang_kabkot_file.map(async(item) => {
                                if (item.tipe == 2) {
                                    if (item.nama != null) {
                                        const check = await checkFileOrUrl(item.nama)
                                        if (check == 'file') {
                                            const idUrl = base64.encode(`peluang/${peluang.id_adm_kabkot}/peluang/${peluang.tahun}/${peluang?.id_prioritas}/${item.nama}`)
                                            const url = `${env.get('APP_URL')}/download/${idUrl}`
                                            return `
                                                    <tr>
                                                        <td align="left">${item.nama}</td>
                                                        <td><a href="${url}" >Download</a></td>
                                                    </tr>
                                                `
                                        }else{
                                            return `
                                            <tr>
                                            <td align="left">${item.nama}</td>
                                                <td><a href="${item.nama}">Ke Vidio </a></td>
                                            </tr>
                                            `
                                        }
                                    }
                                }
                            })
                            const results = await Promise.all(promises);
                            download_list += results.join('');

                        }
                        download_list += footTable

                        download_list += '<h2 align="left">Dokumen</h2>'
                        download_list += headTable
                        peluang.tb_peluang_kabkot_file.map(async(item) => {
                            if (item.tipe == 3) {
                                if (item.nama != null) {
                                    const idUrl = base64.encode(`peluang/${peluang.id_adm_kabkot}/peluang/${peluang.tahun}/${peluang?.id_prioritas}/${item.nama}`)
                                    const url = `${env.get('APP_URL')}/download/${idUrl}`
                                    const str= `
                                                    <tr>
                                                        <td align="left">${item.nama}</td>
                                                        <td><a href="${url}" >Download</a></td>
                                                    </tr>
                                                `
                                    download_list += str
                                }
                            }
                        })
                        download_list += footTable

                        download_list += '<h2 align="left">Infografis/Infomemo English</h2>'
                        download_list += headTable
                        peluang.tb_peluang_kabkot_file.map(async(item) => {
                            if (item.tipe == 4) {
                                if (item.nama != null) {
                                    const idUrl = base64.encode(`peluang/${peluang.id_adm_kabkot}/peluang/${peluang.tahun}/${peluang?.id_prioritas}/${item.nama}`)
                                    const url = `${env.get('APP_URL')}/download/${idUrl}`
                                    const str= `
                                                    <tr>
                                                        <td align="left">${item.nama}</td>
                                                        <td><a href="${url}" >Download</a></td>
                                                    </tr>
                                                `
                                    download_list += str
                                }
                            }
                        })
                        download_list += footTable


                        download_list += '<h2 align="left">Infografis/Infomemo English</h2>'
                        download_list += headTable
                        peluang.tb_peluang_kabkot_file_tr.map((item) => {
                            if (item.nama != null) {
                                const idUrl = base64.encode(`peluang/${peluang.id_adm_kabkot}/peluang/${peluang.tahun}/${peluang.id_prioritas}/${item.nama}`)
                                const url = `${env.get('APP_URL')}/download/${idUrl}`
                                const str= `
                                                <tr>
                                                    <td align="left">${item.nama}</td>
                                                    <td><a href="${url}" >Download</a></td>
                                                </tr>
                                            `
                                download_list += str
                            }
                        });
                        download_list += footTable
                    // )


                }

                // return download_list

                if(files){
                    // Folder tujuan untuk menyimpan zip
                    // const name = peluang?.nama.replaceAll(' ','_')
                    // const random = await this.generateRandomString()
                    // const zipFolderPath = path.join('uploads', 'unduhan', `${data.id_peluang}`);
                    // const zipFilePath = path.join(zipFolderPath, `${name}_${random}.zip`);

                    // // Pastikan folder tujuan ada, jika tidak buat folder
                    // if (!fs.existsSync(zipFolderPath)) {
                    //     fs.mkdirSync(zipFolderPath, { recursive: true });
                    // }

                    // // Membuat file zip
                    // const output = fs.createWriteStream(zipFilePath);
                    // const archive = archiver('zip', {
                    // zlib: { level: 9 } // Kompresi tertinggi
                    // });

                    // // Ketika zip selesai
                    // output.on('close', () => {
                    // console.log(`Zip file berhasil dibuat, total ${archive.pointer()} byte`);
                    // });

                    // // Menangani error
                    // archive.on('error', (err) => {
                    // throw err;
                    // });

                    // // Pipe data zip ke file output
                    // archive.pipe(output);

                    // // Tambahkan file-file ke dalam zip
                    // files.forEach(file => {
                    //     const filePath = path.join( file); // Menggunakan path lokal
                    //     if (fs.existsSync(filePath)) { // Pastikan file ada sebelum menambahkannya ke zip
                    //         archive.file(filePath, { name: path.basename(filePath) });
                    //     } else {
                    //         console.warn(`File tidak ditemukan: ${filePath}`);
                    //     }
                    // });

                    // Finalisasi zip
                    // archive.finalize();
                    //  const idUrl = base64.encode(`${data.id_peluang}/${name}_${random}.zip`)
                    //  const url = `${env.get('APP_URL')}/download/${idUrl}`
                     const logo = `${env.get('APP_URL')}/uploads/logo.png`
                     const body = ` <div style="width: 100%; background-color:#eff3f8; text-align: center; padding: 20px; overflow-x: hidden;">
                                        <table style="width: 100%; border-collapse: collapse; margin: 0 auto; max-width: 100%; table-layout: fixed;">

                                            <tr>
                                                <td align="center">
                                                    <div style="width: 100%; max-width: 600px; background-color: white; text-align: center; padding: 20px; border-radius: 8px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); box-sizing: border-box;padding-top:20px">

                                                        <img src="${logo}" alt="Header Image" style="width: 300px; height: auto; margin: 0 auto;">
                                                        <br>
                                                        <h2 style="margin-top:10px">${peluang?.nama}</h2>
                                                        <span>Terima kasih telah mengakses website peluang investasi regional.</span><br>
                                                        <span>Berikut daftar file yang dapat di unduh sesuai permintaan anda:</span>
                                                        <br><br>
                                                        ${download_list}
                                                        <br><br>
                                                        <label> <center>Informasi lebih lanjut mengenai kajian FS ini, mohon dapat menghubungi kontak berikut ini:</center></label>
                                                        <label> <center>Kementerian Investasi Dan Hilirisasi/BKPM, </center></label>
                                                        <br>
                                                        <label style="margin-top:10px"> <center>Jl. Jend. Gatot Subroto No. 44, Jakarta 12190, </center></label>
                                                        <label> <center>
                                                            <a href="mailto:<EMAIL>"><EMAIL></a>
                                                        </center></label>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>


`
                    await send_mail(data.email,'No-Reply : BKPM Download Link ',body)
                     response.status(201).json({ success: true,data:{
                        nama:data.nama,
                        email:data.email,
                        // link:url
                    }})

                }
            }


        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

    public async generateRandomString(length = 10) {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return result;
    }
    public async get_tujuan_download(params:type) {
        const data = await prisma.tb_unduh_data_keperluan.findMany()
        return {
            success : true,
            data : data
        }
    }

}

    //tes
