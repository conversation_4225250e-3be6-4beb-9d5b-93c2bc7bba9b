
import { createConfigUploadValidator, uploadConfigUploadValidator } from '#validators/config_upload'
import { createRoleValidator, updateRoleValidator } from '#validators/role'
import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'
export default class ConfigUploadsController {
  /**
     * @get_paginate
     * @summary Insert User
     * @paramQuery login_name - Masukan Username Anda - @type(string) @required
     * @paramQuery password - Masukan Password Anda - @type(string) @required
     * @paramQuery email - Ma<PERSON>kan <PERSON>ail <PERSON> - @type(string) @required
     * @paramQuery full_name - <PERSON><PERSON><PERSON> - @type(string) @required
     * @paramQuery role_id - Masukan Role Anda - @type(number) @required
     */
  async index({}: HttpContext) {
    const data = await prisma.config_upload.findMany({})
    if(data)
      return data
    return {}
  }
    /**
     * @get_paginate
     * @summary Get a list of role with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page)
    const pageSize = parseInt(params.pageSize)
    const config = {

                  }

    return get_paginate(page,pageSize,prisma.config_upload,config)
  }
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
     * @store
     * @summary Insert cpnfig uploads
     * @paramQuery jenis - Jenis Config - @type(string) @required
     * @paramQuery size - Maksimal ukuran file yang boleh di uploads - @type(string) @required
     * @paramQuery extnames - jenis extension yang di perbolehkan dengan array contoh =['jpg','png'] - @type(string) @required
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createConfigUploadValidator)
        try {
           const {jenis,size,extnames } = data
           const dataPost = {
                jenis,size,extnames,
            }
            const insert = await prisma.config_upload.create({data:dataPost})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
        const data = await prisma.config_upload.findUnique({
          where: {
            id: Id,
          },
        })

        return {
          success : true,
          data : data
        }
  }


  async edit({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }

      const data = await prisma.config_upload.findUnique({
        where: {
          id: Id,
        },
      })

      return {
        success : true,
        data : {data}
      }
  }

  /**
     * @update
      * @summary Insert cpnfig uploads
     * @paramQuery jenis - Jenis Config - @type(string) @required
     * @paramQuery size - Maksimal ukuran file yang boleh di uploads - @type(string) @required
     * @paramQuery extnames - jenis extension yang di perbolehkan dengan array contoh =['jpg','png'] - @type(string) @required
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
  async update({ params, request ,response}: HttpContext) {
    const dataPost = await request.validateUsing(uploadConfigUploadValidator)
      const { jenis,size,extnames } = dataPost

      const Id = parseInt(params.id, 10)

      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }

      try {
        const updatePost = await prisma.config_upload.update({
          where: {
            id: Id,
          },
          data: {
            jenis,size,extnames
          },
        })
      return response.status(200).json({ success: true, data: updatePost })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data' })
    }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.config_upload.delete({
        where: {
          id: Id,
        },
      })
      return response.status(200).send(deletePost)
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }
}