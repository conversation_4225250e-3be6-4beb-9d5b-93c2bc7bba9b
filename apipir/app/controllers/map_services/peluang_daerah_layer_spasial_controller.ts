import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../helpers/model_helper.js';

import prisma from '../../lib/prisma.js'
// peluang_layers_controller
export default class PeluangDaerahLayerSpasialController {
  private async schemaData() {
    const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
      id_peluang_daerah_layer_spasial: {column: 'id_peluang_daerah_layer_spasial', alias: 'id_peluang_daerah_layer_spasial', type: 'int'},
      id_peluang_daerah: {column: 'id_peluang_daerah', alias: 'id_peluang_daerah', type: 'int'},
      url_service: {column: 'url_service', alias: 'url_service', type: 'string'},
      tipe: {column: 'tipe', alias: 'tipe', type: 'int'},
      nama_layer: {column: 'nama_layer', alias: 'nama_layer', type: 'string'},
      status: {column: 'status', alias: 'status', type: 'int'},
    };

    const joins = {};

    const where = {};

    return {
      columns: columnMappings,
      join: joins,
      where: where,
    };
  }

  public async get({ request, params, response }: HttpContext) {
    const query = request.qs(); // Ini akan mengembalikan objek query string

    console.log(query); // { id_kawasan_industri: '201' }
    let isAll = false;

    interface Options {
      skip: number; // Keep it non-optional
      take: number;
      select?: { [key: string]: boolean };
    }

    if (query['all'] || query['all'] == 'true') {
      isAll = true
      delete query['all'];
    }

    const page = parseInt(request.input('page', 1));
    const perPage = parseInt(request.input('per_page', 10));
    delete query['page'];
    delete query['per_page'];

    let options: Options = {
      skip: (page - 1) * perPage,
      take: perPage,
    };

    if (isAll) {
      delete options['skip'];
      delete options['take'];
    }

    if (query['select']) {
      options['select'] = modelSelect((await this.schemaData()).columns, query['select']);
      delete query['select'];
    }

    options['where'] = modelWhereAnd((await this.schemaData()).columns, query);

    const data = await prisma.tb_peluang_daerah_layer_spasial.findMany(options);

    if (isAll) {
      return response.status(200).json({
        success: true,
        data: data
      });
    }

    const totalCount = await prisma.tb_peluang_daerah_layer_spasial.count();

    return response.status(200).json({
      success: true,
      data: data,
      pagination: {
        page: page,
        per_page: perPage,
        total_count: totalCount,
        total_pages: Math.ceil(totalCount / perPage),
      },
    });
  }

  public async getById({ request, params, response }: HttpContext) {
    interface Options {
      select?: { [key: string]: boolean };
      where?: { [key: string]: any; };
    }

    let options: Options = {};

    if (params['select']) {
      options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
      delete params['select'];
    }

    options['where'] = {
      'id_peluang_daerah_layer_spasial': parseInt(params.id)
    }

    const data = await prisma.tb_peluang_daerah_layer_spasial.findFirst(options);

    return response.status(200).json({
      success: true,
      data: data
    });
  }

  public async createOrUpdate({ request, response }: HttpContext) {
    let params = request.body()

    try {
      const result = await prisma.$transaction(async (prisma) => {
        if (params.id_peluang_daerah_layer_spasial) {
          // Jika `id` ada, lakukan operasi update
          const update = await prisma.tb_peluang_daerah_layer_spasial.update({
            where: { id_peluang_daerah_layer_spasial: params.id_peluang_daerah_layer_spasial },
            data: params,
          })

          return {
            status: 'success',
            message: 'Successfully Updated Data',
            data: update,
          }
        } else {
          console.log("Sync tb_peluang_daerah_layer_spasial id sequence",
            await prisma.$executeRaw`
            SELECT setval((
                SELECT PG_GET_SERIAL_SEQUENCE('"tb_peluang_daerah_layer_spasial"', 'id_peluang_daerah_layer_spasial')),
                (SELECT (MAX("id_peluang_daerah_layer_spasial") + 1) FROM "tb_peluang_daerah_layer_spasial"),
                false) FROM "tb_peluang_daerah_layer_spasial";
            `);

            if (Array.isArray(params)) {
              await prisma.tb_peluang_daerah_layer_spasial.deleteMany({
                where: {
                  id_peluang_daerah: params[0]['id_peluang_daerah'],
                },
              });

              for (const item of params) {
                // await prisma.tb_komoditi_daerah_file_tr.create({
                //   data: {
                //     kd_bahasa: "en",
                //     nama: "White.webp",
                //     id_komoditi_daerah_file: 267,
                //     judul: null,
                //     keterangan: null,
                //     tb_komoditi_daerah_file: {
                //       connect: { id_komoditi_daerah_file: 267 }     }
                //   }
                // });

                await prisma.tb_peluang_daerah_layer_spasial.create({
                  data: {
                    id_peluang_daerah: item.id_peluang_daerah,
                    url_service: item.url_service ?? '',
                    tipe: item.tipe ?? 0,
                    nama_layer: item.nama_layer ?? '',
                    status: item.status ?? 0
                  },
                });
              }

              return {
                status: 'success',
                message: 'Successfully Added Data',
                data: null,
              }
            } else {
              const save = await prisma.tb_peluang_daerah_layer_spasial.create({
                data: params,
              })

              return {
                status: 'success',
                message: 'Successfully Added Data',
                data: save,
              }
            }
        }
      })

      return response.json(result)

    } catch (error) {
      // Handle error jika ada kesalahan dalam transaksi
      console.log(error);
      return response.status(500).json({
        status: 'error',
        message: 'An error occurred while processing data',
        error: error.message,
      })
    }
  }

  public async deleteById({ request, params, response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_peluang_daerah_layer_spasial.delete({
        where: {
          id_peluang_daerah_layer_spasial: Id,
        },
      })

      return response.status(200).json({
        status: 'success',
        message: 'Success Delete Data',
        data: deletePost,
      })
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }
}