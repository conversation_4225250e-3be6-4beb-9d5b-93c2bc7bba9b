import { createMenuValidator, updateMenuValidator } from '#validators/menu';
import type { HttpContext } from '@adonisjs/core/http'
import { configUpload, get_paginate, img_to_webp, upload } from '../helpers/global_helper.js';
import { createAppSLiderValidator } from '#validators/add_slider_peluang';
import { createLinkTerkaitValidator, updateLinkTerkaitValidator } from '#validators/link_terkait';
import env from '#start/env';
import prisma from '../lib/prisma.js';


export default class LinkTerkaitController {
  /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.tb_link_terkait.findMany({
          });

      return {
          success : true,
          data : {data}
      }
  }

  /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
           { nama: { contains: search, mode: 'insensitive' } },
          { alamat: { contains: search, mode: 'insensitive' } },
          { no_telpon: { contains: search, mode: 'insensitive' } },
          { url_web: { contains: search, mode: 'insensitive' } },
          { nama_pic: { contains: search, mode: 'insensitive' } },
                // Assuming status is a number, use `equals`
          { status: parseInt(search) ? { equals: parseInt(search) } : undefined },
        ],
      };
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_link_terkait.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_link_terkait.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    const data = datas.map((item =>{
        return {
          ...item,
          file_logo : `${env.get('APP_URL')}/uploads/icon-web/${item.file_logo}`,
        }
    }))

    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {

  }

  /**
     * @store
     * @paramQuery nama - nama - @type(string)
     * @paramQuery alamat - Alamaat - @type(string)
     * @paramQuery no_telpon - No Telp - @type(string)
     * @paramQuery id_kategori - ID Kategori - @type(number)
     * @paramQuery nama_pic - nama PIC - @type(string)
     * @paramQuery uel_web - url_web - @type(string)
     * @paramQuery is_ikn - bolean 1 true 0 false - @type(number)
     * @paramQuery is_kontak - bolean 1 true 0 false- @type(number)
     * @paramQuery image - image- @type(file)
     */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createLinkTerkaitValidator)
        try {
            const configImg = await configUpload('img')
            const image = request.files('image',configImg);
            const background = request.files('background',configImg);
            // for (const item of image) {
            //   if (item?.errors.length > 0) {
            //     return response.status(500).json({ success: false, message: item.errors });
            //   }
            // }
            let nama_file_img = ''
            let nama_file_logo = ''
              if (image) {
                const uploadPromisess = image.map(async (item) => {
                    const uploadPath = `uploads/icon-web`;
                    const upImg = await img_to_webp(item, uploadPath);
                    nama_file_logo = upImg.data.filename
                });
                await Promise.all(uploadPromisess);
              }

              if (background) {
                const uploadPromisess = background.map(async (item) => {
                    const uploadPath = `uploads/link_terkait/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    nama_file_img = upImg.data.filename
                });
                await Promise.all(uploadPromisess);
              }

            let is_ikn = false
            let is_kontak = false
            if(data.is_ikn == 1){
                is_ikn = true
            }
            if(data.is_kontak == 1){
                is_kontak = true
            }
           const dataPost = {
                nama : data.nama,
                alamat : data.alamat,
                no_telpon : data.no_telpon,
                url_web : data.url_web,
                nama_pic : data.nama_pic,
                status : 99,
                is_ikn ,
                id_kategori : data.id_kategori,
                is_kontak ,
                file_logo : nama_file_logo,
                file_image : nama_file_img,
            }

           const insert = await prisma.tb_link_terkait.create({data:dataPost})

            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        let data = await prisma.tb_link_terkait.findUnique({
          where: {
            id_link_terkait: Id,
          },
        })

        if (data && data.file_logo) {
          data.file_logo = `${env.get('APP_URL')}/uploads/icon-web/${data.file_logo}`;
        }
        if (data && data.file_image) {
          data.file_image = `${env.get('APP_URL')}/uploads/link_terkait/${data.file_image}`;
        }

        return {
            success : true,
            data : {data}
        }
  }

  /**
   * Edit individual record
   */
  async edit({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_link_terkait.findUnique({
          where: {
            id_link_terkait: Id,
          },

        })

        return {
            success : true,
            data
        }
  }

  /**
     * @update
     * @paramQuery nama - nama - @type(string)
     * @paramQuery alamat - Alamaat - @type(string)
     * @paramQuery no_telpon - No Telp - @type(string)
     * @paramQuery id_kategori - ID Kategori - @type(number)
     * @paramQuery nama_pic - nama PIC - @type(string)
     * @paramQuery uel_web - url_web - @type(string)
     * @paramQuery is_ikn - bolean 1 true 0 false - @type(number)
     * @paramQuery is_kontak - bolean 1 true 0 false- @type(number)
     * @paramQuery image - image- @type(file)
     *
     */
  async update({ params, request ,response}: HttpContext) {

      const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }

      const data = await request.validateUsing(updateLinkTerkaitValidator)
        try {
            const configImg = await configUpload('img')
            const image = request.files('image',configImg);
            const background = request.files('background',configImg);
            let dataPost = {}
            let dataPostTr = {}
            let is_ikn
            let is_kontak
            if(data.is_ikn == 1){
                is_ikn = true
            }else if(data.is_ikn == 0){
              is_ikn = false
            }
            if(data.is_kontak == 1){
              is_kontak = true
            }else if (data.is_kontak == 0){
              is_kontak = false
            }
            dataPost = {
              nama : data.nama,
              alamat : data.alamat,
              no_telpon : data.no_telpon,
              url_web : data.url_web,
              nama_pic : data.nama_pic,
              status : data.status ?? 99,
              is_ikn ,
              id_kategori : data.id_kategori,
              is_kontak ,
            }
              if (image) {
                const uploadPromisess = image.map(async (item) => {

                    const uploadPath = `uploads/icon-web`;
                    const upImg = await img_to_webp(image[0], uploadPath);


                      dataPost.file_logo =  upImg.data.filename
                });
                await Promise.all(uploadPromisess);
              }
              if (background) {
                const uploadPromisess = background.map(async (item) => {

                    const uploadPath = `uploads/icon-web`;
                    const upImg = await img_to_webp(item, uploadPath);


                      dataPost.file_image =  upImg.data.filename
                });
                await Promise.all(uploadPromisess);
              }
            const update = await prisma.tb_link_terkait.update({
              data:dataPost,
              where:{
                id_link_terkait:Id
              }
            })
            response.status(201).json({ success: true,data:update })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_link_terkait.delete({
        where: {
          id_link_terkait: Id,
        },
      })
      response.status(201).json({ success: true,data:deletePost })
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }



}