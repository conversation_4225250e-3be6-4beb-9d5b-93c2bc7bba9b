import type { HttpContext } from '@adonisjs/core/http'
import { configUpload, get_paginate, img_to_webp, upload, upload_img_to_webp } from '../helpers/global_helper.js';
import { createAppSLiderValidator } from '#validators/add_slider_peluang';
import { createNewsValidator } from '#validators/news';
import uploadFile from '../helpers/file_uploader.js';

import prisma from '../lib/prisma.js'


export default class AppSliderController {
  /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.tb_app_slider.findMany({
      include:{
        tb_app_slider_tr: true,
      }
    });

      return {
          success : true,
          data : {data}
      }
  }

  async getNewsByKategori({request,params,response}: HttpContext) {
    interface Options {
          skip?: number; // Keep it non-optional
          take?: number;
          select?: { [key: string]: boolean };
          orderBy?: { [key: string]: string };
          where?: any;
          include?: any;
    }

    const page = parseInt(request.input('page', 1));
    const perPage = parseInt(request.input('per_page', 10));

    const queryParams = { ...params, ...request.qs() };

    let idCategory = params.id_news_kategori || request.input('id_news_kategori');

    let q = request.input('q')?.trim() || '';

    let whereDefault = {
        OR: [
            {
                judul: {
                    contains: q,
                    mode: 'insensitive',
                },
            },
            {
                tb_komoditi_nasional_ref: {
                    nama: {
                        contains: q,
                        mode: 'insensitive',
                    },
                },
            },
            {
                tb_adm_kabkot: {
                    nama: {
                        contains: q,
                        mode: 'insensitive',
                    },
                },
            },
            {
                tb_adm_kabkot: {
                    nama_ibukota: {
                        contains: q,
                        mode: 'insensitive',
                    },
                },
            },
            {
                tb_adm_kabkot: {
                    tb_adm_provinsi: {
                        nama: {
                            contains: q,
                            mode: 'insensitive',
                        },
                    },
                },
            },
            {
                tb_adm_kabkot: {
                    tb_adm_provinsi: {
                        nama_ibukota: {
                            contains: q,
                            mode: 'insensitive',
                        },
                    },
                },
            },
        ],
    };

          let options: Options = {
                where:{
                  id_news_kategori: parseInt(idCategory),
                  ...(q ? whereDefault : {}),
                },
                include: {
                  tb_adm_kabkot: {
                    include: {
                      tb_adm_provinsi: true
                    }
                  },
                  tb_komoditi_nasional_ref: true,
                  tb_news_kategori: true,
                  files: true
                },
                skip: (page - 1) * perPage,
                take: perPage,
                orderBy: {
                  id: 'desc',
                },
      }
      let order = queryParams.order
      const by = ['asc','desc'].includes(queryParams.by) ? queryParams.by : 'asc'
      const paramList = ['status','nama_komoditi']
      if (order != undefined && paramList.includes(order)) {
          if (order == 'nama_komoditi') {

            options.orderBy = {judul:by}
          } else {

            options.orderBy = {[order]:by}
          }
      }
    let data = await prisma.tb_news.findMany(options);

    const totalCount = await prisma.tb_news.count({
      where:{
        id_news_kategori: parseInt(idCategory),
        ...(q ? whereDefault : {}),
      },
    });

      data = data.map((item) => {
        return {
            ...item,
            file_cover_path: `${process.env.APP_URL}/kajian/${item.id_news_kategori}/${item.file_cover}`,
            files: item.files.map((file) => ({
                ...file,
                path: `${process.env.APP_URL}/kajian/${item.id_news_kategori}/${file.nama}`
            }))
        };
    });

    return response.status(200).json({
          success: true,
          pagination: {
                  page: page,
                  per_page: perPage,
                  total_count: totalCount,
                  total_pages: Math.ceil(totalCount / perPage),
          },
          data: data,
    });
  }

        public async createOrUpdateKajian({ request, response, params }: HttpContext) {
                const paramID = parseInt(params.id) || request.input('id');
                const model = "kajian";

                try {
                        const result = await prisma.$transaction(async (prisma) => {
                                let resID: number | undefined;

                                let fileCoverName = '';
                                // let fileCoverNameTr = '';
                                if (request.file('file_cover')) {
                                        let uploadFileToServer = await uploadFile(request.file('file_cover'), model, 'cover');
                                        let filenameFromServer = '';
                                        if (uploadFileToServer) {
                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                        }
                                        fileCoverName = filenameFromServer; // Set fileName if file exists
                                }

                                // if (request.file('file_cover_tr')) {
                                //   let uploadFileToServer = await uploadFile(request.file('file_cover_tr'), model, 'cover');
                                //   let filenameFromServer = '';
                                //   if (uploadFileToServer) {
                                //           filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                //           filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                //   }
                                //   fileCoverNameTr = filenameFromServer; // Set fileName if file exists
                                // }

                                if (!paramID) {

                                  const check = await prisma.tb_roadmap.findFirst({
                                    where:{
                                      id_komoditi:parseInt(request.input('id_komoditi'))
                                    }
                                  })
                                  if (!check) {
                                    console.log('ini error');

                                    if (!check) {
                                      throw {
                                        status: 500,
                                        message: "Komoditi belum ada di roadmap. Silahkan tambahkan roadmap terlebih dahulu"
                                      };
                                    }
                                  }

                                        console.log("Sync tb_news id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_news"', 'id')),
                                                (SELECT (MAX("id") + 1) FROM "tb_news"),
                                                false) FROM "tb_news";
                                         `);

                                        const res = await prisma.tb_news.create({
                                                data: {
                                                        jenis: request.input('jenis') ? parseInt(request.input('jenis')) : 0,
                                                        judul: request.input('judul') ? request.input('judul') : '',
                                                        deskripsi_singkat: request.input('deskripsi_singkat') ? request.input('deskripsi_singkat') : '',
                                                        deskripsi: request.input('deskripsi') ? request.input('deskripsi') : '',
                                                        status: 0,
                                                        file_cover: fileCoverName,
                                                        id_news_kategori: request.input('id_news_kategori') ? parseInt(request.input('id_news_kategori')) : 0,
                                                        id_adm_kabkot: request.input('id_adm_kabkot') ? parseInt(request.input('id_adm_kabkot')) : 0,
                                                        id_adm_provinsi: request.input('id_adm_provinsi') ? parseInt(request.input('id_adm_provinsi')) : 0,
                                                        id_komoditi: request.input('id_komoditi') ? parseInt(request.input('id_komoditi')) : 0,
                                                }
                                        });
                                        const resTr = await prisma.tb_news_tr.create({
                                          data: {
                                                  id_news: res.id,
                                                  kd_bahasa: 'en',
                                                  judul: request.input('judul_tr') ? request.input('judul_tr') : '',
                                                  deskripsi_singkat: request.input('deskripsi_singkat_tr') ? request.input('deskripsi_singkat_tr') : '',
                                                  deskripsi: request.input('deskripsi_tr') ? request.input('deskripsi_tr') : '',
                                          }
                                        });
                                        resID = res.id;

                                        console.log("Sync tb_news_file id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_news_file"', 'id_news_file')),
                                                (SELECT (MAX("id_news_file") + 1) FROM "tb_news_file"),
                                                false) FROM "tb_news_file";
                                         `);

                                        for (let n in request.files('nama_file')) {
                                                let fileParam : any = {};
                                                let file = request.files('nama_file')[n];
                                                if (file) {
                                                        let uploadFileToServer = await uploadFile(file, model, 3);
                                                        let filenameFromServer = '';
                                                        if (uploadFileToServer) {
                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                        }
                                                        let filename = filenameFromServer; // Set fileName if file exists
                                                        fileParam['nama'] = filename;
                                                        fileParam['tipe'] = 3;
                                                        fileParam['id_news'] = resID;
                                                        fileParam['judul'] = "";

                                                        await prisma.tb_news_file.create({
                                                                data: fileParam
                                                        });
                                                }
                                        }

                                        for (let n in request.files('nama_file_tr')) {
                                          let fileParam : any = {};
                                          let file = request.files('nama_file_tr')[n];
                                          if (file) {
                                                  let uploadFileToServer = await uploadFile(file, model, 3);
                                                  let filenameFromServer = '';
                                                  if (uploadFileToServer) {
                                                          filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                          filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                  }
                                                  let filename = filenameFromServer; // Set fileName if file exists
                                                  fileParam['nama'] = filename;
                                                  fileParam['tipe'] = 3;
                                                  fileParam['id_news'] = resID;
                                                  fileParam['judul'] = "";

                                                  await prisma.tb_news_file_tr.create({
                                                          data: fileParam
                                                  });
                                          }
                                        }

                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: res
                                        };


                                } else {


                                        const update = await prisma.tb_news.update({
                                                where: { id: parseInt(paramID) },
                                                data: {
                                                  jenis: request.input('jenis') ? parseInt(request.input('jenis')) : 0,
                                                  judul: request.input('judul') ? request.input('judul') : '',
                                                  deskripsi_singkat: request.input('deskripsi_singkat') ? request.input('deskripsi_singkat') : '',
                                                  deskripsi: request.input('deskripsi') ? request.input('deskripsi') : '',
                                                  status: request.input('status') ? parseInt(request.input('status')) : 0,
                                                  file_cover: fileCoverName !+ '' ? fileCoverName : request.input('file_cover') ,
                                                  id_news_kategori: request.input('id_news_kategori') ? parseInt(request.input('id_news_kategori')) : 0,
                                                  id_adm_kabkot: request.input('id_adm_kabkot') ? parseInt(request.input('id_adm_kabkot')) : 0,
                                                  id_adm_provinsi: request.input('id_adm_provinsi') ? parseInt(request.input('id_adm_provinsi')) : 0,
                                                  id_komoditi: request.input('id_komoditi') ? parseInt(request.input('id_komoditi')) : 0,
                                                }
                                        });

                                        const id_news_tr = await prisma.tb_news_tr.findFirst({
                                          where: { id_news: parseInt(paramID) },
                                        });

                                        const updateTr = await prisma.tb_news_tr.update({
                                          where: { id_news_tr: id_news_tr?.id_news_tr ?? 0 },
                                          data: {
                                            judul: request.input('judul_tr') ? request.input('judul_tr') : '',
                                            deskripsi_singkat: request.input('deskripsi_singkat_tr') ? request.input('deskripsi_singkat_tr') : '',
                                            deskripsi: request.input('deskripsi_tr') ? request.input('deskripsi_tr') : '',
                                          }
                                        });

                                        let kajianFileIds = [];

                                        for (let x in request.input('id_news_file')) {
                                                let idRoadmapFile = parseInt(request.input('id_news_file')[x]);
                                                kajianFileIds.push(idRoadmapFile);
                                        }

                                        for (let n in request.files('nama_file')) {
                                                let fileParam : any = {};
                                                let file = request.files('nama_file')[n];

                                                const fieldName = file.fieldName; // Mendapatkan nama field, contoh: "nama_file[2]"
                                                const match = fieldName.match(/\[(\d+)\]/); // Ekstrak indeks dari nama field
                                                const index = match ? parseInt(match[1]) : 0; // Jika ada indeks, gunakan; jika tidak, gunakan 0
                                                let idRoadmapFile = 0;

                                                const element = Array.isArray(request.input('id_news_file'))
                                                              ? request.input('id_news_file')[index]
                                                              : undefined;
                                                if (element) {
                                                  idRoadmapFile = parseInt(request.input('id_news_file')[index]);
                                                }

                                                if (file) {
                                                        let uploadFileToServer = await uploadFile(file, model, undefined);
                                                        let filenameFromServer = '';
                                                        if (uploadFileToServer) {
                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                        }
                                                        let filename = filenameFromServer; // Set fileName if file exists
                                                        fileParam['nama'] = filename;
                                                        fileParam['tipe'] = 3;
                                                        fileParam['id_news'] = parseInt(paramID);
                                                        fileParam['judul'] = "";

                                                        if (idRoadmapFile) {
                                                                await prisma.tb_news_file.update({
                                                                        where: {
                                                                                id_news_file: idRoadmapFile
                                                                        },
                                                                        data: fileParam
                                                                });
                                                        } else {
                                                                let saveFile = await prisma.tb_news_file.create({
                                                                        data: fileParam
                                                                });

                                                                kajianFileIds.push(saveFile.id_news_file);
                                                        }
                                                }
                                        }

                                        let kajianFileIdsTr = [];

                                        for (let x in request.input('id_news_file_tr')) {
                                                let idRoadmapFile = parseInt(request.input('id_news_file_tr')[x]);
                                                kajianFileIdsTr.push(idRoadmapFile);
                                        }

                                        for (let n in request.files('nama_file_tr')) {
                                                let fileParam : any = {};
                                                let file = request.files('nama_file_tr')[n];

                                                const fieldName = file.fieldName; // Mendapatkan nama field, contoh: "nama_file[2]"
                                                const match = fieldName.match(/\[(\d+)\]/); // Ekstrak indeks dari nama field
                                                const index = match ? parseInt(match[1]) : 0; // Jika ada indeks, gunakan; jika tidak, gunakan 0
                                                let idRoadmapFile = 0;

                                                const element = Array.isArray(request.input('id_news_file_tr'))
                                                              ? request.input('id_news_file_tr')[index]
                                                              : undefined;
                                                if (element) {
                                                  idRoadmapFile = parseInt(request.input('id_news_file_tr')[index]);
                                                }

                                                if (file) {
                                                        let uploadFileToServer = await uploadFile(file, model, undefined);
                                                        let filenameFromServer = '';
                                                        if (uploadFileToServer) {
                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                        }
                                                        let filename = filenameFromServer; // Set fileName if file exists
                                                        fileParam['nama'] = filename;
                                                        fileParam['tipe'] = 3;
                                                        fileParam['id_news'] = parseInt(paramID);
                                                        fileParam['judul'] = "";

                                                        if (idRoadmapFile) {
                                                                await prisma.tb_news_file_tr.update({
                                                                        where: {
                                                                                id_news_file_tr: idRoadmapFile
                                                                        },
                                                                        data: fileParam
                                                                });
                                                        } else {
                                                                let saveFile = await prisma.tb_news_file_tr.create({
                                                                        data: fileParam
                                                                });

                                                                kajianFileIdsTr.push(saveFile.id_news_file_tr);
                                                        }
                                                }
                                        }

                                        await prisma.tb_news_file.deleteMany({
                                                where: {
                                                        id_news: parseInt(paramID),
                                                        id_news_file: {
                                                                notIn: kajianFileIds
                                                        }
                                                }
                                        });

                                        await prisma.tb_news_file_tr.deleteMany({
                                                where: {
                                                        id_news: parseInt(paramID),
                                                        id_news_file_tr: {
                                                                notIn: kajianFileIdsTr
                                                        }
                                                }
                                        });

                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update
                                        };
                                }
                        });

                        return response.json(result);
                } catch (error) {
                        console.log(error);
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error.message,
                        });
                }
        }

        async destroyKajian({ params,response }: HttpContext) {
          const Id = parseInt(params.id, 10)
          if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
          }

          try {
            const deletePost = await prisma.tb_news.delete({
              where: {
                id: Id,
              },
            })
            return response.status(200).send(deletePost)
          } catch (error) {
            return response.status(500).send({ error: 'Error deleting data' })
          }
        }

        public async toggleStatusKajian({ request, params, response }: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }

                try {
                        const toggleStatus = await prisma.tb_news.update({
                                where: {
                                        id: Id,
                                },
                                data: {
                                        status: request.input('status') ?? 0
                                },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error Toggle Status' })
                }
        }
  /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',') {
      searchCondition = {
        OR: [
          { judul: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
          { deskripsi: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
          {
            tb_app_slider_tr: {
              some: { judul: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider_tr.judul
            },
          },
          {
            tb_app_slider_tr: {
              some: { deskripsi: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider_tr.deskripsi
            },
          },
        ],
      };
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_app_slider.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const sliders = await prisma.tb_app_slider.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      include: {
        tb_app_slider_tr: true,
      },
    });

    // Flatten the data for inline translation
    const data = sliders.flatMap((slider) => {
      return slider.tb_app_slider_tr.map((translation) => {
        const sliderFields = Object.keys(slider).reduce((acc, key) => {
          // Skip the relational field 'tb_app_slider_tr'
          if (key !== 'tb_app_slider_tr') {
            acc[key] = slider[key];
          }
          return acc;
        }, {});

        const translationFields = Object.keys(translation).reduce((acc, key) => {
          acc[`tr_${key}`] = translation[key];
          return acc;
        }, {});

        // Combine fields from both tables
        return {
          ...sliderFields,
          ...translationFields,
        };
      });
    });

    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {

  }

  /**
     * @store
     * @paramQuery judul - Judul Slider - @type(string)
     * @paramQuery deskripsi - Deskripsi Slider - @type(string)
     * @paramQuery url_link - Url - @type(string)
     * @paramQuery ordering - Urutan - @type(number)
     * @paramQuery judul_tr - Judul Slider - @type(string)
     * @paramQuery deskripsi_tr - Deskripsi Slider - @type(string)
     * @paramQuery ordering_tr - Urutan - @type(number)
     * @paramQuery image - image - @type(file)
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createNewsValidator)
        try {
            const configImg = await configUpload('img')
            const image = request.files('cover',configImg);
            const dokumen = request.files('dokumen',configImg);

           const dataPost = {
                judul:data.judul,
                deskripsi:data.deskripsi,
                deskripsi_singkat:data.deskripsi_singkat,
                jenis:1,
                status:99,
            }
           const dataPostTr = {
                judul:data.judul_tr,
                deskripsi:data.deskripsi_tr,
                deskripsi_singkat:data.deskripsi_singkat_tr,
                kd_bahasa:"en",
              }
              const insert = await prisma.tb_news.create({data:dataPost})
              let cover_name
              if (image) {
                const uploadPromisess = image.map(async (item) => {
                  const uploadPath = `uploads/berita/${insert.id}/`;
                  const upImg = await img_to_webp(image[0], uploadPath);
                  cover_name = upImg.data.filename
                });
                await Promise.all(uploadPromisess);

              }
          const up = await prisma.tb_news.update({
            where:{
              id:insert.id,
            },
            data:{
              file_cover: cover_name
            }
          })
          console.log(insert.id);


           dataPostTr.id_news = insert.id
           const insertTr = await prisma.tb_news_tr.create({data:dataPostTr})

           const insertNews = await prisma.tb_peluang_kabkot_berita_mapping.create({data:{
            id_berita : insert.id,
            id_peluang_kabkot : data.id_peluang
          }})

          if (dokumen) {
                const uploadPromisess = image.map(async (item) => {
                    const uploadPath = `uploads/berita/${insert.id}/`;
                    const upImg = await img_to_webp(item, uploadPath);


                    const insertNews = await prisma.tb_news_file.create({data:{
                      id_news : insert.id,
                      judul : data.judul,
                      nama : upImg.data.filename,
                      tipe : 3,
                    } })
                  });
                await Promise.all(uploadPromisess);
          }

            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_news.findUnique({
          where: {
            id: Id,
          },
          include: {
            files: true,
            files_tr:true,
            tb_adm_kabkot:{
              include: {
                tb_adm_provinsi: true
              }
            },
            tb_komoditi_nasional_ref: true,
            tb_news_kategori: true,
            translations:true
          },
        })

        data['file_cover_path'] = `${process.env.APP_URL}/uploads/kajian/cover/${data['file_cover']}`;
        data['files'] = data['files'].map((item) => {
                return {
                        ...item, // Spread item untuk mempertahankan properti yang ada
                        path: `${process.env.APP_URL}/uploads/kajian/${item.tipe}/${item.nama}`,
                };
        });

        return {
            success : true,
            data : {data}
        }
  }

  /**
   * Edit individual record
   */
  async edit({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_app_slider.findUnique({
          where: {
            id_app_slider: Id,
          },
          include: {
            tb_app_slider_tr: true,
          },
        })

        return {
            success : true,
            data
        }
  }

  /**
     * @update
     * @paramQuery judul - Judul Slider - @type(string)
     * @paramQuery deskripsi - Deskripsi Slider - @type(string)
     * @paramQuery url_link - Url - @type(string)
     * @paramQuery ordering - Urutan - @type(number)
     * @paramQuery judul_tr - Judul Slider - @type(string)
     * @paramQuery deskripsi_tr - Deskripsi Slider - @type(string)
     * @paramQuery ordering_tr - Urutan - @type(number)
     * @paramQuery id_app_slider_tr - Id App Slider - @type(number)
     * @paramQuery image - image - @type(file)
     *
     */
  async update({ params, request ,response}: HttpContext) {

      const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }

      const data = await request.validateUsing(createAppSLiderValidator)
        try {
            const configImg = await configUpload('img')
            const image = request.files('image',configImg);
            let dataPost = {}
            let dataPostTr = {}
            dataPost = {
                      judul : data.judul,
                      deskripsi :data.deskripsi,
                      ordering:data.ordering,
                      url_link : data.url_link
                    }
            dataPostTr = {
                        kd_bahasa : "en",
                        judul:data.judul_tr,
                        deskripsi:data.deskripsi_tr,
                        ordering:data.ordering_tr,
                    }
              if (image) {
                const uploadPromisess = image.map(async (item) => {

                    const uploadPath = `uploads/slider`;
                    const upImg = await img_to_webp(image[0], uploadPath);
                        dataPost.nama_file_image = upImg.data.filename
                        dataPostTr.nama_file_image = upImg.data.filename

                });
                await Promise.all(uploadPromisess);
              }
            const update = await prisma.tb_app_slider.update({
              data:dataPost,
              where:{
                id_app_slider:Id
              }
            })
            const tr = await prisma.tb_app_slider_tr.findFirst({
              where:{
                id_app_slider:Id
              },
              select :{
                id_app_slider_tr:true
              }
            })

            const updateTr = await prisma.tb_app_slider_tr.update({
              data:dataPostTr,
              where:{
                id_app_slider_tr:tr?.id_app_slider_tr
              }
            })
            response.status(201).json({ success: true,data:update })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_app_slider.delete({
        where: {
          id_app_slider: Id,
        },
      })
      return response.status(200).send(deletePost)
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }



}