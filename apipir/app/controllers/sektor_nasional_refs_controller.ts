import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'


export default class SektorNasionalRefsController {
   /**
     * @index
     * @summary List Data Referensi Sektor Unggulan (Dropdown Sektor Unggulan Nasional)
     */
  public async index({ response }:HttpContext) {
    try {
      const data = await prisma.tb_sektor_nasional_ref.findMany({})
      return { 
          success : true,
          data : {data}
      }
    } catch (error) {
      console.error(error);
      return response.status(500).json({
        status: 'error',
        message: 'Error fetching data',
      });
    }
  }
}