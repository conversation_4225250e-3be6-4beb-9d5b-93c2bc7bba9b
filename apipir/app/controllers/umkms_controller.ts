import { createUmkmValidator, updateUmkmValidator } from '#validators/umkm'
import { createUmrProvValidator } from '#validators/umr'
import type { HttpContext } from '@adonisjs/core/http'
import { configUpload, img_to_webp } from '../helpers/global_helper.js'
import env from '#start/env'
import { getPeluangValidator } from '#validators/peluang_investasi'
import prisma from '../lib/prisma.js'
export default class UmkmsController {

  async index({}: HttpContext) {
    const data = await prisma.tb_umkm.findMany({})
    return {
        success : true,
        data : {data}
    }
  }
   /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = params.search || ''; // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          { nama: { contains: search, mode: 'insensitive' } }, // Search in tb_umkm.judul
          { nama_kabkot: { contains: search, mode: 'insensitive' } }, // Search in tb_umkm.deskripsi
          { nama_provinsi: { contains: search, mode: 'insensitive' } }, // Search in tb_umkm.deskripsi
          { bidang_usaha: { contains: search, mode: 'insensitive' } }, // Search in tb_umkm.deskripsi
          { email: { contains: search, mode: 'insensitive' } }, // Search in tb_umkm.deskripsi
                 ],
      };
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_umkm.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_umkm.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty

    });
    const data = datas.map((item =>{
        return {
          ...item,
          nama_file_image : `${env.get('APP_URL')}/uploads/ikn/${item.image}`,
        }
    }))


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
     * @store
     * @summary Insert UMR Provinsi
     * @paramQuery id_adm_provinsi - id_adm_provinsi - @type(number) @required
     * @paramQuery tahun - tahun - @type(number) @required
     * @paramQuery id_sumber_data - id_sumber_data - @type(number)
     * @paramQuery nilai - nilai - @type(number) @required
     * @paramQuery status - status - @type(number)
  */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createUmkmValidator)
        try {
            const configImg = await configUpload('img');
            const image = request.files('image', configImg);
            if (image) {
                const uploadPromisesUpImg = image.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    data.image = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }
            const insert = await prisma.tb_umkm.create({data:data})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
    const data = await prisma.tb_umkm.findUnique({
      where: {
        id_umkm: Id,
      },
    })
// Pengecekan manual apakah `sektor_nasional_file` ada
    if (data && data.image ) {
      data.image = `${env.get('APP_URL')}/uploads/ikn/${data.image}`;
    }

    return {
      success : true,
      data : data
    }
  }


  async edit({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    const data = await prisma.tb_umkm.findUnique({
      where: {
        id_umkm: Id,
      },
    })

    return {
      success : true,
      data : {data}
    }
  }

  /**
     * @update
     * @paramQuery id_adm_provinsi - id_adm_provinsi - @type(number) @required
     * @paramQuery tahun - tahun - @type(number) @required
     * @paramQuery id_sumber_data - id_sumber_data - @type(number)
     * @paramQuery nilai - nilai - @type(number) @required
     */
  async update({ params, request ,response}: HttpContext) {


    const Id = parseInt(params.id, 10)

    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }
    try {
      const dataPost = await request.validateUsing(updateUmkmValidator)
      const configImg = await configUpload('img');
            const image = request.files('image', configImg);
            if (image) {
                const uploadPromisesUpImg = image.map(async (item) => {
                    const uploadPath = `uploads/ikn/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    dataPost.image = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }
      const updatePost = await prisma.tb_umkm.update({
        where: {
          id_umkm: Id,
        },
          data:dataPost
        ,
      })
      return response.status(200).json({ success: true, data: updatePost })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data' })
    }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_umkm.delete({
        where: {
          id_umkm: Id,
        },
      })
      return response.status(200).send({success:true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }

    async list_sektor_umkm({request, response}: HttpContext) {
        const datas = await prisma.tb_umkm.findMany({
          include: {
            tb_peluang_sektor: true
          },
          where:{
            id_peluang_sektor :{
              not:null
            },
            id_umkm_jenis:0
          }
        });

        const data = datas
          .map((item) => ({
            id_sektor: item.id_peluang_sektor,
            id_peluang_kabkot: item.id_peluang_kabkot,
            nama_sektor: item.tb_peluang_sektor?.nama,
            image: item.tb_peluang_sektor?.image ? `${env.get("APP_URL")}/uploads/${item.tb_peluang_sektor?.image?.replace('upload/', '')}` : `${env.get("APP_URL")}/uploads/no_image.png`,
          }))
          .reduce((acc, current) => {
            // Mencari jika sektor sudah ada di accumulator
            const existingSector = acc.find((item) => item.nama_sektor === current.nama_sektor);

            if (existingSector) {
              // Jika sektor sudah ada, tambahkan jumlah item
              existingSector.jumlah += 1;
            } else {
              // Jika sektor belum ada, tambahkan objek baru ke accumulator
              acc.push({
                id_sektor: current.id_sektor,
                nama_sektor: current.nama_sektor,
                jumlah: 1, // Set jumlah awal sebagai 1
                image: current.image, // Simpan hanya satu image
              });
            }

            return acc;
          }, []);


        return {
                success:true,
                data
              }
    }

    async list_sektor_umk({request, response}: HttpContext) {
        const datas = await prisma.tb_umkm.findMany({
          include: {
            tb_peluang_sektor: true
          },
          where:{
            id_peluang_sektor :{
              not:null
            },
            id_umkm_jenis:1
          }
        });

         const data = datas
          .map((item) => ({
            id_sektor: item.id_peluang_sektor,
            id_peluang_kabkot: item.id_peluang_kabkot,
            nama_sektor: item.tb_peluang_sektor?.nama,
            image: item.tb_peluang_sektor?.image ? `${env.get("APP_URL")}/uploads/${item.tb_peluang_sektor?.image?.replace('upload/', '')}` : `${env.get("APP_URL")}/uploads/no_image.png`,
          }))
          .reduce((acc, current) => {
            // Mencari jika sektor sudah ada di accumulator
            const existingSector = acc.find((item) => item.nama_sektor === current.nama_sektor);

            if (existingSector) {
              // Jika sektor sudah ada, tambahkan jumlah item
              existingSector.jumlah += 1;
            } else {
              // Jika sektor belum ada, tambahkan objek baru ke accumulator
              acc.push({
                id_sektor: current.id_sektor,
                nama_sektor: current.nama_sektor,
                jumlah: 1, // Set jumlah awal sebagai 1
                image: current.image, // Simpan hanya satu image
              });
            }

            return acc;
          }, []);

        return {
                success:true,
                data
              }
    }


  /**
     * @list_umkm
     * @paramQuery page - Kalaman ke - @type(number)
     * @paramQuery page_size - data per halaman  - @type(number)
     * @paramQuery search - ketik text pencarian - @type(string)
     * @paramQuery id_adm_provinsi - Masukan Id Provinsi - @type(number)
     * @paramQuery id_adm_kabkot - Masukan Id Kabkot - @type(number)
     * @paramQuery id_sektor - Masukan Id Sektor - @type(number)
     * @paramQuery tahun - Masukan Tahun - @type(number)
     * @paramQuery sortir - Masukan Urutan (contoh "id_sektor - desc") - @type(String)
     */
    async list_umkm({request, response}: HttpContext) {
    const dataPost = await request.validateUsing(getPeluangValidator);

    const whereConditions = {
      id_umkm_jenis:0,
      id_peluang_sektor: {
        not:null
      }
    };

    if (dataPost.id_sektor !== undefined) {
        whereConditions.id_peluang_sektor = dataPost.id_sektor
    }


    if (dataPost.id_adm_provinsi !== undefined) {
        const id_adm_prov = String(dataPost.id_adm_provinsi).slice(0, 2);
        const id_adm_prov_range_start = parseInt(`${id_adm_prov}00`, 10); // e.g., 6300
        const id_adm_prov_range_end = parseInt(`${id_adm_prov}99`, 10); // e.g., 6399

        whereConditions.id_adm_kabkot = {
            gte: id_adm_prov_range_start,
            lt: id_adm_prov_range_end + 1 // Add 1 to include the upper limit
        };
    }
    if (dataPost.id_adm_kabkot !== undefined) {
      whereConditions.id_adm_kabkot = dataPost.id_adm_kabkot
    }


    // Optionally add a search condition if `search` is defined
    if (dataPost.search) {
        whereConditions.OR = [
            { nama: { contains: dataPost.search, mode: 'insensitive' } },
            { bidang_usaha: { contains: dataPost.search, mode: 'insensitive' } },
            { alamat: { contains: dataPost.search, mode: 'insensitive' } },
        ];
    }
    let orderByConditions = [];
    if (dataPost.sortir) {
        const sortOrder = dataPost.sortir.split(' - ');
        const column = sortOrder[0]; // Either "judul" or "tahun"
        const direction = sortOrder[1] === 'asc' ? 'asc' : 'desc'; // Default to 'desc' if not 'asc'

        // Only add valid columns to orderBy
            orderByConditions.push({ [column]: direction });

    }
    // Pagination
    const page = dataPost.page || 1; // Default to page 1 if not provided
    const pageSize = dataPost.page_size || 9; // Default to 10 items per page if not provided
    const skip = (page - 1) * pageSize;
    const take = pageSize;
    // Query with pagination
    const datas = await prisma.tb_umkm.findMany({
        where: whereConditions,
        include: {
            tb_peluang_kabkot: true,
            tb_adm_kabkot: true,
            tb_adm_provinsi: true,
        },
        orderBy: orderByConditions.length > 0 ? orderByConditions : undefined,
        skip, // Skip the number of items based on current page
        take, // Limit the number of items per page
    });

    // Get total count for pagination metadata
    const totalRecords = await prisma.tb_umkm.count({
        where: whereConditions,
    });
    // return datas


    // Format data
    const data = await Promise.all(datas.map(async ({tb_adm_provinsi, tb_adm_kabkot, ...data}) => ({
        // id_pelluang_kabkot : data.id_peluang_kabkot,
        id_sektor: data.id_peluang_sektor,
        judul: data.nama,
        alamat : data.alamat,
        bidang_usaha : data.bidang_usaha,
        image : `${env.get('APP_URL')}/uploads/umkm/${data.image}`,

    })));

    // Pagination response
        return {
            success: true,
            page: page,
            pageSize: pageSize,
            totalRecords: totalRecords,
            totalPage: Math.ceil(totalRecords / pageSize),
            data: { data },

        };
    }
    /**
     * @list_umk
     * @paramQuery page - Kalaman ke - @type(number)
     * @paramQuery page_size - data per halaman  - @type(number)
     * @paramQuery search - ketik text pencarian - @type(string)
     * @paramQuery id_adm_provinsi - Masukan Id Provinsi - @type(number)
     * @paramQuery id_adm_kabkot - Masukan Id Kabkot - @type(number)
     * @paramQuery id_sektor - Masukan Id Sektor - @type(number)
     * @paramQuery tahun - Masukan Tahun - @type(number)
     * @paramQuery sortir - Masukan Urutan (contoh "id_sektor - desc") - @type(String)
     */
    async list_umk({request, response}: HttpContext) {
    const dataPost = await request.validateUsing(getPeluangValidator);

    const whereConditions = {
      id_umkm_jenis:1,
      id_peluang_sektor: {
        not:null
      }
    };

    if (dataPost.id_sektor !== undefined) {
        whereConditions.id_peluang_sektor = dataPost.id_sektor
    }


    if (dataPost.id_adm_provinsi !== undefined) {
        const id_adm_prov = String(dataPost.id_adm_provinsi).slice(0, 2);
        const id_adm_prov_range_start = parseInt(`${id_adm_prov}00`, 10); // e.g., 6300
        const id_adm_prov_range_end = parseInt(`${id_adm_prov}99`, 10); // e.g., 6399

        whereConditions.id_adm_kabkot = {
            gte: id_adm_prov_range_start,
            lt: id_adm_prov_range_end + 1 // Add 1 to include the upper limit
        };
    }
    if (dataPost.id_adm_kabkot !== undefined) {
      whereConditions.id_adm_kabkot = dataPost.id_adm_kabkot
    }


    // Optionally add a search condition if `search` is defined
    if (dataPost.search) {
        whereConditions.OR = [
            { nama: { contains: dataPost.search, mode: 'insensitive' } },
            { bidang_usaha: { contains: dataPost.search, mode: 'insensitive' } },
            { alamat: { contains: dataPost.search, mode: 'insensitive' } },
        ];
    }
    let orderByConditions = [];
    if (dataPost.sortir) {
        const sortOrder = dataPost.sortir.split(' - ');
        const column = sortOrder[0]; // Either "judul" or "tahun"
        const direction = sortOrder[1] === 'asc' ? 'asc' : 'desc'; // Default to 'desc' if not 'asc'

        // Only add valid columns to orderBy
            orderByConditions.push({ [column]: direction });

    }
    // Pagination
    const page = dataPost.page || 1; // Default to page 1 if not provided
    const pageSize = dataPost.page_size || 9; // Default to 10 items per page if not provided
    const skip = (page - 1) * pageSize;
    const take = pageSize;
    // Query with pagination
    const datas = await prisma.tb_umkm.findMany({
        where: whereConditions,
        include: {
            tb_peluang_kabkot: true,
            tb_adm_kabkot: true,
            tb_adm_provinsi: true,
        },
        orderBy: orderByConditions.length > 0 ? orderByConditions : undefined,
        skip, // Skip the number of items based on current page
        take, // Limit the number of items per page
    });

    // Get total count for pagination metadata
    const totalRecords = await prisma.tb_umkm.count({
        where: whereConditions,
    });
    // return datas


    // Format data
    const data = await Promise.all(datas.map(async ({tb_adm_provinsi, tb_adm_kabkot, ...data}) => ({
        // id_pelluang_kabkot : data.id_peluang_kabkot,
        id_sektor: data.id_peluang_sektor,
        judul: data.nama,
        alamat : data.alamat,
        bidang_usaha : data.bidang_usaha,
        image : `${env.get('APP_URL')}/uploads/umkm/${data.image}`,

    })));

    // Pagination response
        return {
            success: true,
            page: page,
            pageSize: pageSize,
            totalRecords: totalRecords,
            totalPage: Math.ceil(totalRecords / pageSize),
            data: { data },

        };
    }
}



