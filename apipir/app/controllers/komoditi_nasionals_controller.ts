import type { HttpContext } from '@adonisjs/core/http'

import { configUpload, get_paginate, img_to_webp, upload, upload_img_to_webp } from '../helpers/global_helper.js';
import { createPeluangValidator } from '#validators/peluang';
import { createSektorNasionalValidator } from '#validators/sektor_nasional';
import { createSubSektorNasionalValidator, updateSubSektorNasionalValidator } from '#validators/sub_sektor_nasional';
import { aproveValidator } from '#validators/aprove';
import env from '#start/env';
import { createKomoditiNasionalValidator } from '#validators/komoditi';
import prisma from '../lib/prisma.js';
export default class SubSektorNasionalsController {

    /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.tb_sub_sektor_nasional.findMany({
        take: 100,
        include: {
            sub_sektor_nasional_value: {
                include: {
                    sub_sektor_nasional_value_detail: true,
                    sub_sektor_nasional_value_tr: true
                }
            }
        }
    });

    return {
        success : true,
        data : {data}
    }
  }

  async get_paginate({params,response,request}:HttpContext) {
    const parameters =  request.qs()
    let order = parameters.order
    const by = ['asc','desc'].includes(parameters.by) ? parameters.by : 'asc'
    let orderBy={}
    const paramList = ['status','nama_sektor','nama_sub_sektor','nama_komoditi']
    if (order != undefined && paramList.includes(order)) {
        if (order == 'nama_sektor') {
            orderBy = {sub_sektor:{sektor:{nama:by}}}
        }else if (order == 'nama_sub_sektor') {
            orderBy = {sub_sektor:{sub_sektor_ref:{nama:by}}}
        }else if (order == 'nama_komoditi') {
            orderBy = {komoditi_nasional_ref:{nama:by}}
        }else{
            orderBy = {[order]:by}
        }
    }
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
            OR: [
                {
                komoditi_nasional_ref: {
                    nama: { contains: search, mode: 'insensitive' }, // Search in komoditi_nasional_ref.nama
                },
                },
                {
                sub_sektor: {
                    sub_sektor_ref: {
                    nama: { contains: search, mode: 'insensitive' }, // Search in sub_sektor_ref.nama
                    },
                },
                },
                {
                sub_sektor: {
                    sektor: {
                    nama: { contains: search, mode: 'insensitive' }, // Search in sektor.nama
                    },
                },
                },
            ],
            };

    }

    const req = request.qs()
    if (req.status) {
        (searchCondition.AND ??= []).push(
          { status: parseInt(req.status) }
  
        );
    }
    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_komoditi_nasional.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_komoditi_nasional.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      orderBy:orderBy,
      include: {
            sub_sektor:{
                include: {
                    sub_sektor_ref:true,
                    sektor: true
                }
            },
            komoditi_nasional_ref:true
        },
    });
    const data = datas.map(({...rest }) => {
        return {
            id_komoditi_nasional : rest.id_komoditi_nasional,
            status : rest.status,
            nama_sektor: rest.sub_sektor.sektor.nama,
            nama_sub_sektor: rest.sub_sektor.sub_sektor_ref.nama,
            nama_komoditi: rest.komoditi_nasional_ref.nama,
        };
    });


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };
  }

    /**
     * @store
     * @paramQuery id_sektor_nasional - id_sektor_nasional - @type(number) @required
     * @paramQuery id_sub_sektor - id_sub_sektor - @type(number) @required
     * @paramQuery deskripsi - id_sektor - @type(string)
     * @paramQuery deskripsi_singkat - tahun - @type(string)
     * @paramQuery potensi_pasar - id_sumber_data - @type(string)
     * @paramQuery cover - gambar cover - @type(file)
     * @paramQuery icon - gambar icon - @type(file)
     * @paramQuery id_sumber_data - id_prioritas - @type(number)
     * @paramQuery parameter_data - id_prioritas contoh - @type(JSON)
     * @paramQuery kontak - id_prioritas - @type(JSON)
     * @paramQuery diagram - gambar diagram - @type(file)
     * @paramQuery diagram_detail - id_prioritas - @type(JSON)
     */

    public async store({ request, response ,auth}: HttpContext) {

      const data = request.all();
      let datas = await request.validateUsing(createKomoditiNasionalValidator)
      let parameterData = request.input('parameter_data')
      let diagramDetail = request.input('diagram_detail')
      let diagramDetailTr = request.input('diagram_detail_tr')

      try {
          const configImg = await configUpload('img');

          let icon_name = String(null);

            const icon = request.files('icon', configImg);
            const icon_tr = request.files('icon_tr', configImg);
            if (icon) {
                const uploadPromisesUpImg = icon.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    icon_name = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }

            const sub_sektor_nasional = await prisma.tb_sub_sektor_nasional.findFirst({
                where:{
                    id_sub_sektor: parseInt(datas.id_sub_sektor_nasional)
                }
            })
            const komoditiRef = await prisma.tb_komoditi_nasional_ref.findUnique({
                where: { id_komoditi: datas.id_komoditi },
                });

            if (!komoditiRef) {
                    throw new Error('ID Komoditi tidak ditemukan di tb_komoditi_nasional_ref');
            }

            const dataPost = {
                    id_sub_sektor_nasional : sub_sektor_nasional?.id_sub_sektor_nasional,
                    id_komoditi : datas.id_komoditi,
                    deskripsi:datas.deskripsi ? datas.deskripsi    : '-',
                    deskripsi_singkat : datas.deskripsi_singkat? datas.deskripsi_singkat : '-',
                    status : 0,
                    file_icon : icon_name
                }

            const diagram = request.files('diagram', configImg);
            const diagramTr = request.files('diagram_tr', configImg);
            // Jalankan semua operasi database dalam transaction Prisma
            await prisma.$transaction(async (prisma) => {
                const insert = await prisma.tb_komoditi_nasional.create({ data:dataPost });
                if (icon_tr) {
                    const uploadPromisesUpImg = icon_tr.map(async (item) => {
                        const uploadPath = `uploads/sektor/`;
                        const upImg = await img_to_webp(item, uploadPath);
                        const insertTr = await prisma.tb_komoditi_nasional_tr.create({
                            data: {
                                id_komoditi_nasional:insert.id_komoditi_nasional,
                                kd_bahasa :'en',
                                deskripsi:'-',
                                deskripsi_singkat:'-',
                                file_icon:upImg.data.filename
                            }
                         });
                });
                await Promise.all(uploadPromisesUpImg);
            }
                if (typeof parameterData === 'string') {
                    parameterData = JSON.parse(parameterData);
                }

                for (const el of parameterData) {
                    const value = {
                        id_komoditi_nasional: insert.id_komoditi_nasional,
                        nama: el.nama,
                        tipe: 1,
                        satuan: el.satuan,
                    };


                    const insertValue = await prisma.tb_komoditi_nasional_value.create({ data: value });
                    const value_tr = {
                        id_komoditi_nasional_value: insertValue?.id_komoditi_nasional_value,
                        nama: el.nama_tr,
                        satuan: el.satuan_tr,
                        kd_bahasa:"en"
                    };
                    await prisma.tb_komoditi_nasional_value_tr.create({ data: value_tr });

                    for (const el2 of el.detail) {
                        const value_detail = {
                            id_komoditi_nasional_value: insertValue?.id_komoditi_nasional_value,
                            tahun: parseInt(el2.tahun),
                            numeric_value: parseFloat(el2.numeric_value),
                        };

                        await prisma.tb_komoditi_nasional_value_detail.create({ data: value_detail });
                    }
                }

                if (typeof diagramDetail === 'string') {
                    diagramDetail = JSON.parse(diagramDetail);
                }
                if (diagram && Array.isArray(diagramDetail)) {
                    // Jika panjang array JSON dan file gambar tidak sesuai, kembalikan error
                    if (diagram.length !== diagramDetail.length) {
                        return response.badRequest('Jumlah gambar dan detail tidak sesuai');
                    }
                    if (typeof diagramDetailTr === 'string') {
                        diagramDetailTr = JSON.parse(diagramDetailTr);
                    }
                    const uploadPromisesUpImg = diagram.map(async (item, index) => {
                        const uploadPath = `uploads/sektor/`;
                        const upImg = await img_to_webp(item, uploadPath); // Mengupload gambar ke WebP
                        const fileName = upImg.data.filename; // Mendapatkan nama file gambar

                        // Menghubungkan gambar yang diupload dengan detail dari JSON
                        const detail = diagramDetail[index]; // Mendapatkan detail JSON berdasarkan index

                        // Lakukan proses insert ke database atau logika apapun yang kamu inginkan
                        const insertDiagram = await prisma.tb_komoditi_nasional_file.create({
                            data: {
                                id_komoditi_nasional : insert.id_komoditi_nasional,
                                tipe : 1,
                                jenis : 1,
                                nama: fileName, // Nama dari JSON
                                judul: detail.judul, // Judul dari JSON
                                keterangan: detail.keterangan, // Nama file yang sudah diupload
                            },
                        });
                        if (diagramTr && Array.isArray(diagramDetailTr)) {
                        // Jika panjang array JSON dan file gambar tidak sesuai, kembalikan error
                            if (diagram.length !== diagramDetailTr.length) {
                                return response.badRequest('Jumlah gambar dan detail tidak sesuai');
                            }

                                const uploadPath = `uploads/sektor/`;
                                const upImg = await img_to_webp(item, uploadPath); // Mengupload gambar ke WebP
                                const fileName = upImg.data.filename; // Mendapatkan nama file gambar

                                // Menghubungkan gambar yang diupload dengan detail dari JSON
                                const detail = diagramDetailTr[index]; // Mendapatkan detail JSON berdasarkan index

                                // Lakukan proses insert ke database atau logika apapun yang kamu inginkan
                                await prisma.tb_komoditi_nasional_file_tr.create({
                                    data: {
                                        id_komoditi_nasional_file : insertDiagram.id_komoditi_nasional_file,
                                        kd_bahasa : 'en',
                                        nama: fileName, // Nama dari JSON
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Nama file yang sudah diupload
                                    },
                                });

                        }
                    });
                    await Promise.all(uploadPromisesUpImg); // Menjalankan semua upload dan insert secara paralel


                }

                response.status(201).json({ success: true,data:insert })
            });
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

    /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_komoditi_nasional.findUnique({
        where: {
            id_komoditi_nasional: Id,
        },
        include: {
            komoditi_nasional_file:{
                include:{
                    komoditi_nasional_file_tr:true
                }
            },
            komoditi_nasional_tr:true,
            komoditi_nasional_value:{
                include:{
                    komoditi_nasional_value_detail:true,
                    komoditi_nasional_value_tr:true
                }
            },
            sub_sektor:{
                include:{
                    sektor:true
                }
            }
        }
        });

        if (data) {
            data.file_icon = `${env.get('APP_URL')}/uploads/sektor/${data.file_icon}`
        }

        if (data?.komoditi_nasional_file) {
            data.komoditi_nasional_file = data.komoditi_nasional_file.map((item) => {

                return {
                    ...item,
                    nama : `${env.get('APP_URL')}/uploads/sektor/${item.nama}`,
                    komoditi_nasional_file_tr : item.komoditi_nasional_file_tr.map((item2) => {
                        return {
                            ...item2,
                            nama : `${env.get('APP_URL')}/uploads/sektor/${item2.nama}`,
                        }
                    }),
                }
            })
        }
        if (data?.komoditi_nasional_tr.length > 0) {
            data.komoditi_nasional_tr[0]['file_icon'] = `${env.get('APP_URL')}/uploads/sektor/${data.komoditi_nasional_tr[0]?.file_icon}`
        }


        return {
            success : true,
            data : data
        }
  }

  public async update({ params,request, response ,auth}: HttpContext) {

    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
      const data = request.all();
      let datas = await request.validateUsing(createKomoditiNasionalValidator)
      let parameterData = request.input('parameter_data')
      let parameterDataTr = request.input('parameter_data_tr')
      let diagramDetail = request.input('diagram_detail')
      let diagramDetailTr = request.input('diagram_detail_tr')
      let tr = request.input('tr')
        const configImg = await configUpload('img');
        const diagram = request.files('diagram', configImg);
        const diagramTr = request.files('diagram_tr', configImg);
        try {




            // return t
            // Jalankan semua operasi database dalam transaction Prisma


            await prisma.$transaction(async (prisma) => {
                // update pertama untuk tb_sub_sektor_nasional
                const sub_sektor_nasional = await prisma.tb_sub_sektor_nasional.findFirst({
                    where:{
                        id_sub_sektor: parseInt(data.id_sub_sektor_nasional)
                    }
                })
            const dataPost = {
                    id_sub_sektor_nasional : datas.id_sub_sektor_nasional,
                    id_komoditi : datas.id_komoditi,
                    deskripsi:datas.deskripsi ? datas.deskripsi    : '-',
                    deskripsi_singkat : datas.deskripsi_singkat? datas.deskripsi_singkat : '-',
                }
                const icon = request.files('icon', configImg);
                if (icon) {
                    const uploadPromisesUpImg = icon.map(async (item) => {
                        const uploadPath = `uploads/sektor/`;
                        const upImg = await img_to_webp(item, uploadPath);
                        dataPost['file_icon'] = upImg.data.filename;
                    });
                    await Promise.all(uploadPromisesUpImg);
                }
                const update = await prisma.tb_komoditi_nasional.update({where:{id_komoditi_nasional:Id}, data: dataPost });
                if (typeof tr === 'string') {
                    tr = JSON.parse(tr);
                }
                const dataPostTr = {
                    deskripsi: tr.deskripsi ? tr.deskripsi    : '-',
                    deskripsi_singkat : tr.deskripsi_singkat? tr.deskripsi_singkat : '-',
                }
                const iconTr = request.files('icon_tr', configImg);
                if (iconTr) {
                    let imgTr = ''
                    const uploadPromisesUpImg = iconTr.map(async (item) => {
                        const uploadPath = `uploads/sektor/`;
                        const upImg = await img_to_webp(item, uploadPath);
                        dataPostTr['file_icon'] = upImg.data.filename;
                        imgTr = upImg.data.filename;
                    });
                    await Promise.all(uploadPromisesUpImg);
                    if (tr.id_komoditi_nasional_tr != undefined) {

                        const updateTr = await prisma.tb_komoditi_nasional_tr.update({where:{id_komoditi_nasional_tr:parseInt(tr.id_komoditi_nasional_tr)}, data: dataPostTr });
                    }else{
                        const insertTr = await prisma.tb_komoditi_nasional_tr.create({
                            data: {
                                id_komoditi_nasional:Id,
                                kd_bahasa :'en',
                                deskripsi:'-',
                                deskripsi_singkat:'-',
                                file_icon:imgTr
                            }
                         });
                    }
                }


                if (typeof parameterData === 'string') {
                    parameterData = JSON.parse(parameterData);
                }

                if (typeof parameterDataTr === 'string') {
                    parameterDataTr = JSON.parse(parameterDataTr);
                }
                console.log(parameterDataTr);


                // Step 1: Ambil semua data terkait dari database
                const existingValues = await prisma.tb_komoditi_nasional_value.findMany({
                where: { id_komoditi_nasional: Id },
                select: { id_komoditi_nasional_value: true },
                });
                const existingValueTrs = await prisma.tb_komoditi_nasional_value_tr.findMany({
                where: {
                    id_komoditi_nasional_value: { in: existingValues.map(v => v.id_komoditi_nasional_value) }
                },
                select: { id_komoditi_nasional_value_tr: true },
                });

                const existingValueDetails = await prisma.tb_komoditi_nasional_value_detail.findMany({
                where: {
                    id_komoditi_nasional_value: { in: existingValues.map(v => v.id_komoditi_nasional_value) }
                },
                select: { id_komoditi_nasional_value_detail: true },
                });

                // Step 2: Ambil ID yang ada di parameterData
                const parameterDataIds = parameterData
                .map(el => parseInt(el.id_komoditi_nasional_value))
                .filter(id => id !== undefined); // Ambil hanya ID yang valid

                const parameterDataTrIds = parameterDataTr
                .map(el => parseInt(el.id_komoditi_nasional_value_tr))
                .filter(id => id !== undefined); // Ambil hanya ID yang valid

                const parameterDataDetailIds = parameterData.flatMap(el => el.detail)
                .map(detail => parseInt(detail.id_komoditi_nasional_value))
                .filter(id => id !== undefined); // Ambil hanya ID yang valid

                // Step 3: Cari data di database yang tidak ada di JSON dan hapus
                const idsToDeleteValues = existingValues
                .filter(value => !parameterDataIds.includes(value.id_komoditi_nasional_value))
                .map(value => value.id_komoditi_nasional_value);

                const idsToDeleteValueTrs = existingValueTrs
                .filter(valueTr => !parameterDataTrIds.includes(valueTr.id_komoditi_nasional_value_tr))
                .map(valueTr => valueTr.id_komoditi_nasional_value_tr);

                const idsToDeleteValueDetails = existingValueDetails
                .filter(detail => !parameterDataDetailIds.includes(detail.id_komoditi_nasional_value_detail))
                .map(detail => detail.id_komoditi_nasional_value_detail);

                if (idsToDeleteValues.length > 0) {
                await prisma.tb_komoditi_nasional_value.deleteMany({
                    where: { id_komoditi_nasional_value: { in: idsToDeleteValues } },
                });
                }

                if (idsToDeleteValueTrs.length > 0) {
                await prisma.tb_komoditi_nasional_value_tr.deleteMany({
                    where: { id_komoditi_nasional_value_tr: { in: idsToDeleteValueTrs } },
                });
                }

                if (idsToDeleteValueDetails.length > 0) {
                await prisma.tb_komoditi_nasional_value_detail.deleteMany({
                    where: { id_komoditi_nasional_value_detail: { in: idsToDeleteValueDetails } },
                });
                }

                // Step 4: Lanjutkan ke proses update/insert seperti biasa
                for (const el of parameterData) {
                const value = {
                    id_komoditi_nasional: Id,
                    nama: el.nama,
                    tipe: 1,
                    satuan: el.satuan,
                };

                let uiValue;
                let value_tr = {};

                if (el.id_komoditi_nasional_value) {
                    uiValue = await prisma.tb_komoditi_nasional_value.update({
                    where: { id_komoditi_nasional_value: parseInt(el.id_komoditi_nasional_value) },
                    data: value,
                    });
                    value_tr = {
                    id_komoditi_nasional_value: uiValue?.id_komoditi_nasional_value,
                    nama: el.nama,
                    satuan: el.satuan,
                    kd_bahasa: "en",
                    };
                } else {
                    uiValue = await prisma.tb_komoditi_nasional_value.create({ data: value });
                    value_tr = {
                    id_komoditi_nasional_value: uiValue?.id_komoditi_nasional_value,
                    nama: el.nama,
                    satuan: el.satuan,
                    kd_bahasa: "en",
                    };
                }

                for (const el of parameterDataTr) {
                    const value_tr = {
                        kd_bahasa: el.kd_bahasa || "en", // Default ke "en" jika tidak ada
                        nama: el.nama,
                        satuan: el.satuan,
                    };

                    let uiValueTr;

                    if (el.id_komoditi_nasional_value_tr) {
                        // Update jika id_komoditi_nasional_value_tr ada
                        uiValueTr = await prisma.tb_komoditi_nasional_value_tr.update({
                            where: { id_komoditi_nasional_value_tr: parseInt(el.id_komoditi_nasional_value_tr) },
                            data: value_tr,
                        });
                    } else {
                        // Create jika id_komoditi_nasional_value_tr tidak ada
                        uiValueTr = await prisma.tb_komoditi_nasional_value_tr.create({
                            data: value_tr,
                        });
                    }

                    console.log(`Proses selesai untuk ${uiValueTr.nama}`);
                }


                if (el.id_komoditi_nasional_value_tr) {
                    await prisma.tb_komoditi_nasional_value_tr.update({
                    where: { id_komoditi_nasional_value_tr: parseInt(el.id_komoditi_nasional_value_tr) },
                    data: value_tr,
                    });
                } else {
                }

                for (const el2 of el.detail) {
                    const value_detail = {
                    id_komoditi_nasional_value: uiValue?.id_komoditi_nasional_value,
                    tahun: parseInt(el2.tahun),
                    numeric_value: parseFloat(el2.numeric_value),
                    };

                    if (el2.id_komoditi_nasional_value) {
                    await prisma.tb_komoditi_nasional_value_detail.update({
                        where: { id_komoditi_nasional_value_detail: parseInt(el2.id_komoditi_nasional_value_detail) },
                        data: value_detail,
                    });
                    } else {
                    await prisma.tb_komoditi_nasional_value_detail.create({ data: value_detail });
                    }
                }
                }

                let idDiagram = []
                if (typeof diagramDetail === 'string') {
                    diagramDetail = JSON.parse(diagramDetail);
                }
                if (diagram && Array.isArray(diagramDetail)) {
                    const existingFiles = await prisma.tb_komoditi_nasional_file.findMany({
                    where: { id_komoditi_nasional: Id },
                    select: { id_komoditi_nasional_file: true },
                    });

                    // Step 2: Ambil ID yang ada di diagramDetail
                    const diagramDetailIds = diagramDetail
                    .map(detail => parseInt(detail.id_komoditi_nasional_file))
                    .filter(id => id !== undefined); // Pastikan hanya mengambil ID yang ada

                    // Step 3: Cari ID di database yang tidak ada di JSON
                    const idsToDelete = existingFiles
                    .filter(file => !diagramDetailIds.includes(file.id_komoditi_nasional_file))
                    .map(file => file.id_komoditi_nasional_file);
                    console.log(idsToDelete);

                    // Step 4: Hapus entri di database yang tidak ada di JSON
                    if (idsToDelete.length > 0) {
                        await prisma.tb_komoditi_nasional_file.deleteMany({
                            where: {
                            id_komoditi_nasional_file: { in: idsToDelete },
                            },
                        });
                    }
                    const uploadPromisesUpImg = diagramDetail.map(async (detail) => {
                        // Cari padanan gambar di diagram berdasarkan nama file yang cocok
                        const item = diagram.find(diagramItem => diagramItem.clientName === detail.fileName);

                        let fileName = detail.fileName; // Default value if no matching image is found

                        // Jika ada gambar yang cocok di diagram, upload dan ambil nama file baru
                        if (item) {
                            const uploadPath = `uploads/sektor/`;
                            const upImg = await img_to_webp(item, uploadPath); // Mengupload gambar ke WebP
                            fileName = upImg.data.filename; // Mendapatkan nama file gambar yang diupload
                            if (detail.id_komoditi_nasional_file) {
                                // Jika `id_sektor_nasional_file` ada, lakukan update
                                await prisma.tb_komoditi_nasional_file.update({
                                    where: {
                                        id_komoditi_nasional_file: parseInt(detail.id_komoditi_nasional_file),
                                    },
                                    data: {
                                        id_komoditi_nasional: Id,
                                        tipe: 1,
                                        jenis: 2,
                                        nama: fileName, // Nama file baru jika ada upload, atau dari detail jika tidak ada
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                            } else {
                                // Jika `id_sektor_nasional_file` tidak ada, lakukan insert
                                console.log('masuk sini');

                                const insert = await prisma.tb_komoditi_nasional_file.create({
                                    data: {
                                        id_komoditi_nasional: Id,
                                        tipe: 1,
                                        jenis: 2,
                                        nama: fileName, // Nama file baru atau dari detail
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                                idDiagram.push(insert.id_komoditi_nasional_file);

                            }
                        }else{
                            if (detail.id_komoditi_nasional_file) {
                                // Jika `id_sektor_nasional_file` ada, lakukan update
                                await prisma.tb_komoditi_nasional_file.update({
                                    where: {
                                        id_komoditi_nasional_file: parseInt(detail.id_komoditi_nasional_file),
                                    },
                                    data: {
                                        id_komoditi_nasional: Id,
                                        tipe: 1,
                                        jenis: 2,
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                            } else {
                                return response.status(500).json({ success: false, message: 'File diagram Required!' })
                            }
                        }


                    });


                    await Promise.all(uploadPromisesUpImg); // Menjalankan semua upload dan insert secara paralel
                }

                if (typeof diagramDetailTr === 'string') {
                    diagramDetailTr = JSON.parse(diagramDetailTr);
                }
                if (diagramTr && Array.isArray(diagramDetailTr)) {


                    const uploadPromisesUpImg = diagramDetailTr.map(async (detail) => {
                        // Cari padanan gambar di diagram berdasarkan nama file yang cocok
                        const item = diagramTr.find(diagramItem => diagramItem.clientName === detail.fileName);

                        let fileName = detail.fileName; // Default value if no matching image is found

                        // Jika ada gambar yang cocok di diagram, upload dan ambil nama file baru
                        if (item) {
                            const uploadPath = `uploads/sektor/`;
                            const upImg = await img_to_webp(item, uploadPath); // Mengupload gambar ke WebP
                            fileName = upImg.data.filename; // Mendapatkan nama file gambar yang diupload
                            if (detail.id_komoditi_nasional_file_tr) {
                                // Jika `id_sektor_nasional_file` ada, lakukan update
                                await prisma.tb_komoditi_nasional_file_tr.update({
                                    where: {
                                        id_komoditi_nasional_file_tr: parseInt(detail.id_komoditi_nasional_file_tr),
                                    },
                                    data: {
                                        kd_bahasa: "en",
                                        nama: fileName, // Nama file baru jika ada upload, atau dari detail jika tidak ada
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                            } else {

                                //Jika `id_sektor_nasional_file` tidak ada, lakukan insert
                                await prisma.tb_komoditi_nasional_file_tr.create({
                                    data: {
                                        id_komoditi_nasional_file:idDiagram[0],
                                        kd_bahasa: "en",
                                        nama: fileName, // Nama file baru atau dari detail
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                                idDiagram = idDiagram.slice(1)
                            }
                        }else{
                            if (detail.id_komoditi_nasional_file) {
                                // Jika `id_sektor_nasional_file` ada, lakukan update
                                await prisma.tb_komoditi_nasional_file_tr.update({
                                    where: {
                                        id_komoditi_nasional_file_tr: parseInt(detail.id_komoditi_nasional_file_tr),
                                    },
                                    data: {
                                        kd_bahasa: "en",
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                            } else {
                                return response.status(500).json({ success: false, message: 'File diagram Required!' })
                            }
                        }


                    });


                    await Promise.all(uploadPromisesUpImg); // Menjalankan semua upload dan insert secara paralel
                }
                response.status(201).json({ success: true,data:update })
            },{timeout:20000});
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_komoditi_nasional.delete({
        where: {
          id_komoditi_nasional: Id,
        },
      })
      return response.status(200).send({success:true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }

  /**
     * @aprove
     * @paramQuery status - status - @type(number) @required
     * @paramQuery keterangan - keterangan - @type(string)  @required
     */
    async aprove({ params, request, response ,auth}: HttpContext) {
        // Ambil dan parse ID dari parameter URL
        const Id = parseInt(params.id_komoditi_nasional, 10);
        // Validasi ID
        if (isNaN(Id)) {
            return response.status(400).json({ error: 'Invalid ID provided' });
        }
        try {
            // Validasi input dengan validator yang telah dibuat
            const dataPost = await request.validateUsing(aproveValidator);

            // Update data di tb_sektor_nasional
            const update = await prisma.tb_komoditi_nasional.update({
            where: {
                id_komoditi_nasional: Id
            },
                data: {
                    status: dataPost.status
                },
            });
            // Insert ke tb_sektor_nasional_status
            const insert = await prisma.tb_komoditi_nasional_status.create({
                data: {
                    id_komoditi_nasional: Id,
                    status: dataPost.status,
                    status_proses: dataPost.status,
                    keterangan: dataPost.keterangan,
                    created_by:auth.user?.id,
                    updated_by:auth.user?.id
                },
            });
            // Berikan respon sukses dengan data update
            return response.status(201).json({ success: true, data: update });
        } catch (error) {
            // Tangani error dan kirimkan respon gagal
            return response.status(500).json({ success: false, message: error.message });
        }
    }

      /**
   * Show individual record
   */
  async get_by_sub_sektor({ params,response }: HttpContext) {
      const Id = parseInt(params.id_sub_sektor, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        const datas = await prisma.tb_komoditi_nasional_ref.findMany({
            where: {
                id_sub_sektor: Id,
            },
        });

        const data = datas.map((item) => {
            return {
                id_komoditi: item.id_komoditi,
                id_sub_sektor: item.id_sub_sektor,
                nama: item.nama,
            }
        })


        return {
            success : true,
            data : data
        }
    }

    /**
        * @change_status
        * @paramQuery status - status - @type(number) @required
        * @paramQuery keterangan - keterangan - @type(string)  @required
        */
       async change_status({ params, request, response ,auth}: HttpContext) {
           // Ambil dan parse ID dari parameter URL
           const Id = parseInt(params.id, 10);

           // Validasi ID
           if (isNaN(Id)) {
               return response.status(400).json({ error: 'Invalid ID provided' });
           }
           try {
               // Validasi input dengan validator yang telah dibuat
               const dataPost = await request.validateUsing(aproveValidator);

               // Update data di tb_sektor_nasional
               const update = await prisma.tb_komoditi_nasional.update({
               where: {
                   id_komoditi_nasional: Id
               },
                   data: {
                       status: parseInt(dataPost.status)
                   },
               });
               // Insert ke tb_sektor_nasional_status
               const insert = await prisma.tb_komoditi_nasional_status.create({
                   data: {
                       id_komoditi_nasional: Id,
                       status: dataPost.status,
                       status_proses: dataPost.status,
                       keterangan: dataPost.keterangan,
                       created_by:auth.user?.id,
                       updated_by:auth.user?.id,
                       created_date:new Date()
                   },
               });
               // Berikan respon sukses dengan data update
               return response.status(201).json({ success: true, data: update });
           } catch (error) {
               // Tangani error dan kirimkan respon gagal
               return response.status(500).json({ success: false, message: error.message });
           }
       }
}