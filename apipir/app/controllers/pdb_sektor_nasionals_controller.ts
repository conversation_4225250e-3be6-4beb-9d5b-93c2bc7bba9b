import { createMenuValidator, updateMenuValidator } from '#validators/menu';
import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'
import { configUpload, get_paginate, img_to_webp, numberFormat, upload } from '../helpers/global_helper.js';
import { createAppSLiderPeluangValidator } from '#validators/add_slider_peluang';
import { createPdbSNValidator } from '#validators/sektor_nasional';
import { aproveValidator } from '#validators/aprove';
import { error } from 'node:console';


export default class PdbSektorNasionalsController {

    /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.tb_sektor_nasional_pdb.findMany({
      
    });

      return { 
          success : true,
          data : {data}
      }
  }

  async get_paginate({params,response,request}:HttpContext) {
    const parameters =  request.qs()
    const order = parameters.order 
    const by = ['asc','desc'].includes(parameters.by) ? parameters.by : 'asc'
    let orderBy={}
    const paramList = ['tahun_pdb','status']
    if (order != undefined && paramList.includes(order)) {
      orderBy = {[order]:by}
    }
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided
 
    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object
    if (search !== ',' && search !== "%7Bsearch%7D" && search !== '') { // Apply search condition only if 'search' is not empty
      searchCondition = {
        OR: [
         
          {
            sumber_data: {
              tb_sumber_data_judul: {
                tb_sumber_data_instansi: {
                  nama: { contains: search, mode: 'insensitive' } 
                }
              }
            }
          },
          {
            sumber_data: {
              tb_sumber_data_judul: {
                  judul: { contains: search, mode: 'insensitive' } 
              }
            }
          },
          {
            sektor_nasional: {
              sektor: {
                nama: { contains: search, mode: 'insensitive' }
              }
            }
          }
        ]
      };
      if (!isNaN(parseInt(search))) {
      searchCondition.OR.push({
        tahun_pdb: {
          equals: parseInt(search)  // Only include this condition if search is a valid number
        },
        jumlah_pdb: {
          equals: parseInt(search)  // Only include this condition if search is a valid number
        }
      });
    }
    }
    const req = request.qs()
    if (req.status) {
        (searchCondition.AND ??= []).push(
          { status: parseInt(req.status) }
  
        );
    }
    // Get total record count with the search condition applied (or no filter if search is empty)
    const totalRecords = await prisma.tb_sektor_nasional_pdb.count({
      where: searchCondition
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize);
    

    // Fetch records with pagination and search filtering (or no filter if search is empty)
    const datas = await prisma.tb_sektor_nasional_pdb.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply search condition or no filter
      orderBy:orderBy,
      include: {
        sektor_nasional: {
          include: {
            sektor: true
          }
        },
        sumber_data: {
          include: {
            tb_sumber_data_judul: {
              include: {
                tb_sumber_data_instansi: true
              }
            }
          }
        }
      }
    });

    // const data = datas
    const data = datas.map((item =>{
        const status_text = item.status == 99 ? "Aprove" : "Delete"
        return {
            id_sektor_nasional_pdb : item.id_sektor_nasional_pdb,
            nama_sektor : item.sektor_nasional.sektor.nama,
            tahun_pdb : item.tahun_pdb,
            jumlah_pdb : `Rp. ${numberFormat(parseInt(item.jumlah_pdb))}`,
            sumber_data : `${item.sumber_data.tb_sumber_data_judul.judul} - ${item.sumber_data.tb_sumber_data_judul.tb_sumber_data_instansi.nama}`,
            status:item.status,
            status_text
        }
    }))
    

    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {

  }

  /**
     * @store
     * @paramQuery id_sektor_nasional - Id Sektor Nasional - @type(number) @required
     * @paramQuery tahun_pdb - Tahun PDB - @type(number) 
     * @paramQuery id_sumber_data - id sumber data - @type(number) @required
     * @paramQuery jumlah_pdb - Nominal PDB - @type(number) @required
     */
    public async store({ request, response ,auth}: HttpContext) {


      let data = await request.validateUsing(createPdbSNValidator)
      const exist = await prisma.tb_sektor_nasional_pdb.findFirst({
        where:{
          id_sektor_nasional:data.id_sektor_nasional,
          tahun_pdb:data.tahun_pdb
        }
      })
      if (exist) {
        return response.status(500).json({ success: false, message: 'Sektor dan Tahun PDB sudah ada.' })
      }
        try {
            data['status'] = 0
            const insert = await prisma.tb_sektor_nasional_pdb.create({data:data})
            await auth.check()
            const inserts = await prisma.tb_sektor_nasional_pdb_status.create({
                    data: {
                        id_sektor_nasional: data.id_sektor_nasional,
                        tahun_pdb:data.tahun_pdb,
                        status: 0,
                        status_proses: 0,
                        keterangan: request.input('keterangan'),
                        created_by:auth.user?.id,
                        updated_by:auth.user?.id,
                        created_date:new Date()
                    },
                });
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_sektor_nasional_pdb.findUnique({
          where: {
            id_sektor_nasional_pdb: Id,
          },
        })

        return { 
            success : true,
            data : {data}
        }
  }

  /**
   * Edit individual record
   */
  async edit({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_sektor_nasional_pdb.findUnique({
          where: {
            id_sektor_nasional_pdb: Id,
          },
        })

        return { 
            success : true,
            data
        }
  }

  /**
     * @update
     * @paramQuery id_sektor_nasional - Id Sektor Nasional - @type(number) @required
     * @paramQuery tahun_pdb - Tahun PDB - @type(number) 
     * @paramQuery id_sumber_data - id sumber data - @type(number) @required
     * @paramQuery jumlah_pdb - Nominal PDB - @type(number) @required
     */
  async update({ params, request ,response}: HttpContext) {
  
      const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }

      const data = await request.validateUsing(createPdbSNValidator)
        try {
            data['status']= 0
            const insert = await prisma.tb_sektor_nasional_pdb.update({
              data:data,
              where:{
                id_sektor_nasional_pdb:Id
              }
            })
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_sektor_nasional_pdb.delete({
        where: {
          id_sektor_nasional_pdb: Id,
        },
      })
      return response.status(200).send(deletePost)
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }

  /**
     * @aprove
     * @paramQuery status - status - @type(number) @required
     * @paramQuery keterangan - keterangan - @type(string)  @required
     */
    async aprove({ params, request, response ,auth}: HttpContext) {
        // Ambil dan parse ID dari parameter URL
        const Id = parseInt(params.id_sektor_nasional_pdb, 10);
        // Validasi ID
        if (isNaN(Id)) {
            return response.status(400).json({ error: 'Invalid ID provided' });
        }
        try {
            // Validasi input dengan validator yang telah dibuat
            const dataPost = await request.validateUsing(aproveValidator);

            // Update data di tb_sektor_nasional
            const update = await prisma.tb_sektor_nasional_pdb.update({
            where: {
                id_sektor_nasional_pdb: Id
            },
                data: {
                    status: dataPost.status
                },
            });
            // Insert ke tb_sektor_nasional_status
            const insert = await prisma.tb_sektor_nasional_pdb_status.create({
                data: {
                    id_sektor_nasional: update.id_sektor_nasional,
                    tahun_pdb: update.tahun_pdb,
                    status: dataPost.status,
                    status_proses: dataPost.status,
                    keterangan: dataPost.keterangan,
                    created_by:auth.user?.id,
                    updated_by:auth.user?.id,
                },
            });
            // Berikan respon sukses dengan data update
            return response.status(201).json({ success: true, data: update });
        } catch (error) {
            // Tangani error dan kirimkan respon gagal
            return response.status(500).json({ success: false, message: error.message });
        }
    }

}