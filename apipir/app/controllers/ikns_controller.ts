import type { HttpContext } from '@adonisjs/core/http'

import fs from 'fs';
import { Prisma } from '@prisma/client';
import { createObjectCsvStringifier } from 'csv-writer';

import { checkFileOrUrl, configUpload, img_to_webp, send_mail, upload, upload_file, upload_img_to_webp } from '../helpers/global_helper.js';
import env from '#start/env';
import { updateIknValidator } from '#validators/ikn';

import prisma from '../lib/prisma.js';
export default class IknsController {

/**
     * @update_ikn
     * @paramQuery nama - Nama  - @type(string)
     * @paramQuery nama_tr - Nama Translate  - @type(string)
     * @paramQuery deskripsi - Deskripsi IKN - @type(string)
     * @paramQuery deskripsi_tr - Deskripsi IKN Translate - @type(string)
     * @paramQuery lon - Url - @type(number)
     * @paramQuery lat - Urutan - @type(number)
     * @paramQuery file_video - url Video - @type(string)
     * @paramQuery image - image - @type(file)
     * @paramQuery logo - image logo- @type(file)
     * @paramQuery vidio - vidio- @type(file)
     */
    public async update_ikn({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(updateIknValidator)
        try {
            const configImg = await configUpload('img')
            const configVid = await configUpload('vid')
            const image = request.files('image',configImg);
            const logo = request.files('logo',configImg);
            const vidio = request.files('video',configVid);
            const dataPost = {
                nama : data.nama,
                deskripsi : data.deskripsi,
                lon : data.lon,
                lat : data.lat,
                file_video : data.file_video,
            }
            if (image) {
                const uploadPromisess = image.map(async (item) => {

                    const uploadPath = `uploads/ikn`;
                    const upImg = await img_to_webp(image[0], uploadPath);
                    dataPost.file_image = upImg.data.filename
                });
                await Promise.all(uploadPromisess);
            }

            if (logo) {
                const uploadPromisess = logo.map(async (item) => {

                    const uploadPath = `uploads/ikn`;
                    const upImg = await img_to_webp(logo[0], uploadPath);
                    dataPost.file_logo = upImg.data.filename
                });
                await Promise.all(uploadPromisess);
            }

            if (vidio) {
              const uploadPromisess = vidio.map(async (item) => {
                  const uploadPath = `uploads/ikn`;
                  const upDoc = await upload(item, uploadPath);
                  dataPost.file_video = upDoc.data?.filename

              });
              await Promise.all(uploadPromisess);

            }

            const update = await prisma.tb_ikn.update({
                where:{
                    id_ikn:1
                },
                data:dataPost
            })

            const dataPostTr ={
                nama : data.nama_tr,
                deskripsi : data.deskripsi_tr,
                id_ikn : 1,
                kd_bahasa : "en"
            }
            const updateTr = await prisma.tb_ikn_tr.update({
                where:{
                    id_ikn_tr:1
                },
                data:dataPostTr
            })

            response.status(201).json({ success: true,data:dataPost })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

    /**
   * Show individual record
   */
  async get_headline({ response }: HttpContext) {

        let data = await prisma.tb_ikn.findUnique({
          where: {
            id_ikn: 1,
          },
          include: {
            tb_ikn_tr: true,
          },
        })

        if (data?.file_video) {
          const check = await checkFileOrUrl(data?.file_video);

          if (check == 'file') {
            data.file_video = `${env.get('APP_URL')}/uploads/ikn/${data?.file_video}`;
            data.url_video = '';
          } else {
              data.url_video = data?.file_video;
              data.file_video = '';
          }
        }
        if (data?.file_image) {
          data.file_image = `${env.get('APP_URL')}/uploads/ikn/${data.file_image}`;
        }

        if (data?.file_logo) {
          data.file_logo = `${env.get('APP_URL')}/uploads/ikn/${data.file_logo}`;
        }

        return {
            success : true,
            data : data
        }
  }

  async get_dt_image({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = params.search || ''; // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {
        AND: [
            { status: 99 }, // Kondisi wajib
        ],
        };

        if (search !== ',' && search !== '') {
        searchCondition.AND.push({
            OR: [
            { judul: { contains: search, mode: 'insensitive' } }, // Search in keterangan
            ],
        });
        }
    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_ikn_file.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });


    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_ikn_file.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty

    });
    const data = datas.map((item) => {

      return {
        id_ikn_file:item.id_ikn_file,
        judul :item.judul,
        status:item.status,
        image : `${env.get('APP_URL')}/uploads/ikn/${item.nama}`

      }
    })


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };
  }
  async get_dt_peluang({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const searchKey = params.search || ''; // Default to an empty string if search is not provided
    const [ppi, pid] = await Promise.all([
        prisma.tb_peluang_kabkot.findMany({
            select:{
                id_adm_kabkot:true,
                id_peluang_kabkot:true,
                nama:true,
                nilai_investasi:true,
                nilai_irr:true,
                nilai_npv:true,
                nilai_pp:true,
                project_status_enum:true,
                is_ipro:true,
                tahun:true,
                is_ikn:true,
                tb_peluang_sektor:{
                    select:{
                        kategori_sektor:{
                            select:{
                                nama:true,
                                id_kategori_sektor:true
                            }
                        },
                        nama:true,
                        icon:true
                    }
                },
                tb_peluang_kabkot_file:{
                    select:{
                        nama:true
                    },
                    where:{
                        tipe:1
                    }
                },
                tb_adm_kabkot:{
                    select:{
                        nama:true,
                        tb_adm_provinsi:{
                            select:{
                                nama:true
                            }
                        },
                    }
                }
            },
            where: {
                    status:"99",
                    id_prioritas:1,
                    id_adm_kabkot:  {
                                gte: parseInt(`6400`),
                                lt: parseInt(`6499`)
                            },

            },
            orderBy:{
                nilai_investasi:'desc'
            }
        }),
        prisma.$queryRaw`
            SELECT
                p.is_ikn,
                p.id_peluang_daerah,
                p.id_adm_provinsi,
                p.id_adm_kabkot,
                p.judul as nama,
                k.initial_invesment as nilai_investasi,
                k.irr as nilai_irr,
                k.npv as nilai_npv,
                k.pp as nilai_pp,
                skn.id_kategori_sektor as id_kategori_sektor,
                skn.nama as nama_sektor,
                snf.nama as nama_sektor_peluang,
                snf.icon as icon,
                pdf.nama as images,
                p.status,
                ak.nama as nama_kabkot,
                ap.nama as nama_provinsi
            FROM tb_peluang_daerah p
            LEFT JOIN tb_peluang_daerah_kelayakan k
                ON p.id_peluang_daerah = k.id_peluang_daerah
            LEFT JOIN tb_sub_sektor_daerah ssd
                ON p.id_sub_sektor_daerah = ssd.id_sub_sektor_daerah
            LEFT JOIN tb_sub_sektor_nasional ssn
                ON ssd.id_sub_sektor_nasional = ssn.id_sub_sektor_nasional
            LEFT JOIN tb_sektor_nasional sn
                ON sn.id_sektor_nasional = ssn.id_sektor_nasional
            LEFT JOIN tb_sektor_nasional_ref snf
                ON sn.id_sektor_nasional = snf.id_sektor
            LEFT JOIN tb_kategori_sektor skn
                ON skn.id_kategori_sektor = snf.id_kategori_sektor
            LEFT JOIN tb_peluang_daerah_file pdf
                ON pdf.id_peluang_daerah = p.id_peluang_daerah and pdf.tipe = 1
            LEFT JOIN tb_adm_kabkot ak
                on ak.id_adm_kabkot = p.id_adm_kabkot
            LEFT JOIN tb_adm_provinsi ap
                on ap.id_adm_provinsi = p.id_adm_provinsi
            WHERE p.status = 99
            and (p.id_adm_provinsi = 64 or (p.id_adm_kabkot between 6400 and 6499))
            ORDER BY k.initial_invesment desc;
        `
        ]);

        // Mapping data dengan keterangan masing-masing
        const proyeks = [
        ...ppi.map(item => ({
            id_peluang:item.id_peluang_kabkot,
            id_adm_provinsi:parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
            id_adm_kabkot:item.id_adm_kabkot,
            nama_kabkot:item.tb_adm_kabkot.nama,
            nama_provinsi:item.tb_adm_kabkot.tb_adm_provinsi.nama,
            nama:item.nama,
            status: 'PPI',
            is_ikn:item.is_ikn
        })),
        ...pid.map(item => ({
            id_peluang:item.id_peluang_daerah,
            id_adm_provinsi:item.id_adm_provinsi,
            id_adm_kabkot:item.id_adm_kabkot,
            nama_kabkot:item.nama_kabkot,
            nama_provinsi:item.nama_provinsi,
            nama:item.nama,
            status: 'PID',
            is_ikn:item.is_ikn
        }))
    ]

    // return proyeks
    let search = ''
    let filteredData = proyeks
    if (searchKey != ',' && searchKey != '' && searchKey != undefined ) {

        search = searchKey?.toLowerCase() || ''; // String pencarian (case insensitive)

         filteredData = proyeks.filter(item =>
            Object.values(item).some(value =>
                typeof value === 'string' && value.toLowerCase().includes(search)
            )
        );
    }


    // Paginasi secara manual
    const skip = (page - 1) * pageSize;
    const take = pageSize;
    const totalRecords = filteredData.length; // Total jumlah data setelah pencarian
    const totalPage = Math.ceil(totalRecords / pageSize); // Total halaman
    const paginatedData = filteredData.slice((page - 1) * pageSize, page * pageSize);

    return {
        success: true,
        totalRecords, // Total number of records after filtering
        totalPage, // Total number of pages
        page,
        pageSize,
        data: paginatedData, // Data for the current page
    };
  }
  async get_dt_peluangOld({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = params.search || ''; // Default to an empty string if search is not provided
    const kabkot = await prisma.tb_ikn_kabkot.findMany({
      select:{
        id_adm_kabkot:true
      }
    })

    const kabkot_id = kabkot.map(item=>item.id_adm_kabkot)

    // Define the search conditions (optional)
    let searchCondition = {
        AND: [
            { status: "99" }, // Kondisi wajib
            { id_prioritas: 1 }, // Kondisi wajib
            { id_adm_kabkot: { in: kabkot_id } } // Kondisi wajib
        ],
        };

        if (search !== ',' && search !== '') {
        searchCondition.AND.push({
            OR: [
              { deskripsi: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
              { deskripsi_singkat: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
              {
                tb_peluang_sektor: {
                  nama:  { contains: search, mode: 'insensitive' }, // Search in tb_app_slider_tr.deskripsi
                },
              },
            ],
        });
        }
    const totalRecords = await prisma.tb_peluang_kabkot.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });


    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_peluang_kabkot.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
          include:{
              tb_peluang_kabkot_file:true,
              tb_peluang_kabkot_insentif:true,
              tb_peluang_kabkot_kontak:true,
              tb_peluang_kabkot_file_tr:true,
              tb_peluang_kabkot_tr:true,
              tb_peluang_sektor:true,
              tb_adm_kabkot:{
                include:{
                  tb_adm_provinsi:
                  {
                    include:{
                      tb_adm_wilayah:true
                    }
                  }
                }
              },
          }
    });
    const data = datas.map((item) => {
      let prioritas = '';
      if (item.id_prioritas == 1) {
         prioritas = 'Prioritas'
      } else if (item.id_prioritas == 2) {
         prioritas = 'Daerah'
      } else {
         prioritas = 'Prioritas & IKN'
      }

      return {
        id_peluang_kabkot :item.id_peluang_kabkot,
        judul_peluang : item.nama,
        lokasi_kawasan : item.lokasi_kawasan,
        deskripsi:item.deskripsi,
        deskripsi_singkat:item.deskripsi_singkat,
        pulau : item.tb_adm_kabkot.tb_adm_provinsi.tb_adm_wilayah.nama,
        provinsi : item.tb_adm_kabkot.tb_adm_provinsi.nama,
        kabkot : item.tb_adm_kabkot.nama,
        nama_Sektor : item.tb_peluang_sektor.nama,
        prioritas : prioritas,
        tahun : item.tahun,
        status : item.status,
        keterangan : item.keterangan,
        projek_status_enum :item.project_status_enum
      }

    })


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };
  }
  async get_dt_bandara({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = params.search || ''; // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {
        AND: [
            { status: 99 }, // Kondisi wajib
            { id_adm_kabkot:  {
                                gte: parseInt(`6400`),
                                lt: parseInt(`6499`)
                            }, } // Kondisi wajib
        ],
        };

        if (search !== ',' && search !== '') {
        searchCondition.AND.push({
            OR: [
              { nama: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
              { keterangan: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
              { alamat: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
            ],
        });
        }
    const totalRecords = await prisma.tb_bandara.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });


    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_bandara.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
    });



    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: datas, // Data for the current page
    };
  }
  async get_dt_hotel({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = params.search || ''; // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {
        AND: [
            { status: 99 }, // Kondisi wajib
            { id_adm_kabkot:  {
                                gte: parseInt(`6400`),
                                lt: parseInt(`6499`)
                            }, } // Kondisi wajib
        ],
        };

        if (search !== ',' && search !== '') {
        searchCondition.AND.push({
            OR: [
              { nama: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
              { keterangan: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
              { alamat: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
            ],
        });
        }
    const totalRecords = await prisma.tb_hotel.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });


    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_hotel.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
    });



    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: datas, // Data for the current page
    };
  }
  async get_dt_pelabuhan({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = params.search || ''; // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {
        AND: [
            { status: 99 }, // Kondisi wajib
            { id_adm_kabkot:  {
                                gte: parseInt(`6400`),
                                lt: parseInt(`6499`)
                            }, } // Kondisi wajib
        ],
        };

        if (search !== ',' && search !== '') {
        searchCondition.AND.push({
            OR: [
              { nama: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
              { keterangan: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
              { alamat: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
            ],
        });
        }
    const totalRecords = await prisma.tb_pelabuhan.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });


    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_pelabuhan.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
    });



    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: datas, // Data for the current page
    };
  }
  async get_dt_pendidikan({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = params.search || ''; // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {
        AND: [
            { status: 99 }, // Kondisi wajib
            { id_adm_kabkot:  {
                                gte: parseInt(`6400`),
                                lt: parseInt(`6499`)
                            }, } // Kondisi wajib
        ],
        };

        if (search !== ',' && search !== '') {
        searchCondition.AND.push({
            OR: [
              { nama: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
              { keterangan: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
              { alamat: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
            ],
        });
        }
    const totalRecords = await prisma.tb_pendidikan.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });


    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_pendidikan.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
    });



    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: datas, // Data for the current page
    };
  }
  async get_dt_rumah_sakit({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = params.search || ''; // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {
      AND: [
          { status: 99 }, // Kondisi wajib
          { id_adm_kabkot:  {
                              gte: parseInt(`6400`),
                              lt: parseInt(`6499`)
                          }, } // Kondisi wajib
      ],
      };

      if (search !== ',' && search !== '') {
      searchCondition.AND.push({
          OR: [
            { nama: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
            { keterangan: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
            { alamat: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
          ],
      });
    }
    const totalRecords = await prisma.tb_rumah_sakit.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });


    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_rumah_sakit.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
    });



    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: datas, // Data for the current page
    };
  }
  async get_dt_kawasan_industri({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = params.search || ''; // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {
      AND: [
          { status: '99' }, // Kondisi wajib
          { id_adm_kabkot:  {
                              gte: parseInt(`6400`),
                              lt: parseInt(`6499`)
                          }, } // Kondisi wajib
      ],
      };

      if (search !== ',' && search !== '') {
      searchCondition.AND.push({
          OR: [
            { nama: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
            { keterangan: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
            { alamat: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
          ],
      });
    }
    const totalRecords = await prisma.tb_kawasan_industri.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });


    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_kawasan_industri.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      include:{
        tb_adm_kabkot:{
          include:{
            tb_adm_provinsi:true
          }
        }
      }
    });
    const data = datas.map(({tb_adm_kabkot,...rest}) => {
      return {
        nama_kabkot : tb_adm_kabkot.nama,
        nama_provinsi : tb_adm_kabkot.tb_adm_provinsi.nama,
        ...rest,
      }
    })


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };
  }

  /**
     * @set_ikn_peluang
     * @paramQuery list_id_ppi - list id PPI yang di IKN array contoh [12,13,14]- @type(string) @required
     * @paramQuery list_id_pid - list id PID yang di IKN array contoh [12,13,14]- @type(string) @required
     */
  async set_ikn_peluang({request,response}:HttpContext) {
      let list_id_ppi = request.input('list_id_ppi')
      let list_id_pid = request.input('list_id_pid')
        if (typeof list_id_ppi === 'string') {
            list_id_ppi = JSON.parse(list_id_ppi);
        }
        if (typeof list_id_pid === 'string') {
            list_id_pid = JSON.parse(list_id_pid);
        }
        // return list_id_ppi
        if (Array.isArray(list_id_ppi) && Array.isArray(list_id_pid)) {
          list_id_ppi = list_id_ppi.map(Number);
          list_id_pid = list_id_pid.map(Number);
          try {
            await prisma.tb_peluang_kabkot.updateMany({
              where: {
                id_peluang_kabkot: {
                  notIn:list_id_ppi,
                },
              },
              data: {
                is_ikn: false,
              },
            });
            await prisma.tb_peluang_kabkot.updateMany({
              where: {
                id_peluang_kabkot: {
                  in:list_id_ppi,
                },
              },
              data: {
                is_ikn: true,
              },
            });
            await prisma.tb_peluang_daerah.updateMany({
              where: {
                id_peluang_daerah: {
                  notIn:list_id_pid,
                },
              },
              data: {
                is_ikn: false,
              },
            });
            await prisma.tb_peluang_daerah.updateMany({
              where: {
                id_peluang_daerah: {
                  in:list_id_pid,
                },
              },
              data: {
                is_ikn: true,
              },
            });
            response.status(201).json({ success: true,data:{ppi :list_id_ppi,pid : list_id_pid} })
          } catch (error) {
          response.status(500).json({ success: false, message: error.message })
          }

        }else{
          response.status(500).json({ success: false, message: 'Input List Id Bukan Array' })
        }

  }
  /**
     * @set_ikn_bandara
     * @paramQuery list_id - list id yang di IKN array contoh [12,13,14]- @type(string) @required
     */
  async set_ikn_bandara({request,response}:HttpContext) {
      let list_id = request.input('list_id')
        if (typeof list_id === 'string') {
        list_id = JSON.parse(list_id);

      }
      if (Array.isArray(list_id)) {
        list_id = list_id.map(Number);
          try {
            await prisma.tb_bandara.updateMany({
              where: {
                id_bandara: {
                  notIn:list_id,
                },
              },
              data: {
                is_ikn: false,
              },
            });
            await prisma.tb_bandara.updateMany({
              where: {
                id_bandara: {
                  in:list_id,
                },
              },
              data: {
                is_ikn: true,
              },
            });
            response.status(201).json({ success: true,data:list_id })
          } catch (error) {
          response.status(500).json({ success: false, message: error.message })
          }

        }else{
          response.status(500).json({ success: false, message: 'Input List Id Bukan Array' })
        }


  }

  /**
     * @set_ikn_hotel
     * @paramQuery list_id - list id yang di IKN array contoh [12,13,14]- @type(string) @required
     */
  async set_ikn_hotel({request,response}:HttpContext) {
      let list_id = request.input('list_id')
        if (typeof list_id === 'string') {
            list_id = JSON.parse(list_id);

        }
        if (Array.isArray(list_id)) {
          list_id = list_id.map(Number);
          try {
            await prisma.tb_hotel.updateMany({
              where: {
                id_hotel: {
                  notIn:list_id,
                },
              },
              data: {
                is_ikn: false,
              },
            });
            await prisma.tb_hotel.updateMany({
              where: {
                id_hotel: {
                  in:list_id,
                },
              },
              data: {
                is_ikn: true,
              },
            });
            response.status(201).json({ success: true,data:list_id })
          } catch (error) {
          response.status(500).json({ success: false, message: error.message })
          }

        }else{
          response.status(500).json({ success: false, message: 'Input List Id Bukan Array' })
        }
  }
  /**
     * @set_ikn_pelabuhan
     * @paramQuery list_id - list id yang di IKN array contoh [12,13,14]- @type(string) @required
     */
  async set_ikn_pelabuhan({request,response}:HttpContext) {
      let list_id = request.input('list_id')
        if (typeof list_id === 'string') {
            list_id = JSON.parse(list_id);

        }
        if (Array.isArray(list_id)) {
          list_id = list_id.map(Number);
          try {
            await prisma.tb_pelabuhan.updateMany({
              where: {
                id_pelabuhan: {
                  notIn:list_id,
                },
              },
              data: {
                is_ikn: false,
              },
            });
            await prisma.tb_pelabuhan.updateMany({
              where: {
                id_pelabuhan: {
                  in:list_id,
                },
              },
              data: {
                is_ikn: true,
              },
            });
            response.status(201).json({ success: true,data:list_id })
          } catch (error) {
          response.status(500).json({ success: false, message: error.message })
          }

        }else{
          response.status(500).json({ success: false, message: 'Input List Id Bukan Array' })
        }
  }

  /**
     * @set_ikn_rumah_sakit
     * @paramQuery list_id - list id yang di IKN array contoh [12,13,14]- @type(string) @required
     */
  async set_ikn_rumah_sakit({request,response}:HttpContext) {
    let list_id = request.input('list_id')
    if (typeof list_id === 'string') {
        list_id = JSON.parse(list_id);

    }
    if (Array.isArray(list_id)) {
      list_id = list_id.map(Number);
      try {
        await prisma.tb_rumah_sakit.updateMany({
          where: {
            id_rumah_sakit: {
              notIn:list_id,
            },
          },
          data: {
            is_ikn: false,
          },
        });

        await prisma.tb_rumah_sakit.updateMany({
          where: {
            id_rumah_sakit: {
              in:list_id,
            },
          },
          data: {
            is_ikn: true,
          },
        });
        response.status(201).json({ success: true,data:list_id })
      } catch (error) {
      response.status(500).json({ success: false, message: error.message })
      }

    }else{
      response.status(500).json({ success: false, message: 'Input List Id Bukan Array' })
    }
  }
  /**
     * @set_ikn_pendidikan
     * @paramQuery list_id - list id yang di IKN array contoh [12,13,14]- @type(string) @required
     */
  async set_ikn_pendidikan({request,response}:HttpContext) {
    let list_id = request.input('list_id')
    if (typeof list_id === 'string') {
        list_id = JSON.parse(list_id);

    }
    if (Array.isArray(list_id)) {
      list_id = list_id.map(Number);
      try {
        await prisma.tb_pendidikan.updateMany({
          where: {
            id_pendidikan: {
              notIn:list_id,
            },
          },
          data: {
            is_ikn: false,
          },
        });

        await prisma.tb_pendidikan.updateMany({
          where: {
            id_pendidikan: {
              in:list_id,
            },
          },
          data: {
            is_ikn: true,
          },
        });
        response.status(201).json({ success: true,data:list_id })
      } catch (error) {
      response.status(500).json({ success: false, message: error.message })
      }

    }else{
      response.status(500).json({ success: false, message: 'Input List Id Bukan Array' })
    }
  }




   /**
     * @set_ikn_kawasan
     * @paramQuery list_id - id kawasan array contoh [12,13,14]- @type(string) @required
     */
 async set_ikn_kawasan({request,response}:HttpContext) {
    let list_id = request.input('list_id')
    if (typeof list_id === 'string') {
        list_id = JSON.parse(list_id);

    }
    if (Array.isArray(list_id)) {
      list_id = list_id.map(Number);
      try {
        await prisma.tb_kawasan_industri.updateMany({
          where: {
            id_kawasan_industri: {
              notIn:list_id,
            },
          },
          data: {
            is_ikn: false,
          },
        });

        await prisma.tb_kawasan_industri.updateMany({
          where: {
            id_kawasan_industri: {
              in:list_id,
            },
          },
          data: {
            is_ikn: true,
          },
        });
        response.status(201).json({ success: true,data:list_id })
      } catch (error) {
      response.status(500).json({ success: false, message: error.message })
      }

    }else{
      response.status(500).json({ success: false, message: 'Input List Id Bukan Array' })
    }
  }
}