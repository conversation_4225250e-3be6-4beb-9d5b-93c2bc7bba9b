import { createProvinsiValidator } from '#validators/adm_prov';
import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'

export default class AdmProvinsisController {
  /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.tb_adm_provinsi.findMany({

    });

      return {
          success : true,
          data : {data}
      }
  }

  /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    // const {page ,pageSize} = await request.validateUsing(paginateUserValidator)
    const page = parseInt(params.page)
    const pageSize = parseInt(params.pageSize)
    const config = {

                  }

    return get_paginate(page,pageSize,prisma.tb_adm_provinsi,config)
  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {

  }

  /**
     * @store
     * @paramQuery judul - Ju<PERSON><PERSON>lide<PERSON> - @type(string) @required
     * @paramQuery deskripsi - Deskripsi Slider - @type(string)
     * @paramQuery nama_file_image - Nama FIle Image - @type(string) @required
     * @paramQuery url_link - Url - @type(string) @required
     * @paramQuery ordering - Urutan - @type(number) @required
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async store({ request, response ,auth}: HttpContext) {


        try {
            const dataPost = await request.validateUsing(createProvinsiValidator)
            const insert = await prisma.tb_adm_provinsi.create(
                {
                    data:dataPost
                }
            )
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_adm_provinsi.findUnique({
          where: {
            id_adm_provinsi: Id,
          },
        })

        return {
            success : true,
            data : {data}
        }
  }

  /**
   * Edit individual record
   */
  async edit({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_adm_provinsi.findUnique({
          where: {
            id_adm_provinsi: Id,
          },
        })

        return {
            success : true,
            data
        }
  }

  /**
     * @update
     * @paramQuery menu_name - Nama Menu - @type(string)
     * @paramQuery url - Url Menu - @type(string)
     * @paramQuery order - Urutan Menu - @type(number)
     * @paramQuery parent_id - ID Parent - @type(number)
     */
  async update({ params, request ,response}: HttpContext) {
    const dataPost = await request.validateUsing(createProvinsiValidator)

      const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }

      try {
        const updatePost = await prisma.tb_adm_provinsi.update({
          where: {
            id_adm_provinsi: Id,
          },
          data: dataPost,
        })
      return response.status(200).json({ success: true, data: updatePost })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data' })
    }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_adm_provinsi.delete({
        where: {
          id_adm_provinsi: Id,
        },
      })

      return response.status(200).send({ success: true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }


  async  getMenuWithChildren(parentId = null) {
    const menus =  prisma.tb_adm_provinsi.findMany({
      where: { parentId },
      include: { children: true },
      orderBy: { order: 'asc' },
    });

    return menus.map(menu => ({
          ...menu,
          children: getMenuWithChildren(menu.id),
        }));
  }

}