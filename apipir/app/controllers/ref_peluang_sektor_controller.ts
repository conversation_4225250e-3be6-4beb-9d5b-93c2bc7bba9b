import { createUmkmValidator, updateUmkmValidator } from '#validators/umkm'
import { createUmrProvValidator } from '#validators/umr'
import type { HttpContext } from '@adonisjs/core/http'
import { configUpload, img_to_webp } from '../helpers/global_helper.js'
import env from '#start/env'
import { createPSRefValidator, createSNRefValidator, updatePSRefValidator, updateSNRefValidator } from '#validators/referensi'
import prisma from '../lib/prisma.js'
export default class RefPeluangSektorController {

  async index({}: HttpContext) {
    const data = await prisma.tb_peluang_sektor.findMany({})
    return {
        success : true,
        data : data
    }
  }
   /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          { nama: { contains: search, mode: 'insensitive' } },
          {
            tb_peluang_sektor_tr: {
              some: {
                nama: {
                  contains: search,mode: "insensitive"
                }
              }
            }
          }
        ],
      };
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_peluang_sektor.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_peluang_sektor.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      include:{
        tb_peluang_sektor_tr:true
      }
    });
    const data = datas.map((item =>{
        return {
          id_peluang_sektor : item.id_peluang_sektor,
          nama_sektor : item.nama ,
          keteran : item.keterangan ,
          icon: `${env.get('APP_URL')}/uploads/sektor/${item.icon}`,
          image: `${env.get('APP_URL')}/uploads/${item.image?.replace('upload/','')}`,
        }
    }))


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
     * @store
     * @summary Insert Referensi Sektor Nasional Provinsi
     * @paramQuery nama - nama - @type(string) @required
     * @paramQuery nama_tr - nama_tr - @type(string) @required
     * @paramQuery kd_bahasa - kd_bahasa - @type(string) @required
     * @paramQuery image - image - @type(file) @required
  */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createPSRefValidator)
        try {
            const configImg = await configUpload('img');
            const icon = request.files('icon', configImg);
            const icon_map = request.files('icon_map', configImg);
            const image = request.files('image', configImg);
            if (icon) {
              const uploadPromisesUpImg = icon.map(async (item) => {
                const uploadPath = `uploads/icon-web/`;
                const upImg = await img_to_webp(item, uploadPath);
                    data.icon = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }
            if (icon_map) {
                const uploadPromisesUpImg = icon_map.map(async (item) => {
                  const uploadPath = `uploads/icon-web/`;
                  const upImg = await img_to_webp(item, uploadPath);
                  data.icon_map = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
              }


            const dataPost = {
              nama : data.nama,
              keterangan : data.keterangan,
              icon : data?.icon,
              iconmap : data?.icon_map,
              id_kategori_sektor : data?.id_kategori_sektor

            }
            const insert = await prisma.tb_peluang_sektor.create({data:dataPost})
            if (insert) {

              if (image) {
                const uploadPromisesUpImg = image.map(async (item) => {
                  const uploadPath = `uploads/sektor/img/${insert.id_peluang_sektor}/`;
                  const upImg = await img_to_webp(item, uploadPath);
                  const up = await prisma.tb_peluang_sektor.update({
                    data: {image :upImg.data.filename},
                    where: {
                      id_peluang_sektor: insert.id_peluang_sektor,
                    },
                  });
                  console.log(upImg);

                });
                await Promise.all(uploadPromisesUpImg);
              }
            }
            const dataPostTr = {
              id_peluang_sektor : insert.id_peluang_sektor,
              nama : data.nama_tr,
              keterangan : data.keterangan_tr,
              kd_bahasa : data.kd_bahasa,
            }
            const insertTr = await prisma.tb_peluang_sektor_tr.create({data:dataPostTr})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
    const data = await prisma.tb_peluang_sektor.findUnique({
      where: {
        id_peluang_sektor: Id,
      },
      include:{
        tb_peluang_sektor_tr:true
      }
    })

    if (data && data.icon ) {
      data.file_icon = `${env.get('APP_URL')}/uploads/icon-web/${data.icon}`;
    }
    if (data && data.iconmap ) {
      data.file_iconmap = `${env.get('APP_URL')}/uploads/icon-web/${data.iconmap}`;
    }
    if (data && data.image ) {
      data.file_image = `${env.get('APP_URL')}/uploads/sektor/img/${data.id_peluang_sektor}/${data.image.replace('upload/','').replace('uploads/','')}`;
    }



    return {
      success : true,
      data : data
    }
  }


  /**
     * @update
     * @paramQuery nama - nama - @type(string) @required
     * @paramQuery nama_tr - nama_tr - @type(string) @required
     * @paramQuery kd_bahasa - kd_bahasa - @type(string) @required
     * @paramQuery image - image - @type(file) @required
     */
  async update({ params, request ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)

    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }
    try {
      const data = await request.validateUsing(updatePSRefValidator)
      const configImg = await configUpload('img');
      const image = request.files('image', configImg);
      const icon = request.files('icon', configImg);
      const icon_map = request.files('icon_map', configImg);
      const dataPost = {
          id_peluang_sektor : Id,
          nama : data.nama,
          keterangan : data.keterangan,
          id_kategori_sektor : data?.id_kategori_sektor

      }
      if (icon) {
          const uploadPromisesUpImg = icon.map(async (item) => {
              const uploadPath = `uploads/icon-web/`;
              const upImg = await img_to_webp(item, uploadPath);
              dataPost.icon = upImg.data.filename;
          });
          await Promise.all(uploadPromisesUpImg);
      }
      if (icon_map) {
          const uploadPromisesUpImg = icon_map.map(async (item) => {
              const uploadPath = `uploads/icon-web/`;
              const upImg = await img_to_webp(item, uploadPath);
              dataPost.iconmap = upImg.data.filename;
          });
          await Promise.all(uploadPromisesUpImg);
      }
      if (image) {
        const uploadPromisesUpImg = image.map(async (item) => {
          const uploadPath = `uploads/sektor/img/${Id}/`;
          const upImg = await img_to_webp(item, uploadPath);
          dataPost.image = upImg.data.filename;

        });
        await Promise.all(uploadPromisesUpImg);
      }


      let tr = request.input('tr')
      if (typeof tr === 'string') {
          tr = JSON.parse(tr);
      }
      if (Array.isArray(tr)) {
          // Jika panjang array JSON dan file gambar tidak sesuai, kembalikan error
          await Promise.all(
            tr.map(item => {
              const dataPostTr = {
                id_peluang_sektor : Id,
                nama: item.nama,
                keterangan: item.keterangan,
                kd_bahasa: item.kd_bahasa,
              };

              if (!item.id_peluang_sektor_tr) {
                // Create jika id_peluang_sektor_tr tidak ada atau null
                return prisma.tb_peluang_sektor_tr.create({
                  data: dataPostTr
                });
              } else {
                // Update jika id_peluang_sektor_tr ada
                return prisma.tb_peluang_sektor_tr.update({
                  data: dataPostTr,
                  where: {
                    id_peluang_sektor_tr: parseInt(item.id_peluang_sektor_tr),
                  },
                });
              }
            })
          );

      }

      const update = await prisma.tb_peluang_sektor.update({
          data:dataPost,
          where:{
            id_peluang_sektor : Id,
          }
        })


      return response.status(200).json({ success: true, data: update })
    } catch (error) {
      return response.status(500).send({ error: error.message })
    }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_peluang_sektor.delete({
        where: {
          id_peluang_sektor: Id,
        },
      })
      return response.status(200).send({success:true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data',message:error.message })
    }
  }
}