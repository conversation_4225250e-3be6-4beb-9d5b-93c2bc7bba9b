import { createUmrProvValidator } from '#validators/umr'
import type { HttpContext } from '@adonisjs/core/http'
import env from '#start/env'
import { numberFormat } from '../helpers/global_helper.js'
import { aproveValidator } from '../validators/aprove.js'
import { equal } from 'node:assert'

import prisma from '../lib/prisma.js'
export default class UmrProvinsisController {

  async index({}: HttpContext) {
    const data = await prisma.tb_umr_provinsi.findMany({})
    return {
        success : true,
        data : {data}
    }
  }
    /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
    async get_paginate({params,response,request}:HttpContext) {
      const parameters =  request.qs()
      let order = parameters.order
      const by = ['asc','desc'].includes(parameters.by) ? parameters.by : 'asc'
      let orderBy={}
      const paramList = ['status','tahun']
      if (order != undefined && paramList.includes(order)) {
              orderBy = {[order]:by}
      }
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          {
            tb_adm_provinsi: {
              nama: {
                contains: search,
                mode: "insensitive",
              },
            },
          },
          {
            tb_sumber_data: {
              tb_sumber_data_judul: {
                judul: {
                  contains: search,
                  mode: "insensitive",
                },
              },
            },
          },
          {
            tahun: {
                // Cek apakah search adalah angka
                equals: isNaN(search) ? undefined : parseInt(search),
            },
          },
        ],
      };

    }
    const req = request.qs()
    if (req.id_adm_provinsi) {
      (searchCondition.AND ??= []).push(
        { id_adm_provinsi: parseInt(req.id_adm_provinsi) }

      );
      
    }
    if (req.status) {
      (searchCondition.AND ??= []).push(
        { status: parseInt(req.status) }

      );
      
    }
    
    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_umr_provinsi.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_umr_provinsi.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      orderBy:orderBy,
      include:{
        tb_adm_provinsi:true,
        tb_sumber_data:{
          include:{
            tb_sumber_data_judul:true
          }
        }
      }
    });

    const data = datas.map((item)=>{
      return {
          id_umr_provinsi: item.id_umr_provinsi,
          nama_provinsi: item.tb_adm_provinsi.nama,
          tahun : item.tahun,
          nilai : `${numberFormat(item.nilai)},00`,
          status : item.status,
        }
    })


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
     * @store
     * @summary Insert UMR Provinsi
     * @paramQuery id_adm_provinsi - id_adm_provinsi - @type(number) @required
     * @paramQuery tahun - tahun - @type(number) @required
     * @paramQuery id_sumber_data - id_sumber_data - @type(number)
     * @paramQuery nilai - nilai - @type(number) @required
     * @paramQuery status - status - @type(number)
  */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createUmrProvValidator)
      const checkUnique = await prisma.tb_umr_provinsi.count({
        where: {
                id_adm_provinsi: data.id_adm_provinsi||  undefined,
                tahun: data.tahun || undefined,
        }
      })

      if (checkUnique > 0) {
              return {
                      status: 'error',
                      message: 'Data Duplikat! Data Sudah Pernah Dibuat Pada Daerah Yang Sama!',
              };
      }
        try {
            data['status'] = 0;
            const dataPost = data
            const insert = await prisma.tb_umr_provinsi.create({data:dataPost})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
    const data = await prisma.tb_umr_provinsi.findUnique({
      where: {
        id_umr_provinsi: Id,
      },
    })

    return {
      success : true,
      data : data
    }
  }


  async edit({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    const data = await prisma.tb_umr_provinsi.findUnique({
      where: {
        id_umr_provinsi: Id,
      },
    })

    return {
      success : true,
      data : {data}
    }
  }

  /**
     * @update
     * @paramQuery id_adm_provinsi - id_adm_provinsi - @type(number) @required
     * @paramQuery tahun - tahun - @type(number) @required
     * @paramQuery id_sumber_data - id_sumber_data - @type(number)
     * @paramQuery nilai - nilai - @type(number) @required
     */
  async update({ params, request ,response}: HttpContext) {
    let dataPost = await request.validateUsing(createUmrProvValidator)
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      dataPost['status']=0
      const updatePost = await prisma.tb_umr_provinsi.update({
        where: {
          id_umr_provinsi: Id,
        },
        data: dataPost,
      })
      return response.status(200).json({ success: true, data: updatePost })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data' })
    }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_umr_provinsi.delete({
        where: {
          id_umr_provinsi: Id,
        },
      })
      return response.status(200).send({success:true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }

  /**
       * @change_status
       * @paramQuery status - status - @type(number) @required
       * @paramQuery keterangan - keterangan - @type(string)  @required
       */
      async change_status({ params, request, response ,auth}: HttpContext) {
          // Ambil dan parse ID dari parameter URL
          const Id = parseInt(params.id, 10);
          // Validasi ID
          if (isNaN(Id)) {
              return response.status(400).json({ error: 'Invalid ID provided' });
          }
          try {
              // Validasi input dengan validator yang telah dibuat
              const dataPost = await request.validateUsing(aproveValidator);

              // Update data di tb_sektor_nasional
              const update = await prisma.tb_umr_provinsi.update({
              where: {
                  id_umr_provinsi: Id
              },
                  data: {
                      status: parseInt(dataPost.status)
                  },
              });
              // Insert ke tb_sektor_nasional_status
              const insert = await prisma.tb_umr_provinsi_status.create({
                  data: {
                      id_umr_provinsi : Id,
                      status: dataPost.status,
                      status_proses: dataPost.status,
                      keterangan: dataPost.keterangan,
                      created_by:auth.user?.id,
                      updated_by:auth.user?.id,
                      created_date:new Date()
                  },
              });
              // Berikan respon sukses dengan data update
              return response.status(201).json({ success: true, data: update });
          } catch (error) {
              // Tangani error dan kirimkan respon gagal
              return response.status(500).json({ success: false, message: error.message });
          }
      }
}