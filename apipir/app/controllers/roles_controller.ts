import { createRoleValidator, updateRoleValidator } from '#validators/role'
import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'
export default class RolesController {

  async index({}: HttpContext) {
    const data = await prisma.role.findMany({})
    return {
        success : true,
        data : {data}
    }
  }
    /**
     * @get_paginate
     * @summary Get a list of role with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          {
            role_name: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
          },
        ],
      };


    }
    // Get total record count with the search condition applied
    const totalRecords = await prisma.role.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.role.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty

    });
    const data = datas.map(({...rest }) => {
        return {
            id : rest.id,
            role_name: rest.role_name,
        };
    });


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };
  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
     * @store
     * @summary Insert role
     * @paramQuery role_name - Role Name - @type(string) @required
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createRoleValidator)
        try {
           const {role_name } = data
           const dataPost = {
                role_name: role_name,
                }
            const insert = await prisma.role.create({data:dataPost})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
    const data = await prisma.role.findUnique({
      where: {
        id: Id,
      },
    })

    return {
      success : true,
      data : data
    }
  }


  async edit({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    const data = await prisma.role.findUnique({
      where: {
        id: Id,
      },
    })

    return {
      success : true,
      data : {data}
    }
  }

  /**
     * @update
     * @paramQuery role_name - Role Name - @type(string) @required
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
  async update({ params, request ,response}: HttpContext) {
    const dataPost = await request.validateUsing(updateRoleValidator)
    const { role_name } = dataPost

    const Id = parseInt(params.id, 10)

    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const updatePost = await prisma.role.update({
        where: {
          id: Id,
        },
        data: {
          role_name: role_name,
        },
      })
      return response.status(200).json({ success: true, data: updatePost })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data' })
    }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.role.delete({
        where: {
          id: Id,
        },
      })
      return response.status(200).send({success:true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }
}