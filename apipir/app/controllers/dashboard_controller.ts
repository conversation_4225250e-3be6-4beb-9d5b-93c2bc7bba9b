import type { HttpContext } from '@adonisjs/core/http'
import { checkFileOrUrl, numberFormat, numberFormatRpLengkap, numberFormatRpLengkapEn, numberFormatRpSingkatan, rupiah, satuanNumber, satuanNumberEn, titleCase } from '../helpers/global_helper.js'
import { Application } from '@adonisjs/core/app'
import { Env } from '@adonisjs/core/env'
import env from '#start/env'
import { isTemplateExpression } from 'typescript'
import prisma from '../lib/prisma.js'

import { request } from 'node:http'
import { arrayBuffer } from 'node:stream/consumers'
import { DateTime } from "luxon";
import { FileWriter } from 'csv-writer/src/lib/file-writer.js'
import requestIp from 'request-ip';
import { dasboardNDValidator } from '#validators/dashboard'


export default class DashboardController {
    /**
     * @index
     * @summary List Data Referensi Sektor Unggulan (Dropdown Sektor Unggulan Nasional)
     */
    public async get_user_dashboard({ params,response }:HttpContext) {
      const users = await prisma.users.findMany({
        where: {
          role_id: {
            in: [14, 17],
          },
          first_name: {
            not: null,
          },
        },
        include: {
          roles: {
            select: {
              role_name: true,
            },
          },
          tb_user_internal: {
            include: {
              tb_ref_user_jabatan: {
                select: {
                  nama: true,
                },
              },
            },
          },
          tb_user_internal_provinsi: {
            include: {
              tb_adm_provinsi: {
                select: {
                  nama: true,
                },
              },
            },
          },
          tb_user_internal_kawasan_industri: {
            include: {
              tb_kawasan_industri: {
                select: {
                  nama: true,
                },
              },
            },
          },
        },
      });
      
      // Memproses hasil untuk agregasi (STRING_AGG)
      const user = users
        .map((user) => ({
          id: user.id,
          nama: `${user.first_name} ${user.middle_name || ''} ${user.last_name || ''}`.trim(),
          jabatan: user.tb_user_internal
            ?.map((internal) => internal.tb_ref_user_jabatan?.nama)
            .filter((nama) => nama !== null) // Hilangkan null pada jabatan
            .join(', ') || null, // Gabungkan jabatan jika ada lebih dari satu
          role_name: user.roles?.role_name || null,
          email: user.email || null,
          provinsi: Array.from(
            new Set(user.tb_user_internal_provinsi?.map((prov) => prov.tb_adm_provinsi?.nama) || [])
          )
            .sort()
            .join(', '),
          kawasan: Array.from(
            new Set(
              user.tb_user_internal_kawasan_industri?.map((ki) => ki.tb_kawasan_industri?.nama) || []
            )
          )
            .sort()
            .join(', '),
        }))
        .filter((user) => user.jabatan); 

      return {
        sucess:true,
        data:user
      }


    }
    public async get_dashboard({ params,response }:HttpContext) {
            
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;

      // Jika params.startDate atau params.endDate kosong, set default nilai
      const now = new Date();
      const defaultStartDate = new Date(now.getFullYear(), 0, 1); // 1 Januari tahun ini
      const defaultEndDate = now; // Tanggal hari ini

      const startDateString = params.startDate || defaultStartDate.toISOString().split('T')[0];
      const endDateString = params.endDate || defaultEndDate.toISOString().split('T')[0];

      // Validasi format tanggal
      if (!dateRegex.test(startDateString) || !dateRegex.test(endDateString)) {
        throw new Error('Invalid date format. Expected format is YYYY-MM-DD');
      }

      const startDate = new Date(startDateString);
      const endDate = new Date(endDateString);

      // Validasi apakah tanggal valid
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new Error('Invalid date provided for startDate or endDate.');
      }
        try {
            

            const pengunjungs = await prisma.tb_halaman_pengunjung.findMany({
              select:{
                nama:true,
                halaman_pengunjung_det:{
                    select:{
                        ip_pengunjung:true
                    }
                }
            },
            where: {
                id_halaman_pengunjung: {
                in: [1, 3, 5, 7],
                },
                halaman_pengunjung_det: {
                  some: {
                      created_date: {
                      gte: startDate, // Gunakan variabel startDate
                      lt: endDate,    // Gunakan variabel endDate
                      },
                  },
                  },
              },
            });

            // Menghitung jumlah pengunjung per nama
            const pengunjung = pengunjungs.map((item) => ({
              nama: titleCase(item.nama),
              jumlah_pengunjung: item.halaman_pengunjung_det.length,
            }));

            const unduhDatas = await prisma.tb_unduh_data.groupBy({
                by: ['asal_negara'],
                where: {
                  created_date: {
                    gte: startDate,
                    lt: endDate,
                  },
                },
                _count: {
                  asal_negara: true, // Hitung total unduhan berdasarkan asal_negara
                },
              });
              console.log('unduh datas');
              console.log(unduhDatas);
              
              
              
              // Urutkan hasil di sisi aplikasi
              const unduhData = unduhDatas
                .map((item) => ({
                  negara: item.asal_negara,
                  jumlah: item._count.asal_negara,
                }))
                .sort((a, b) => b.jumlah - a.jumlah)
                .slice(0,10);
            
                // const pengunjungProv = await prisma.$queryRaw`
                //     WITH provinsi_counts AS (
                //         SELECT 
                //             b.id_adm_provinsi,
                //             b.nama,
                //             COUNT(*) as direct_count
                //         FROM tb_halaman_pengunjung_det a
                //         JOIN tb_adm_provinsi b ON a.id_konten = b.id_adm_provinsi
                //         WHERE a.id_halaman_pengunjung = 5 
                //             AND a.created_date >= ${startDate}
                //             AND a.created_date < ${endDate}
                //         GROUP BY b.id_adm_provinsi, b.nama
                //     ),
                //     kabkot_counts AS (
                //         -- Hitung akses ke kabupaten/kota
                //         SELECT 
                //             LEFT(CAST(a.id_konten AS TEXT), 2)::INTEGER as id_prov,
                //             p.nama,
                //             COUNT(*) as kabkot_count
                //         FROM tb_halaman_pengunjung_det a
                //         JOIN tb_adm_kabkot k ON a.id_konten = k.id_adm_kabkot
                //         JOIN tb_adm_provinsi p ON LEFT(CAST(k.id_adm_kabkot AS TEXT), 2)::INTEGER = p.id_adm_provinsi
                //         WHERE a.id_halaman_pengunjung = 5 
                //             AND a.created_date >= ${startDate}
                //             AND a.created_date < ${endDate}
                //         GROUP BY LEFT(CAST(a.id_konten AS TEXT), 2)::INTEGER, p.nama
                //     )
                //     SELECT 
                //         COALESCE(pc.nama, kc.nama) as nama,
                //         CAST(COALESCE(pc.direct_count, 0) + COALESCE(kc.kabkot_count, 0) AS INTEGER) as jumlah,
                //         CAST(COALESCE(pc.direct_count, 0) AS INTEGER) as jumlah_provinsi,
                //         CAST(COALESCE(kc.kabkot_count, 0) AS INTEGER) as jumlah_kabkot
                //     FROM provinsi_counts pc
                //     FULL OUTER JOIN kabkot_counts kc ON pc.id_adm_provinsi = kc.id_prov
                //     ORDER BY jumlah DESC
                //     LIMIT 10;
                // `;

                const pengunjungProv = await prisma.$queryRaw`
                        SELECT 
                        p.nama,
                        CAST(SUM(CASE WHEN a.id_konten = p.id_adm_provinsi THEN 1 ELSE 0 END) AS INTEGER) as jumlah_provinsi,
                        CAST(SUM(CASE WHEN a.id_konten != p.id_adm_provinsi THEN 1 ELSE 0 END) AS INTEGER) as jumlah_kabkot,
                        CAST(COUNT(*) AS INTEGER) as jumlah
                    FROM tb_halaman_pengunjung_det a
                    JOIN tb_adm_provinsi p ON 
                        p.id_adm_provinsi = CASE 
                            WHEN a.id_halaman_pengunjung = 5 AND EXISTS (SELECT 1 FROM tb_adm_provinsi WHERE id_adm_provinsi = a.id_konten) 
                                THEN a.id_konten
                            ELSE LEFT(CAST(a.id_konten AS TEXT), 2)::INTEGER
                        END
                    WHERE a.id_halaman_pengunjung = 5
                        AND a.created_date >= ${startDate}
                        AND a.created_date < ${endDate}
                        AND p.nama IS NOT NULL
                    GROUP BY p.nama
                    ORDER BY jumlah DESC
                    LIMIT 10;
                `;
                
            //     const pengunjungProvAll = await prisma.$queryRaw`
            //     WITH provinsi_counts AS (
            //         -- Hitung akses langsung ke provinsi
            //         SELECT 
            //             b.id_adm_provinsi,
            //             b.nama,
            //             COUNT(*) as direct_count
            //         FROM tb_halaman_pengunjung_det a
            //         JOIN tb_adm_provinsi b ON a.id_konten = b.id_adm_provinsi
            //         WHERE a.id_halaman_pengunjung = 5 
            //             AND a.created_date >= ${startDate}
            //             AND a.created_date < ${endDate}
            //         GROUP BY b.id_adm_provinsi, b.nama
            //     ),
            //     kabkot_counts AS (
            //         -- Hitung akses ke kabupaten/kota
            //         SELECT 
            //             LEFT(CAST(a.id_konten AS TEXT), 2)::INTEGER as id_prov,
            //             p.nama,
            //             COUNT(*) as kabkot_count
            //         FROM tb_halaman_pengunjung_det a
            //         JOIN tb_adm_kabkot k ON a.id_konten = k.id_adm_kabkot
            //         JOIN tb_adm_provinsi p ON LEFT(CAST(k.id_adm_kabkot AS TEXT), 2)::INTEGER = p.id_adm_provinsi
            //         WHERE a.id_halaman_pengunjung = 5 
            //             AND a.created_date >= ${startDate}
            //             AND a.created_date < ${endDate}
            //         GROUP BY LEFT(CAST(a.id_konten AS TEXT), 2)::INTEGER, p.nama
            //     )
            //     SELECT 
            //         COALESCE(pc.nama, kc.nama) as nama,
            //         CAST(COALESCE(pc.direct_count, 0) + COALESCE(kc.kabkot_count, 0) AS INTEGER) as jumlah,
            //         CAST(COALESCE(pc.direct_count, 0) AS INTEGER) as jumlah_provinsi,
            //         CAST(COALESCE(kc.kabkot_count, 0) AS INTEGER) as jumlah_kabkot
            //     FROM provinsi_counts pc
            //     FULL OUTER JOIN kabkot_counts kc ON pc.id_adm_provinsi = kc.id_prov
            //     ORDER BY jumlah DESC
            //     ;
            // `;

            const pengunjungProvAll = await prisma.$queryRaw`
              SELECT 
                  p.nama,
                  CAST(SUM(CASE 
                      WHEN a.id_halaman_pengunjung = 5 AND EXISTS (
                          SELECT 1 FROM tb_adm_provinsi WHERE id_adm_provinsi = a.id_konten
                      ) THEN 1 
                      ELSE 0 
                  END) AS INTEGER) as jumlah_provinsi,
                  CAST(SUM(CASE 
                      WHEN a.id_halaman_pengunjung = 5 AND EXISTS (
                          SELECT 1 FROM tb_adm_kabkot WHERE id_adm_kabkot = a.id_konten
                      ) THEN 1 
                      ELSE 0 
                  END) AS INTEGER) as jumlah_kabkot,
                  CAST(COUNT(*) AS INTEGER) as jumlah
              FROM tb_halaman_pengunjung_det a
              JOIN tb_adm_provinsi p ON 
                  p.id_adm_provinsi = CASE 
                      WHEN EXISTS (SELECT 1 FROM tb_adm_provinsi WHERE id_adm_provinsi = a.id_konten) 
                          THEN a.id_konten
                      ELSE LEFT(CAST(a.id_konten AS TEXT), 2)::INTEGER
                  END
              WHERE a.id_halaman_pengunjung = 5
                  AND a.created_date >= ${startDate}
                  AND a.created_date < ${endDate}
              GROUP BY p.nama
              ORDER BY jumlah DESC
            `;

                const pengunjungPeluang = await prisma.$queryRaw`
                    SELECT b.nama, cast(COUNT(*) as integer) AS jumlah
                    FROM tb_halaman_pengunjung_det a
                    JOIN tb_peluang_kabkot b 
                        ON a.id_konten = b.id_peluang_kabkot AND b.id_prioritas = 1
                    WHERE a.id_halaman_pengunjung = 7
                        AND a.created_date >= ${startDate}
                        AND a.created_date < ${endDate}
                    GROUP BY b.id_peluang_kabkot
                    ORDER BY jumlah DESC
                    LIMIT 10;
                    `;
                // const pengunjungPeluangProv = await prisma.$queryRaw`
                // SELECT tap.nama, cast(COUNT(*) as integer) AS jumlah
                // FROM tb_halaman_pengunjung_det a
                // JOIN tb_peluang_kabkot b 
                // ON a.id_konten = b.id_peluang_kabkot AND b.id_prioritas = 1
                // JOIN tb_adm_kabkot tak 
                // ON b.id_adm_kabkot = tak.id_adm_kabkot
                // JOIN tb_adm_provinsi tap 
                // on tap.id_adm_provinsi = tak.id_adm_provinsi
                // WHERE a.id_halaman_pengunjung = 7
                // AND a.created_date >= ${startDate}
                // AND a.created_date < ${endDate}
                // GROUP BY tap.id_adm_provinsi
                // ORDER BY jumlah DESC
                // LIMIT 10;
                // `;
                
                const pengunjungPeluangProv = await prisma.$queryRaw`
                WITH unique_visitors AS (
                  SELECT DISTINCT
                  tap.nama as nama_provinsi,
                  thpd.ip_pengunjung
                  FROM tb_halaman_pengunjung_det thpd 
                  JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = thpd.id_konten
                  WHERE thpd.id_halaman_pengunjung = 5
                )
                SELECT 
                nama_provinsi,
                COUNT(*) as jumlah
                FROM unique_visitors
                GROUP BY nama_provinsi
                ORDER BY jumlah DESC;
                `;
                  
            //   console.log(processedUsers);
               
            return { 
              success : true,
              data : {
                  pengunjung_menu : pengunjung,
                  pengunjung_provinsi : pengunjungProv,
                  pengunjung_provinsi_all : pengunjungProvAll,
                  pengunjung_peluang : pengunjungPeluang,
                  unduh_data : unduhData,
                }
              }
           
        } catch (error) {
            console.error(error);
            return response.status(500).json({
                status: 'error',
                message: error.message,
            });
        }
    }

    public async get_status_infrastruktur({ params,response }:HttpContext) {
      const Id = parseInt(params.id_user, 10)
      // if (isNaN(Id)) {
      //     return response.status(400).send({ error: 'Invalid ID provided' })
      // }
      let userProv : number[] =[]
      let optWhere = {}
      console.log('Id');
      console.log(Id);
      
      if (!isNaN(Id)) {
          const user = await prisma.users.findFirst({
            where:{
              id:Id
            },
            include:{
              tb_user_internal_provinsi: {
                include: {
                  tb_adm_provinsi: {
                  select: {
                    nama: true,
                  },
                },
              },
            },
          }
        })
        if(Array.isArray(user?.tb_user_internal_provinsi)){
                user?.tb_user_internal_provinsi.map((item) => {
                  userProv.push(item.id_adm_provinsi)
                })
        }
      }
      const currentYear = new Date().getFullYear()-2; // Mendapatkan tahun saat ini

      // Daftar tabel yang ingin dihitung
      const tables = [
        {table : "tb_bandara_status" , id: "id_bandara"},
        {table : "tb_hotel_status" , id: "id_hotel"},
        {table : "tb_pendidikan_status" , id: "id_pendidikan"},
        {table : "tb_pelabuhan_status" , id: "id_pelabuhan"},
        {table : "tb_rumah_sakit_status" , id: "id_rumah_sakit"},
        {table : "tb_sektor_daerah_status" , id: "id_sektor_daerah"},
        {table : "tb_sub_sektor_daerah_status" , id: "id_sub_sektor_daerah"},
        {table : "tb_peluang_daerah_status" , id: "id_peluang_daerah"},
        {table : "tb_peluang_kabkot_status", id: "id_peluang_kabkot"},

      ];
      // Fungsi untuk menghitung data berdasarkan kondisi
      const countByConditions = async (tableName: string,id:string) => {
        const tab = tableName.replace('_status', ''); // Misalnya, 'users_status' menjadi 'users'
        const startDate = new Date(`${currentYear}-01-01`)
        const endDate = new Date(`${currentYear + 1}-01-01`)

        const counts = await Promise.all([
          
          // Kondisi 1: status = 0
          prisma[tableName].count({
            where: {
              status: 0,
              // OR: userProv.map((prov) => ({
              //   [tab]: { // Gunakan computed property name untuk kunci dinamis
              //     id_adm_kabkot: {
              //       gte: Number(`${prov}00`),
              //       lt: Number(`${prov}99`),
              //     },
              //   },
              // })),
            },
          }),
          // Kondisi 2: status = 0 dan created_date pada tahun ini
          prisma[tableName].groupBy({
            by: [id],
            where: {
              status: {
                not: -99,
              },
              OR: [
                {
                  updated_date: {
                    not: null,
                    gte: new Date(`${currentYear}-01-01`),
                    lt: new Date(`${currentYear + 1}-01-01`),
                  },
                },
                {
                  updated_date: null,
                  created_date: {
                    gte: new Date(`${currentYear}-01-01`),
                    lt: new Date(`${currentYear + 1}-01-01`),
                  },
                },
              ],
            },
          }),
          
          // Kondisi 3: status = 0 dan created_date sebelum tahun ini
          prisma[tableName].groupBy({
            by: [id],
            where: {
              status: 0,
              OR: [
                {
                  updated_date: null,
                  created_date: {
                    lt: new Date(`${currentYear}-01-01`),
                  },
                },
                {
                  updated_date: {
                    not: null,
                    lt: new Date(`${currentYear}-01-01`),
                  },
                },
              ],
              // OR: userProv.map((prov) => ({
              //   [tab]: {
              //     id_adm_kabkot: {
              //       gte: Number(`${prov}00`),
              //       lt: Number(`${prov}99`),
              //     },
              //   },
              // })),
            },
          }),
        ]);
      
        return counts;
      };
      // Loop melalui semua tabel dan hitung data
      const results = [];
      for (const table of tables) {
        // Hilangkan prefix "tb_" dan suffix "_status" dari nama tabel
        const formattedName = table.table.replace(/^tb_/, "",).replace(/_status$/, "").replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase());
        const id = table.id
        // Ambil data berdasarkan kondisi dan tambahkan nama tabel ke hasil
        const counts = await countByConditions(table.table,id);
        console.log('count');
        console.log(counts);
        
        results.push({
          param_nama:table.table,
          nama: formattedName,
          total:counts[0],
          update:counts[1].length,
          belum_update:counts[2].length,
        });
      }

      const ki = await Promise.all([
        // Kondisi 1: status = 0
        prisma.tb_kawasan_industri.count({
          where: {
            tb_kawasan_industri_status:{
                some:{
                  status:0
                }
            },
            id_kategori:3,
            status:{
              not:'-99'
            }
          },
        }),
        // Kondisi 2: status = 0 dan created_date pada tahun ini
        prisma.tb_kawasan_industri.count({
          where: {
            tb_kawasan_industri_status:{
              some:{
                status: 0,
                created_date: {
                  gte: new Date(`${currentYear}-01-01`), // Awal tahun sekarang
                  lt: new Date(`${currentYear + 1}-01-01`), // Awal tahun berikutnya
                }
              }
            },
            id_kategori:3,
            status:{
              not:'-99'
            }
          },
        }),
        // Kondisi 3: status = 0 dan created_date sebelum tahun ini
        prisma.tb_kawasan_industri.count({
          where: {
            tb_kawasan_industri_status:{
              some:{
                status: 0,
                created_date: {
                  lt: new Date(`${currentYear}-01-01`), // Sebelum awal tahun sekarang
              },
              },
            },
            id_kategori:3,
            status:{
              not:'-99'
            }
          },
        }),
      ]);

      const kek = await Promise.all([
        // Kondisi 1: status = 0
        prisma.tb_kawasan_industri.count({
          where: {
            tb_kawasan_industri_status:{
                some:{
                  status:0
                }
            },
            id_kategori:1,
            status:{
              not:'-99'
            }
          },
        }),
        // Kondisi 2: status = 0 dan created_date pada tahun ini
        prisma.tb_kawasan_industri.count({
          where: {
            tb_kawasan_industri_status:{
              some:{
                status: 0,
                created_date: {
                  gte: new Date(`${currentYear}-01-01`), // Awal tahun sekarang
                  lt: new Date(`${currentYear + 1}-01-01`), // Awal tahun berikutnya
                }
              }
            },
            id_kategori:1,
            status:{
              not:'-99'
            }
          },
        }),
        // Kondisi 3: status = 0 dan created_date sebelum tahun ini
        prisma.tb_kawasan_industri.count({
          where: {
            tb_kawasan_industri_status:{
              some:{
                status: 0,
                created_date: {
                  lt: new Date(`${currentYear}-01-01`), // Sebelum awal tahun sekarang
              },
              },
            },
            id_kategori:1,
            status:{
              not:'-99'
            }
          },
        }),
      ]);

      results.push({
        param_nama: 'kawasan_industri_status',
        nama: 'Kawasan Industri',
        total: ki[0],
        update: ki[1],
        belum_update: ki[2],
      });
      
      results.push({
        param_nama: 'kawasan_industri_status_kek',
        nama: 'Kawasan Ekonomi Khusus (KEK)',
        total: kek[0],
        update: kek[1],
        belum_update: kek[2],
      });


      return results
    }

    public async get_pic_data({params,response}:HttpContext){
      const Id = parseInt(params.id_user, 10)
      if (isNaN(Id)) {
          return response.status(400).send({ error: 'Invalid ID provided' })
      }
      const user = await prisma.users.findFirst({
        where:{
          id:Id
        },
        include:{
          tb_user_internal_provinsi: {
            include: {
              tb_adm_provinsi: {
                select: {
                  nama: true,
                },
              },
            },
          },
        }
      })
      
      const indikators = await prisma.tb_indikator_pic.findMany({
        where:{
          nama_tabel:{
            not:null
          }
        }
      })
      const indikatorKabkot = [
       
        {
          "id_indikator_pic": 2,
          "nama_indikator": "Bandara",
          "nama_tabel": "tb_bandara_status"
        },
        {
          "id_indikator_pic": 4,
          "nama_indikator": "Hotel",
          "nama_tabel": "tb_hotel_status"
        },
                {
          "id_indikator_pic": 10,
          "nama_indikator": "Pelabuhan",
          "nama_tabel": "tb_pelabuhan_status"
        },
                {
          "id_indikator_pic": 12,
          "nama_indikator": "Pendidikan",
          "nama_tabel": "tb_pendidikan_status"
        },
                {
          "id_indikator_pic": 15,
          "nama_indikator": "Rumah Sakit",
          "nama_tabel": "tb_rumah_sakit_status"
        },
      ]
      const indikatorProv = [
       
        {
          "id_indikator_pic": 18,
          "nama_indikator": "UMR",
          "nama_tabel": "tb_umr_provinsi"
        },
        {
          "id_indikator_pic": 6,
          "nama_indikator": "Menara BTS",
          "nama_tabel": "tb_utilitas_provinsi"
        },
        {
          "id_indikator_pic": 8,
          "nama_indikator": "Panjang Jalan",
          "nama_tabel": "tb_utilitas_provinsi"
        },
        {
          "id_indikator_pic": 3,
          "nama_indikator": "Daya Terpasang",
          "nama_tabel": "tb_utilitas_provinsi"
        },
      ]
      const data = { ada: [], tidak_ada: [],indikator_prov:[] };
      const currentYear = DateTime.now().year; // Ambil tahun saat ini (misal: 2025)
      await Promise.all(
        indikatorKabkot.map(async (item) => {
          if (item.nama_tabel && Array.isArray(user?.tb_user_internal_provinsi) && user.tb_user_internal_provinsi.length > 0) {
            try {
              const tableName = item.nama_tabel; 
              const includeTable = tableName.replace(/_status$/, ""); 

              const mappedData = await Promise.all(user.tb_user_internal_provinsi.map(async (item2) => {
                
                  const datas = await prisma[item.nama_tabel].findMany({
                  take: 1,
                  where: {
                    created_date: {
                      gte: new Date(`${currentYear}-01-01T00:00:00.000Z`), // Awal tahun
                      lt: new Date(`${currentYear + 1}-01-01T00:00:00.000Z`), // Awal tahun berikutnya
                    },
                    [includeTable]:{
                      id_adm_kabkot: {
                        gte: parseInt(`${item2.id_adm_provinsi}00`),
                        lt: parseInt(`${item2.id_adm_provinsi}99`)
                      },
                    }
                  },
                  include: {
                    [includeTable]: true
                  },
                });
                // console.log(includeTable);
                
                const dataPush = {
                    id_indikator_pic: item.id_indikator_pic,
                    nama_tabel: item.nama_tabel,
                    indikator: item.nama_indikator,
                    kd_prov: item2.id_adm_provinsi,
                    nama_prov: item2.tb_adm_provinsi.nama,
                      status: datas.length > 0 ? 'Ada' : 'Tidak Ada'
                    }
                    if (datas.length > 0) {
                      data.ada.push(dataPush);
                      const exist = data.indikator_prov.find(entry => entry.kd_prov === item2.id_adm_provinsi);
                      const jmlPush ={
                        nama_prov: item2.tb_adm_provinsi.nama,
                        kd_prov: item2.id_adm_provinsi,
                        ada : 1,
                        tidak_ada:17

                      }
                      if (exist) {
                        // Jika ada, tingkatkan count
                        exist.ada++;
                        exist.tidak_ada--
                        exist.persen_ada = parseFloat(((exist.ada/18)*100).toFixed(2))
                        exist.persen_tidak_ada = parseFloat(((exist.tidak_ada/18)*100).toFixed(2))


                      } else {
                        data.indikator_prov.push(jmlPush);
                      }
                    } else {
                      data.tidak_ada.push(dataPush);
                    }
              }));
              
            } catch (error) {
              console.error(`Error fetching data for table ${item.nama_tabel}:`, error);
            }
          }
        })
      );
      await Promise.all(
        indikatorProv.map(async (item) => {
          if (item.nama_tabel && Array.isArray(user?.tb_user_internal_provinsi) && user.tb_user_internal_provinsi.length > 0) {
            try {
              // const tableName = item.nama_tabel; 
              // const includeTable = tableName.replace(/_status$/, ""); 

              const mappedData = await Promise.all(user.tb_user_internal_provinsi.map(async (item2) => {
                
                const datas = await prisma[item.nama_tabel].findMany({
                  take: 1,
                  where: {
                    id_adm_provinsi:item2.id_adm_provinsi,
                    tahun:currentYear
                  },
                  
                });
                // console.log(includeTable);
                
                const dataPush = {
                      id_indikator_pic: item.id_indikator_pic,
                      nama_tabel: item.nama_tabel,
                      indikator: item.nama_indikator,
                      kd_prov: item2.id_adm_provinsi,
                      nama_prov: item2.tb_adm_provinsi.nama,
                      status: datas.length > 0 ? 'Ada' : 'Tidak Ada'
                    }
                    if (datas.length > 0) {
                      data.ada.push(dataPush);
                      const exist = data.indikator_prov.find(entry => entry.kd_prov === item2.id_adm_provinsi);
                      const jmlPush ={
                        nama_prov: item2.tb_adm_provinsi.nama,
                        kd_prov: item2.id_adm_provinsi,
                        ada : 1,
                        tidak_ada:17

                      }
                      if (exist) {
                        // Jika ada, tingkatkan count
                        exist.ada++;
                        exist.tidak_ada--
                        exist.persen_ada = parseFloat(((exist.ada/18)*100).toFixed(2))
                        exist.persen_tidak_ada = parseFloat(((exist.tidak_ada/18)*100).toFixed(2))
                      } else {
                        data.indikator_prov.push(jmlPush);
                      }
                    } else {
                      data.tidak_ada.push(dataPush);
                    }
              }));
              
            } catch (error) {
              console.error(`Error fetching data for table ${item.nama_tabel}:`, error);
            }
          }
        })
      );

      if (user) {
        
        const dataPopulasi = await Promise.all(user.tb_user_internal_provinsi.map(async (item2) => {
          
          const datas = await prisma.tb_demografi_provinsi.findMany({
            take: 1,
            where: {
              id_adm_provinsi:item2.id_adm_provinsi,
              tahun:currentYear,
              id_kategori:1
            },
            
          });
          
          const dataPush = {
            id_indikator_pic: 5,
            indikator: 'Jumlah Populasi',
            nama_prov: item2.tb_adm_provinsi.nama,
            kd_prov: item2.id_adm_provinsi,
            status: datas.length > 0 ? 'Ada' : 'Tidak Ada'
          }

          
          if (datas.length > 0) {
            data.ada.push(dataPush);
            const jmlPush ={
              nama_prov: item2.tb_adm_provinsi.nama,
              kd_prov: item2.id_adm_provinsi,
              ada : 1,
              tidak_ada:17

            }
            const exist = data.indikator_prov.find(entry => entry.kd_prov === item2.id_adm_provinsi);

            if (exist) {
              // Jika ada, tingkatkan count
              exist.ada++;
              exist.tidak_ada--
              exist.persen_ada = parseFloat(((exist.ada/18)*100).toFixed(2))
              exist.persen_tidak_ada = parseFloat(((exist.tidak_ada/18)*100).toFixed(2))
            } else {
              data.indikator_prov.push(jmlPush);
            }
          } else {
            data.tidak_ada.push(dataPush);
          }
        }));

        const dataAngkatanKerja = await Promise.all(user.tb_user_internal_provinsi.map(async (item2) => {
          
          const datas = await prisma.tb_demografi_provinsi.findMany({
            take: 1,
            where: {
              id_adm_provinsi:item2.id_adm_provinsi,
              tahun:currentYear,
              id_kategori:{
                not:1
              }
            },
            
          });
          // console.log(includeTable);
          
          const dataPush = {
            id_indikator_pic: 1,
            indikator: 'Angkatan Kerja',
            nama_prov: item2.tb_adm_provinsi.nama,
            kd_prov: item2.id_adm_provinsi,
            status: datas.length > 0 ? 'Ada' : 'Tidak Ada',
            
          }
          if (datas.length > 0) {
            data.ada.push(dataPush);
          } else {
            data.tidak_ada.push(dataPush);
          }
        }));
      }

      const mapping = await prisma.tb_mapping_indikator_pic.findMany({
        where: {
          tahun: currentYear,
          id_user: Id,
        },
      });
      
      const filteredMapping = Object.values(
        mapping.reduce((acc, item) => {
          const key = `${item.kd_prov}-${item.id_indikator_pic}`;
          if (!acc[key]) {
            acc[key] = item;
          }
          return acc;
        }, {})
      );
      
      const uniqueCombinations = new Set(
        data.ada.reduce((acc, item) => {
          const key = `${item.kd_prov}-${item.id_indikator_pic}`;
          if (!acc.includes(key)) {
            acc.push(key);
          }
          return acc;
        }, [])
      );
      
      const filteredMappedData = filteredMapping.filter((item) => {
        const key = `${item.kd_prov}-${item.id_indikator_pic}`;
        return !uniqueCombinations.has(key);
      });
      
      // Optimasi dengan Set untuk pencarian cepat
      const mappedKeys = new Set(
        filteredMappedData.map((item) => `${item.kd_prov}-${item.id_indikator_pic}`)
      );
      
      if (!data.tidak_ada) {
        data.tidak_ada = [];
      }
      
      // Modifikasi data.tidak_ada untuk menambahkan flag dan id_mapping_indikator_pic
      data.tidak_ada = data.tidak_ada.map((item) => {
        const key = `${item.kd_prov}-${item.id_indikator_pic}`;
        if (mappedKeys.has(key)) {
          // Cari objek yang cocok di filteredMappedData untuk mendapatkan id_mapping_indikator_pic
          const matchedItem = filteredMappedData.find((mappedItem) => {
            const mappedKey = `${mappedItem.kd_prov}-${mappedItem.id_indikator_pic}`;
            return mappedKey === key;
          });
          return {
            ...item,
            flag: true,
            id_mapping_indikator_pic: matchedItem?.id_mapping_indikator_pic, // Ambil id_mapping_indikator_pic dari matchedItem
          };
        }
        return { ...item, flag: false };
      });
      
      const indikatorTidakAdaData = 0
      const indikatorAda = data.ada.length+indikatorTidakAdaData+filteredMappedData.length 
      const totalIndikator = data.ada.length + data.tidak_ada.length
      const percent = ((indikatorAda/totalIndikator)*100).toFixed(2)
      data.total_ada=data.ada.length
      data.total_tidak_ada=data.tidak_ada.length
      data.total=totalIndikator
      // data.total_tidak_ada_data=0
      data.persentase_ada=parseFloat(percent)
      data.persentase_tidak_ada=100-percent

      
      return {
        success : true,
        data :data
    }

    }

    async destroy_mapped({ params,response }: HttpContext) {
        const Id = parseInt(params.id, 10)
        if (isNaN(Id)) {
          return response.status(400).send({ error: 'Invalid ID provided' })
        }
    
        try {
          const deletePost = await prisma.tb_mapping_indikator_pic.delete({
            where: {
              id_mapping_indikator_pic: Id,
            },
          })
          
          return response.status(200).send({ success: true,data:deletePost})
        } catch (error) {
          return response.status(500).send({ error: 'Error deleting data' })
        }
    }
    



    public async get_status_data_prov({params,response}:HttpContext){
      const Id = parseInt(params.id_user, 10)
      // if (isNaN(Id)) {
      //     return response.status(400).send({ error: 'Invalid ID provided' })
      // }
      let userProv : number[] =[]
      let optWhere = {}
      
      if (!Number.isNaN(Id)) {
          const user = await prisma.users.findFirst({
            where:{
              id:Id
            },
            include:{
              tb_user_internal_provinsi: {
                include: {
                  tb_adm_provinsi: {
                  select: {
                    nama: true,
                  },
                },
              },
            },
          }
        })
        if(Array.isArray(user?.tb_user_internal_provinsi)){
                user?.tb_user_internal_provinsi.map((item) => {
                  userProv.push(item.id_adm_provinsi)
                })
                
        }
      }
      let data =[]
      let tahun : number[]= []
      tahun.push(DateTime.now().year-2); 
      tahun.push(DateTime.now().year-1); 
      tahun.push(DateTime.now().year); 
      const prov = await prisma.tb_adm_provinsi.findMany({
        where:{
          ...(userProv.length > 0 && {
            id_adm_provinsi: {
              in: userProv,
            },
          }),
        }
      })

      const provLength = prov.length

      const resultDemografi =  await Promise.all(
        
        tahun.map(async(item) => {
          const datass = await prisma.tb_demografi_provinsi.findMany({
            select:{
              id_adm_provinsi:true
            },  
            where:{
              tahun:item,
              status:{
                not:-99
              },
              id_kategori:1,
              ...(userProv.length > 0 && {
                id_adm_provinsi: {
                  in: userProv,
                },
              }),
            },
          })

          let datas = Array.from(new Set(datass.map(data => data.id_adm_provinsi)))
          .map(id => ({ id_adm_provinsi: id }));

          if (Id) {
            datas = datas.filter(item => userProv.includes(item.id_adm_provinsi))
          }
          

          return {
            table:'tb_demografi_provinsi_status',
            tahun:item,
            total:provLength,
            ada:datas.length,
            tidak_ada:provLength-datas.length,
            persen_ada:parseFloat(((datas.length/provLength)*100).toFixed(2)),
            persen_tidak_ada:parseFloat((((provLength-datas.length)/provLength)*100).toFixed(2))
          }
        })
      )
      

      const resultEksporimpor =  await Promise.all(
        tahun.map(async(item) => {
          console.log(item);
          
          const datass = await prisma.tb_ekspor_provinsi.findMany({
              select:{
                id_adm_provinsi:true
              },  
              where:{
              tahun:item,
              status:{
                not:-99
              },
              ...(userProv.length > 0 && {
                id_adm_provinsi: {
                  in: userProv,
                },
              }),
            },

          })
          let datas = Array.from(new Set(datass.map(data => data.id_adm_provinsi)))
          .map(id => ({ id_adm_provinsi: id }));

          if (Id) {
            datas = datas.filter(item => userProv.includes(item.id_adm_provinsi))
          }
          return {
            table:'tb_ekspor_provinsi_status',
            tahun:item,
            total:provLength,
            ada:datas.length,
            tidak_ada:provLength-datas.length,
            persen_ada:parseFloat(((datas.length/provLength)*100).toFixed(2)),
            persen_tidak_ada:parseFloat((((provLength-datas.length)/provLength)*100).toFixed(2))
          }
        })
      )

      const resultRealisasi =  await Promise.all(
        tahun.map(async(item) => {
          console.log(item);
          
          const datass = await prisma.tb_investasi_provinsi.findMany({
              select:{
                id_adm_provinsi:true
              },  
              where:{
              tahun:item,
              status:{
                not:-99
              },
              ...(userProv.length > 0 && {
                id_adm_provinsi: {
                  in: userProv,
                },
              }),
            },

          })
          let datas = Array.from(new Set(datass.map(data => data.id_adm_provinsi)))
          .map(id => ({ id_adm_provinsi: id }));

          if (Id) {
            datas = datas.filter(item => userProv.includes(item.id_adm_provinsi))
          }
          return {
            table:'tb_investasi_provinsi',
            tahun:item,
            total:provLength,
            ada:datas.length,
            tidak_ada:provLength-datas.length,
            persen_ada:parseFloat(((datas.length/provLength)*100).toFixed(2)),
            persen_tidak_ada:parseFloat((((provLength-datas.length)/provLength)*100).toFixed(2))
          }
        })
      )

      const resultKomoditi =  await Promise.all(
        tahun.map(async(item) => {
          console.log(item);
          
          const datass = await prisma.tb_komoditi_provinsi.findMany({
                select:{
                  id_adm_provinsi:true
                },
                where:{
                  tahun:item,
                  status:{
                    not:-99
                  },
                  ...(userProv.length > 0 && {
                    id_adm_provinsi: {
                      in: userProv,
                    },
                  }),
                },

          })

          let datas = Array.from(new Set(datass.map(data => data.id_adm_provinsi)))
          .map(id => ({ id_adm_provinsi: id }));
          
          if (Id) {
            datas = datas.filter(item => userProv.includes(item.id_adm_provinsi))
          }
          return {
            table:'tb_komoditi_provinsi',
            tahun:item,
            total:provLength,
            ada:datas.length,
            tidak_ada:provLength-datas.length,
            persen_ada:parseFloat(((datas.length/provLength)*100).toFixed(2)),
            persen_tidak_ada:parseFloat((((provLength-datas.length)/provLength)*100).toFixed(2))
          }
        })
      )
      const resultPDRB =  await Promise.all(
        tahun.map(async(item) => {
          console.log(item);
          
          const datass = await prisma.tb_sektor_daerah_pdrb.findMany({
                select:{
                  id_adm_provinsi:true
                },
                where:{
                  tahun_pdrb:item,
                  status:{
                    not:-99
                  },
                  ...(userProv.length > 0 && {
                    id_adm_provinsi: {
                      in: userProv,
                    },
                  }),
                },

          })

          let datas = Array.from(new Set(datass.map(data => data.id_adm_provinsi)))
          .map(id => ({ id_adm_provinsi: id }));
          
          if (Id) {
            datas = datas.filter(item => userProv.includes(item.id_adm_provinsi))
          }
          return {
            table:'tb_sektor_daerah_pdrb',
            tahun:item,
            total:provLength,
            ada:datas.length,
            tidak_ada:provLength-datas.length,
            persen_ada:parseFloat(((datas.length/provLength)*100).toFixed(2)),
            persen_tidak_ada:parseFloat((((provLength-datas.length)/provLength)*100).toFixed(2))
          }
        })
      )
      const resultUMR =  await Promise.all(
        tahun.map(async(item) => {
          console.log(item);
          
          const datass = await prisma.tb_umr_provinsi.findMany({
                select:{
                  id_adm_provinsi:true
                },
                where:{
                  tahun:item,
                  status:{
                    not:-99
                  },
                  ...(userProv.length > 0 && {
                    id_adm_provinsi: {
                      in: userProv,
                    },
                  }),
            },

          })

          let datas = Array.from(new Set(datass.map(data => data.id_adm_provinsi)))
          .map(id => ({ id_adm_provinsi: id }));
          
          if (Id) {
            datas = datas.filter(item => userProv.includes(item.id_adm_provinsi))
          }

          return {
            table:'tb_umr_provinsi',
            tahun:item,
            ada:datas.length,
            total:provLength,
            tidak_ada:provLength-datas.length,
            persen_ada:parseFloat(((datas.length/provLength)*100).toFixed(2)),
            persen_tidak_ada:parseFloat((((provLength-datas.length)/provLength)*100).toFixed(2))
          }
        })
      )
      
      const resultUtilitas =  await Promise.all(
        tahun.map(async(item) => {
          console.log(item);
          
          const datass = await prisma.tb_utilitas_provinsi.findMany({
                select:{
                  id_adm_provinsi:true
                },
                where:{
                  tahun:item,
                  status:{
                    not:-99
                  },
                  ...(userProv.length > 0 && {
                    id_adm_provinsi: {
                      in: userProv,
                    },
                  }),
                },

          })

          let datas = Array.from(new Set(datass.map(data => data.id_adm_provinsi)))
          .map(id => ({ id_adm_provinsi: id }));          
          if (Id) {
            datas = datas.filter(item => userProv.includes(item.id_adm_provinsi))
          }

          return {
            table:'tb_utilitas_provinsi',
            tahun:item,
            ada:datas.length,
            total:provLength,
            tidak_ada:provLength-datas.length,
            persen_ada:parseFloat(((datas.length/provLength)*100).toFixed(2)),
            persen_tidak_ada:parseFloat((((provLength-datas.length)/provLength)*100).toFixed(2))
          }
        })
      )

      data.push({'DEMOGRAFI':resultDemografi})
      data.push({'EKSPOR/IMPOR':resultEksporimpor})
      data.push({'REALISASI':resultRealisasi})
      data.push({'KOMODITI':resultKomoditi})
      data.push({'PDRB':resultPDRB})
      data.push({'UMR':resultUMR})
      data.push({'UTILITAS':resultUtilitas})
      return data                                             
    }
    async  getDetailDetailProv({request, response }) {
      
      const {
        page: pageRaw = '1',
        pageSize: pageSizeRaw = '10',
        search: searchRaw = '',
        list = '',
        tahun : tahun_data = new Date().getFullYear(),
        status = 'update'
      } = request.qs()
      
      const page = parseInt(pageRaw)
      const pageSize = parseInt(pageSizeRaw)
      const search = searchRaw === 'undefined' ? '' : decodeURIComponent(searchRaw)
      const year = parseInt(tahun_data)
      // Ambil tahun sekarang
      const currentYear = new Date().getFullYear()-2
      const tables = [
        {table : 'tb_demografi_provinsi_status', id : 'id_demografi_provinsi', label :  'Demografi Provinsi'},
        {table : 'tb_investasi_provinsi', id : 'id_investasi_provinsi', label :  'Investasi Provinsi'},
        {table : 'tb_ekspor_provinsi_status', id : 'id_ekspor_provinsi', label :  'Ekspor/Impor Provinsi'},
        {table : 'tb_komoditi_provinsi_status', id : 'id_komoditi_provinsi', label :  'Komoditi Provinsi'},
        {table : 'tb_umr_provinsi_status', id : 'id_umr_provinsi', label :  'UMR Provinsi'},
        {table : 'tb_utilitas_provinsi_status', id : 'id_utilitas_provinsi', label :  'Utilitas Provinsi'},
      ];
      const table = tables.find((item) => item.table === list);
      let data =[]
      let totalRecords =0
      let totalPage =1
      let join_table = table?.table.replace('_status','')
      const sts = status === 'update' ? '!= 0' : '= 0'
      if(table && table.table == 'tb_demografi_provinsi_status'){
        const countResult = await prisma.$queryRawUnsafe(`
          SELECT count(*)
            FROM (
              SELECT tap.nama AS nama_prov,
                    CASE 
                      WHEN COUNT(DISTINCT tep.id_kategori) >= 2 THEN 2 
                      ELSE COUNT(DISTINCT tep.id_kategori) 
                    END AS update,
                    CASE 
                      WHEN COUNT(DISTINCT tep.id_kategori) = 2 THEN 0 
                      ELSE 2 - COUNT(DISTINCT tep.id_kategori) 
                    END AS belum_update
              FROM tb_adm_provinsi tap
              LEFT JOIN ${join_table} tep 
                ON tep.id_adm_provinsi = tap.id_adm_provinsi AND tep.tahun = $1
              LEFT JOIN ${table?.table} teps 
                ON teps.${table?.id} = tep.${table?.id}
                where tep.id_kategori in(1,20)
              GROUP BY tap.nama
            ) AS sub
            WHERE sub.update ${sts}      
            AND  sub.nama_prov ILIKE $2
          `, year, `%${search}%`);
          
          totalRecords = Number(countResult[0]?.count)
          totalPage = Math.ceil(totalRecords / pageSize)
        
          data = await prisma.$queryRawUnsafe(`
            SELECT *
            FROM (
              SELECT tap.nama AS nama_prov,
                    CASE 
                      WHEN COUNT(DISTINCT tep.id_kategori) >= 2 THEN 2 
                      ELSE COUNT(DISTINCT tep.id_kategori) 
                    END AS update,
                    CASE 
                      WHEN COUNT(DISTINCT tep.id_kategori) = 2 THEN 0 
                      ELSE 2 - COUNT(DISTINCT tep.id_kategori) 
                    END AS belum_update
              FROM tb_adm_provinsi tap
              LEFT JOIN ${join_table} tep 
                ON tep.id_adm_provinsi = tap.id_adm_provinsi AND tep.tahun = $3
              LEFT JOIN ${table?.table} teps 
                ON teps.${table?.id} = tep.${table?.id}
                where tep.id_kategori in(1,20)
              GROUP BY tap.nama
            ) AS sub
            WHERE sub.update ${sts} and sub.nama_prov ILIKE $4
            LIMIT $1 OFFSET $2;
            `, pageSize, (page - 1) * pageSize, year, `%${search}%`);
      }else{
        const countResult = await prisma.$queryRawUnsafe(`
          SELECT count(*)
            FROM (
              SELECT tap.nama AS nama_prov,
                    CASE 
                      WHEN COUNT(tep.id_adm_provinsi) > 0 THEN 1 
                      ELSE 0 
                    END AS update,
                    CASE 
                      WHEN COUNT(tep.id_adm_provinsi) > 0 THEN 0 
                      ELSE 1 
                    END AS belum_update
              FROM tb_adm_provinsi tap
              LEFT JOIN ${join_table} tep 
                ON tep.id_adm_provinsi = tap.id_adm_provinsi AND tep.tahun = 2022
              GROUP BY tap.nama
            ) AS sub
            WHERE sub.update ${sts} and sub.nama_prov ILIKE $2      
          `, year, `%${search}%`);
          
          totalRecords = Number(countResult[0]?.count)
          totalPage = Math.ceil(totalRecords / pageSize)
        
          data = await prisma.$queryRawUnsafe(`
            SELECT *
            FROM (
              SELECT tap.nama AS nama_prov,
                    CASE 
                      WHEN COUNT(tep.id_adm_provinsi) > 0 THEN 1 
                      ELSE 0 
                    END AS update,
                    CASE 
                      WHEN COUNT(tep.id_adm_provinsi) > 0 THEN 0 
                      ELSE 1 
                    END AS belum_update
              FROM tb_adm_provinsi tap
              LEFT JOIN ${join_table} tep 
                ON tep.id_adm_provinsi = tap.id_adm_provinsi AND tep.tahun = 2022
              GROUP BY tap.nama
            ) AS sub
            WHERE sub.update ${sts} and sub.nama_prov ILIKE $4
            LIMIT $1 OFFSET $2;
            `, pageSize, (page - 1) * pageSize, year, `%${search}%`);

        // data = await prisma.$queryRawUnsafe(`
        //   SELECT tep.id_adm_provinsi, tap.nama,
        //     (
        //       SELECT COUNT(DISTINCT teps2.${table?.id}) 
        //       FROM ${table?.table} teps2
        //       LEFT JOIN ${join_table} tep2 ON teps2.${table?.id} = tep2.${table?.id}
        //       WHERE tep2.id_adm_provinsi = tep.id_adm_provinsi
        //         AND tep2.tahun = $3
        //     ) AS jml_update,
        //     CASE 
        //       WHEN (
        //         SELECT COUNT(DISTINCT teps2.${table?.id}) 
        //         FROM ${table?.table} teps2
        //         LEFT JOIN ${join_table} tep2 ON teps2.${table?.id} = tep2.${table?.id}
        //         WHERE tep2.id_adm_provinsi = tep.id_adm_provinsi
        //           AND tep2.tahun = $3
        //       ) = 2 THEN 0 
        //       ELSE 1 
        //     END AS jml_belum
        //   FROM ${table?.table} teps 
        //   LEFT JOIN ${join_table} tep ON teps.${table?.id} = tep.${table?.id}
        //   LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tep.id_adm_provinsi
        //   WHERE tep.tahun = $3
        //     AND tap.nama ILIKE $4
        //   GROUP BY tep.id_adm_provinsi, tap.nama, tep.status
        //   LIMIT $1 OFFSET $2;
        // `, pageSize, (page - 1) * pageSize, year, `%${search}%`);
        
      }
      
      return {
        success: true,
        totalRecords: totalRecords, // Total number of records in the database
        totalPage: totalPage, // Total number of pages
        page: page,
        pageSize: pageSize,
        data: data, // Data for the current page
      };
      
    }

    public async get_status_data_kabkot({params,response}:HttpContext){
      
      const Id = parseInt(params.id_user, 10)
      // if (isNaN(Id)) {
      //     return response.status(400).send({ error: 'Invalid ID provided' })
      // }
      let userProv : number[] =[]
      let optWhere = {}
      if (!Number.isNaN(Id)) {
          const user = await prisma.users.findFirst({
            where:{
              id:Id
            },
            include:{
              tb_user_internal_provinsi: {
                include: {
                  tb_adm_provinsi: {
                  select: {
                    nama: true,
                  },
                },
              },
            },
          }
        })
        if(Array.isArray(user?.tb_user_internal_provinsi)){
                user?.tb_user_internal_provinsi.map((item) => {
                  userProv.push(item.id_adm_provinsi)
                })
                optWhere  = {
                  id_adm_provinsi:{
                    in:userProv
                  }
                }
        }
      }
      let data =[]
      let tahun : number[] = []
      tahun.push(DateTime.now().year-2); 
      tahun.push(DateTime.now().year-1); 
      tahun.push(DateTime.now().year); 
      const kabkot = await prisma.tb_adm_kabkot.findMany({where:{
        ...(userProv.length > 0 && {
          OR: userProv.map((prov) => ({
            id_adm_kabkot: {
              gte: Number(`${prov}00`),
              lt: Number(`${prov}99`),
            },
          })),
        }),
      }
      })

      const kabkotLength = kabkot.length

      const resultDemografi =  await Promise.all(
        
        tahun.map(async(item) => {
          
          const datass = await prisma.tb_demografi_kabkot.findMany({
            select:{
              id_adm_kabkot:true
            },  
            where:{
                  tahun:item,
                  status:{
                    not:-99
                  },
                  id_kategori:1,
                  ...(userProv.length > 0 && {
                    OR: userProv.map((prov) => ({
                      id_adm_kabkot: {
                        gte: Number(`${prov}00`),
                        lt: Number(`${prov}99`),
                      },
                    })),
                  }),
            },
          })

          const datas = Array.from(new Set(datass.map(data => data.id_adm_kabkot)))
          .map(id => ({ id_adm_kabkot: id }));

          return {
            table:'tb_demografi_kabkot',
            tahun:item,
            total:kabkotLength,
            ada:datas.length,
            tidak_ada:kabkotLength-datas.length,
            persen_ada:parseFloat(((datas.length/kabkotLength)*100).toFixed(2)),
            persen_tidak_ada:parseFloat((((kabkotLength-datas.length)/kabkotLength)*100).toFixed(2))
          }
        })
      )

      const resultEksporimpor =  await Promise.all(
        tahun.map(async(item) => {
          console.log(item);
          
          const datass = await prisma.tb_ekspor_kabkot.findMany({
              select:{
                id_adm_kabkot:true
              },  
              where:{
                  tahun: item,
                  status:{
                    not:-99
                  },
                  ...(userProv.length > 0 && {
                    OR: userProv.map((prov) => ({
                      id_adm_kabkot: {
                        gte: Number(`${prov}00`),
                        lt: Number(`${prov}99`),
                      },
                    })),
                  }),
            },
          })
          const datas = Array.from(new Set(datass.map(data => data.id_adm_kabkot)))
          .map(id => ({ id_adm_kabkot: id }));

          return {
            tahun:item,
            total:kabkotLength,
            ada:datas.length,
            tidak_ada:kabkotLength-datas.length,
            persen_ada:parseFloat(((datas.length/kabkotLength)*100).toFixed(2)),
            persen_tidak_ada:parseFloat((((kabkotLength-datas.length)/kabkotLength)*100).toFixed(2))
          }
        })
      )
      
      const resultRealisasi =  await Promise.all(
        tahun.map(async(item) => {
          const datass = await prisma.tb_investasi_kabkot.findMany({
              select:{
                id_adm_kabkot:true
              },  
              where:{
                  tahun: item,
                  status:{
                    not:-99
                  },
                  ...(userProv.length > 0 && {
                    OR: userProv.map((prov) => ({
                      id_adm_kabkot: {
                        gte: Number(`${prov}00`),
                        lt: Number(`${prov}99`),
                      },
                    })),
                  }),
            },
          })
          const datas = Array.from(new Set(datass.map(data => data.id_adm_kabkot)))
          .map(id => ({ id_adm_kabkot: id }));

          return {
            table:'tb_investasi_kabkot',
            tahun:item,
            total:kabkotLength,
            ada:datas.length,
            tidak_ada:kabkotLength-datas.length,
            persen_ada:parseFloat(((datas.length/kabkotLength)*100).toFixed(2)),
            persen_tidak_ada:parseFloat((((kabkotLength-datas.length)/kabkotLength)*100).toFixed(2))
          }
        })
      )

      const resultKomoditi =  await Promise.all(
        tahun.map(async(item) => {
          console.log(item);
          
          const datass = await prisma.tb_komoditi_kabkot.findMany({
                select:{
                  id_adm_kabkot:true
                },
                where:{
                  tahun: item,
                  status:{
                    not:-99
                  },
                  ...(userProv.length > 0 && {
                    OR: userProv.map((prov) => ({
                      id_adm_kabkot: {
                        gte: Number(`${prov}00`),
                        lt: Number(`${prov}99`),
                      },
                    })),
                  }),
            },
          })

          const datas = Array.from(new Set(datass.map(data => data.id_adm_kabkot)))
          .map(id => ({ id_adm_kabkot: id }));

          return {
            tahun:item,
            total:kabkotLength,
            ada:datas.length,
            tidak_ada:kabkotLength-datas.length,
            persen_ada:parseFloat(((datas.length/kabkotLength)*100).toFixed(2)),
            persen_tidak_ada:parseFloat((((kabkotLength-datas.length)/kabkotLength)*100).toFixed(2))
          }
        })
      )
      const resultPDRB =  await Promise.all(
        tahun.map(async(item) => {
          console.log(item);
          
          const datass = await prisma.tb_sektor_daerah_pdrb.findMany({
                select:{
                  id_adm_kabkot:true
                },
                where:{
                  tahun_pdrb: item,
                  status:{
                    not:-99
                  },
                  ...(userProv.length > 0 && {
                    OR: userProv.map((prov) => ({
                      id_adm_kabkot: {
                        gte: Number(`${prov}00`),
                        lt: Number(`${prov}99`),
                      },
                    })),
                  }),
            },
          })

          const datas = Array.from(new Set(datass.map(data => data.id_adm_kabkot)))
          .map(id => ({ id_adm_kabkot: id }));

          return {
            table:'tb_sektor_daerah_pdrb',
            tahun:item,
            total:kabkotLength,
            ada:datas.length,
            tidak_ada:kabkotLength-datas.length,
            persen_ada:parseFloat(((datas.length/kabkotLength)*100).toFixed(2)),
            persen_tidak_ada:parseFloat((((kabkotLength-datas.length)/kabkotLength)*100).toFixed(2))
          }
        })
      )
      const resultUMR =  await Promise.all(
        tahun.map(async(item) => {
          console.log(item);
          
          const datass = await prisma.tb_umr_kabkot.findMany({
                select:{
                  id_adm_kabkot:true
                },
                where:{
                  tahun: item,
                  status:{
                    not:-99
                  },
                  ...(userProv.length > 0 && {
                    OR: userProv.map((prov) => ({
                      id_adm_kabkot: {
                        gte: Number(`${prov}00`),
                        lt: Number(`${prov}99`),
                      },
                    })),
                  }),
            },
          })

          const datas = Array.from(new Set(datass.map(data => data.id_adm_kabkot)))
          .map(id => ({ id_adm_kabkot: id }));

          return {
            table:'tb_umr_kabkot',
            tahun:item,
            ada:datas.length,
            total:kabkotLength,
            tidak_ada:kabkotLength-datas.length,
            persen_ada:parseFloat(((datas.length/kabkotLength)*100).toFixed(2)),
            persen_tidak_ada:parseFloat((((kabkotLength-datas.length)/kabkotLength)*100).toFixed(2))
          }
        })
      )
      
      const resultUtilitas =  await Promise.all(
        tahun.map(async(item) => {
          console.log(item);
          
          const datass = await prisma.tb_utilitas_kabkot.findMany({
                select:{
                  id_adm_kabkot:true
                },
                where:{
                  tahun: item,
                  status:{
                    not:-99
                  },
                  ...(userProv.length > 0 && {
                    OR: userProv.map((prov) => ({
                      id_adm_kabkot: {
                        gte: Number(`${prov}00`),
                        lt: Number(`${prov}99`),
                      },
                    })),
                  }),
            },
          })

          const datas = Array.from(new Set(datass.map(data => data.id_adm_kabkot)))
          .map(id => ({ id_adm_kabkot: id }));

          return {
            table:'tb_utilitas_kabkot',
            tahun:item,
            ada:datas.length,
            total:kabkotLength,
            tidak_ada:kabkotLength-datas.length,
            persen_ada:parseFloat(((datas.length/kabkotLength)*100).toFixed(2)),
            persen_tidak_ada:parseFloat((((kabkotLength-datas.length)/kabkotLength)*100).toFixed(2))
          }
        })
      )

      data.push({'DEMOGRAFI':resultDemografi})
      data.push({'EKSPOR/IMPOR':resultEksporimpor})
      data.push({'REALISASI':resultRealisasi})
      data.push({'KOMODITI':resultKomoditi})
      data.push({'PDRB':resultPDRB})
      data.push({'UMR':resultUMR})
      data.push({'UTILITAS':resultUtilitas})
      return data                                             
    }

    async  getDetailDetailKabkot({request, response }) {
      
      const {
        page: pageRaw = '1',
        pageSize: pageSizeRaw = '10',
        search: searchRaw = '',
        list = '',
        tahun : tahun_data = new Date().getFullYear(),
        status = 'update'
      } = request.qs()
      
      const page = parseInt(pageRaw)
      const pageSize = parseInt(pageSizeRaw)
      const search = searchRaw === 'undefined' ? '' : decodeURIComponent(searchRaw)
      const year = parseInt(tahun_data)
      // Ambil tahun sekarang
      const currentYear = new Date().getFullYear()-2
      const tables = [
        {table : 'tb_demografi_kabkot_status', id : 'id_demografi_kabkot', label :  'Demografi kabkot'},
        {table : 'tb_investasi_kabkot', id : 'id_investasi_kabkot', label :  'Investasi kabkot'},
        {table : 'tb_ekspor_kabkot_status', id : 'id_ekspor_kabkot', label :  'Ekspor/Impor kabkot'},
        {table : 'tb_komoditi_kabkot_status', id : 'id_komoditi_kabkot', label :  'Komoditi kabkot'},
        {table : 'tb_umr_kabkot_status', id : 'id_umr_kabkot', label :  'UMR kabkot'},
        {table : 'tb_utilitas_kabkot_status', id : 'id_utilitas_kabkot', label :  'Utilitas kabkot'},
      ];
      const table = tables.find((item) => item.table === list);
      let data =[]
      let totalRecords =0
      let totalPage =1
      let join_table = table?.table.replace('_status','')
      const sts = status === 'update' ? '!= 0' : '= 0'

      if(table && table.table == 'tb_demografi_kabkot_status'){
        const countResult = await prisma.$queryRawUnsafe(`
          SELECT COUNT(*) 
          FROM (
            SELECT tak.id_adm_kabkot,
                  tak.nama AS nama_kabkot,
                  tap.nama AS nama_prov,
                  CASE 
                    WHEN COUNT(DISTINCT tep.id_kategori) >= 2 THEN 2 
                    ELSE COUNT(DISTINCT tep.id_kategori) 
                  END AS update,
                  CASE 
                    WHEN COUNT(DISTINCT tep.id_kategori) = 2 THEN 0 
                    ELSE 2 - COUNT(DISTINCT tep.id_kategori) 
                  END AS belum_update
            FROM tb_adm_kabkot tak
            JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
            LEFT JOIN ${join_table} tep 
              ON tep.id_adm_kabkot = tak.id_adm_kabkot AND tep.tahun = $1
            LEFT JOIN ${table.table} teps 
              ON teps.${table.id} = tep.${table.id}
            GROUP BY tak.id_adm_kabkot, tak.nama, tap.nama
          ) AS sub
          WHERE sub.update ${sts}
          AND (sub.nama_kabkot ILIKE $2 OR sub.nama_prov ILIKE $2)
        `, year, `%${search}%`);


        
        totalRecords = Number(countResult[0]?.count)
        totalPage = Math.ceil(totalRecords / pageSize)
        
        data = await prisma.$queryRawUnsafe(`
          SELECT tak.id_adm_kabkot,
               tak.nama AS nama_kabkot,
               tap.nama AS nama_prov,
               CASE 
                 WHEN COUNT(DISTINCT tep.id_kategori) >= 2 THEN 2 
                 ELSE COUNT(DISTINCT tep.id_kategori) 
               END AS update,
               CASE 
                 WHEN COUNT(DISTINCT tep.id_kategori) = 2 THEN 0 
                 ELSE 2 - COUNT(DISTINCT tep.id_kategori) 
               END AS belum_update
        FROM tb_adm_kabkot tak
        JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
        LEFT JOIN ${join_table} tep 
          ON tep.id_adm_kabkot = tak.id_adm_kabkot AND tep.tahun = $3
        LEFT JOIN ${table.table} teps 
          ON teps.${table.id} = tep.${table.id}
        GROUP BY tak.id_adm_kabkot, tak.nama, tap.nama
        HAVING CASE 
                WHEN COUNT(DISTINCT tep.id_kategori) >= 2 THEN 2 
                ELSE COUNT(DISTINCT tep.id_kategori) 
              END ${sts}
        AND (tak.nama ILIKE $4 OR tap.nama ILIKE $4)
        ORDER BY tap.nama, tak.nama
        LIMIT $1 OFFSET $2
      `, pageSize, (page - 1) * pageSize, year, `%${search}%`)
        // data = await prisma.$queryRawUnsafe(`
        //   SELECT tep.id_adm_kabkot, tap.nama,
        //     (
        //       SELECT COUNT(DISTINCT teps2.${table?.id}) 
        //       FROM ${table?.table} teps2
        //       JOIN ${join_table} tep2 ON teps2.${table?.id} = tep2.${table?.id}
        //       WHERE tep2.id_adm_kabkot = tep.id_adm_kabkot
        //         AND tep2.tahun = $3
        //     ) AS jml_update,
        //     CASE 
        //       WHEN (
        //         SELECT COUNT(DISTINCT teps2.${table?.id}) 
        //         FROM ${table?.table} teps2
        //         JOIN ${join_table} tep2 ON teps2.${table?.id} = tep2.${table?.id}
        //         WHERE tep2.id_adm_kabkot = tep.id_adm_kabkot
        //           AND tep2.tahun = $3
        //       ) = 2 THEN 0 
        //       ELSE 1 
        //     END AS jml_belum
        //   FROM ${table?.table} teps 
        //   JOIN ${join_table} tep ON teps.${table?.id} = tep.${table?.id}
        //   JOIN tb_adm_kabkot tap ON tap.id_adm_kabkot = tep.id_adm_kabkot
        //   WHERE tep.tahun = $3
        //     AND tap.nama ILIKE $4
        //   GROUP BY tep.id_adm_kabkot, tap.nama, tep.status
        //   LIMIT $1 OFFSET $2;
        // `, pageSize, (page - 1) * pageSize, year, `%${search}%`);
      }else if(table){
        const countResult = await prisma.$queryRawUnsafe(`
          SELECT COUNT(*) 
          from (SELECT tak.id_adm_kabkot,
               tak.nama AS nama_kabkot,
               tap.nama AS nama_prov,
               COUNT(teps.${table.id}) AS update,
               CASE 
                 WHEN COUNT(teps.${table.id}) > 0 THEN 0 
                 ELSE 1 
               END AS belum_update
              FROM tb_adm_kabkot tak
              JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
              LEFT JOIN ${join_table} tep 
                ON tep.id_adm_kabkot = tak.id_adm_kabkot AND tep.tahun = $1
              LEFT JOIN ${table.table} teps 
                ON teps.${table.id} = tep.${table.id}
              WHERE (tak.nama ILIKE $2 OR tap.nama ILIKE $2)
              GROUP BY tak.id_adm_kabkot, tak.nama, tap.nama
              HAVING COUNT(teps.${table.id}) ${sts}
              ORDER BY tap.nama, tak.nama
          ) as sub
        `, year, `%${search}%`);
        
        totalRecords = Number(countResult[0]?.count)
        totalPage = Math.ceil(totalRecords / pageSize)
        
        data = await prisma.$queryRawUnsafe(`
           SELECT tak.id_adm_kabkot,
               tak.nama AS nama_kabkot,
               tap.nama AS nama_prov,
               CASE 
                 WHEN COUNT(teps.${table.id}) >= 1 THEN 1 else 0 end AS update,
               CASE 
                 WHEN COUNT(teps.${table.id}) > 0 THEN 0 
                 ELSE 1 
               END AS belum_update
              FROM tb_adm_kabkot tak
              JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
              LEFT JOIN ${join_table} tep 
                ON tep.id_adm_kabkot = tak.id_adm_kabkot AND tep.tahun = $3
              LEFT JOIN ${table.table} teps 
                ON teps.${table.id} = tep.${table.id}
              WHERE (tak.nama ILIKE $4 OR tap.nama ILIKE $4)
              GROUP BY tak.id_adm_kabkot, tak.nama, tap.nama
              HAVING COUNT(teps.${table.id}) ${sts}
              ORDER BY tap.nama, tak.nama
              LIMIT $1 OFFSET $2
        `, pageSize, (page - 1) * pageSize, year, `%${search}%`);
      }
      return {
        success: true,
        totalRecords: totalRecords, // Total number of records in the database
        totalPage: totalPage, // Total number of pages
        page: page,
        pageSize: pageSize,
        data: data, // Data for the current page
      };
      
    }
    public async get_pic_data_old({params,response}:HttpContext){
      const Id = parseInt(params.id_user, 10)
      if (isNaN(Id)) {
          return response.status(400).send({ error: 'Invalid ID provided' })
      }
      const user = await prisma.users.findFirst({
        where:{
          id:Id
        },
        include:{
          tb_user_internal_provinsi: {
            include: {
              tb_adm_provinsi: {
                select: {
                  nama: true,
                },
              },
            },
          },
        }
      })

      const indikator = await prisma.tb_indikator_pic.findMany({

      })
      
      const data = { ada: [], tidak_ada: [] };
      const currentYear = DateTime.now().year; // Ambil tahun saat ini (misal: 2025)

      const results = await Promise.all(
        indikator.map(async (item) => {
          if (item.nama_tabel && Array.isArray(user?.tb_user_internal_provinsi) && user.tb_user_internal_provinsi.length > 0) {
            try {
              let datas = [];
              const tableName = item.nama_tabel; // Misal: "tb_bandata_status"
              const includeTable = tableName.replace(/_status$/, ""); // Menghapus _status di akhir

              const model = prisma[item.nama_tabel as keyof typeof prisma];

              if (model && typeof model.findMany === 'function') {
                datas = await model.findMany({
                  take: 1,
                  where: {
                    created_date: {
                      gte: new Date(`${currentYear}-01-01T00:00:00.000Z`), // Awal tahun
                      lt: new Date(`${currentYear + 1}-01-01T00:00:00.000Z`), // Awal tahun berikutnya
                    },
                  },
                  include: {
                    [includeTable]: true, // Include tabel dengan nama hasil replace
                  },
                });
              } else {
                console.error(`Model ${item.nama_tabel} tidak ditemukan di Prisma`);
              }

              const status = datas.length > 0 ? 'Ada' : 'Tidak Ada';

              const mappedData = user.tb_user_internal_provinsi.map((item2) => ({
                id_indikator_pic: item.id_indikator_pic,
                nama_tabel: item.nama_tabel,
                indikator: item.nama_indikator,
                kd_prov: item2.id_adm_provinsi,
                nama_prov: item2.tb_adm_provinsi.nama,
                status: status,
                datas:datas
              }));

              if (datas.length > 0) {
                data.ada.push(...mappedData);
              } else {
                data.tidak_ada.push(...mappedData);
              }
            } catch (error) {
              console.error(`Error fetching data for table ${item.nama_tabel}:`, error);
            }
          }
        })
      );

      console.log(data);


      return {
        success : true,
        data :data
    }

    }

    /**
     * @flag_kosong
     * @paramQuery id_indikator_pic - id_indikator_pic - @type(number) @required
     * @paramQuery id_user - id user - @type(number) @required
     * @paramQuery kd_prov - kode provinsi - @type(number) @required
     * @paramQuery tahun - tahun - @type(number) @required
     */
    async flag_provinsi({ response,request }: HttpContext) {
      const data = await request.validateUsing(dasboardNDValidator);            
        const dataPost = {
          id_indikator_pic:data.id_indikator_pic,
          id_user : data.id_user || 0,
          kd_prov:data.kd_prov,
          tahun:data.tahun
        }
        const insert = await prisma.tb_mapping_indikator_pic.create({
            data:dataPost
        })

        return { 
            success : true,
            data : insert
        }
    }

    async  getPendingInfrastruktur({ response }) {
      // const currentYear = new Date().getFullYear();
      // const startOfYear = new Date(`${currentYear}-01-01`);
      // const endOfYear = new Date(`${currentYear}-12-31 23:59:59`);
    
      const tables = [
        "tb_bandara",
        "tb_hotel",
        "tb_pendidikan",
        "tb_pelabuhan",
        "tb_rumah_sakit",
      ];
    
      try {
        // Buat array promise untuk menghitung jumlah data di setiap tabel
        const queryPromises = tables.map(async (table) => {
          const tableName = table.replace("tb_", ""); // Nama tanpa "tb_"
          const statusTable = `${table}_status`; // Nama tabel status (misalnya tb_bandara_status)
    
          const count = await prisma[table].count({
            where: {
              // [statusTable]: {
              //   some: {
              //     OR: [
              //       {
              //         created_date: { // Pastikan nama kolom sesuai skema
              //           gte: startOfYear,
              //           lte: endOfYear,
              //         },
              //       },
              //       {
              //         updated_date: { // Pastikan nama kolom sesuai skema
              //           gte: startOfYear,
              //           lte: endOfYear,
              //         },
              //       },
              //     ],
              //   },
              // },
              status: 0, // Asumsi kolom 'status' ada di tabel utama (tb_bandara, dll.)
            },
          });
    
          return {
            nama :tableName.split("_").map(str => str.charAt(0).toUpperCase() + str.slice(1)).join(" "),jumlah: count, 
          };
        });
    
        // Tunggu semua promise selesai
        const results = await Promise.all(queryPromises);
    
        // Kembalikan respons sukses
        return response.json({
          success: true,
          data: results,
        });
      } catch (error) {
        return response.status(500).json({
          success: false,
          error: error.message,
        });
      }
    }

    async  getPendingProvinsi({ response }) {
      try {

          const jmlCount = await prisma.tb_demografi_provinsi.count({
            where: {
              status: 0, 
              id_kategori: 1, 
            },
          });

          const akCount = await prisma.tb_demografi_provinsi.count({
            where: {
              status: 0, 
              id_kategori: 20, 
            },
          });

          const umrCount = await prisma.tb_umr_provinsi.count({
            where: {
              status: 0, 
            },
          });
    
        return response.json({
          success: true,
          data: [
            {nama : 'Jumlah Penduduk',jumlah : jmlCount},
            {nama : 'Angkatan Kerja',jumlah : akCount},
            {nama : 'UMR',jumlah : umrCount}
          ]

        });
      } catch (error) {
        return response.status(500).json({
          success: false,
          error: error.message,
        });
      }
    }
    
    async  getPendingKabkot({ response }) {
      try {

          const jmlCount = await prisma.tb_demografi_kabkot.count({
            where: {
              status: 0, 
              id_kategori: 1, 
            },
          });

          const akCount = await prisma.tb_demografi_kabkot.count({
            where: {
              status: 0, 
              id_kategori: 20, 
            },
          });

          const umrCount = await prisma.tb_umr_kabkot.count({
            where: {
              status: 0, 
            },
          });
    
        return response.json({
          success: true,
          data: [
            {nama : 'Jumlah Penduduk',jumlah : jmlCount},
            {nama : 'Angkatan Kerja',jumlah : akCount},
            {nama : 'UMR',jumlah : umrCount}
          ]

        });
      } catch (error) {
        return response.status(500).json({
          success: false,
          error: error.message,
        });
      }
    }
    
    async  getPendingKantor({ response }) {
    
     
      
      const tables = [
        {table : 'tb_adm_provinsi_kantor',label :  'Provinsi'},
        {table : 'tb_adm_kabkot_kantor',label :  'Kabupaten'},
        {table : 'tb_komoditi_provinsi',label : 'Provinl Komoditi Provinsi' },
        {table : 'tb_komoditi_kabkot',label :  'Profil Komoditi Kab/Kota'},
        {table : 'tb_peluang_kabkot',label :  'Info Peluang'},
      ];

    
      try {
        // Buat array promise untuk menghitung jumlah data di setiap tabel
        const queryPromises = tables.map(async (item) => {
          let count;
          try {
            // Coba dengan Int terlebih dahulu
            count = await prisma[item.table].count({
              where: {
                status: 0,
              },
            });
          } catch (error) {
            // Jika gagal (mungkin karena String), coba dengan String
            if (error.message.includes("Invalid value provided")) {
              count = await prisma[item.table].count({
                where: {
                  status: "0",
                },
              });
            } else {
              throw error; // Lempar error lain yang tidak terkait tipe
            }
          }

          return {
            nama :item.label,jumlah: count, 
          };
        });
    
        const results = await Promise.all(queryPromises);
    
        return response.json({
          success: true,
          data: results,
        });
      } catch (error) {
        return response.status(500).json({
          success: false,
          error: error.message,
        });
      }
    }
    async  getPendingNasional({ response }) {
    
      

      
      const tables = [
        {table :'tb_sektor_nasional', label :  'Sektor Nasional'},
        {table :'tb_sub_sektor_nasional', label :  'Sub Sektor Nasional'},
        {table :'tb_komoditi_nasional', label : 'Komoditi Nasional' },
        {table :'tb_sektor_nasional_pdb', label :  'PDB Sektor Nasional'},
        {table :'tb_sektor_nasional_insentif', label :  'Insentif Sektor Nasional'},
      ];

    
      try {
        // Buat array promise untuk menghitung jumlah data di setiap tabel
        const queryPromises = tables.map(async (item) => {
          let count;
          try {
            // Coba dengan Int terlebih dahulu
            count = await prisma[item.table].count({
              where: {
                status: 0,
              },
            });
          } catch (error) {
            // Jika gagal (mungkin karena String), coba dengan String
            if (error.message.includes("Invalid value provided")) {
              count = await prisma[item.table].count({
                where: {
                  status: "0",
                },
              });
            } else {
              throw error; // Lempar error lain yang tidak terkait tipe
            }
          }
          
          return {
            nama :item.label,jumlah: count, 
          };
        });
    
        const results = await Promise.all(queryPromises);
    
        return response.json({
          success: true,
          data: results,
        });
      } catch (error) {
        return response.status(500).json({
          success: false,
          error: error.message,
        });
      }
    }

    async  getPendingDaerah({ response }) {
    
      
      const tables = [
        {table : 'tb_sektor_daerah',label :  'Sektor Daerah'},
        {table : 'tb_sub_sektor_daerah',label :  'Sub Sektor Daerah'},
        {table : 'tb_komoditi_daerah',label : 'Komoditi Daerah' },
        {table : 'tb_sektor_daerah_pdrb',label :  'PDRB Sektor Daerah'},
        {table : 'tb_sektor_daerah_insentif',label :  'Insentif Sektor Daerah'},
      ];

    
      // try {
        // Buat array promise untuk menghitung jumlah data di setiap tabel
        const queryPromises = tables.map(async (item) => {
          let count;
          try {
            // Coba dengan Int terlebih dahulu
            count = await prisma[item.table].count({
              where: {
                status: 0,
              },
            });
          } catch (error) {
            // Jika gagal (mungkin karena String), coba dengan String
            if (error.message.includes("Invalid value provided")) {
              count = await prisma[item.table].count({
                where: {
                  status: "0",
                },
              });
            } else {
              throw error; // Lempar error lain yang tidak terkait tipe
            }
          }
          return {
            nama :item.label,jumlah: count, 
          };
        });
    
        const results = await Promise.all(queryPromises);
    
        return response.json({
          success: true,
          data: results,
        });
      // } catch (error) {
      //   return response.status(500).json({
      //     success: false,
      //     error: error.message,
      //   });
      // }
    }
    async  getDetailInfrastrukturUpdate({request, response }) {
    
      const {
        page: pageRaw = '1',
        pageSize: pageSizeRaw = '10',
        search: searchRaw = '',
        list = '',
      } = request.qs()
      
      const page = parseInt(pageRaw)
      const pageSize = parseInt(pageSizeRaw)
      const search = searchRaw === 'undefined' ? '' : decodeURIComponent(searchRaw)
      
      // Ambil tahun sekarang
      const currentYear = new Date().getFullYear()-2
      const tables = [
        {table : 'tb_bandara_status',id : 'id_bandara',label :  'Detail Bandara'},
        {table : 'tb_hotel_status',id : 'id_hotel',label :  'Detail hotel'},
        {table : 'tb_pendidikan_status',id : 'id_pendidikan',label :  'Detail pendidikan'},
        {table : 'tb_pelabuhan_status',id : 'id_pelabuhan',label :  'Detail pelabuhan'},
        {table : 'tb_rumah_sakit_status',id : 'id_rumah_sakit',label :  'Detail Rumah Sakit'},
        {table : 'tb_sektor_daerah_status',id : 'id_sektor_daerah',label :  'Detail Rumah Sakit'},
        {table : 'tb_sub_sektor_daerah_status',id : 'id_sub_sektor_daerah',label :  'Detail Rumah Sakit'},
        {table : 'tb_peluang_daerah_status',id : 'id_peluang_daerah',label :  'Detail Rumah Sakit'},
        {table : 'tb_peluang_kabkot_status',id : 'id_peluang_kabkot',label :  'Detail Rumah Sakit'},
        {table : 'tb_kawasan_industri_status',id : 'id_kawasan_industri',label :  'Detail Rumah Sakit'},
        {table : 'tb_kawasan_industri_status_kek',id : 'id_kawasan_industri',label :  'Detail Rumah Sakit'},
      ];
      const table = tables.find((item) => item.table === list);
      let data =[]
      let totalRecords =0
      let totalPage =1
      if(table && table.table!=='tb_kawasan_industri_status_kek' && table.table!=='tb_kawasan_industri_status_kek' && table.table!=='tb_sub_sektor_daerah_status'){
        const countResult = await prisma.$queryRawUnsafe(`
          SELECT count(*) FROM (
            SELECT DISTINCT ON (tbs.${table?.id}) tbs.${table?.id}
            FROM ${table?.table} tbs
            LEFT JOIN ${table?.table.replace('_status','')} tb ON tb.${table?.id} = tbs.${table?.id}
            LEFT JOIN tb_user_internal_provinsi tuip ON tuip.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
            LEFT JOIN users tu ON tu.id = tuip.id_user
            LEFT JOIN tb_user_internal tui ON tui.id_user = tu.id
            LEFT JOIN tb_ref_user_jabatan truj ON truj.id_jabatan = tui.id_jabatan
            LEFT JOIN users tu2 ON tu2.id = tbs.created_by 
            LEFT JOIN users tu3 ON tu3.id = tbs.updated_by
            LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
            LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tb.id_adm_kabkot
            WHERE (
              (tbs.updated_date IS NOT NULL AND EXTRACT(YEAR FROM tbs.updated_date) = $1)
              OR (tbs.updated_date IS NULL AND EXTRACT(YEAR FROM tbs.created_date) = $1)
            )
            AND tbs.status != -99
            ${search ? `
              AND (
                tu.first_name ILIKE $4 OR
                tu.middle_name ILIKE $4 OR
                tu.last_name ILIKE $4 OR
                tu.email ILIKE $4 OR
                tu.mobile_number ILIKE $4 OR
                truj.nama ILIKE $4 OR
                tap.nama ILIKE $4 OR
                tak.nama ILIKE $4
              )
            ` : ''}
          ) AS subquery
        `, currentYear,`%${search}%`)
        
        totalRecords = Number(countResult[0]?.count)
        totalPage = Math.ceil(totalRecords / pageSize)
        
          data = await prisma.$queryRawUnsafe(`
          SELECT DISTINCT ON (tbs.${table?.id}) 
            tbs.${table?.id},
            replace(concat(tu.first_name, ' ', tu.middle_name, ' ', tu.last_name), '-', ' ') as nama_pic,
            truj.nama as jabatan,
            tu.email,
            tu.mobile_number,
            CASE 
              WHEN tbs.updated_date IS NOT NULL THEN tbs.updated_date 
              ELSE tbs.created_date 
            END as tanggal_update,
            CASE 
              WHEN tbs.updated_by IS NOT NULL 
              THEN replace(concat(tu3.first_name, ' ', tu3.middle_name, ' ', tu3.last_name), '-', ' ') 
              ELSE replace(concat(tu2.first_name, ' ', tu2.middle_name, ' ', tu2.last_name), '-', ' ') 
            END as diupdate_oleh,
            tap.nama as nama_provinsi,
            tak.nama as nama_kabkot,
            CASE 
              WHEN tbs.updated_date IS NOT NULL 
              THEN EXTRACT(YEAR FROM tbs.updated_date) 
              ELSE EXTRACT(YEAR FROM tbs.created_date) 
            END as tahun,
            tbs.status_proses,
            tbs.keterangan
          FROM ${table?.table} tbs
          LEFT JOIN ${table?.table.replace('_status','')} tb ON tb.${table?.id} = tbs.${table?.id}
          LEFT JOIN tb_user_internal_provinsi tuip ON tuip.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
          LEFT JOIN users tu ON tu.id = tuip.id_user
          LEFT JOIN tb_user_internal tui ON tui.id_user = tu.id
          LEFT JOIN tb_ref_user_jabatan truj ON truj.id_jabatan = tui.id_jabatan
          LEFT JOIN users tu2 ON tu2.id = tbs.created_by 
          LEFT JOIN users tu3 ON tu3.id = tbs.updated_by
          LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
          LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tb.id_adm_kabkot
          WHERE (
            (tbs.updated_date IS NOT NULL AND EXTRACT(YEAR FROM tbs.updated_date) = $3)
            OR (tbs.updated_date IS NULL AND EXTRACT(YEAR FROM tbs.created_date) = $3)
            )
            AND tbs.status != -99
            ${search ? `
              AND (
                tu.first_name ILIKE $4 OR
                tu.middle_name ILIKE $4 OR
                tu.last_name ILIKE $4 OR
                tu.email ILIKE $4 OR
                tu.mobile_number ILIKE $4 OR
                truj.nama ILIKE $4 OR
                tap.nama ILIKE $4 OR
                tak.nama ILIKE $4
              )
            ` : ''}
          LIMIT $1 OFFSET $2  
        `, pageSize, (page - 1) * pageSize, currentYear,`%${search}%`);
        
      }else if(table && table.table === 'tb_kawasan_industri_status_kek' || table?.table === 'tb_kawasan_industri_status'){
        const idKategori = table?.table === 'tb_kawasan_industri_status_kek' ? 1 : 3
        const table_join = table?.table === 'tb_kawasan_industri_status_kek' ? table?.table.replace('_status_kek','') : table?.table.replace('_status','')
        let table_name = table?.table.replace('_kek','')
        
        const countResult = await prisma.$queryRawUnsafe(`
          SELECT count(*) FROM (
            SELECT DISTINCT ON (tbs.${table?.id}) tbs.${table?.id}
            FROM ${table_name} tbs
            LEFT JOIN ${table_join} tb ON tb.${table?.id} = tbs.${table?.id}
            LEFT JOIN tb_user_internal_provinsi tuip ON tuip.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
            LEFT JOIN users tu ON tu.id = tuip.id_user
            LEFT JOIN tb_user_internal tui ON tui.id_user = tu.id
            LEFT JOIN tb_ref_user_jabatan truj ON truj.id_jabatan = tui.id_jabatan
            LEFT JOIN users tu2 ON tu2.id = tbs.created_by 
            LEFT JOIN users tu3 ON tu3.id = tbs.updated_by
            LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
            LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tb.id_adm_kabkot
            WHERE (
              (tbs.updated_date IS NOT NULL AND EXTRACT(YEAR FROM tbs.updated_date) = $1)
              OR (tbs.updated_date IS NULL AND EXTRACT(YEAR FROM tbs.created_date) = $1)
            )
            AND tbs.status != -99
            AND tb.id_kategori = $3
            ${search ? `
              AND (
                tu.first_name ILIKE $2 OR
                tu.middle_name ILIKE $2 OR
                tu.last_name ILIKE $2 OR
                tu.email ILIKE $2 OR
                tu.mobile_number ILIKE $2 OR
                truj.nama ILIKE $2 OR
                tap.nama ILIKE $2 OR
                tak.nama ILIKE $2
              )
            ` : ''}
          ) AS subquery
        `, currentYear,`%${search}%`,idKategori)
        
        totalRecords = Number(countResult[0]?.count)
        totalPage = Math.ceil(totalRecords / pageSize)
        
          data = await prisma.$queryRawUnsafe(`
          SELECT DISTINCT ON (tbs.${table?.id}) 
            tbs.${table?.id},
            replace(concat(tu.first_name, ' ', tu.middle_name, ' ', tu.last_name), '-', ' ') as nama_pic,
            truj.nama as jabatan,
            tu.email,
            tu.mobile_number,
            CASE 
              WHEN tbs.updated_date IS NOT NULL THEN tbs.updated_date 
              ELSE tbs.created_date 
            END as tanggal_update,
            CASE 
              WHEN tbs.updated_by IS NOT NULL 
              THEN replace(concat(tu3.first_name, ' ', tu3.middle_name, ' ', tu3.last_name), '-', ' ') 
              ELSE replace(concat(tu2.first_name, ' ', tu2.middle_name, ' ', tu2.last_name), '-', ' ') 
            END as diupdate_oleh,
            tap.nama as nama_provinsi,
            tak.nama as nama_kabkot,
            CASE 
              WHEN tbs.updated_date IS NOT NULL 
              THEN EXTRACT(YEAR FROM tbs.updated_date) 
              ELSE EXTRACT(YEAR FROM tbs.created_date) 
            END as tahun,
            tbs.status_proses,
            tbs.keterangan
          FROM ${table_name} tbs
          LEFT JOIN ${table_join} tb ON tb.${table?.id} = tbs.${table?.id}
          LEFT JOIN tb_user_internal_provinsi tuip ON tuip.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
          LEFT JOIN users tu ON tu.id = tuip.id_user
          LEFT JOIN tb_user_internal tui ON tui.id_user = tu.id
          LEFT JOIN tb_ref_user_jabatan truj ON truj.id_jabatan = tui.id_jabatan
          LEFT JOIN users tu2 ON tu2.id = tbs.created_by 
          LEFT JOIN users tu3 ON tu3.id = tbs.updated_by
          LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
          LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tb.id_adm_kabkot
          WHERE (
            (tbs.updated_date IS NOT NULL AND EXTRACT(YEAR FROM tbs.updated_date) = $3)
            OR (tbs.updated_date IS NULL AND EXTRACT(YEAR FROM tbs.created_date) = $3)
            )
            AND tbs.status != -99
            AND tb.id_kategori = $5
            ${search ? `
              AND (
                tu.first_name ILIKE $4 OR
                tu.middle_name ILIKE $4 OR
                tu.last_name ILIKE $4 OR
                tu.email ILIKE $4 OR
                tu.mobile_number ILIKE $4 OR
                truj.nama ILIKE $4 OR
                tap.nama ILIKE $4 OR
                tak.nama ILIKE $4
              )
            ` : ''}
          LIMIT $1 OFFSET $2  
        `, pageSize, (page - 1) * pageSize, currentYear,`%${search}%`,idKategori);
      }else if(table && table.table === 'tb_sub_sektor_daerah_status'){
        const countResult = await prisma.$queryRawUnsafe(`
          SELECT count(*) FROM (
            SELECT DISTINCT ON (tbs.${table?.id}) tbs.${table?.id}
            FROM ${table?.table} tbs
            LEFT JOIN ${table?.table.replace('_status','')} tb ON tb.${table?.id} = tbs.${table?.id}
            LEFT JOIN tb_sektor_daerah tsd on tsd.id_sektor_daerah = tb.id_sektor_daerah 
            LEFT JOIN tb_user_internal_provinsi tuip ON tuip.id_adm_provinsi = CAST(LEFT(CAST(tsd.id_adm_kabkot AS TEXT), 2) AS INTEGER)
            LEFT JOIN tb_user_internal_provinsi tuip2 ON tuip.id_adm_provinsi = tsd.id_adm_provinsi
            LEFT JOIN users tu ON tu.id = tuip.id_user
            LEFT JOIN tb_user_internal tui ON tui.id_user = tu.id
            LEFT JOIN tb_ref_user_jabatan truj ON truj.id_jabatan = tui.id_jabatan
            LEFT JOIN users tu2 ON tu2.id = tbs.created_by 
            LEFT JOIN users tu3 ON tu3.id = tbs.updated_by
            LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = CAST(LEFT(CAST(tsd.id_adm_kabkot AS TEXT), 2) AS INTEGER)
            LEFT JOIN tb_adm_provinsi tap2 ON tap.id_adm_provinsi = tsd.id_adm_provinsi
            LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tsd.id_adm_kabkot
            WHERE (
              (tbs.updated_date IS NOT NULL AND EXTRACT(YEAR FROM tbs.updated_date) = $1)
              OR (tbs.updated_date IS NULL AND EXTRACT(YEAR FROM tbs.created_date) = $1)
            )
            AND tbs.status != -99
            ${search ? `
              AND (
                tu.first_name ILIKE $2 OR
                tu.middle_name ILIKE $2 OR
                tu.last_name ILIKE $2 OR
                tu.email ILIKE $2 OR
                tu.mobile_number ILIKE $2 OR
                truj.nama ILIKE $2 OR
                tap.nama ILIKE $2 OR
                tak.nama ILIKE $2
              )
            ` : ''}
          ) AS subquery
        `, currentYear,`%${search}%`)
        
        totalRecords = Number(countResult[0]?.count)
        totalPage = Math.ceil(totalRecords / pageSize)
        
          data = await prisma.$queryRawUnsafe(`
          SELECT DISTINCT ON (tbs.${table?.id}) 
            tbs.${table?.id},
            replace(concat(tu.first_name, ' ', tu.middle_name, ' ', tu.last_name), '-', ' ') as nama_pic,
            truj.nama as jabatan,
            tu.email,
            tu.mobile_number,
            CASE 
              WHEN tbs.updated_date IS NOT NULL THEN tbs.updated_date 
              ELSE tbs.created_date 
            END as tanggal_update,
            CASE 
              WHEN tbs.updated_by IS NOT NULL 
              THEN replace(concat(tu3.first_name, ' ', tu3.middle_name, ' ', tu3.last_name), '-', ' ') 
              ELSE replace(concat(tu2.first_name, ' ', tu2.middle_name, ' ', tu2.last_name), '-', ' ') 
            END as diupdate_oleh,
            tap.nama as nama_provinsi,
            tak.nama as nama_kabkot,
            CASE 
              WHEN tbs.updated_date IS NOT NULL 
              THEN EXTRACT(YEAR FROM tbs.updated_date) 
              ELSE EXTRACT(YEAR FROM tbs.created_date) 
            END as tahun,
            tbs.status_proses,
            tbs.keterangan
          FROM ${table?.table} tbs
          LEFT JOIN ${table?.table.replace('_status','')} tb ON tb.${table?.id} = tbs.${table?.id}
          left JOIn tb_sektor_daerah tsd on tsd.id_sektor_daerah = tb.id_sektor_daerah 
          LEFT JOIN tb_user_internal_provinsi tuip ON tuip.id_adm_provinsi = CAST(LEFT(CAST(tsd.id_adm_kabkot AS TEXT), 2) AS INTEGER)
          LEFT JOIN tb_user_internal_provinsi tuip2 ON tuip.id_adm_provinsi = tsd.id_adm_provinsi
          LEFT JOIN users tu ON tu.id = tuip.id_user
          LEFT JOIN tb_user_internal tui ON tui.id_user = tu.id
          LEFT JOIN tb_ref_user_jabatan truj ON truj.id_jabatan = tui.id_jabatan
          LEFT JOIN users tu2 ON tu2.id = tbs.created_by 
          LEFT JOIN users tu3 ON tu3.id = tbs.updated_by
          LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = CAST(LEFT(CAST(tsd.id_adm_kabkot AS TEXT), 2) AS INTEGER)
          LEFT JOIN tb_adm_provinsi tap2 ON tap.id_adm_provinsi = tsd.id_adm_provinsi
          LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tsd.id_adm_kabkot
          WHERE (
            (tbs.updated_date IS NOT NULL AND EXTRACT(YEAR FROM tbs.updated_date) = $3)
            OR (tbs.updated_date IS NULL AND EXTRACT(YEAR FROM tbs.created_date) = $3)
            )
            AND tbs.status != -99
            ${search ? `
              AND (
                tu.first_name ILIKE $4 OR
                tu.middle_name ILIKE $4 OR
                tu.last_name ILIKE $4 OR
                tu.email ILIKE $4 OR
                tu.mobile_number ILIKE $4 OR
                truj.nama ILIKE $4 OR
                tap.nama ILIKE $4 OR
                tak.nama ILIKE $4
              )
            ` : ''}
          LIMIT $1 OFFSET $2  
        `, pageSize, (page - 1) * pageSize, currentYear,`%${search}%`);
      }
    
      
    
      return {
        success: true,
        totalRecords: totalRecords, // Total number of records in the database
        totalPage: totalPage, // Total number of pages
        page: page,
        pageSize: pageSize,
        data: data, // Data for the current page
      };
    }
    async  getDetailInfrastrukturBelum({request, response }) {
      
      const {
        page: pageRaw = '1',
        pageSize: pageSizeRaw = '10',
        search: searchRaw = '',
        list = '',
      } = request.qs()
      
      const page = parseInt(pageRaw)
      const pageSize = parseInt(pageSizeRaw)
      const search = searchRaw === 'undefined' ? '' : decodeURIComponent(searchRaw)
      
      // Ambil tahun sekarang
      const currentYear = new Date().getFullYear()-2
      const tables = [
        {table : 'tb_bandara_status',id : 'id_bandara',label :  'Detail Bandara'},
        {table : 'tb_hotel_status',id : 'id_hotel',label :  'Detail hotel'},
        {table : 'tb_pendidikan_status',id : 'id_pendidikan',label :  'Detail pendidikan'},
        {table : 'tb_pelabuhan_status',id : 'id_pelabuhan',label :  'Detail pelabuhan'},
        {table : 'tb_rumah_sakit_status',id : 'id_rumah_sakit',label :  'Detail Rumah Sakit'},
        {table : 'tb_sektor_daerah_status',id : 'id_sektor_daerah',label :  'Detail Rumah Sakit'},
        {table : 'tb_sub_sektor_daerah_status',id : 'id_sub_sektor_daerah',label :  'Detail Rumah Sakit'},
        {table : 'tb_peluang_daerah_status',id : 'id_peluang_daerah',label :  'Detail Rumah Sakit'},
        {table : 'tb_peluang_kabkot_status',id : 'id_peluang_kabkot',label :  'Detail Rumah Sakit'},
        {table : 'tb_kawasan_industri_status',id : 'id_kawasan_industri',label :  'Detail Rumah Sakit'},
        {table : 'tb_kawasan_industri_status_kek',id : 'id_kawasan_industri',label :  'Detail Rumah Sakit'},
      ];
      const table = tables.find((item) => item.table === list);
      let data =[]
      let totalRecords =0
      let totalPage =1
      if(table && table.table!=='tb_kawasan_industri_status_kek' && table.table!=='tb_kawasan_industri_status_kek' && table.table!=='tb_sub_sektor_daerah_status'){
        const countResult = await prisma.$queryRawUnsafe(`
          SELECT count(*) FROM (
            SELECT DISTINCT ON (tbs.${table?.id}) tbs.${table?.id}
            FROM ${table?.table} tbs
            LEFT JOIN ${table?.table.replace('_status','')} tb ON tb.${table?.id} = tbs.${table?.id}
            LEFT JOIN tb_user_internal_provinsi tuip ON tuip.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
            LEFT JOIN users tu ON tu.id = tuip.id_user
            LEFT JOIN tb_user_internal tui ON tui.id_user = tu.id
            LEFT JOIN tb_ref_user_jabatan truj ON truj.id_jabatan = tui.id_jabatan
            LEFT JOIN users tu2 ON tu2.id = tbs.created_by 
            LEFT JOIN users tu3 ON tu3.id = tbs.updated_by
            LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
            LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tb.id_adm_kabkot
            WHERE (
              (tbs.updated_date IS NOT NULL AND EXTRACT(YEAR FROM tbs.updated_date) < $1)
              OR (tbs.updated_date IS NULL AND EXTRACT(YEAR FROM tbs.created_date) < $1)
            )
            AND tbs.status != -99
            ${search ? `
              AND (
                tu.first_name ILIKE $2 OR
                tu.middle_name ILIKE $2 OR
                tu.last_name ILIKE $2 OR
                tu.email ILIKE $2 OR
                tu.mobile_number ILIKE $2 OR
                truj.nama ILIKE $2 OR
                tap.nama ILIKE $2 OR
                tak.nama ILIKE $2
              )
            ` : ''}
          ) AS subquery
        `, currentYear,`%${search}%`)
        
        totalRecords = Number(countResult[0]?.count)
        totalPage = Math.ceil(totalRecords / pageSize)
        
          data = await prisma.$queryRawUnsafe(`
          SELECT DISTINCT ON (tbs.${table?.id}) 
            tbs.${table?.id},
            replace(concat(tu.first_name, ' ', tu.middle_name, ' ', tu.last_name), '-', ' ') as nama_pic,
            truj.nama as jabatan,
            tu.email,
            tu.mobile_number,
            CASE 
              WHEN tbs.updated_date IS NOT NULL THEN tbs.updated_date 
              ELSE tbs.created_date 
            END as tanggal_update,
            CASE 
              WHEN tbs.updated_by IS NOT NULL 
              THEN replace(concat(tu3.first_name, ' ', tu3.middle_name, ' ', tu3.last_name), '-', ' ') 
              ELSE replace(concat(tu2.first_name, ' ', tu2.middle_name, ' ', tu2.last_name), '-', ' ') 
            END as diupdate_oleh,
            tap.nama as nama_provinsi,
            tak.nama as nama_kabkot,
            CASE 
              WHEN tbs.updated_date IS NOT NULL 
              THEN EXTRACT(YEAR FROM tbs.updated_date) 
              ELSE EXTRACT(YEAR FROM tbs.created_date) 
            END as tahun,
            tbs.status_proses,
            tbs.keterangan
          FROM ${table?.table} tbs
          LEFT JOIN ${table?.table.replace('_status','')} tb ON tb.${table?.id} = tbs.${table?.id}
          LEFT JOIN tb_user_internal_provinsi tuip ON tuip.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
          LEFT JOIN users tu ON tu.id = tuip.id_user
          LEFT JOIN tb_user_internal tui ON tui.id_user = tu.id
          LEFT JOIN tb_ref_user_jabatan truj ON truj.id_jabatan = tui.id_jabatan
          LEFT JOIN users tu2 ON tu2.id = tbs.created_by 
          LEFT JOIN users tu3 ON tu3.id = tbs.updated_by
          LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
          LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tb.id_adm_kabkot
          WHERE (
            (tbs.updated_date IS NOT NULL AND EXTRACT(YEAR FROM tbs.updated_date) < $3)
            OR (tbs.updated_date IS NULL AND EXTRACT(YEAR FROM tbs.created_date) < $3)
            )
            AND tbs.status != -99
            ${search ? `
              AND (
                tu.first_name ILIKE $4 OR
                tu.middle_name ILIKE $4 OR
                tu.last_name ILIKE $4 OR
                tu.email ILIKE $4 OR
                tu.mobile_number ILIKE $4 OR
                truj.nama ILIKE $4 OR
                tap.nama ILIKE $4 OR
                tak.nama ILIKE $4
              )
            ` : ''}
          LIMIT $1 OFFSET $2  
        `, pageSize, (page - 1) * pageSize, currentYear,`%${search}%`);
        
      }else if(table && table.table === 'tb_kawasan_industri_status_kek' || table?.table === 'tb_kawasan_industri_status'){
        const idKategori = table?.table === 'tb_kawasan_industri_status_kek' ? 1 : 3
        const table_join = table?.table === 'tb_kawasan_industri_status_kek' ? table?.table.replace('_status_kek','') : table?.table.replace('_status','')
        let table_name = table?.table.replace('_kek','')
        
        const countResult = await prisma.$queryRawUnsafe(`
          SELECT count(*) FROM (
            SELECT DISTINCT ON (tbs.${table?.id}) tbs.${table?.id}
            FROM ${table_name} tbs
            LEFT JOIN ${table_join} tb ON tb.${table?.id} = tbs.${table?.id}
            LEFT JOIN tb_user_internal_provinsi tuip ON tuip.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
            LEFT JOIN users tu ON tu.id = tuip.id_user
            LEFT JOIN tb_user_internal tui ON tui.id_user = tu.id
            LEFT JOIN tb_ref_user_jabatan truj ON truj.id_jabatan = tui.id_jabatan
            LEFT JOIN users tu2 ON tu2.id = tbs.created_by 
            LEFT JOIN users tu3 ON tu3.id = tbs.updated_by
            LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
            LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tb.id_adm_kabkot
            WHERE (
              (tbs.updated_date IS NOT NULL AND EXTRACT(YEAR FROM tbs.updated_date) < $1)
              OR (tbs.updated_date IS NULL AND EXTRACT(YEAR FROM tbs.created_date) < $1)
            )
            AND tbs.status != -99
            AND tb.id_kategori = $3
            ${search ? `
              AND (
                tu.first_name ILIKE $2 OR
                tu.middle_name ILIKE $2 OR
                tu.last_name ILIKE $2 OR
                tu.email ILIKE $2 OR
                tu.mobile_number ILIKE $2 OR
                truj.nama ILIKE $2 OR
                tap.nama ILIKE $2 OR
                tak.nama ILIKE $2
              )
            ` : ''}
          ) AS subquery
        `, currentYear,`%${search}%`,idKategori)
        
        totalRecords = Number(countResult[0]?.count)
        totalPage = Math.ceil(totalRecords / pageSize)
        
          data = await prisma.$queryRawUnsafe(`
          SELECT DISTINCT ON (tbs.${table?.id}) 
            tbs.${table?.id},
            replace(concat(tu.first_name, ' ', tu.middle_name, ' ', tu.last_name), '-', ' ') as nama_pic,
            truj.nama as jabatan,
            tu.email,
            tu.mobile_number,
            CASE 
              WHEN tbs.updated_date IS NOT NULL THEN tbs.updated_date 
              ELSE tbs.created_date 
            END as tanggal_update,
            CASE 
              WHEN tbs.updated_by IS NOT NULL 
              THEN replace(concat(tu3.first_name, ' ', tu3.middle_name, ' ', tu3.last_name), '-', ' ') 
              ELSE replace(concat(tu2.first_name, ' ', tu2.middle_name, ' ', tu2.last_name), '-', ' ') 
            END as diupdate_oleh,
            tap.nama as nama_provinsi,
            tak.nama as nama_kabkot,
            CASE 
              WHEN tbs.updated_date IS NOT NULL 
              THEN EXTRACT(YEAR FROM tbs.updated_date) 
              ELSE EXTRACT(YEAR FROM tbs.created_date) 
            END as tahun,
            tbs.status_proses,
            tbs.keterangan
          FROM ${table_name} tbs
          LEFT JOIN ${table_join} tb ON tb.${table?.id} = tbs.${table?.id}
          LEFT JOIN tb_user_internal_provinsi tuip ON tuip.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
          LEFT JOIN users tu ON tu.id = tuip.id_user
          LEFT JOIN tb_user_internal tui ON tui.id_user = tu.id
          LEFT JOIN tb_ref_user_jabatan truj ON truj.id_jabatan = tui.id_jabatan
          LEFT JOIN users tu2 ON tu2.id = tbs.created_by 
          LEFT JOIN users tu3 ON tu3.id = tbs.updated_by
          LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
          LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tb.id_adm_kabkot
          WHERE (
            (tbs.updated_date IS NOT NULL AND EXTRACT(YEAR FROM tbs.updated_date) < $3)
            OR (tbs.updated_date IS NULL AND EXTRACT(YEAR FROM tbs.created_date) < $3)
            )
            AND tbs.status != -99
            AND tb.id_kategori = $5
            ${search ? `
              AND (
                tu.first_name ILIKE $4 OR
                tu.middle_name ILIKE $4 OR
                tu.last_name ILIKE $4 OR
                tu.email ILIKE $4 OR
                tu.mobile_number ILIKE $4 OR
                truj.nama ILIKE $4 OR
                tap.nama ILIKE $4 OR
                tak.nama ILIKE $4
              )
            ` : ''}
          LIMIT $1 OFFSET $2  
        `, pageSize, (page - 1) * pageSize, currentYear,`%${search}%`,idKategori);
      }else if(table && table.table === 'tb_sub_sektor_daerah_status'){
        const countResult = await prisma.$queryRawUnsafe(`
          SELECT count(*) FROM (
            SELECT DISTINCT ON (tbs.${table?.id}) tbs.${table?.id}
            FROM ${table?.table} tbs
            LEFT JOIN ${table?.table.replace('_status','')} tb ON tb.${table?.id} = tbs.${table?.id}
            LEFT JOIN tb_sektor_daerah tsd on tsd.id_sektor_daerah = tb.id_sektor_daerah 
            LEFT JOIN tb_user_internal_provinsi tuip ON tuip.id_adm_provinsi = CAST(LEFT(CAST(tsd.id_adm_kabkot AS TEXT), 2) AS INTEGER)
            LEFT JOIN tb_user_internal_provinsi tuip2 ON tuip.id_adm_provinsi = tsd.id_adm_provinsi
            LEFT JOIN users tu ON tu.id = tuip.id_user
            LEFT JOIN tb_user_internal tui ON tui.id_user = tu.id
            LEFT JOIN tb_ref_user_jabatan truj ON truj.id_jabatan = tui.id_jabatan
            LEFT JOIN users tu2 ON tu2.id = tbs.created_by 
            LEFT JOIN users tu3 ON tu3.id = tbs.updated_by
            LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = CAST(LEFT(CAST(tsd.id_adm_kabkot AS TEXT), 2) AS INTEGER)
            LEFT JOIN tb_adm_provinsi tap2 ON tap.id_adm_provinsi = tsd.id_adm_provinsi
            LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tsd.id_adm_kabkot
            WHERE (
              (tbs.updated_date IS NOT NULL AND EXTRACT(YEAR FROM tbs.updated_date) < $1)
              OR (tbs.updated_date IS NULL AND EXTRACT(YEAR FROM tbs.created_date) < $1)
            )
            AND tbs.status != -99
            ${search ? `
              AND (
                tu.first_name ILIKE $2 OR
                tu.middle_name ILIKE $2 OR
                tu.last_name ILIKE $2 OR
                tu.email ILIKE $2 OR
                tu.mobile_number ILIKE $2 OR
                truj.nama ILIKE $2 OR
                tap.nama ILIKE $2 OR
                tak.nama ILIKE $2
              )
            ` : ''}
          ) AS subquery
        `, currentYear,`%${search}%`)
        
        totalRecords = Number(countResult[0]?.count)
        totalPage = Math.ceil(totalRecords / pageSize)
        
          data = await prisma.$queryRawUnsafe(`
          SELECT DISTINCT ON (tbs.${table?.id}) 
            tbs.${table?.id},
            replace(concat(tu.first_name, ' ', tu.middle_name, ' ', tu.last_name), '-', ' ') as nama_pic,
            truj.nama as jabatan,
            tu.email,
            tu.mobile_number,
            CASE 
              WHEN tbs.updated_date IS NOT NULL THEN tbs.updated_date 
              ELSE tbs.created_date 
            END as tanggal_update,
            CASE 
              WHEN tbs.updated_by IS NOT NULL 
              THEN replace(concat(tu3.first_name, ' ', tu3.middle_name, ' ', tu3.last_name), '-', ' ') 
              ELSE replace(concat(tu2.first_name, ' ', tu2.middle_name, ' ', tu2.last_name), '-', ' ') 
            END as diupdate_oleh,
            tap.nama as nama_provinsi,
            tak.nama as nama_kabkot,
            CASE 
              WHEN tbs.updated_date IS NOT NULL 
              THEN EXTRACT(YEAR FROM tbs.updated_date) 
              ELSE EXTRACT(YEAR FROM tbs.created_date) 
            END as tahun,
            tbs.status_proses,
            tbs.keterangan
          FROM ${table?.table} tbs
          LEFT JOIN ${table?.table.replace('_status','')} tb ON tb.${table?.id} = tbs.${table?.id}
          left JOIn tb_sektor_daerah tsd on tsd.id_sektor_daerah = tb.id_sektor_daerah 
          LEFT JOIN tb_user_internal_provinsi tuip ON tuip.id_adm_provinsi = CAST(LEFT(CAST(tsd.id_adm_kabkot AS TEXT), 2) AS INTEGER)
          LEFT JOIN tb_user_internal_provinsi tuip2 ON tuip.id_adm_provinsi = tsd.id_adm_provinsi
          LEFT JOIN users tu ON tu.id = tuip.id_user
          LEFT JOIN tb_user_internal tui ON tui.id_user = tu.id
          LEFT JOIN tb_ref_user_jabatan truj ON truj.id_jabatan = tui.id_jabatan
          LEFT JOIN users tu2 ON tu2.id = tbs.created_by 
          LEFT JOIN users tu3 ON tu3.id = tbs.updated_by
          LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = CAST(LEFT(CAST(tsd.id_adm_kabkot AS TEXT), 2) AS INTEGER)
          LEFT JOIN tb_adm_provinsi tap2 ON tap.id_adm_provinsi = tsd.id_adm_provinsi
          LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tsd.id_adm_kabkot
          WHERE (
            (tbs.updated_date IS NOT NULL AND EXTRACT(YEAR FROM tbs.updated_date) < $3)
            OR (tbs.updated_date IS NULL AND EXTRACT(YEAR FROM tbs.created_date) < $3)
            )
            AND tbs.status != -99
            ${search ? `
              AND (
                tu.first_name ILIKE $4 OR
                tu.middle_name ILIKE $4 OR
                tu.last_name ILIKE $4 OR
                tu.email ILIKE $4 OR
                tu.mobile_number ILIKE $4 OR
                truj.nama ILIKE $4 OR
                tap.nama ILIKE $4 OR
                tak.nama ILIKE $4
              )
            ` : ''}
          LIMIT $1 OFFSET $2  
        `, pageSize, (page - 1) * pageSize, currentYear,`%${search}%`);
      }
    
      
    
      return {
        success: true,
        totalRecords: totalRecords, // Total number of records in the database
        totalPage: totalPage, // Total number of pages
        page: page,
        pageSize: pageSize,
        data: data, // Data for the current page
      };
      
    }
    
    
}