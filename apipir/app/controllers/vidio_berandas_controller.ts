import { createMenuValidator, updateMenuValidator } from '#validators/menu';
import type { HttpContext } from '@adonisjs/core/http'
import { configUpload, get_paginate, img_to_webp, upload } from '../helpers/global_helper.js';
import { createAppSLiderValidator } from '#validators/add_slider_peluang';
import { createLinkTerkaitValidator, createVidioBerandaValidator } from '#validators/link_terkait';
import prisma from '../lib/prisma.js'


export default class LinkTerkaitController {
  /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.tb_vidio_beranda.findMany({
          });

      return {
          success : true,
          data : {data}
      }
  }

  /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
           { url: { contains: search, mode: 'insensitive' } },
        ],
      };
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_vidio_beranda.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const data = await prisma.tb_vidio_beranda.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty

    });

    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {

  }

  /**
     * @store
     * @paramQuery url - Link Url  - @type(string)
     */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createVidioBerandaValidator)
        try {
           const insert = await prisma.tb_vidio_beranda.create({data:data})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_vidio_beranda.findUnique({
          where: {
            id_vidio: Id,
          },
        })

        return {
            success : true,
            data : {data}
        }
  }

  /**
   * Edit individual record
   */
  async edit({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_vidio_beranda.findUnique({
          where: {
            id_vidio: Id,
          },

        })

        return {
            success : true,
            data
        }
  }

  /**
     * @update
     * @paramQuery url - Link Url - @type(string)
     *
     */
  async update({ params, request ,response}: HttpContext) {

      const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }

      const data = await request.validateUsing(createVidioBerandaValidator)
        try {

            const update = await prisma.tb_vidio_beranda.update({
              data:data,
              where:{
                id_vidio:Id
              }
            })
            response.status(201).json({ success: true,data:update })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_vidio_beranda.delete({
        where: {
          id_vidio: Id,
        },
      })
      response.status(201).json({ success: true,data:deletePost })

    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }



}