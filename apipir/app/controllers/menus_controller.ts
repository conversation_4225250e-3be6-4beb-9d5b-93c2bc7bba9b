import { createMenuValidator, updateMenuValidator } from '#validators/menu';
import type { HttpContext } from '@adonisjs/core/http'
import { get_paginate } from '../helpers/global_helper.js';
import prisma from '../lib/prisma.js'

export default class MenusController {
  /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.menu.findMany();
      return {
          success : true,
          data : {data}
      }
  }

  private async getMenuTree(parentId: number | null = null) {
      // Hanya mengambil menu dengan parent_id null (elemen root)
      const menuItems = await prisma.menu.findMany({
        where: {
          parent_id: parentId,
        },
        include: {
          children: true,
        },
        orderBy: {
          order: 'asc',
        },
      });

      const menuTreePromises = menuItems.map(async (menuItem) => {
        const children = await this.getMenuTree(menuItem.id);

        // Struktur dasar untuk setiap item menu
        const transformedMenuItem: any = {
          id: menuItem.id,
          name: menuItem.menu_name,
          icon: menuItem.icon,
        };

        // Tambahkan 'link' hanya jika tidak ada anak
        if (children.length === 0) {
          transformedMenuItem['link'] = menuItem.url;
        } else {
          // Tambahkan 'options' hanya jika ada anak
          transformedMenuItem['options'] = children;
        }

        return transformedMenuItem;
      });

      return Promise.all(menuTreePromises);
  }

  private async getRoleMenuTree(roleId: number | 1) {

      const role = await prisma.role_menu_access.findMany({
        where: {
          role_id: roleId,
        },
        include: {
          menu: true,
        },
      })

      const rolesId = role.map((item) => item.menu?.id)
      const menuItems = await prisma.menu.findMany({
        where: {
          id:{
            in : rolesId
          },
          parent_id: null,
        },
        include: {
          children: {
            where:{
              id: {
                in : rolesId
              }
            },
            include: {
              children: {
                where:{
                  id: {
                    in : rolesId
                  }
                },
                include: {
                  children: {
                    where:{
                      id: {
                        in : rolesId
                      }
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          order: 'asc',
        },
      });

  // Map menu items into a structured format
  return menuItems.map((item) => this.formatMenuItem(item));
}


private formatMenuItem(item) {
  const formattedItem = {
    id: item.menu_name.toLowerCase().replace(/ /g, '-'),
    name: item.menu_name,
    icon: item.parent_id ? undefined : item.icon, // Only root items get an icon
  };

  if (item.children && item.children.length > 0) {
    formattedItem.options = item.children.map((child) => this.formatMenuItem(child));
  } else {
    formattedItem.link = `${item.url.toLowerCase().replace(/ /g, '-')}`; // Adjust link as needed
  }

  return formattedItem;
}


  async get_role_menu({params,response}: HttpContext) {
    const { id_role } = params
    const Id = parseInt(id_role, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ success:false,error: 'Invalid ID provided' })
    }
    // try {
      const menuTree = await this.getRoleMenuTree(Id)
      return response.json({success:true,data:menuTree})
    // } catch (error) {
    //   return response.status(500).json({ error: 'Failed to fetch menu tree' })
    // }

  }

  /**
     * @get_tree
     * @summary Get a list of role with tree view
     */
  async get_tree({response}: HttpContext) {
    try {
      const menuTree = await this.getMenuTree()
      return response.json({success:true,data:menuTree})
    } catch (error) {
      return response.status(500).json({ error: 'Failed to fetch menu tree' })
    }

  }

  /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          {
            menu_name: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
          },
        ],
      };


    }
    // Get total record count with the search condition applied
    const totalRecords = await prisma.menu.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.menu.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty

    });
    const data = datas.map(({...rest }) => {
        return {
            id : rest.id,
            menu_name: rest.menu_name,
            url: rest.url,
        };
    });


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };
  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {

  }

  /**
     * @store
     * @paramQuery menu_name - Nama Menu - @type(string) @required
     * @paramQuery url - Url Menu - @type(string)
     * @paramQuery order - Urutan Menu - @type(number) @required
     * @paramQuery parent_id - ID Parent - @type(number)
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createMenuValidator)
        try {
           const {url,menu_name,order,parent_id,icon } = data
           const dataPost = {
                url,
                menu_name,
                order,
                parent_id,
                icon
                }

            const insert = await prisma.menu.create({data:dataPost})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        const data = await prisma.menu.findUnique({
          where: {
            id: Id,
          },
        })

        return {
            success : true,
            data : {data}
        }
  }

  /**
   * Edit individual record
   */
  async edit({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
        const data = await prisma.menu.findUnique({
          where: {
            id: Id,
          },
        })

        return {
            success : true,
            data
        }
  }

  /**
     * @update
     * @paramQuery menu_name - Nama Menu - @type(string)
     * @paramQuery url - Url Menu - @type(string)
     * @paramQuery order - Urutan Menu - @type(number)
     * @paramQuery parent_id - ID Parent - @type(number)
     */
  async update({ params, request ,response}: HttpContext) {
    const dataPost = await request.validateUsing(updateMenuValidator)
      const { url,menu_name,order,parent_id,icon } = dataPost

      const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }

      try {
        const updatePost = await prisma.menu.update({
          where: {
            id: Id,
          },
          data: {
                url,
                menu_name,
                order,
                parent_id,
                icon
          },
        })
      return response.status(200).json({ success: true, data: updatePost })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data' })
    }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.menu.delete({
        where: {
          id: Id,
        },
      })

      return response.status(200).send({ success: true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }


  async  getMenuWithChildren(parentId = null) {
    const menus =  prisma.menu.findMany({
      where: { parentId },
      include: { children: true },
      orderBy: { order: 'asc' },
    });

    return menus.map(menu => ({
          ...menu,
          children: getMenuWithChildren(menu.id),
        }));
  }

}