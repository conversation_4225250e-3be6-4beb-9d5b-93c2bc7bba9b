import { createPKRefValidator } from '#validators/referensi'
import { createRoleValidator, updateRoleValidator } from '#validators/role'
import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'

export default class RefKontakController {
 
  async index({}: HttpContext) {
    const data = await prisma.tb_peluang_kontak.findMany({})
    return { 
        success : true,
        data : {data}
    }
  }
    /**
     * @get_paginate
     * @summary Get a list of role with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          {
            nama: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
          },
          {
            alamat: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
          },
          {
            cp: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
          },
          {
            jabatan_cp: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
          },
        ],
      };


    }
    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_peluang_kontak.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const data = await prisma.tb_peluang_kontak.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
     
    });
    

    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };
  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
     * @store
     * @summary Insert peluang kontak
     * @paramQuery nama -  Nama - @type(string) @required
     * @paramQuery nama_tr -  Nama_tr - @type(string) @required
     * @paramQuery alamat -  alamat - @type(string) @required
     * @paramQuery jabatan_cp -  urlweb - @type(string) @required
     * @paramQuery cp -  cp - @type(string) @required
     * @paramQuery jabatan_cp -  jabatan_cp - @type(string) @required
     * @paramQuery email -  email - @type(string) @required
     * @paramQuery no_telp -  no_telp - @type(string) @required
     * @paramQuery no_fax -  no_fax - @type(string) @required
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createPKRefValidator)
        try {
           
           const dataPost = {
              nama: data.nama,
              nama_tr: data.nama_tr,
              alamat: data.alamat,
              url_web: data.url_web,
              cp: data.cp,
              jabatan_cp: data.jabatan_cp,
              email:data.email,
              no_telp:data.no_telp,
              no_fax:data.no_fax,
            }
            const insert = await prisma.tb_peluang_kontak.create({data:dataPost})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
    const data = await prisma.tb_peluang_kontak.findUnique({
      where: {
        id_peluang_kontak: Id,
      },
    })

    return { 
      success : true,
      data : data
    }
  }

  
  async edit({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    const data = await prisma.tb_peluang_kontak.findUnique({
      where: {
        id_peluang_kontak: Id,
      },
    })

    return { 
      success : true,
      data : {data}
    }
  }

  /**
     * @update
     * @paramQuery nama -  Nama - @type(string) @required
     * @paramQuery nama_tr -  Nama Tr - @type(string) @required
     * @paramQuery alamat -  alamat - @type(string) @required
     * @paramQuery jabatan_cp -  urlweb - @type(string) @required
     * @paramQuery cp -  cp - @type(string) @required
     * @paramQuery jabatan_cp -  jabatan_cp - @type(string) @required
     * @paramQuery email -  email - @type(string) @required
     * @paramQuery no_telp -  no_telp - @type(string) @required
     * @paramQuery no_fax -  no_fax - @type(string) @required
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
  async update({ params, request ,response}: HttpContext) {
    const data = await request.validateUsing(createPKRefValidator)

    const Id = parseInt(params.id, 10)

    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const updatePost = await prisma.tb_peluang_kontak.update({
        where: {
          id_peluang_kontak: Id,
        },
        data: {
          nama: data.nama,
          nama_tr: data.nama_tr,
          alamat: data.alamat,
          url_web: data.url_web,
          cp: data.cp,
          jabatan_cp: data.jabatan_cp,
          email:data.email,
          no_telp:data.no_telp,
          no_fax:data.no_fax,
        },
      })
      return response.status(200).json({ success: true, data: updatePost })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data' })
    }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_peluang_kontak.delete({
        where: {
          id_peluang_kontak: Id,
        },
      })
      return response.status(200).send({success:true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }
}