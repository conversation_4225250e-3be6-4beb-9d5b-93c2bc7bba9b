import { createUmkmValidator, updateUmkmValidator } from '#validators/umkm'
import { createUmrProvValidator } from '#validators/umr'
import type { HttpContext } from '@adonisjs/core/http'
import { configUpload, img_to_webp } from '../helpers/global_helper.js'
import env from '#start/env'
import { createSNRefValidator, updateSNRefValidator } from '#validators/referensi'
import { messages } from '@vinejs/vine/defaults'
import prisma from '../lib/prisma.js'
export default class RefSektorNasionalController {

  async index({}: HttpContext) {
    const data = await prisma.tb_sektor_nasional_ref.findMany({})
    return {
        success : true,
        data : data
    }
  }
   /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
   async get_paginate({params,response,request}:HttpContext) {
    const parameters =  request.qs()
    let order = parameters.order
    const by = ['asc','desc'].includes(parameters.by) ? parameters.by : 'asc'
    let orderBy={}
    const paramList = ['nama_sektor','nama_sektor_tr']
    if (order != undefined && paramList.includes(order)) {
        if (order == 'nama_sektor') {
            orderBy = {nama:by}
        }else if (order == 'nama_sektor_tr') {
          orderBy = {sektor_nasional_tr:{nama:by}}
        }else{
            orderBy = {[order]:by}
        }
    }

    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = params.search || ''; // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          { nama: { contains: search, mode: 'insensitive' } },
          {
            sektor_nasional_tr: {
                nama: {
                  contains: search,mode: "insensitive"
              }
            }
          }
        ],
      };
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_sektor_nasional_ref.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_sektor_nasional_ref.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      orderBy:orderBy,
      include:{
        sektor_nasional_tr:true
      }
    });
    const data = datas.map((item =>{
        return {
            id_sektor_nasional_ref : item.id_sektor_nasional_ref,
            id_sektor : item.id_sektor,
            nama_sektor : item.nama,
            nama_sektor_tr : item.sektor_nasional_tr?.nama,
            nama_file_image : `${env.get('APP_URL')}/uploads/icon-web/${item.icon}`,
        }
    }))


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
     * @store
     * @summary Insert Referensi Sektor Nasional Provinsi
     * @paramQuery nama - nama - @type(string) @required
     * @paramQuery nama_tr - nama_tr - @type(string) @required
     * @paramQuery kd_bahasa - kd_bahasa - @type(string) @required
     * @paramQuery image - image - @type(file) @required
     * @paramQuery image_slider - image slider - @type(file) @required
  */
    public async store({ request, response }: HttpContext) {
      let data = await request.validateUsing(createSNRefValidator)
        try {
            const configImg = await configUpload('img');
            const icon = request.files('icon', configImg);
            const iconmap = request.files('iconmap', configImg);
            if (icon) {
                const uploadPromisesUpImg = icon.map(async (item) => {
                    const uploadPath = `uploads/icon-web/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    data.icon = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }
            if (iconmap) {
                const uploadPromisesUpImg = iconmap.map(async (item) => {
                    const uploadPath = `uploads/icon-web/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    data.iconmap = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }
            const lastSektor = await prisma.tb_sektor_nasional_ref.findFirst({
              orderBy: {
                id_sektor: 'desc',
              },
            })
            const id_sektor =  lastSektor?.id_sektor != undefined ? lastSektor?.id_sektor+1 : 1;
            const dataPost = {
              id_sektor,
              nama : data.nama,
              icon : data?.icon,
              iconmap : data?.iconmap,
              id_kategori_sektor :data.id_kategori_sektor
            }
            const insert = await prisma.tb_sektor_nasional_ref.create({data:dataPost})
            const dataPostTr = {
              id_sektor ,
              nama : data.nama_tr,
              kd_bahasa : data.kd_bahasa,
              icon : data?.icon
            }
            const insertTr = await prisma.tb_sektor_nasional_ref_tr.create({data:dataPostTr})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
    const data = await prisma.tb_sektor_nasional_ref.findUnique({
      where: {
        id_sektor_nasional_ref: Id,
      },
      include:{
        sektor_nasional_tr:true
      }
    })
// Pengecekan manual apakah `sektor_nasional_file` ada
    if (data && data.icon ) {
      data.file_icon = `${env.get('APP_URL')}/uploads/icon-web/${data.icon}`;
    }
    if (data && data.iconmap ) {
      data.file_iconmap = `${env.get('APP_URL')}/uploads/icon-web/${data.iconmap}`;
    }

    return {
      success : true,
      data : data
    }
  }


  /**
     * @update
     * @paramQuery nama - nama - @type(string) @required
     * @paramQuery nama_tr - nama_tr - @type(string) @required
     * @paramQuery kd_bahasa - kd_bahasa - @type(string) @required
     * @paramQuery image - image - @type(file) @required
     */
  async update({ params, request ,response}: HttpContext) {


    const Id = parseInt(params.id, 10)

    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }
    try {
      const data = await request.validateUsing(updateSNRefValidator)
      const configImg = await configUpload('img');
      const icon = request.files('icon', configImg);
      const iconmap = request.files('iconmap', configImg);
      let dataPost = {
          id_sektor : Id,
          nama : data.nama,
          id_kategori_sektor : data.id_kategori_sektor,
        }
      if (icon) {
          const uploadPromisesUpImg = icon.map(async (item) => {
              const uploadPath = `uploads/icon-web/`;
              const upImg = await img_to_webp(item, uploadPath);
              dataPost.icon = upImg.data.filename;
          });
          await Promise.all(uploadPromisesUpImg);
      }
      if (iconmap) {
          const uploadPromisesUpImg = iconmap.map(async (item) => {
              const uploadPath = `uploads/icon-web/`;
              const upImg = await img_to_webp(item, uploadPath);
              dataPost.iconmap = upImg.data.filename;
          });
          await Promise.all(uploadPromisesUpImg);
      }


      const update = await prisma.tb_sektor_nasional_ref.update({
          data:dataPost,
          where:{
            id_sektor : Id,
          }
        })
         let tr = request.input('tr')
      if (typeof tr === 'string') {
          tr = JSON.parse(tr);
      }
      if (Array.isArray(tr)) {
          // Jika panjang array JSON dan file gambar tidak sesuai, kembalikan error
          await Promise.all(
            tr.map(item => {
              const dataPostTr = {
                nama: item.nama,
                kd_bahasa: item.kd_bahasa,
                icon: data.image
              };
              console.log(dataPostTr);

              return prisma.tb_sektor_nasional_ref_tr.update({
                data: dataPostTr,
                where: {
                  id_sektor: parseInt(item.id_sektor),
                },
              });
            })
          );

      }




      return response.status(200).json({ success: true, data: update })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data',messages:error.message })
    }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_sektor_nasional_ref.delete({
        where: {
          id_sektor_nasional_ref: Id,
        },
      })
      return response.status(200).send({success:true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }
}