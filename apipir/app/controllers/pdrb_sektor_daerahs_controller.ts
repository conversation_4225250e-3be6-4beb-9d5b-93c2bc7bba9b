// import type { HttpContext } from '@adonisjs/core/http'

import { createPdrbSDValidator } from "#validators/sektor_daerah";
import type { HttpContext } from '@adonisjs/core/http'
import { get_paginate, getMe, numberFormat } from '../helpers/global_helper.js';
import { aprovePdrbValidator, aproveValidator } from "../validators/aprove.js";
import { getUserData } from "../helpers/auth_helper.js";
import prisma from '../lib/prisma.js'
export default class PdrbSektorDaerahsController {

     /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.tb_sektor_daerah_pdrb.findMany({

    });

      return {
          success : true,
          data : {data}
      }
  }

  async get_paginate({params,response,request}:HttpContext) {
    const parameters =  request.qs()
    const order = parameters.order
    const by = ['asc','desc'].includes(parameters.by) ? parameters.by : 'asc'
    let orderBy={}
    const paramList = ['tahun_pdrb','status']
    if (order != undefined && paramList.includes(order)) {
      orderBy = {[order]:by}
    }
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided
    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object
    if (search !== ',' && search !== '' && search !== '%7Bsearch%7D') {
      searchCondition = {
        OR: [
          {
            tb_adm_provinsi: {
              nama: {
                contains: search,
                mode: "insensitive",
              },
            },
          },
          {
            tb_adm_kabkot: {
              nama: {
                contains: search,
                mode: "insensitive",
              },
            },
          },
          {
            tb_adm_kabkot: {
              tb_adm_provinsi:{
                nama: {
                  contains: search,
                  mode: "insensitive",
                },
              },
            }
          },
          {
            tb_sumber_data: {
              tb_sumber_data_judul: {
                judul: {
                  contains: search,
                  mode: "insensitive",
                },
              },
            },
          },
          {
            tb_sektor_nasional: {
              sektor: {
                nama: {
                  contains: search,
                  mode: "insensitive",
                },
              },
            },
          },
        ],
      };
    }

    const req = request.qs()
    if (req.id_adm_provinsi) {
      (searchCondition.AND ??= []).push({
        OR: [
          { id_adm_provinsi: parseInt(req.id_adm_provinsi) },
          {
            id_adm_kabkot: {
              gte: parseInt(`${req.id_adm_provinsi}00`),
              lt: parseInt(`${req.id_adm_provinsi}99`)
            }
          }
        ]
      });
    }



    if (req.id_adm_kabkot) {
      (searchCondition.AND ??= []).push({ id_adm_kabkot: parseInt(req.id_adm_kabkot) });
    }
    if (req.status) {
        (searchCondition.AND ??= []).push(
          { status: parseInt(req.status) }
  
        );
    }
    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_sektor_daerah_pdrb.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    const datas = await prisma.tb_sektor_daerah_pdrb.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      orderBy:orderBy,
      where: searchCondition, // Apply searchCondition only if it's not empty
      include:{
        tb_adm_provinsi:true,
        tb_adm_kabkot:{
          include:{
            tb_adm_provinsi:true
          }
        },
        tb_sumber_data:{
          include:{
            tb_sumber_data_judul:true
          }
        },
        tb_sektor_nasional:{
          include:{
            sektor:true
          }
        },
      }
    });

    const data = await Promise.all(
      datas.map(async (item) => {
        let lq = 0;
        let pdb_sektor = null;
        let pdb_total = null;
        let pdrb_sektor = null;
        let pdrb_total = null;

        if (item.id_adm_kabkot == null) {
          const tot_pdb_sektor = await prisma.tb_sektor_nasional_pdb.aggregate({
            where: {
              id_sektor_nasional: item.id_sektor_nasional,
              tahun_pdb: item.tahun_pdrb,
            },
            _sum: {
              jumlah_pdb: true, // Kolom yang ingin dijumlahkan
            },
          });
          const tot_pdb_total = await prisma.tb_sektor_nasional_pdb.aggregate({
            where: {
              tahun_pdb: item.tahun_pdrb,
            },
            _sum: {
              jumlah_pdb: true, // Kolom yang ingin dijumlahkan
            },
          });
          const tot_pdrb_sektor = await prisma.tb_sektor_daerah_pdrb.aggregate({
            where: {
              id_adm_provinsi:item.id_adm_provinsi,
              tahun_pdrb: item.tahun_pdrb,
              id_sektor_nasional:item.id_sektor_nasional
            },
            _sum: {
              jumlah_pdrb: true, // Kolom yang ingin dijumlahkan
            },
          });
          const tot_pdrb_total = await prisma.tb_sektor_daerah_pdrb.aggregate({
            where: {
              id_adm_provinsi:item.id_adm_provinsi,
              tahun_pdrb: item.tahun_pdrb,
            },
            _sum: {
              jumlah_pdrb: true, // Kolom yang ingin dijumlahkan
            },
          });

          // Akses total jumlah_pdb
          pdb_sektor = tot_pdb_sektor._sum.jumlah_pdb?.toNumber?.() || 0;
          pdb_total = tot_pdb_total._sum.jumlah_pdb?.toNumber?.() || 0;
          pdrb_sektor = tot_pdrb_sektor._sum.jumlah_pdrb?.toNumber?.() || 0;
          pdrb_total = tot_pdrb_total._sum.jumlah_pdrb?.toNumber?.() || 0;

          lq = (pdrb_sektor/pdrb_total)/(pdb_sektor /pdb_total)

        } else {
            const tot_pdb_sektor = await prisma.tb_sektor_daerah_pdrb.aggregate({
              where: {
                id_adm_provinsi:parseInt(item.id_adm_kabkot.toString().slice(0,2)),
                tahun_pdrb: item.tahun_pdrb,
                id_sektor_nasional: item.id_sektor_nasional,
              },
              _sum: {
                jumlah_pdrb: true, // Kolom yang ingin dijumlahkan
              },
            });

          const tot_pdb_total = await prisma.tb_sektor_daerah_pdrb.aggregate({
            where: {
              id_adm_provinsi:parseInt(item.id_adm_kabkot.toString().slice(0,2)),
              tahun_pdrb: item.tahun_pdrb,
            },
            _sum: {
              jumlah_pdrb: true, // Kolom yang ingin dijumlahkan
            },
          });

          const tot_pdrb_sektor = await prisma.tb_sektor_daerah_pdrb.aggregate({
            where: {
              id_adm_kabkot:item.id_adm_kabkot,
              id_sektor_nasional:item.id_sektor_nasional,
              tahun_pdrb: item.tahun_pdrb,
            },
            _sum: {
              jumlah_pdrb: true,
            },
          });

          const tot_pdrb_total = await prisma.tb_sektor_daerah_pdrb.aggregate({
            where: {
              id_adm_kabkot:item.id_adm_kabkot,
              tahun_pdrb: item.tahun_pdrb,
            },
            _sum: {
              jumlah_pdrb: true,
            },
          });
           pdb_sektor = tot_pdb_sektor._sum.jumlah_pdrb?.toNumber?.() || 0;
          pdb_total = tot_pdb_total._sum.jumlah_pdrb?.toNumber?.() || 0;
          pdrb_sektor = tot_pdrb_sektor._sum.jumlah_pdrb?.toNumber?.() || 0;
          pdrb_total = tot_pdrb_total._sum.jumlah_pdrb?.toNumber?.() || 0;

          lq = (pdrb_sektor/pdrb_total)/(pdb_sektor /pdb_total)
        }

        return {
          id_sektor_daerah_pdrb: item.id_sektor_daerah_pdrb,
          nama_provinsi: item.tb_adm_provinsi?.nama ? item.tb_adm_provinsi?.nama  :  item.tb_adm_kabkot?.tb_adm_provinsi.nama,
          nama_kabkot: item.tb_adm_kabkot?.nama,
          nama_sektor: item.tb_sektor_nasional?.sektor.nama,
          tahun_pdrb: item.tahun_pdrb,
          sumber_data: item.tb_sumber_data?.tb_sumber_data_judul.judul,
          jumlah_pdrb: `Rp. ${numberFormat(parseInt(item.jumlah_pdrb))}`,
          pdb_sektor : `Rp. ${numberFormat(parseInt(pdb_sektor))}`,
          pdb_total : `Rp. ${numberFormat(parseInt(pdb_total))}`,
          pdrb_sektor : `Rp. ${numberFormat(parseInt(pdrb_sektor))}`,
          pdrb_total : `Rp. ${numberFormat(parseInt(pdrb_total))}`,
          lq : lq,
          status: item.status,
        };
      })
    );



    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {

  }

  /**
     * @store
     * @paramQuery id_adm_provinsi - id adm provinsi - @type(number)
     * @paramQuery id_adm_kabkot - id adm kabkot - @type(number)
     * @paramQuery id_sektor_nasional - Id Sektor Nasional - @type(number) @required
     * @paramQuery tahun_pdrb - Tahun PDRB - @type(number) @required
     * @paramQuery id_sumber_data - id sumber data - @type(number) @required
     * @paramQuery jumlah_pdrb - Nominal PDRB - @type(number) @required
     */
    public async store({ request, response ,auth}: HttpContext) {


      let data = await request.validateUsing(createPdrbSDValidator)
      if (data.id_adm_kabkot ) {
        const exist = await prisma.tb_sektor_daerah_pdrb.findFirst({
          where:{
            id_sektor_nasional:data.id_sektor_nasional,
            tahun_pdrb:data.tahun_pdrb,
            id_adm_kabkot:data.id_adm_kabkot
          }
        })
        if (exist) {
          return response.status(500).json({ success: false, message: 'Provinsi dengan Sektor dan Tahun PDRB sudah ada.' })
        }
      }else{
        const exist = await prisma.tb_sektor_daerah_pdrb.findFirst({
          where:{
            id_sektor_nasional:data.id_sektor_nasional,
            tahun_pdrb:data.tahun_pdrb,
            id_adm_provinsi:data.id_adm_provinsi
          }
        })
        if (exist) {
          return response.status(500).json({ success: false, message: 'Kabupaten/Kota dengan Sektor dan Tahun PDRB sudah ada.' })
        }
      }

        try {

            data['status'] = 0
            const insert = await prisma.tb_sektor_daerah_pdrb.create({data:data})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_sektor_daerah_pdrb.findUnique({
          where: {
            id_sektor_daerah_pdrb: Id,
          },
          include:{
            tb_adm_kabkot:{
              include:{
                tb_adm_provinsi:true
              }
            },
          }
        })

        if (data && data.tb_adm_kabkot?.tb_adm_provinsi.id_adm_provinsi) {
          data.id_adm_provinsi= data.tb_adm_kabkot?.tb_adm_provinsi.id_adm_provinsi
        }

        return {
            success : true,
            data : {data}
        }
  }

  /**
   * Edit individual record
   */
  async edit({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_sektor_daerah_pdrb.findUnique({
          where: {
            id_sektor_daerah_pdrb: Id,
          },
        })

        return {
            success : true,
            data
        }
  }

  /**
     * @update
     * @paramQuery id_adm_provinsi - id adm provinsi - @type(number)
     * @paramQuery id_adm_kabkot - id adm kabkot - @type(number)
     * @paramQuery id_sektor_nasional - Id Sektor Nasional - @type(number) @required
     * @paramQuery tahun_pdrb - Tahun PDRB - @type(number) @required
     * @paramQuery id_sumber_data - id sumber data - @type(number) @required
     * @paramQuery jumlah_pdrb - Nominal PDRB - @type(number) @required
     */
  async update({ params, request ,response}: HttpContext) {

      const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }

      const data = await request.validateUsing(createPdrbSDValidator)
        try {
            data['status'] = 0
            const insert = await prisma.tb_sektor_daerah_pdrb.update({
              data:data,
              where:{
                id_sektor_daerah_pdrb:Id
              }
            })
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_sektor_daerah_pdrb.delete({
        where: {
          id_sektor_daerah_pdrb: Id,
        },
      })
      return response.status(200).send(deletePost)
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }
  /**
       * @change_status
       * @paramQuery status - status - @type(number) @required
       * @paramQuery tahun_pdrb - tahun_pdrb - @type(number) @required
       * @paramQuery keterangan - keterangan - @type(string)  @required
       */
      async change_status({ params, request, response ,auth}: HttpContext) {
          // Ambil dan parse ID dari parameter URL
          const Id = parseInt(params.id, 10);

          // Validasi ID
          if (isNaN(Id)) {
              return response.status(400).json({ error: 'Invalid ID provided' });
          }
          try {
              // Validasi input dengan validator yang telah dibuat
              const dataPost = await request.validateUsing(aprovePdrbValidator);

              // Update data di tb_sektor_nasional
              const update = await prisma.tb_sektor_daerah_pdrb.update({
              where: {
                  id_sektor_daerah_pdrb: Id
              },
                  data: {
                      status: parseInt(dataPost.status)
                  },
              });
              // Insert ke tb_sektor_nasional_status
              const insert = await prisma.tb_sektor_daerah_pdrb_status.create({
                  data: {
                      id_sektor_daerah_pdrb: Id,
                      tahun_pdrb: dataPost.tahun_pdrb,
                      status: dataPost.status,
                      status_proses: dataPost.status,
                      keterangan: dataPost.keterangan,
                      created_by:auth.user?.id,
                      updated_by:auth.user?.id,
                      created_date:new Date()
                  },
              });
              // Berikan respon sukses dengan data update
              return response.status(201).json({ success: true, data: update });
          } catch (error) {
              // Tangani error dan kirimkan respon gagal
              return response.status(500).json({ success: false, message: error.message });
          }
      }

}