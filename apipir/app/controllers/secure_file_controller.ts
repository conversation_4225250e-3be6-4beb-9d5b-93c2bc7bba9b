import type { HttpContext } from '@adonisjs/core/http'
import { SecureFileUploader } from '#helpers/secure_file_uploader'

export default class SecureFileController {

  /**
   * Secure file upload endpoint
   */
  async upload({ request, response, auth }: HttpContext) {
    try {
      // Check authentication
      if (!auth.user) {
        return response.status(401).json({
          success: false,
          error: 'Authentication required'
        })
      }

      // Get upload parameters
      const model = request.input('model')
      const idPath = request.input('id_path')
      
      // Validate required parameters
      if (!model) {
        return response.status(400).json({
          success: false,
          error: 'Model parameter is required'
        })
      }

      // Get uploaded file
      const file = request.file('file', {
        size: '50mb', // Maximum size handled by SecureFileUploader
        extnames: ['jpg', 'jpeg', 'png', 'webp', 'gif', 'pdf', 'doc', 'docx', 'txt', 'mp4', 'mov', 'avi', 'webm']
      })

      if (!file) {
        return response.status(400).json({
          success: false,
          error: 'No file uploaded'
        })
      }

      // Use secure file uploader
      const result = await SecureFileUploader.uploadFile(
        file,
        model,
        idPath ? parseInt(idPath, 10) : undefined
      )

      if (!result.success) {
        return response.status(400).json(result)
      }

      // Log successful upload
      console.log(`File uploaded successfully by user ${auth.user.id}: ${result.fileName}`)

      return response.json({
        success: true,
        message: 'File uploaded successfully',
        data: {
          fileName: result.fileName,
          originalName: result.originalName,
          uploadedBy: auth.user.id,
          uploadedAt: new Date().toISOString()
        }
      })

    } catch (error) {
      console.error('File upload error:', error)
      return response.status(500).json({
        success: false,
        error: 'Upload failed'
      })
    }
  }

  /**
   * Secure file serving endpoint
   */
  async serve({ request, response, auth }: HttpContext) {
    try {
      const filePath = request.input('path')
      
      if (!filePath) {
        return response.status(400).json({
          success: false,
          error: 'File path is required'
        })
      }

      // Get user role for access control
      const userRole = auth.user?.role || 'guest'

      // Use secure file server
      const result = await SecureFileUploader.serveFile(filePath, userRole)

      if (!result.success) {
        return response.status(404).json(result)
      }

      // Serve the file
      return response.download(result.filePath!)

    } catch (error) {
      console.error('File serving error:', error)
      return response.status(500).json({
        success: false,
        error: 'File serving failed'
      })
    }
  }

  /**
   * Secure file deletion endpoint
   */
  async delete({ request, response, auth }: HttpContext) {
    try {
      // Check authentication and authorization
      if (!auth.user) {
        return response.status(401).json({
          success: false,
          error: 'Authentication required'
        })
      }

      // Only allow admin users to delete files
      if (auth.user.role !== 'admin') {
        return response.status(403).json({
          success: false,
          error: 'Admin access required'
        })
      }

      const filePath = request.input('path')
      
      if (!filePath) {
        return response.status(400).json({
          success: false,
          error: 'File path is required'
        })
      }

      // Use secure file deletion
      const success = await SecureFileUploader.deleteFile(filePath)

      if (!success) {
        return response.status(400).json({
          success: false,
          error: 'File deletion failed'
        })
      }

      // Log file deletion
      console.log(`File deleted by admin user ${auth.user.id}: ${filePath}`)

      return response.json({
        success: true,
        message: 'File deleted successfully'
      })

    } catch (error) {
      console.error('File deletion error:', error)
      return response.status(500).json({
        success: false,
        error: 'File deletion failed'
      })
    }
  }

  /**
   * Get upload configuration and limits
   */
  async getUploadConfig({ response }: HttpContext) {
    try {
      const config = {
        maxFileSizes: {
          image: '5MB',
          document: '10MB',
          video: '50MB'
        },
        allowedExtensions: {
          image: ['jpg', 'jpeg', 'png', 'webp', 'gif'],
          document: ['pdf', 'doc', 'docx', 'txt'],
          video: ['mp4', 'mov', 'avi', 'webm']
        },
        allowedMimeTypes: [
          'image/jpeg',
          'image/png',
          'image/webp',
          'image/gif',
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain',
          'video/mp4',
          'video/quicktime',
          'video/x-msvideo',
          'video/webm'
        ],
        securityFeatures: [
          'File type validation',
          'MIME type verification',
          'File size limits',
          'Malicious content scanning',
          'Path traversal prevention',
          'UUID-based file naming',
          'Secure storage location'
        ]
      }

      return response.json({
        success: true,
        data: config
      })

    } catch (error) {
      console.error('Error getting upload config:', error)
      return response.status(500).json({
        success: false,
        error: 'Failed to get upload configuration'
      })
    }
  }
} 