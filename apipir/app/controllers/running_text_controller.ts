import { createMenuValidator, updateMenuValidator } from '#validators/menu';
import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'
import { configUpload, get_paginate, img_to_webp, upload } from '../helpers/global_helper.js';
import { createAppSLiderValidator } from '#validators/add_slider_peluang';
import { createLinkTerkaitValidator, createRunningTextValidator, updateLinkTerkaitValidator, updateRunningTextValidator } from '#validators/link_terkait';
import env from '#start/env';



export default class RunningTextController {
  /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.tb_running_text.findMany({
          });

      return { 
          success : true,
          data : {data}
      }
  }

  /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          { keterangan: { contains: search, mode: 'insensitive' } }, 
        ],
      };
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_running_text.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_running_text.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    const data = datas.map((item =>{
        return {
          ...item,
        }
    }))

    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {

  }

  /**
     * @store
     * @paramQuery keterangan - keterangan - @type(string) 
     * @paramQuery keterangan_tr - keterangan tr - @type(string) 
     * @paramQuery date - date - @type(string) 
     */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createRunningTextValidator)
        try {
            const configImg = await configUpload('img')
            const image = request.files('image',configImg);
            
         
           const dataPost = {
                keterangan : data.keterangan,
                keterangan_tr : data.keterangan_tr,
                date : `${data.date}T00:00:00Z`,
                status:99
            }

                
           const insert = await prisma.tb_running_text.create({data:dataPost})
 
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        let data = await prisma.tb_running_text.findUnique({
          where: {
            id_running_text: Id,
          },
        })

        
        return { 
            success : true,
            data : {data}
        }
  }

  /**
   * Edit individual record
   */
  async edit({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_running_text.findUnique({
          where: {
            id_running_text: Id,
          },
          
        })

        return { 
            success : true,
            data
        }
  }

  /**
     * @update
     * @paramQuery keterangan - keterangan - @type(string) 
     * @paramQuery date - date - @type(string) 
     * 
     */
  async update({ params, request ,response}: HttpContext) {
  
      const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
      const data = await request.validateUsing(updateRunningTextValidator)
        try {
           
            const dataPost = {
                keterangan : data.keterangan,
                keterangan_tr : data.keterangan_tr,
                date : `${data.date}T00:00:00Z`,
                status:99
            }
              
            const update = await prisma.tb_running_text.update({
              data:dataPost,
              where:{
                id_running_text:Id
              }
            })
            response.status(201).json({ success: true,data:update })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_running_text.delete({
        where: {
          id_running_text: Id,
        },
      })
      response.status(201).json({ success: true,data:deletePost })
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }



}