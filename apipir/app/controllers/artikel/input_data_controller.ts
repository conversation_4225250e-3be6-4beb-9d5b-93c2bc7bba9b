import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../helpers/model_helper.js';

import prisma from '../../lib/prisma.js';
import uploadFile from '../../helpers/file_uploader.js';
// import { env } from 'process';



export default class ArtikelInputDataController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id: { column: 'id', alias: 'id', type: 'int' },
                        jenis: { column: 'jenis', alias: 'jenis', type: 'int' },
                        judul: { column: 'judul', alias: 'judul', type: 'string' },
                        deskripsi_singkat: { column: 'deskripsi_singkat', alias: 'deskripsi_singkat', type: 'string' },
                        deskripsi: { column: 'deskripsi', alias: 'deskripsi', type: 'string' },
                        status: { column: 'status', alias: 'status', type: 'int' },
                        file_cover: { column: 'file_cover', alias: 'file_cover', type: 'string' },
                        id_news_kategori: { column: 'id_news_kategori', alias: 'id_news_kategori', type: 'int' },
                        id_adm_kabkot: { column: 'id_adm_kabkot', alias: 'id_adm_kabkot', type: 'int' },
                        id_adm_provinsi: { column: 'id_adm_provinsi', alias: 'id_adm_provinsi', type: 'int' },
                        id_komoditi: { column: 'id_komoditi', alias: 'id_komoditi', type: 'int' },
                        files: { column: 'files', alias: 'files', type: 'array' },
                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, response }: HttpContext) {
                const params = request.qs();
                let q =''
                let isAll = false;

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }
                if (request.input('q')) {
                        q = request.input('q');
                }
                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let whereDefault = {}
                if (q) {
                        whereDefault = {
                                OR: [
                                    {
                                        tb_adm_provinsi :{
                                                nama: {
                                                        contains: q,
                                                        mode: 'insensitive', // Mengabaikan besar kecil huruf
                                                },
                                        },
                                    },
                                    {
                                        tb_adm_kabkot :{
                                                nama: {
                                                        contains: q,
                                                        mode: 'insensitive', // Mengabaikan besar kecil huruf
                                                },
                                        },
                                    },
                                    {
                                            judul: {
                                                contains: q,
                                                mode: 'insensitive', // Mengabaikan besar kecil huruf
                                            },
                                    },
                                    {
                                            deskripsi: {
                                                contains: q,
                                                mode: 'insensitive', // Mengabaikan besar kecil huruf
                                            },
                                    },
                                    {
                                            deskripsi_singkat: {
                                                contains: q,
                                                mode: 'insensitive', // Mengabaikan besar kecil huruf
                                            },
                                    },
                                ]
                            };
                }
                
                const kategori = queryParams.kategori
                const idProvinsi = queryParams.id_adm_provinsi
                const idKabkot = queryParams.id_adm_kabkot
                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        // where: {
                        //         tb_adm_provinsi: {
                        //                 nama: {
                        //                         equals: queryParams
                        //                 },
                        //         }
                        // },
                        include: {
                                tb_news_kategori: true,
                                tb_adm_kabkot:true,
                                tb_adm_provinsi:true
                        },
                        orderBy: {
                                id: "desc"
                        }
                }
                // return idProvinsi
                let order = queryParams.order 
                const by = ['asc','desc'].includes(queryParams.by) ? queryParams.by : 'asc'
                const paramList = ['status']
                if (order != undefined && paramList.includes(order)) {
                        options.orderBy = {[order]:by}
                }
                if (kategori=='potensi') {
                        whereDefault.AND = []
                        whereDefault.AND.push({id_news_kategori: 1})
                        if (idProvinsi != undefined ) {
                                whereDefault.AND.push({id_adm_provinsi : parseInt(idProvinsi)})
                        }
                        
                        if (queryParams.prov == "true") {
                                whereDefault.AND.push({id_adm_provinsi : {
                                        not: null
                                        }
                                })
                        }


                        if (idKabkot != undefined && idKabkot != "true") {
                                whereDefault.AND.push({id_adm_kabkot:parseInt(idKabkot)})
                        }
                        if (queryParams.kabkot == "true") {
                                whereDefault.AND.push({id_adm_kabkot : {
                                        not:null
                                }})
                        }
                }
                if (kategori=='hilirisasi') {
                        whereDefault.AND = []
                        whereDefault.AND.push({id_news_kategori:5})
                }
                if (kategori=='artikel') {
                        whereDefault.AND = []
                        whereDefault.AND.push({OR:[{id_news_kategori:2},{id_news_kategori:3},{id_news_kategori:4}]})
                }
                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }
                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                let where = modelWhereAnd((await this.schemaData()).columns, queryParams);
                if (Object.keys(where).length !== 0 || Object.keys(whereDefault).length > 0) {
                        options['where'] = {
                            AND: [
                                whereDefault, 
                                where,        
                            ],
                        };
                        // return whereDefault
                }
                // return options
                try {
                        const data = await prisma.tb_news.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        // if (options['where'] && (!data || data.length === 0)) {
                        //         return response.status(404).json({
                        //                 success: false,
                        //                 message: "Data yang kamu cari tidak ada"
                        //         });
                        // }
                        // Pagination
                        const totalCount = await prisma.tb_news.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                // const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        // status_text,
                                }
                        })

                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                                error:error.message
                        });
                }
        }
        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {};

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id': parseInt(params.id)
                }

                const data = await prisma.tb_news.findFirst(
                        {
                                where: { id: parseInt(params.id) },
                                include:
                                        { translations: true, files: true }
                        });
                const sanitizeFileName = (fileName: string): string => {
                        const fileBaseName = fileName.replace(/\s+/g, "_").replace(/\.[^/.]+$/, ""); // Remove spaces and extension
                        const fileExtension = fileName.split('.').pop()?.toLowerCase() || "";
                        return fileExtension === "pdf" ? `${fileBaseName}.pdf` : `${fileBaseName}.webp`; // Replace non-PDF extensions with .webp
                };
                const getFoto = data?.file_cover ? sanitizeFileName(data.file_cover) : "";
                const getDokumen = data?.files.map((item) => sanitizeFileName(item.nama));
                const pathFoto = `${process.env.APP_URL}/uploads/berita/${params.id}/${getFoto}`;
                const pathDokumen = getDokumen?.map(
                        (doc) => `${process.env.APP_URL}/uploads/berita/${params.id}/${doc}`
                );
                return response.status(200).json({
                        success: true,
                        data: { ...data, pathFoto: pathFoto, pathDokumen: pathDokumen },
                });
        }

        public async createOrUpdate({ request, response, params,auth }: HttpContext) {
                const paramID = parseInt(params.id);
                const model = "artikel_input_data";

                try {
                        const result = await prisma.$transaction(async (prisma) => {
                                let fileFotoName: string = "";
                                let fileDokumenName: string = "";
                                let resID: number | undefined;

                                const fileFoto = request.file("file_foto");
                                const fileDokumen = request.file("file_dokumen");

                                if (!paramID) {
                                        console.log("Sync tb_news id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_news"', 'id')),
                                                (SELECT (MAX("id") + 1) FROM "tb_news"),
                                                false) FROM "tb_news";
                                         `);

                                        // Create news first to get res.id for resID
                                        const res = await prisma.tb_news.create({
                                                data: {
                                                        jenis: parseInt(request.input("jenis")) || 0,
                                                        judul: request.input("judul") || "",
                                                        deskripsi_singkat: request.input("deskripsi_singkat") || "",
                                                        deskripsi: request.input("deskripsi") || "",
                                                        status:  0,
                                                        file_cover: "",
                                                        id_news_kategori: request.input("id_news_kategori") ? parseInt(request.input("id_news_kategori")) : null,
                                                        id_adm_kabkot: request.input("id_adm_kabkot") ? parseInt(request.input("id_adm_kabkot")) : null,
                                                        id_adm_provinsi: request.input("id_adm_provinsi") ? parseInt(request.input("id_adm_provinsi")) : null,
                                                        id_komoditi: request.input("id_komoditi") ? parseInt(request.input("id_komoditi")) : null,
                                                }
                                        });

                                        resID = res.id; // Assign the ID here
                                        await auth.check()
                                        const insert = await prisma.tb_news_status.create({
                                                data: {
                                                        id_news: resID,
                                                        status: parseInt(request.input('status')),
                                                        status_proses: 0,
                                                        keterangan: 'Dokumen Baru',
                                                        created_by:auth.user?.id,
                                                        updated_by:auth.user?.id,
                                                        created_date:new Date()
                                                },
                                                });

                                        fileFotoName = "";
                                        if (fileFoto) {
                                                let uploadFileToServer = await uploadFile(fileFoto, model, resID);
                                                let filenameFromServer = '';
                                                if (uploadFileToServer) {
                                                        filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                        filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                }
                                                fileFotoName = filenameFromServer;
                                        }

                                        fileDokumenName = "";
                                        if (fileDokumen) {
                                                let uploadFileToServer = await uploadFile(fileDokumen, model, resID);
                                                let filenameFromServer = '';
                                                if (uploadFileToServer) {
                                                        filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                        filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                }
                                                fileDokumenName = filenameFromServer;
                                        }

                                        // Update the record with the cover file name
                                        if (fileFotoName) {
                                                await prisma.tb_news.update({
                                                        where: { id: resID },
                                                        data: { file_cover: fileFotoName },
                                                });
                                        }

                                        const response = await prisma.tb_news.findUnique({
                                                where: { id: resID },
                                        })

                                        // Proceed to create the other records (tb_news_file, tb_news_tr, etc.)
                                        const data_news_file = {
                                                id_news: res.id,
                                                tipe: 3,
                                                nama: fileDokumenName || "",
                                                judul: request.input("file_judul") || "",
                                        };

                                        const res_data_news_file = await prisma.tb_news_file.create({ data: data_news_file });

                                        const data_news_tr: any = {
                                                id_news: res.id,
                                                kd_bahasa: request.input("kd_bahasa") || "",
                                                judul: request.input("tr_judul") || "",
                                                deskripsi_singkat: request.input("tr_deskripsi_singkat") || "",
                                                deskripsi: request.input("tr_deskripsi") || "",
                                        };

                                        const res_data_news_tr = await prisma.tb_news_tr.create({ data: data_news_tr });

                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: response,
                                                data_file: res_data_news_file,
                                                data_tr: res_data_news_tr,

                                        };
                                } else {
                                        let old = await prisma.tb_news.findFirst({
                                                where: {
                                                        id: paramID
                                                }
                                        });

                                        let oldTbNewsFile = await prisma.tb_news_file.findMany({
                                                where: {
                                                        id_news: paramID
                                                }
                                        });

                                        // Update case for existing record
                                        const update = await prisma.tb_news.update({
                                                where: { id: paramID },
                                                data: {
                                                        jenis: parseInt(request.input("jenis")) ? parseInt(request.input("jenis")) : (old['jenis'] ?? 0),
                                                        judul: request.input("judul") ? request.input("judul") : (old['judul'] ?? ''),
                                                        deskripsi_singkat: request.input("deskripsi_singkat") ? request.input("deskripsi_singkat") : (old['deskripsi_singkat'] ?? ''),
                                                        deskripsi: request.input("deskripsi") ? request.input("deskripsi") : (old['deskripsi'] ?? ""),
                                                        status: parseInt(request.input("status")) ? parseInt(request.input("status")) : (old['status'] ?? 0),
                                                        file_cover: fileFotoName ? fileFotoName : (old['file_cover'] ?? ""),
                                                        id_news_kategori: request.input("id_news_kategori") ? parseInt(request.input("id_news_kategori")) : (old['id_news_kategori'] ?? null),
                                                        id_adm_kabkot: request.input("id_adm_kabkot") ? parseInt(request.input("id_adm_kabkot")) : (old['id_adm_kabkot'] ?? null),
                                                        id_adm_provinsi: request.input("id_adm_provinsi") ? parseInt(request.input("id_adm_provinsi")) : (old['id_adm_provinsi'] ?? null),
                                                        id_komoditi: request.input("id_komoditi") ? parseInt(request.input("id_komoditi")) : (old['id_komoditi'] ?? null),
                                                }
                                        });
                                        await auth.check()
                                        const insert = await prisma.tb_news_status.create({
                                                data: {
                                                        id_news: paramID,
                                                        status: 0,
                                                        status_proses: 0,
                                                        keterangan: 'Dokumen ada perubahan',
                                                        created_by:auth.user?.id,
                                                        updated_by:auth.user?.id,
                                                        created_date:new Date()
                                                },
                                                });


                                        if (fileFoto) {
                                                await uploadFile(fileFoto, model, paramID);
                                                fileFotoName = fileFoto.fileName || ""
                                        }

                                        if (fileDokumen) {
                                                await uploadFile(fileDokumen, model, paramID);
                                                fileDokumenName = fileDokumen.fileName || ""
                                        }

                                        // Update the record with the cover file name
                                        if (fileFotoName) {
                                                await prisma.tb_news.update({
                                                        where: { id: paramID },
                                                        data: { file_cover: fileFotoName },
                                                });
                                        }

                                        const response = await prisma.tb_news.findUnique({
                                                where: { id: paramID },
                                        })

                                        let res_data_news_file: any

                                        if (fileDokumen)  {
                                                const data_news_file = {
                                                        id_news: update.id,
                                                        tipe: parseInt(request.input("tipe")) || 0,
                                                        nama: fileDokumenName || "",
                                                        judul: request.input("judul") || "",
                                                };
        
                                                const existing_data_news_file = await prisma.tb_news_file.findFirst({
                                                        where: { id_news: update.id }
                                                });
        
                                                if (existing_data_news_file) {
                                                        res_data_news_file = await prisma.tb_news_file.update({
                                                                where: { id_news_file: existing_data_news_file.id_news_file },
                                                                data: data_news_file,
                                                        });
                                                }
                                        }

                                        const data_news_tr = {
                                                id_news: update.id,
                                                kd_bahasa: request.input("kd_bahasa") || "",
                                                judul: request.input("tr_judul") || "",
                                                deskripsi_singkat: request.input("tr_deskripsi_singkat") || "",
                                                deskripsi: request.input("tr_deskripsi") || "",
                                        };

                                        const existing_data_news_tr = await prisma.tb_news_tr.findFirst({
                                                where: { id_news_tr: update.id }
                                        });

                                        let res_data_news_tr: any;
                                        if (existing_data_news_tr) {
                                                res_data_news_tr = await prisma.tb_news_tr.update({
                                                        where: { id_news_tr: existing_data_news_tr.id_news_tr },
                                                        data: data_news_tr,
                                                });
                                        }


                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: response,
                                                data_news_file: res_data_news_file,
                                                data_news_tr: res_data_news_tr,
                                        };
                                }
                        });

                        return response.json(result);
                } catch (error) {
                        console.log(error);
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error.message,
                        });
                }
        }



        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_news.delete({
                                where: { id: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response,auth }: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }

                try {
                        const toggleStatus = await prisma.tb_news.update({
                                where: {
                                        id: Id,
                                },
                                data: {
                                        status: request.input('status') ?? 0
                                },
                        })
                        await auth.check()
                        const insert = await prisma.tb_news_status.create({
                                data: {
                                        id_news: Id,
                                        status: parseInt(request.input('status')),
                                        status_proses: parseInt(request.input('status')),
                                        keterangan: request.input('keterangan'),
                                        created_by:auth.user?.id,
                                        updated_by:auth.user?.id,
                                        created_date:new Date()
                                },
                                });
                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error Toggle Status' })
                }
        }
}