import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'
import pLimit from 'p-limit';
import { checkFileOrUrl, numberFormat,  numberFormatRpSingkatan } from '../helpers/global_helper.js';
import env from '#start/env';
import { request } from 'http';
import { isParameter } from 'typescript';

const limit = pLimit(10); // Limit to a maximum of 5 concurrent connections

export default class DaerahsController {

    public async daerah({ params,response,request }:HttpContext) {
    const search = params.search || ''; // Default to an empty string if search is not provided
        try {
            const parameters = request.qs()
            let prov = await prisma.tb_adm_provinsi.findMany({
                select: {
                    id_adm_provinsi: true,
                    nama: true,
                    luas_wilayah:true,
                    file_image: true,
                    file_logo: true,
                    tb_adm_kabkot: {
                        select: {
                            id_adm_kabkot:true,
                            _count: {
                                select: {
                                    tb_peluang_kabkot: {
                                        where: {
                                            status: "99",
                                            id_prioritas:1
                                        }
                                    },
                                    tb_peluang_daerah: {
                                        where: {
                                            status: 99
                                        }
                                    }
                                }
                            }
                        }
                    },
                    _count: {
                        select: {
                            tb_peluang_daerah: {
                                where: {
                                    status: 99
                                }
                            }
                        }
                    },
                    tb_zona_waktu:{
                        select:{
                            nama:true
                        }
                    },
                    tb_adm_provinsi_tr:true
                },
                orderBy:{
                    id_adm_provinsi:'asc'
                }
            });
            const groupedResults = {};

                await Promise.all(
                    prov.map(item =>
                        limit(async () => {
                            try {
                                // Fetching data from Prisma
                                const latestYearData = await prisma.tb_investasi_provinsi.findMany({
                                    where: {
                                        status: 99,
                                        id_adm_provinsi: item.id_adm_provinsi
                                    },
                                    orderBy: [
                                        { id_jenis: 'asc' },
                                        { tahun: 'desc' }
                                    ],
                                    select: {
                                        id_jenis: true,
                                        tahun: true,
                                        jumlah_investasi: true
                                    }
                                });

                                const totalInvestasiPerJenis = latestYearData.reduce((acc, curr) => {
                                    const jenis = curr.id_jenis === 1 ? 'PMA' : 'PMDN';
                                    if (!acc[curr.id_jenis]) {
                                        acc[curr.id_jenis] = { id_jenis: curr.id_jenis, jenis: jenis, total_investasi: 0 };
                                    }
                                    acc[curr.id_jenis].total_investasi += curr.jumlah_investasi;
                                    return acc;
                                }, {});

                                let peluangs = item.tb_adm_kabkot.reduce((sum, item) => {
                                    return sum + item._count.tb_peluang_kabkot+item._count.tb_peluang_daerah;
                                }, 0) + item._count.tb_peluang_daerah;;
                                console.log(`peluangng: ${peluangs}`);
                                

                                const realisasi = Object.values(totalInvestasiPerJenis).map((item) => {
                                    return {
                                        nilai: item.jenis === 'PMA' ? `PMA : (US$ Ribu) : ${numberFormat(item.total_investasi)}` : `PMDN : (RP Juta) : ${numberFormat(item.total_investasi)}`,
                                    };
                                });

                                // Group by tb_zona_waktu.nama
                                const zoneName = item.tb_zona_waktu.nama.toLowerCase(); // Convert to lowercase for consistency
                                if (!groupedResults[zoneName]) {
                                    groupedResults[zoneName] = [];
                                }

                                    let nama_prov =item.nama
                                    if (parameters.en) {
                                        nama_prov = item.tb_adm_provinsi_tr[0]?.nama || item.nama
                                    }

                                groupedResults[zoneName].push({
                                    id_adm_provinsi: item.id_adm_provinsi,
                                    nama: nama_prov,
                                    luas_wilayah: item.luas_wilayah,
                                    image:  `${process.env.APP_URL}/uploads/daerah/${item.id_adm_provinsi}/${item.file_logo} `,
                                    peluang: peluangs,
                                    realisasi: realisasi
                                });

                            } catch (error) {
                                console.error(`Error processing province ${item.id_adm_provinsi}:`, error);
                                if (!groupedResults['error']) {
                                    groupedResults['error'] = [];
                                }
                                groupedResults['error'].push({
                                    id_adm_provinsi: item.id_adm_provinsi,
                                    error: true,
                                    message: 'Error fetching data'
                                });
                            }
                        })
                    )
                );

                // Convert the grouped results into the desired array format
                const resultArray = Object.keys(groupedResults).map(key => {
                    const sortedGroup = groupedResults[key].sort((a, b) => 
                        a.id_adm_provinsi - b.id_adm_provinsi
                    );
                    
                    return { [key]: sortedGroup };
                });

            return {
                success : true,
                data : resultArray
            }
        } catch (error) {
            console.error(error);
            return response.status(500).json({
                status: 'error',
                message: 'Error fetching data',
            });
        }
    }
    public async detail_provinsi({ params,response,request }:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }
        const parameters =  request.qs()


        // try {
            const prov = await prisma.tb_adm_provinsi.findFirst({
                where: {
                    id_adm_provinsi: Id,
                },
                select:{
                    id_adm_provinsi: true,
                    tb_adm_provinsi_tr:{
                        select:{
                            nama:true
                        }
                    },
                    nama: true,
                    luas_wilayah: true,
                    file_image:true,
                    file_logo:true,
                    lon:true,
                    lat:true,
                    deskripsi:true,
                    _count:{
                        select:{
                            tb_adm_kabkot:true
                        }
                    },
                    tb_demografi_provinsi:{
                        where:{
                            status: 99,
                            id_kategori:1
                        },
                        orderBy:{
                            tahun:'desc'
                        },
                        take:5
                    }


                }
            })

            const umr = await prisma.tb_umr_provinsi.findMany({
                where: {
                    id_adm_provinsi: Id,
                    status:99
                },
                orderBy:{
                    tahun: 'desc',
                },
                take:5
            })

            const ekspor = await prisma.tb_ekspor_provinsi.findFirst({
                where:{
                    id_adm_provinsi: Id,
                    status:99
                },
                orderBy:{
                    tahun: 'desc',
                },
                take:1
            })

            const latestYearRecord = await prisma.tb_investasi_provinsi.findFirst({
                where: {
                    status: 99,
                    id_adm_provinsi: Id
                },
                orderBy: { tahun: 'desc' },
                select: { tahun: true }
            });

            // Check if latestYearRecord exists and get the year
            const latestYear = latestYearRecord?.tahun;
                let investasi=[]
                if (latestYear) {
                    // Step 2: Get all records for the latest year only
                    const latestYearData = await prisma.tb_investasi_provinsi.findMany({
                        where: {
                            status: 99,
                            id_adm_provinsi: Id,
                            tahun: latestYear
                        },
                        orderBy: { id_jenis: 'asc' }, // Sort by id_jenis if needed
                        select: {
                            id_jenis: true,
                            tahun: true,
                            jumlah_investasi: true
                        }
                    });


                const inves = Object.values(
                    latestYearData.reduce((acc, curr) => {
                        const jenis = curr.id_jenis === 1 ? 'PMA' : 'PMDN';
                        // Check if the current id_jenis is already accumulated
                        if (!acc[curr.id_jenis]) {
                            acc[curr.id_jenis] = { id_jenis: curr.id_jenis, jenis: jenis, total_investasi: 0 };
                        }
                        // Sum up jumlah_investasi for each id_jenis
                        acc[curr.id_jenis].total_investasi += curr.jumlah_investasi;
                        return acc;
                    }, {})
                );
                 investasi = Object.values(inves)
            }else{
                 investasi = []
            }

            const sektorDaerahs = await prisma.tb_sektor_daerah.findMany({
                where: {
                    status: 99,
                    id_adm_provinsi: Id
                },
                include:{
                    sektor_nasional:{
                        include:{
                            sektor:{
                                include:{
                                    sektor_nasional_tr:true
                                }
                            }
                        }
                    },
                    tb_sektor_daerah_file:{
                        include:{
                            tb_sektor_daerah_file_tr:true
                        }
                    },
                    tb_sektor_daerah_insentif:
                    {
                        where:{
                            status:99
                        },
                        include:{
                            tb_sektor_daerah_insentif_file:true
                        }
                    },
                    tb_sektor_daerah_tr:true,
                    tb_sektor_daerah_lq:true
                }

            });
            let sektorRef =[]
            let strProfil
            let sektorDaerah
            let kajian
            if (!parameters.en) {
                const kajians = await prisma.tb_news.findMany({
                    where:{
                        id_news_kategori:1,
                        id_adm_provinsi:Id,
                        id_adm_kabkot:null,
                        status:99
                    },
                    include:{
                        tb_news_kategori:true,
                        translations:true,
                        files:true
                    }
                })
                if (kajians.length > 0) {
                    kajian =await Promise.all( kajians.map(async (item) => {
                        console.log('ini item');
                        console.log(item);
                        const files =  item.files.map((i)=>{
                           return {
                            nama:i.nama,
                            judul:i.judul,
                            path:  `${process.env.APP_URL}/uploads/berita/${item.id}/${i.nama} `
                           }
                        })
                        return {
                            judul:item.judul,
                            deskripsi:item.deskripsi,
                            deskripsi_singkat:item.deskripsi_singkat,
                            cover:`${process.env.APP_URL}/uploads/berita/${item.id}/${item.file_cover} `,
                            files
                        }
                    }))
                }
                // return investasi
                strProfil = `${prov.nama} memiliki wilayah dengan luas ${numberFormat(prov?.luas_wilayah)} km\u00B2 dan terbagi dalam ${prov?._count.tb_adm_kabkot} Kabupaten/Kota. Total jumlah penduduk ${numberFormat(prov?.tb_demografi_provinsi[0].jumlah_pria+prov?.tb_demografi_provinsi[0].jumlah_wanita)} jiwa. Jumlah penduduk laki-laki sebanyak ${numberFormat(prov?.tb_demografi_provinsi[0].jumlah_pria)} dan penduduk perempuan sebesar ${numberFormat(prov?.tb_demografi_provinsi[0].jumlah_wanita)} jiwa (${prov?.tb_demografi_provinsi[0].tahun}). dan UMR wilayah ini tercatat sebesar Rp. ${numberFormat(umr[0]?.nilai)} (${umr[0]?.tahun}). Dari sisi ekonomi makro, nilai ekspor provinsi sebesar ${numberFormat(ekspor?.nilai_ekspor)}  USD. Nilai impor tercatat sebesar ${numberFormat(ekspor?.nilai_ekspor)} USD. Realisasi investasi asing atau PMA sebesar ${numberFormat(investasi[0]?.total_investasi)}  USD (${ekspor?.tahun}). Total investasi dalam negeri provinsi ini sebesar  ${numberFormat(investasi[1]?.total_investasi)} rupiah (${ekspor?.tahun}).`



                sektorDaerah = await Promise.all(
                    sektorDaerahs.map(async (item) => {
                        if (item.sektor_nasional.status == 99) {

                            const komo = await prisma.tb_sektor_daerah.findMany({
                                where: {
                                    status: 99,
                                    id_adm_provinsi: Id,
                                    id_sektor_daerah: item.id_sektor_daerah,
                                },
                                include: {
                                    tb_sub_sektor_daerah: {
                                        include: {
                                            tb_komoditi_daerah: {
                                                include:{
                                                    tb_komoditi_daerah_file:true

                                                }
                                            },
                                            tb_sub_sektor_nasional: {
                                                include: {
                                                    sub_sektor_ref: true,
                                                },
                                            },
                                            tb_sub_sektor_daerah_file:{
                                                include:{
                                                    tb_sub_sektor_daerah_file_tr:true
                                                }
                                            }
                                        },
                                    },
                                    tb_sektor_daerah_insentif :{
                                        where:{
                                            status:99
                                        },
                                        include: {
                                            tb_sektor_daerah_insentif_file: true,
                                        },
                                    }

                                },
                            });

                            let strWilayah = '';
                            let sub_sektor = [];
                            let diagram = [];
                            let insentif = []
                            let subSektorRef =[]
                            const idSektor= item.sektor_nasional?.id_sektor_nasional
                            for (const item of komo) {
                                insentif = item.tb_sektor_daerah_insentif.map((items) =>{
                                    return {
                                            judul: items.nama,
                                            judul_file: items.tb_sektor_daerah_insentif_file[0]?.judul,
                                            url:  `${process.env.APP_URL}/uploads/sektor/${items.tb_sektor_daerah_insentif_file[0]?.nama} `
                                        }
                                })
                                for (const item2 of item.tb_sub_sektor_daerah) {
                                    const a = {
                                        nama_sub_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.nama,
                                        deskripsi: item2.deskripsi,
                                        infografis: item2.tb_sub_sektor_daerah_file.map((item3)=>{
                                            return {
                                                judul:item3.judul,
                                                url:`${process.env.APP_URL}/uploads/sektor/${item3.nama}`
                                            }
                                        })
                                    };
                                    console.log(`ini files ${item2.tb_sub_sektor_nasional.sub_sektor_ref.nama}`);
                                    console.log(item2.tb_sub_sektor_daerah_file);


                                    subSektorRef.push({
                                        id_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.id_sektor,
                                        id_sub_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.id_sub_sektor,
                                        nama_sub_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.nama,
                                    })


                                    if (item2.tb_komoditi_daerah[0]?.status==99) {

                                        item2.tb_komoditi_daerah[0]?.tb_komoditi_daerah_file.map((file) => {
                                            const b = {
                                                label: file.judul,
                                                image: `${process.env.APP_URL}/uploads/sektor/${file.nama}`,
                                            }
                                            diagram.push(b);
                                        })
                                    }

                                    const komoditis = await prisma.tb_komoditi_daerah.findMany({
                                            where: {
                                                id_sub_sektor_daerah : item2.id_sub_sektor_daerah,
                                                status:99
                                            },
                                            include: {
                                                tb_komoditi_daerah_value: {
                                                    include: {
                                                        tb_komoditi_daerah_value_detail: {
                                                            orderBy:{
                                                                tahun : 'desc'
                                                            },
                                                            take:1
                                                        },
                                                    },
                                                },
                                                tb_komoditi_nasional_ref:true
                                            },
                                        });
                                        if (komoditis.length > 0) {

                                            const detKomoditi = komoditis.map((data) => {
                                                const detail = data.tb_komoditi_daerah_value.map((detail) => {
                                                    return {
                                                        jenis: detail.nama,
                                                        nilai: `${detail.tb_komoditi_daerah_value_detail[0]?.numeric_value} ${data.tb_komoditi_daerah_value[0]?.satuan} (${detail.tb_komoditi_daerah_value_detail[0]?.tahun})`,
                                                    }
                                                })
                                                detail.push({
                                                    jenis: 'Sentra Produksi',
                                                    nilai: data.sentra_produksi,
                                                })
                                                return {
                                                    nama: data.tb_komoditi_nasional_ref.nama,
                                                    file: `${process.env.APP_URL}/uploads/sektor/${data.file_icon}`,
                                                    detail,
                                                }
                                            })
                                            a.komoditi=detKomoditi
                                        }

                                        if (item2.tb_sub_sektor_nasional.status == 99) {
                                            sub_sektor.push(a);
                                        }

                                    for (const item3 of item2.tb_komoditi_daerah) {


                                        strWilayah += ` ${item3.sentra_produksi},`;
                                    }
                                }
                            }

                            // Hapus koma terakhir dan menghilangkan duplikat
                            strWilayah = strWilayah.slice(0, -1);
                            const array = strWilayah.split(', ');
                            const uniqueArray = [...new Set(array)];
                            strWilayah = uniqueArray.join(', ');
                            if (item.sektor_nasional?.id_sektor_nasional) {
                                
                                const sektorExists = sektorRef.some(sector => 
                                    sector.id_sektor === item.sektor_nasional.id_sektor_nasional
                                );
                                
                                // Only push if it doesn't exist already
                                if (!sektorExists) {
                                    sektorRef.push({
                                        id_sektor: item.sektor_nasional.id_sektor_nasional || null,
                                        nama_sektor: item.sektor_nasional?.sektor?.nama || '',
                                        sub_sektor: subSektorRef,
                                    });
                                }
                            }

                            return {
                                nama: item.sektor_nasional?.sektor?.nama || '',
                                tahun:item.tb_sektor_daerah_lq?.tahun_pdrb,
                                deskripsi: item.deskripsi,
                                deskripsi_singkat: item.deskripsi_singkat,
                                diagram:diagram,
                                potensi: {
                                    detail: item.potensi_pasar,
                                    image: item.tb_sektor_daerah_file[0]?.nama != undefined ? `${process.env.APP_URL}/uploads/sektor/${item.tb_sektor_daerah_file[0]?.nama}` : null,
                                },
                                insentif: insentif,
                                wilayah: strWilayah,
                                sub_sektor: sub_sektor,
                            };
                        }

                    })
                );

            }else{

                const kajians = await prisma.tb_news.findMany({
                    where:{
                        id_news_kategori:1,
                        id_adm_provinsi:Id,
                        id_adm_kabkot:null,
                        status:99
                    },
                    include:{
                        tb_news_kategori:true,
                        translations:true,
                        files:true
                    }
                })
                let kajian
                if (kajians.length > 0) {
                    kajian =await Promise.all( kajians.map(async (item) => {
                        console.log('ini item');
                        console.log(item);
                        const files =  item.files.map((i)=>{
                           return {
                            nama:i.nama,
                            judul:i.judul,
                            url:  `${process.env.APP_URL}/uploads/berita/${item.id}/${i.nama} `
                           }
                        })
                        return {
                            judul:item.translations[0]?.judul || item.judul,
                            deskripsi:item.translations[0]?.deskripsi || item.deskripsi,
                            deskripsi_singkat: item.translations[0]?.deskripsi_singkat ||item.deskripsi_singkat,
                            cover:`${process.env.APP_URL}/uploads/berita/${item.id}/${item.file_cover}`,
                            files
                        }
                    }))
                }

                strProfil = `${prov?.tb_adm_provinsi_tr[0]?.nama} Proviece's has an area of ${numberFormat(prov?.luas_wilayah)} square kilometers, include ${prov?._count.tb_adm_kabkot} Distric/Cities. The province's population is ${numberFormat(prov?.tb_demografi_provinsi[0].jumlah_pria+prov?.tb_demografi_provinsi[0].jumlah_wanita)} people. The male population is ${numberFormat(prov?.tb_demografi_provinsi[0].jumlah_pria)}, and the female population is ${numberFormat(prov?.tb_demografi_provinsi[0].jumlah_wanita)} people (${prov?.tb_demografi_provinsi[0].tahun}). The regional minimum wage for this area is Rp. ${numberFormat(umr[0]?.nilai)} (${umr[0]?.tahun}). From a macroeconomic standpoint, the province's export value is ${numberFormat(ekspor?.nilai_ekspor)} thousand USD. and the import value at ${numberFormat(ekspor?.nilai_ekspor)} thousand USD.Realization of foreign investment or PMA of ${numberFormat(investasi[0]?.total_investasi)} thausand USD (${ekspor?.tahun}). domestic investment in this province is  ${numberFormat(investasi[1]?.total_investasi)} rupiah (${ekspor?.tahun}).`


                sektorDaerah = await Promise.all(
                    sektorDaerahs.map(async (item) => {
                        const komo = await prisma.tb_sektor_daerah.findMany({
                            where: {
                                status: 99,
                                id_adm_provinsi: Id,
                                id_sektor_daerah: item.id_sektor_daerah,
                            },
                            include: {
                                tb_sub_sektor_daerah: {
                                    where:{
                                        status:99
                                    },
                                    include: {
                                        tb_sub_sektor_daerah_tr:true,
                                        tb_komoditi_daerah: {
                                            where:{
                                                status:99
                                            },
                                            include:{
                                                tb_komoditi_daerah_tr:true,
                                                tb_komoditi_daerah_file:{
                                                    include:{
                                                        tb_komoditi_daerah_file_tr:true
                                                    }
                                                }
                                            }
                                        },
                                        tb_sub_sektor_nasional: {
                                            include: {
                                                sub_sektor_ref: {
                                                    include:{
                                                        sub_sektor_nasional_ref_tr:true
                                                    }
                                                },
                                            },
                                        },
                                    },
                                },
                                tb_sektor_daerah_insentif :{
                                    include: {
                                        tb_sektor_daerah_insentif_tr:true,
                                        tb_sektor_daerah_insentif_file: {
                                            include:{
                                                tb_sektor_daerah_insentif_file_tr:true
                                            }
                                        },
                                    },
                                }

                            },
                        });

                        let strWilayah = '';
                        let sub_sektor = [];
                        let diagram = [];
                        let insentif = []
                        let subSektorRef =[]
                        const idSektor= item.sektor_nasional?.id_sektor_nasional
                        for (const item of komo) {
                            insentif = item.tb_sektor_daerah_insentif.map((items) =>{
                                return {
                                        judul: items.tb_sektor_daerah_insentif_tr[0].nama || items.nama,
                                        judul_file: items.tb_sektor_daerah_insentif_file[0].tb_sektor_daerah_insentif_file_tr[0].judul || items.tb_sektor_daerah_insentif_file[0].judul,
                                        url:  `${process.env.APP_URL}/uploads/sektor/${items.tb_sektor_daerah_insentif_file[0].tb_sektor_daerah_insentif_file_tr[0].nama} `||`${process.env.APP_URL}/uploads/sektor/${items.tb_sektor_daerah_insentif_file[0].nama} `
                                    }

                            })
                            for (const item2 of item.tb_sub_sektor_daerah) {
                                const a = {
                                    nama_sub_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.sub_sektor_nasional_ref_tr?.nama || item2.tb_sub_sektor_nasional.sub_sektor_ref.nama,
                                    deskripsi: item2.tb_sub_sektor_daerah_tr[0]?.deskripsi || item2.deskripsi,
                                };


                                subSektorRef.push({
                                    id_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.id_sektor,
                                    id_sub_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.sub_sektor_nasional_ref_tr?.id_sub_sektor || item2.tb_sub_sektor_nasional.sub_sektor_ref.id_sub_sektor,
                                    nama_sub_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.sub_sektor_nasional_ref_tr?.nama || item2.tb_sub_sektor_nasional.sub_sektor_ref.nama,
                                })



                                item2.tb_komoditi_daerah[0]?.tb_komoditi_daerah_file.map((file) => {
                                    const b = {
                                        label: file.tb_komoditi_daerah_file_tr[0]?.judul || file.judul,
                                        image: file.tb_komoditi_daerah_file_tr[0]?.nama != undefined ?   `${process.env.APP_URL}/uploads/sektor/${file.tb_komoditi_daerah_file_tr[0].nama}` : `${process.env.APP_URL}/uploads/sektor/${file.nama}`,
                                    }
                                    diagram.push(b);
                                })

                                const komoditis = await prisma.tb_komoditi_daerah.findMany({
                                        where: {
                                            id_sub_sektor_daerah : item2.id_sub_sektor_daerah,
                                        },
                                        include: {
                                            tb_komoditi_daerah_tr:true,
                                            tb_komoditi_daerah_value: {
                                                include: {
                                                    tb_komoditi_daerah_value_tr:true,
                                                    tb_komoditi_daerah_value_detail: {
                                                        orderBy:{
                                                            tahun : 'desc'
                                                        },
                                                        take:1
                                                    },
                                                },
                                            },
                                            tb_komoditi_nasional_ref:{
                                                include:{
                                                    tb_komoditi_nasional_ref_tr:true
                                                }
                                            }
                                        },
                                    });

                                    const detKomoditi = komoditis.map((data) => {
                                        const detail = data.tb_komoditi_daerah_value.map((detail) => {
                                                return {
                                                    jenis:detail.tb_komoditi_daerah_value_tr[0].nama || detail.nama,
                                                    nilai: `${detail.tb_komoditi_daerah_value_detail[0]?.numeric_value} ${data.tb_komoditi_daerah_value[0]?.satuan} (${detail.tb_komoditi_daerah_value_detail[0]?.tahun})`,
                                                }
                                            })
                                            detail.push({
                                                jenis: 'Sentra Produksi',
                                                nilai: data.tb_komoditi_daerah_tr[0].sentra_produksi || data.sentra_produksi,
                                            })
                                        return {
                                            nama: data.tb_komoditi_nasional_ref?.tb_komoditi_nasional_ref_tr?.nama || data.tb_komoditi_nasional_ref?.nama,
                                            file: `${process.env.APP_URL}/uploads/sektor/${data.file_icon}`,
                                            detail,
                                        }
                                    })

                                    a.komoditi=detKomoditi
                                sub_sektor.push(a);

                                for (const item3 of item2.tb_komoditi_daerah) {


                                    strWilayah += item3.tb_komoditi_daerah_tr[0].sentra_produksi != undefined ? item3.tb_komoditi_daerah_tr[0].sentra_produksi  :  ` ${item3.sentra_produksi},`;
                                }
                            }
                        }

                        // Hapus koma terakhir dan menghilangkan duplikat
                        strWilayah = strWilayah.slice(0, -1);
                        const array = strWilayah.split(', ');
                        const uniqueArray = [...new Set(array)];
                        strWilayah = uniqueArray.join(', ');
                        const sektorExists = sektorRef.some(sector => 
                            sector.id_sektor === item.sektor_nasional.id_sektor_nasional
                        );
                        
                        // Only push if it doesn't exist already
                        if (!sektorExists) {
                            sektorRef.push({
                                id_sektor: item.sektor_nasional.id_sektor_nasional || null,
                                nama_sektor: item.sektor_nasional?.sektor?.sektor_nasional_tr?.nama || item.sektor_nasional?.sektor?.nama || '',
                                sub_sektor: subSektorRef,
                            });
                        }

                        return {
                            nama: item.sektor_nasional?.sektor?.sektor_nasional_tr?.nama || item.sektor_nasional?.sektor?.nama,
                            tahun:item.tb_sektor_daerah_lq?.tahun_pdrb,
                            deskripsi: item.tb_sektor_daerah_tr?.deskripsi || item.deskripsi,
                            deskripsi_singkat: item.tb_sektor_daerah_tr?.deskripsi_singkat ||item.deskripsi_singkat,
                            diagram:diagram,
                            potensi: {
                                detail: item.potensi_pasar,
                                image: item.tb_sektor_daerah_file[0]?.nama != undefined ? `${process.env.APP_URL}/uploads/sektor/${item.tb_sektor_daerah_file[0]?.nama}` : null,
                            },
                            insentif: insentif,
                            wilayah: strWilayah,
                            sub_sektor: sub_sektor,
                        };
                    })
                );
            }


            const startNumber = parseInt(Id + '00'); // e.g., 1100
            const endNumber = parseInt(Id + '99'); // e.g., 1199
            const [ppi, pid] = await Promise.all([
                prisma.tb_peluang_kabkot.findMany({
                    select:{
                        id_adm_kabkot:true,
                        id_peluang_kabkot:true,
                        nama:true,
                        deskripsi:true,
                        nilai_investasi:true,
                        nilai_irr:true,
                        nilai_npv:true,
                        nilai_pp:true,
                        id_sektor:true,
                        id_prioritas:true,
                        project_status_enum:true,
                        is_ipro:true,
                        tahun:true,
                        tb_peluang_sektor:{
                            select:{
                                kategori_sektor:{
                                    select:{
                                        nama:true,
                                        id_kategori_sektor:true
                                    }
                                },
                                nama:true,
                                icon:true
                            }
                        },
                        tb_peluang_kabkot_file:{
                            select:{
                                nama:true
                            },
                            where:{
                                tipe:1
                            }
                        },
                        tb_adm_kabkot:{
                            select:{
                                nama:true,
                                tb_adm_provinsi:{
                                    select:{
                                        nama:true,
                                        tb_adm_provinsi_tr:{
                                            select:{
                                                nama:true
                                            }
                                        }
                                    }
                                },
                                tb_adm_kabkot_tr:{
                                    select:{
                                        nama:true
                                    }
                                }
                            }
                        },
                        tb_peluang_kabkot_tr:{
                            select:{
                                nama:true
                            }
                        }
                    },
                    where: {
                            status:"99",
                            id_prioritas:1,
                            id_adm_kabkot: {
                                gte: startNumber,
                                lt: endNumber
                            },
                            // project_status_enum:{
                            //     not:'-1'
                            // }
                    },
                    orderBy:{
                        nilai_investasi:'desc'
                    }
                }),
                prisma.$queryRaw`
                    SELECT
                                    p.id_peluang_daerah,
                                    p.id_adm_provinsi,
                                    p.id_adm_kabkot,
                                    p.keterangan,
                                    0 as id_sektor,
                                    p.judul as nama,
                                    p.tahun as tahun,
                                    pt.judul as nama_tr,
                                    k.initial_invesment as nilai_investasi,
                                    k.irr as nilai_irr,
                                    k.npv as nilai_npv,
                                    k.pp as nilai_pp,
                                    skn.id_kategori_sektor as id_kategori_sektor,
                                    skn.nama as nama_sektor,
                                    snf.nama as nama_sektor_peluang,
                                    snf.icon as icon,
                                    pdf.nama as images,
                                    p.status,
                                    ak.nama as nama_kabkot,
                                    akt.nama as nama_kabkot_tr,
                                    ap.nama as nama_provinsi,
                                    apt.nama as nama_provinsi_tr,
                                    p.project_status_enum,
                                    ps.nama as status_proyek
                                FROM tb_peluang_daerah p
                                LEFT JOIN tb_peluang_daerah_tr pt
                                    ON p.id_peluang_daerah = pt.id_peluang_daerah
                                LEFT JOIN tb_peluang_daerah_kelayakan k
                                    ON p.id_peluang_daerah = k.id_peluang_daerah
                                LEFT JOIN tb_sub_sektor_daerah ssd
                                    ON p.id_sub_sektor_daerah = ssd.id_sub_sektor_daerah
                                LEFT JOIN tb_sub_sektor_nasional ssn
                                    ON ssd.id_sub_sektor_nasional = ssn.id_sub_sektor_nasional
                                LEFT JOIN tb_sektor_nasional sn
                                    ON sn.id_sektor_nasional = ssn.id_sektor_nasional
                                LEFT JOIN tb_sektor_nasional_ref snf
                                    ON sn.id_sektor_nasional = snf.id_sektor
                                LEFT JOIN tb_kategori_sektor skn
                                    ON skn.id_kategori_sektor = snf.id_kategori_sektor
                                LEFT JOIN tb_peluang_daerah_file pdf
                                    ON pdf.id_peluang_daerah = p.id_peluang_daerah AND pdf.tipe = 1
                                LEFT JOIN tb_adm_kabkot ak
                                    ON ak.id_adm_kabkot = p.id_adm_kabkot
                                LEFT JOIN tb_adm_kabkot_tr akt
                                    ON ak.id_adm_kabkot = akt.id_adm_kabkot
                                LEFT JOIN tb_adm_provinsi ap
                                    ON ap.id_adm_provinsi = p.id_adm_provinsi
                                LEFT JOIN tb_adm_provinsi_tr apt
                                    ON apt.id_adm_provinsi = ap.id_adm_provinsi
                                LEFT JOIN tb_peluang_status ps
                                    ON ps.id_Peluang_status = p.project_status_enum
                                WHERE p.status = 99
                                and (p.id_adm_kabkot between ${startNumber} and ${endNumber}
                                or p.id_adm_provinsi = ${Id})
                    ORDER BY k.initial_invesment desc;
                `
                ]);
        
                // Mapping data dengan keterangan masing-masing
                const peluang_status = await prisma.tb_peluang_status.findMany()
        
                const peluang_investasi = [
        
                ...ppi.map(item => {
                        const status = {
                            2: 'DIMINATI',
                            7: 'SOLD',
                        };
        
                        
                        let status_proyek = '';
                        
                        if (item.project_status_enum !== '') {
                            status_proyek = peluang_status.find((items) => items.id_peluang_status == parseInt(item.project_status_enum || '0'))?.nama || ''
                            // status_proyek = status[item.project_status_enum];
                        }
                        let kabkot = item.tb_adm_kabkot.nama
                        let prov = item.tb_adm_kabkot.tb_adm_provinsi.nama
                        let nama = item.nama
                        if (parameters.en) {
                            kabkot = item.tb_adm_kabkot.tb_adm_kabkot_tr[0]?.nama || item.tb_adm_kabkot.nama
                            prov = item.tb_adm_kabkot.tb_adm_provinsi.tb_adm_provinsi_tr[0]?.nama || item.tb_adm_kabkot.tb_adm_provinsi.nama
                            nama = item.tb_peluang_kabkot_tr[0]?.nama || item.nama
                        }
                        let sts = 'PPI'
                        if(item.is_ipro){
                            sts = 'IPRO'
                        }else if(item.id_prioritas ==2){
                            sts = 'PID'
                        }
                        return {
                            id_peluang: item.id_peluang_kabkot,
                            id_adm_provinsi: parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                            id_adm_kabkot: item.id_adm_kabkot,
                            id_sektor: item.id_sektor,
                            nama_kabkot: kabkot,
                            nama_provinsi: prov,
                            nama: nama,
                            tahun: item.tahun,
                            deskripsi: item.deskripsi,
                            nilai_irr: `${item.nilai_irr}%`,
                            nilai_investasi: numberFormatRpSingkatan(item.nilai_investasi),
                            nilai_npv: numberFormatRpSingkatan(item.nilai_npv),
                            nilai_pp: item.nilai_pp,
                            id_kategori_sektor: item.tb_peluang_sektor.kategori_sektor?.id_kategori_sektor,
                            nama_sektor: item.tb_peluang_sektor.kategori_sektor?.nama,
                            nama_sektor_peluang: item.tb_peluang_sektor.nama,
                            project_status_enum: item.project_status_enum,
                            status_proyek, // Properti status_proyek ditambahkan (opsional, jika dibutuhkan)
                            // status: sts,
                            status: item.is_ipro ? 'IPRO' : 'PPI',
                            icon: `${env.get('APP_URL')}/uploads/icon-web/${item.tb_peluang_sektor.icon}`,
                            image: `${env.get('APP_URL')}/uploads/peluang/${item.id_adm_kabkot}/peluang/${item.tahun}/${item.id_prioritas}/${item.tb_peluang_kabkot_file[0]?.nama || 'default.jpg'}`, // Fallback untuk nama file
                        };
                    }),
                ...pid.map(item => {
                        let kabkot = item.nama_kabkot
                        let prov = item.nama_provinsi
                        let nama = item.nama
                        if (parameters.en) {
                            kabkot = item.nama_kabkot_tr || kabkot
                            prov =item.nama_provinsi_tr || prov
                            nama =item.nama_tr || nama
                        }
        
                    return {
                        id_peluang:item.id_peluang_daerah,
                        id_adm_provinsi:item.id_adm_provinsi,
                        id_adm_kabkot:item.id_adm_kabkot,
                        id_sektor:item.id_sektor,
                        nama_kabkot:kabkot,
                        nama_provinsi:prov,
                        nama:nama,
                        tahun:item.tahun,
                        deskripsi:item.keterangangit,
                        nilai_irr: `${item.nilai_irr}%`,
                        nilai_investasi: numberFormatRpSingkatan(item.nilai_investasi),
                        nilai_npv: numberFormatRpSingkatan(item.nilai_npv),
                        nilai_pp:item.nilai_pp,
                        id_kategori_sektor:item.id_kategori_sektor,
                        nama_sektor:item.nama_sektor,
                        nama_sektor_peluang:item.nama_sektor_peluang,
                        project_status_enum:item.project_status_enum == null ? 0 : item.project_status_enum,
                        status_proyek:item.status_proyek,
                        status: 'PID',
                        icon: `${env.get('APP_URL')}/uploads/icon-web/${item.icon}`,
                        image:`${env.get('APP_URL')}/uploads/peluang_daerah/${item.id_peluang_daerah}/${item.images}`
                        }
                })
            ]


            // const peluang = await prisma.tb_peluang_kabkot.findMany({
            //     where: {
            //         id_prioritas: 1,
            //         id_adm_kabkot:  {
            //             gte: parseInt(`${Id}00`),
            //             lt: parseInt(`${Id}99`)
            //         },
            //         status:"99"
            //     },
            //     include:{
            //         tb_adm_kabkot:true,
            //         tb_peluang_kabkot_file:{
            //             where: {
            //                 tipe:1,
            //             },
            //         }
            //     }
            // });
            // const peluang_investasi = peluang.map(({ tb_adm_kabkot, ...data }) => ({
            //     id_peluang_kabkot:data.id_peluang_kabkot,
            //     judul:data.nama,
            //     tahun:data.tahun,
            //     nilai_irr: `${data.nilai_irr}%`,
            //     nilai_investasi: numberFormatRpSingkatan(data.nilai_investasi),
            //     nilai_npv: numberFormatRpSingkatan(data.nilai_npv),
            //     nama_kabkot: tb_adm_kabkot.nama,
            //     image : `${process.env.APP_URL}/uploads/peluang/${data.id_adm_kabkot}/peluang/${data.tahun}/1/${data.tb_peluang_kabkot_file[0]?.nama}`,
            // }));

            // const peluangDaerah = await prisma.tb_peluang_daerah.findMany({
            //     where: {
            //         id_adm_provinsi:  Id,
            //         status:99
            //     },
            //     include:{
            //         tb_adm_provinsi:true,
            //         tb_peluang_daerah_file:{
            //             where: {
            //                 tipe:1,
            //             },
            //         }
            //     }
            // });

            // const peluang_daerah = peluangDaerah.map(({ tb_adm_provinsi, ...data }) => ({
            //     id_peluang_daerah: data.id_peluang_daerah,
            //     judul:data.judul,
            //     tahun:data.tahun,
            //     nama_provinsi: tb_adm_provinsi?.nama,
            //     image : `${process.env.APP_URL}/uploads/peluang_daerah/${data.id_peluang_daerah}/${data.tb_peluang_daerah_file[0]?.nama}`,
            // }));




            const detUmr = umr.map((item) => {
                return {
                    tahun : item.tahun,
                    nilai : item.nilai,
                    nilai_text : `Rp. ${numberFormat(item.nilai)}`
                }
            })
            const detPenduduk = prov?.tb_demografi_provinsi.map((item) => {
                return {
                    tahun : item.tahun,
                    jumlah_pria : item.jumlah_pria,
                    jumlah_wanita : item.jumlah_wanita,
                    jumlah_pria_text : `Pria : ${item.jumlah_pria} jiwa`,
                    jumlah_wanita_text : `Wanita : ${item.jumlah_wanita} jiwa`,
                }
            })

            let latestYears = new Date().getFullYear();
            const inves5 = await prisma.tb_investasi_provinsi.findMany({
                where: {
                    status: 99,
                    id_adm_provinsi: Id,
                    tahun: {
                        gte: latestYears - 4 // assuming `latestYear` is the most recent year available
                    }
                },
                orderBy: [
                    { tahun: 'desc' }, // Order by year descending
                    { id_jenis: 'asc' } // Order by id_jenis within each year
                ],
                select: {
                    id_jenis: true,
                    tahun: true,
                    jumlah_investasi: true
                }
            });
            const investasi5 = inves5.reduce((acc, curr) => {
                const jenis = curr.id_jenis === 1 ? 'PMA' : 'PMDN';
                const satuan = curr.id_jenis === 1 ? 'US$' : 'RP';

                // Ensure a sub-object for the current year exists
                if (!acc[curr.tahun]) {
                    acc[curr.tahun] = [];
                }

                // Find if id_jenis entry already exists for the current year
                const jenisEntry = acc[curr.tahun].find(entry => entry.id_jenis === curr.id_jenis);

                // If the entry exists, update its total_investasi, otherwise create a new entry
                if (jenisEntry) {
                    jenisEntry.total_investasi += curr.jumlah_investasi;
                } else {
                    acc[curr.tahun].push({
                        id_jenis: curr.id_jenis,
                        jenis: jenis,
                        total_investasi: curr.jumlah_investasi,
                    });
                }

                return acc;
            }, {});

            // Convert the result to an array format
            const investasiPerYear = Object.entries(investasi5).map(([tahun, data]) => ({
                tahun: parseInt(tahun),
                data
            }));

            const inves1Year = await prisma.tb_investasi_provinsi.findFirst({
                where: {
                    status: 99,
                    id_adm_provinsi: Id,
                },
                orderBy:{
                    tahun: 'desc',
                },
                select:{
                    tahun:true
                }
            })

            let inves1;
            let groupedData = {};

            if (inves1Year) {
                inves1 = await prisma.tb_investasi_provinsi.findMany({
                    where: {
                        status: 99,
                        id_adm_provinsi: Id,
                        tahun: inves1Year.tahun
                    },
                    select: {
                        id_jenis: true,
                        tahun: true,
                        jumlah_investasi: true,
                        tb_investasi_sektor: {
                            select:{
                                nama:true
                            }
                        }
                    }
                });

                inves1.forEach(item => {
                    const sectorName = item.tb_investasi_sektor.nama;

                    // If sector name does not exist in groupedData, initialize it with an empty array
                    if (!groupedData[sectorName]) {
                        groupedData[sectorName] = [];
                    }

                    const jenis = item.id_jenis === 1 ? 'PMA' : 'PMDN';
                    const satuan = item.id_jenis === 1 ? 'US$' : 'RP';


                    // Push the formatted object into the array for this sector
                    groupedData[sectorName].push({
                        id_jenis: item.id_jenis,
                        tahun: item.tahun,
                        jumlah_investasi_text : `${jenis} ${sectorName} : ${satuan} ${numberFormat(item.jumlah_investasi)}`,
                        jumlah_investasi: item.jumlah_investasi,
                        nama: sectorName
                    });
                });

                // Convert groupedData object back to an array of grouped results if needed

            }

            inves1 = Object.values(groupedData);



            const currentYear = new Date().getFullYear();

            const pdrbs = await prisma.tb_sektor_daerah_pdrb.findMany({
                include :{
                    tb_sektor_nasional:{
                        include:{
                            sektor:true
                        }
                    },
                },
                where: {
                    status: 99,
                    id_adm_provinsi:Id,
                    tahun_pdrb: {
                        gte: currentYear - 4
                    }
                },
                orderBy:{
                    jumlah_pdrb:'desc'
                }
            });
            const detPdrb = pdrbs.reduce((acc, item) => {
                const year = item.tahun_pdrb;
                const sectorName = item.tb_sektor_nasional.sektor.nama;
                const value = Number(item.jumlah_pdrb); // Ensure value is a number

                // Initialize an object for the year if it doesn't exist
                if (!acc[year]) {
                    acc[year] = {};
                }

                // Check if the sector already exists for the current year
                if (!acc[year][sectorName]) {
                    acc[year][sectorName] = {
                        tahun: year,
                        nama_sektor: sectorName,
                        nilai: 0 // Initialize as 0 for summing
                    };
                }

                // Sum the values
                acc[year][sectorName].nilai += value; // Sum as number

                return acc;
            }, {});

        // Convert the result into the desired format
        const formattedDetPdrb = Object.entries(detPdrb).reduce((acc, [year, sectors]) => {
            acc[year] = Object.values(sectors).map(sector => ({
                ...sector,
                nilai_text: `${sector.nama_sektor} : Rp ${numberFormat(sector.nilai)}` // Format for display
            }));
            return acc;
        }, {});






            const daerahDetil = {
                pdrb:formattedDetPdrb,
                investasi : {
                        by_sektor : inves1,
                        by_year :investasiPerYear
                    },
                umr:detUmr,
                penduduk : detPenduduk,
                komoditi : {
                                ref_sektor : sektorRef
                            }
                }
                const sektorDaerahFilter = sektorDaerah.filter(item => item != null)
                const filterSektorDaerah = this.filterSektorDaerah(sektorDaerahFilter)
            return {
                success : true,
                data : {
                    nama_provinsi :prov?.nama,
                    icon: `${process.env.APP_URL}/uploads/daerah/${prov?.id_adm_provinsi}/${prov?.file_logo}` ,
                    banner:`${process.env.APP_URL}/uploads/daerah/${prov?.id_adm_provinsi}/${prov?.file_image}`,
                    luas :`area : ${numberFormat(prov?.luas_wilayah)}  km\u00B2`,
                    deskripsi :prov?.deskripsi,
                    profil :strProfil,
                    lon :prov?.lon,
                    lat :prov?.lat,
                    sektorDaerah :filterSektorDaerah,
                    peluang_investasi,
                    daerahDetil,
                    kajian
                }
            }
        // } catch (error) {
        //     console.error(error);
        //     return response.status(500).json({
        //         status: 'error',
        //         message: 'Error fetching data',
        //     });
        // }
    }

    public async detail_komoditi({ params,response }:HttpContext) {
            const prov = params.id_adm_provinsi
            const subSektor = params.id_sub_sektor
            const tahun = params.tahun

             if (isNaN(prov) || isNaN(subSektor) || isNaN(tahun)) {
                return response.status(400).json({
                    error: 'Semua parameter harus berupa angka.'
                });
            }
    }

    public async detail_kabkot({params,response,request}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const parameters =  request.qs()

        // try {
            const kab = await prisma.tb_adm_kabkot.findFirst({
                where: {
                    id_adm_kabkot: Id,
                },
                include:{
                    tb_adm_provinsi:{
                        include:{
                            tb_adm_provinsi_tr:true
                        }
                    },
                    tb_adm_kabkot_tr:true
                }
            })

            const [ppi, pid] = await Promise.all([
                    prisma.tb_peluang_kabkot.findMany({
                        select:{
                            id_adm_kabkot:true,
                            id_peluang_kabkot:true,
                            nama:true,
                            nilai_investasi:true,
                            nilai_irr:true,
                            nilai_npv:true,
                            nilai_pp:true,
                            project_status_enum:true,
                            is_ipro:true,
                            tahun:true,
                            tb_peluang_sektor:{
                                select:{
                                    kategori_sektor:{
                                        select:{
                                            nama:true,
                                            id_kategori_sektor:true
                                        }
                                    },
                                    nama:true,
                                    icon:true
                                }
                            },
                            tb_peluang_kabkot_file:{
                                select:{
                                    nama:true
                                },
                                where:{
                                    tipe:1
                                }
                            },
                            tb_adm_kabkot:{
                                select:{
                                    nama:true,
                                    tb_adm_provinsi:{
                                        select:{
                                            nama:true,
                                            tb_adm_provinsi_tr:{
                                                select:{
                                                    nama:true
                                                }
                                            }
                                        }
                                    },
                                    tb_adm_kabkot_tr:{
                                        select:{
                                            nama:true
                                        }
                                    }
                                }
                            },
                            tb_peluang_kabkot_tr:{
                                select:{
                                    nama:true
                                }
                            }
                        },
                        where: {
                                status:"99",
                                id_prioritas:1,
                                id_adm_kabkot:Id,
                        },
                        orderBy:{
                            nilai_investasi:'desc'
                        }
                    }),
                    prisma.$queryRaw`
                        SELECT
                            p.id_peluang_daerah,
                            p.id_adm_provinsi,
                            p.id_adm_kabkot,
                            p.judul as nama,
                            pt.judul as nama_tr,
                            k.initial_invesment as nilai_investasi,
                            k.irr as nilai_irr,
                            k.npv as nilai_npv,
                            k.pp as nilai_pp,
                            skn.id_kategori_sektor as id_kategori_sektor,
                            skn.nama as nama_sektor,
                            snf.nama as nama_sektor_peluang,
                            snf.icon as icon,
                            pdf.nama as images,
                            p.status,
                            ak.nama as nama_kabkot,
                            akt.nama as nama_kabkot_tr,
                            ap.nama as nama_provinsi,
                            apt.nama as nama_provinsi_tr,
                            p.project_status_enum,
                            ps.nama as status_proyek
                        FROM tb_peluang_daerah p
                        LEFT JOIN tb_peluang_daerah_tr pt
                            ON p.id_peluang_daerah = pt.id_peluang_daerah
                        LEFT JOIN tb_peluang_daerah_kelayakan k
                            ON p.id_peluang_daerah = k.id_peluang_daerah
                        LEFT JOIN tb_sub_sektor_daerah ssd
                            ON p.id_sub_sektor_daerah = ssd.id_sub_sektor_daerah
                        LEFT JOIN tb_sub_sektor_nasional ssn
                            ON ssd.id_sub_sektor_nasional = ssn.id_sub_sektor_nasional
                        LEFT JOIN tb_sektor_nasional sn
                            ON sn.id_sektor_nasional = ssn.id_sektor_nasional
                        LEFT JOIN tb_sektor_nasional_ref snf
                            ON sn.id_sektor_nasional = snf.id_sektor
                        LEFT JOIN tb_kategori_sektor skn
                            ON skn.id_kategori_sektor = snf.id_kategori_sektor
                        LEFT JOIN tb_peluang_daerah_file pdf
                            ON pdf.id_peluang_daerah = p.id_peluang_daerah AND pdf.tipe = 1
                        LEFT JOIN tb_adm_kabkot ak
                            ON ak.id_adm_kabkot = p.id_adm_kabkot
                        LEFT JOIN tb_adm_kabkot_tr akt
                            ON ak.id_adm_kabkot = akt.id_adm_kabkot
                        LEFT JOIN tb_adm_provinsi ap
                            ON ap.id_adm_provinsi = p.id_adm_provinsi
                        LEFT JOIN tb_adm_provinsi_tr apt
                            ON apt.id_adm_provinsi = ap.id_adm_provinsi
                        LEFT JOIN tb_peluang_status ps
                            ON ps.id_Peluang_status = p.project_status_enum
                        WHERE p.status = 99
                            AND p.id_adm_kabkot = ${Id}
                        ORDER BY k.initial_invesment DESC;
                    `
                    ]);

                // Mapping data dengan keterangan masing-masing
                let peluang_investasi = [
                    ...ppi.map(item => {
                            const status = {
                                2: 'DIMINATI',
                                7: 'SOLD',
                                };

                                let status_proyek = '';

                                if (item.project_status_enum !== '') {
                                status_proyek = status[item.project_status_enum] ;
                            }

                            let kabkot = item.tb_adm_kabkot.nama
                            let prov = item.tb_adm_kabkot.tb_adm_provinsi.nama
                            let nama = item.nama
                            if (parameters.en) {
                                kabkot = item.tb_adm_kabkot.tb_adm_kabkot_tr[0].nama || item.tb_adm_kabkot.nama
                                prov = item.tb_adm_kabkot.tb_adm_provinsi.tb_adm_provinsi_tr[0].nama || item.tb_adm_kabkot.tb_adm_provinsi.nama
                                nama = item.tb_peluang_kabkot_tr[0]?.nama || item.nama
                            }

                            return {
                                id_peluang: item.id_peluang_kabkot,
                                id_adm_provinsi: parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                                id_adm_kabkot: item.id_adm_kabkot,
                                nama_kabkot: kabkot,
                                nama_provinsi: prov,
                                nama: nama,
                                nilai_irr: `${item.nilai_irr}%`,
                                nilai_investasi: numberFormatRpSingkatan(item.nilai_investasi),
                                nilai_npv: numberFormatRpSingkatan(item.nilai_npv),
                                nilai_pp: item.nilai_pp,
                                id_kategori_sektor: item.tb_peluang_sektor.kategori_sektor?.id_kategori_sektor,
                                nama_sektor: item.tb_peluang_sektor.kategori_sektor?.nama,
                                nama_sektor_peluang: item.tb_peluang_sektor.nama,
                                project_status_enum: item.project_status_enum,
                                status_proyek, // Properti status_proyek ditambahkan (opsional, jika dibutuhkan)
                                status: item.is_ipro ? 'IPRO' : 'PPI',
                                icon: `${env.get('APP_URL')}/uploads/icon-web/${item.tb_peluang_sektor.icon}`,
                                image: `${env.get('APP_URL')}/uploads/peluang/${item.id_adm_kabkot}/peluang/${item.tahun}/1/${item.tb_peluang_kabkot_file[0]?.nama || 'default.jpg'}`, // Fallback untuk nama file
                            };
                        }),
                    ...pid.map(item => {
                            let kabkot = item.nama_kabkot
                            let prov = item.nama_provinsi
                            let nama = item.nama
                            if (parameters.en) {
                                kabkot = item.nama_kabkot_tr || kabkot
                                prov =item.nama_provinsi_tr || prov
                                nama =item.nama_tr || nama
                            }

                        return {
                            id_peluang:item.id_peluang_daerah,
                            id_adm_provinsi:item.id_adm_provinsi,
                            id_adm_kabkot:item.id_adm_kabkot,
                            nama_kabkot:kabkot,
                            nama_provinsi:prov,
                            nama:nama,
                            nilai_irr: `${item.nilai_irr}%`,
                            nilai_investasi: numberFormatRpSingkatan(item.nilai_investasi),
                            nilai_npv: numberFormatRpSingkatan(item.nilai_npv),
                            nilai_pp:item.nilai_pp,
                            id_kategori_sektor:item.id_kategori_sektor,
                            nama_sektor:item.nama_sektor,
                            nama_sektor_peluang:item.nama_sektor_peluang,
                            project_status_enum:item.project_status_enum == null ? 0 : item.project_status_enum,
                            status_proyek:item.status_proyek,
                            status: 'PID',
                            icon: `${env.get('APP_URL')}/uploads/icon-web/${item.icon}`,
                            image:`${env.get('APP_URL')}/uploads/peluang_daerah/${item.id_peluang_daerah}/${item.images}`
                            }
                    })
                ]

            const demografi = await prisma.tb_demografi_kabkot.findFirst({
                where:{
                    id_adm_kabkot:Id,
                    status:99
                },
                orderBy:{
                    tahun:'desc'
                }
            })
            const umr = await prisma.tb_umr_kabkot.findFirst({
                where:{
                    id_adm_kabkot:Id,
                    status:99
                },
                orderBy:{
                    tahun:'desc'
                }
            })
            const investTahun = await prisma.tb_investasi_kabkot.findFirst({
                where: {
                    id_adm_kabkot: Id,
                    status: 99
                },
                orderBy: {
                    tahun: 'desc'
                },
                select: {
                    tahun: true
                }
            });

            // Jika data ditemukan, ambil tahun terbaru
            const tahunTerbaru = investTahun?.tahun;

            let investasi = [];

            if (tahunTerbaru) {
                // Step 2: Ambil semua data pada tahun terbaru
                const invesTahunTerbaru = await prisma.tb_investasi_kabkot.findMany({
                    where: {
                        id_adm_kabkot: Id,
                        status: 99,
                        tahun: tahunTerbaru
                    }
                });

                // Step 3: Kelompokkan data berdasarkan `id_jenis` dan jumlahkan investasi
                const totalPMA = invesTahunTerbaru
                    .filter(item => item.id_jenis === 1)
                    .reduce((total, item) => total + item.jumlah_investasi, 0);

                const totalPMDN = invesTahunTerbaru
                    .filter(item => item.id_jenis === 2)
                    .reduce((total, item) => total + item.jumlah_investasi, 0);

                // Step 4: Simpan hasil dalam array dengan jenis PMA dan PMDN
                investasi = [
                    { tahun: tahunTerbaru, jenis: 'PMA', jumlah: totalPMA },
                    { tahun: tahunTerbaru, jenis: 'PMDN', jumlah: totalPMDN }
                ];
            }

            let strProfil=''
            if (!parameters.en) {
                strProfil = `Kabupaten/Kota ${kab?.nama} memiliki wilayah dengan luas ${numberFormat(kab?.luas_wilayah)} km\u00B2.Total jumlah penduduk sebanyak ${numberFormat(demografi?.jumlah_pria+demografi?.jumlah_wanita)} jiwa. Jumlah penduduk laki-laki sebanyak ${numberFormat(demografi?.jumlah_pria)}  dan pupulasi perempuan sebanyak ${numberFormat(demografi?.jumlah_wanita)} orang (${demografi?.tahun}). Realisasi investasi asing atau PMA sebesar ${numberFormat(investasi[0].jumlah)} USD (${investasi[0].tahun}). Total investasi dalam negeri Kabupaten/Kota ini sebesar ${numberFormat(investasi[1].jumlah)} Rupiah (${investasi[1].tahun})`
            } else {
                strProfil = `Regency  ${kab?.nama} has an area ${numberFormat(kab?.luas_wilayah)} square kilometers.The Regency/City's population is ${numberFormat(demografi?.jumlah_pria+demografi?.jumlah_wanita)} people. The male population is  ${numberFormat(demografi?.jumlah_pria)} and the female population is  ${numberFormat(demografi?.jumlah_wanita)} people (${demografi?.tahun}). Realization of foreign investment or PMA of ${numberFormat(investasi[0].jumlah)} USD (${investasi[0].tahun}). Total domestic investment in this Regency/City is  ${numberFormat(investasi[1].jumlah)} Rupiah (${investasi[1].tahun})`
            }





            const sektorDaerahs = await prisma.tb_sektor_daerah.findMany({
                where: {
                    status: 99,
                    id_adm_provinsi: Id
                },
                include:{
                    tb_sektor_daerah_file:{
                        include:{
                            tb_sektor_daerah_file_tr:true
                        }
                    },
                    tb_sektor_daerah_insentif:
                    {
                        include:{
                            tb_sektor_daerah_insentif_file:true
                        }
                    },
                    sektor_nasional:{
                        include:{
                            sektor:{
                                include:{
                                    sektor_nasional_tr:true
                                }
                            }
                        }
                    },
                    tb_sektor_daerah_tr:true
                }

            });

            let sektorRef =[]
            let sektorDaerah
            if (!parameters.en) {
                sektorDaerah = await Promise.all(
                    sektorDaerahs.map(async (item) => {
                        const komo = await prisma.tb_sektor_daerah.findMany({
                            where: {
                                status: 99,
                                id_adm_kabkot: Id,
                                id_sektor_daerah: item.id_sektor_daerah,
                            },
                            include: {
                                tb_sub_sektor_daerah: {
                                    include: {
                                        tb_komoditi_daerah: {
                                            include:{
                                                tb_komoditi_daerah_file:true

                                            }
                                        },
                                        tb_sub_sektor_nasional: {
                                            include: {
                                                sub_sektor_ref: true,
                                            },
                                        },
                                    },
                                },
                                tb_sektor_daerah_insentif :{
                                    include: {
                                        tb_sektor_daerah_insentif_file: true,
                                    },
                                }

                            },
                        });

                        let strWilayah = '';
                        let sub_sektor = [];
                        let diagram = [];
                        let insentif = []
                        let subSektorRef =[]
                        const idSektor= item.sektor_nasional?.id_sektor_nasional
                        for (const item of komo) {
                            insentif = item.tb_sektor_daerah_insentif.map((items) =>{
                                return {
                                        judul: items.nama,
                                        judul_file: items.tb_sektor_daerah_insentif_file[0]?.judul,
                                        url:  `${process.env.APP_URL}/uploads/sektor/${items.tb_sektor_daerah_insentif_file[0]?.nama} `
                                    }
                            })
                            for (const item2 of item.tb_sub_sektor_daerah) {
                                const a = {
                                    nama_sub_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.nama,
                                    deskripsi: item2.deskripsi,
                                };


                                subSektorRef.push({
                                    id_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.id_sektor,
                                    id_sub_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.id_sub_sektor,
                                    nama_sub_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.nama,
                                })



                                item2.tb_komoditi_daerah[0]?.tb_komoditi_daerah_file.map((file) => {
                                    const b = {
                                        label: file.judul,
                                        image: `${process.env.APP_URL}/uploads/sektor/${file.nama}`,
                                    }
                                    diagram.push(b);
                                })

                                const komoditis = await prisma.tb_komoditi_daerah.findMany({
                                        where: {
                                            id_sub_sektor_daerah : item2.id_sub_sektor_daerah,
                                        },
                                        include: {
                                            tb_komoditi_daerah_value: {
                                                include: {
                                                    tb_komoditi_daerah_value_detail: {
                                                        orderBy:{
                                                            tahun : 'desc'
                                                        },
                                                        take:1
                                                    },
                                                },
                                            },
                                            tb_komoditi_nasional_ref:true
                                        },
                                    });

                                    const detKomoditi = komoditis.map((data) => {
                                        const detail = data.tb_komoditi_daerah_value.map((detail) => {
                                                return {
                                                    jenis: detail.nama,
                                                    nilai: `${detail.tb_komoditi_daerah_value_detail[0]?.numeric_value} ${data.tb_komoditi_daerah_value[0]?.satuan} (${detail.tb_komoditi_daerah_value_detail[0]?.tahun})`,
                                                }
                                            })
                                            detail.push({
                                                jenis: 'Sentra Produksi',
                                                nilai: data.sentra_produksi,
                                            })
                                        return {
                                            nama: data.tb_komoditi_nasional_ref.nama,
                                            file: `${process.env.APP_URL}/uploads/sektor/${data.file_icon}`,
                                            detail,
                                        }
                                     })

                                    a.komoditi=detKomoditi
                                sub_sektor.push(a);

                                for (const item3 of item2.tb_komoditi_daerah) {


                                    strWilayah += ` ${item3.sentra_produksi},`;
                                }
                            }
                        }

                        // Hapus koma terakhir dan menghilangkan duplikat
                        strWilayah = strWilayah.slice(0, -1);
                        const array = strWilayah.split(', ');
                        const uniqueArray = [...new Set(array)];
                        strWilayah = uniqueArray.join(', ');

                        sektorRef.push({
                            id_sektor :item.sektor_nasional?.id_sektor_nasional ||'',
                            nama_sektor: item.sektor_nasional?.sektor?.nama || '',
                            sub_sektor: subSektorRef,

                        })

                        return {
                            nama: item.sektor_nasional?.sektor?.nama || '',
                            deskripsi: item.deskripsi,
                            deskripsi_singkat: item.deskripsi_singkat,
                            diagram:diagram,
                            potensi: {
                                detail: item.potensi_pasar,
                                image: `${process.env.APP_URL}/uploads/sektor/${item.tb_sektor_daerah_file[0]?.nama}`,
                            },
                            insentif: insentif,
                            wilayah: strWilayah,
                            sub_sektor: sub_sektor,
                        };
                    })
                );
            } else {
                sektorDaerah = await Promise.all(
                    sektorDaerahs.map(async (item) => {
                        const komo = await prisma.tb_sektor_daerah.findMany({
                            where: {
                                status: 99,
                                id_adm_kabkot: Id,
                                id_sektor_daerah: item.id_sektor_daerah,
                            },
                            include: {
                                tb_sub_sektor_daerah: {
                                    include: {
                                        tb_sub_sektor_daerah_tr:true,
                                        tb_komoditi_daerah: {
                                            include:{
                                                tb_komoditi_daerah_tr:true,
                                                tb_komoditi_daerah_file:{
                                                    include:{
                                                        tb_komoditi_daerah_file_tr:true
                                                    }
                                                }
                                            }
                                        },
                                        tb_sub_sektor_nasional: {
                                            include: {
                                                sub_sektor_ref: {
                                                    include:{
                                                        sub_sektor_nasional_ref_tr:true
                                                    }
                                                },
                                            },
                                        },
                                    },
                                },
                                tb_sektor_daerah_insentif :{
                                    include: {
                                        tb_sektor_daerah_insentif_tr:true,
                                        tb_sektor_daerah_insentif_file: {
                                            include:{
                                                tb_sektor_daerah_insentif_file_tr:true
                                            }
                                        },
                                    },
                                }

                            },
                        });

                        let strWilayah = '';
                        let sub_sektor = [];
                        let diagram = [];
                        let insentif = []
                        let subSektorRef =[]
                        const idSektor= item.sektor_nasional?.id_sektor_nasional
                        for (const item of komo) {
                            insentif = item.tb_sektor_daerah_insentif.map((items) =>{
                                return {
                                        judul: items.tb_sektor_daerah_insentif_tr[0].nama || items.nama,
                                        judul_file: items.tb_sektor_daerah_insentif_file[0].tb_sektor_daerah_insentif_file_tr[0].judul || items.tb_sektor_daerah_insentif_file[0].judul,
                                        url:  `${process.env.APP_URL}/uploads/sektor/${items.tb_sektor_daerah_insentif_file[0].tb_sektor_daerah_insentif_file_tr[0].nama} `||`${process.env.APP_URL}/uploads/sektor/${items.tb_sektor_daerah_insentif_file[0].nama} `
                                    }

                            })
                            for (const item2 of item.tb_sub_sektor_daerah) {
                                const a = {
                                    nama_sub_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.sub_sektor_nasional_ref_tr?.nama || item2.tb_sub_sektor_nasional.sub_sektor_ref.nama,
                                    deskripsi: item2.tb_sub_sektor_daerah_tr[0].deskripsi || item2.deskripsi,
                                };


                                subSektorRef.push({
                                    id_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.id_sektor,
                                    id_sub_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.sub_sektor_nasional_ref_tr?.id_sub_sektor || item2.tb_sub_sektor_nasional.sub_sektor_ref.id_sub_sektor,
                                    nama_sub_sektor: item2.tb_sub_sektor_nasional.sub_sektor_ref.sub_sektor_nasional_ref_tr?.nama || item2.tb_sub_sektor_nasional.sub_sektor_ref.nama,
                                })



                                item2.tb_komoditi_daerah[0]?.tb_komoditi_daerah_file.map((file) => {
                                    const b = {
                                        label: file.tb_komoditi_daerah_file_tr[0]?.judul || file.judul,
                                        image: file.tb_komoditi_daerah_file_tr[0]?.nama != undefined ?   `${process.env.APP_URL}/uploads/sektor/${file.tb_komoditi_daerah_file_tr[0].nama}` : `${process.env.APP_URL}/uploads/sektor/${file.nama}`,
                                    }
                                    diagram.push(b);
                                })

                                const komoditis = await prisma.tb_komoditi_daerah.findMany({
                                        where: {
                                            id_sub_sektor_daerah : item2.id_sub_sektor_daerah,
                                        },
                                        include: {
                                            tb_komoditi_daerah_tr:true,
                                            tb_komoditi_daerah_value: {
                                                include: {
                                                    tb_komoditi_daerah_value_tr:true,
                                                    tb_komoditi_daerah_value_detail: {
                                                        orderBy:{
                                                            tahun : 'desc'
                                                        },
                                                        take:1
                                                    },
                                                },
                                            },
                                            tb_komoditi_nasional_ref:{
                                                include:{
                                                    tb_komoditi_nasional_ref_tr:true
                                                }
                                            }
                                        },
                                    });

                                    const detKomoditi = komoditis.map((data) => {
                                        const detail = data.tb_komoditi_daerah_value.map((detail) => {
                                                return {
                                                    jenis:detail.tb_komoditi_daerah_value_tr[0].nama || detail.nama,
                                                    nilai: `${detail.tb_komoditi_daerah_value_detail[0]?.numeric_value} ${data.tb_komoditi_daerah_value[0]?.satuan} (${detail.tb_komoditi_daerah_value_detail[0]?.tahun})`,
                                                }
                                            })
                                            detail.push({
                                                jenis: 'Sentra Produksi',
                                                nilai: data.tb_komoditi_daerah_tr[0].sentra_produksi || data.sentra_produksi,
                                            })
                                        return {
                                            nama: data.tb_komoditi_nasional_ref?.tb_komoditi_nasional_ref_tr?.nama || data.tb_komoditi_nasional_ref?.nama,
                                            file: `${process.env.APP_URL}/uploads/sektor/${data.file_icon}`,
                                            detail,
                                        }
                                    })

                                    a.komoditi=detKomoditi
                                sub_sektor.push(a);

                                for (const item3 of item2.tb_komoditi_daerah) {


                                    strWilayah += item3.tb_komoditi_daerah_tr[0].sentra_produksi != undefined ? item3.tb_komoditi_daerah_tr[0].sentra_produksi  :  ` ${item3.sentra_produksi},`;
                                }
                            }
                        }

                        // Hapus koma terakhir dan menghilangkan duplikat
                        strWilayah = strWilayah.slice(0, -1);
                        const array = strWilayah.split(', ');
                        const uniqueArray = [...new Set(array)];
                        strWilayah = uniqueArray.join(', ');

                        sektorRef.push({
                            id_sektor : item.sektor_nasional?.id_sektor_nasional ||'',
                            nama_sektor: item.sektor_nasional?.sektor?.sektor_nasional_tr?.nama || item.sektor_nasional?.sektor?.nama || '',
                            sub_sektor: subSektorRef,

                        })

                        return {
                            nama: item.sektor_nasional?.sektor?.sektor_nasional_tr?.nama || item.sektor_nasional?.sektor?.nama,
                            deskripsi: item.tb_sektor_daerah_tr?.deskripsi || item.deskripsi,
                            deskripsi_singkat: item.tb_sektor_daerah_tr?.deskripsi_singkat ||item.deskripsi_singkat,
                            diagram:diagram,
                            potensi: {
                                detail: item.potensi_pasar,
                                image: item.tb_sektor_daerah_file[0]?.nama != undefined ? `${process.env.APP_URL}/uploads/sektor/${item.tb_sektor_daerah_file[0]?.nama}` : null,
                            },
                            insentif: insentif,
                            wilayah: strWilayah,
                            sub_sektor: sub_sektor,
                        };
                    })
                );
            }





            const currentYear = new Date().getFullYear();

            const pdrbs = await prisma.tb_sektor_daerah_pdrb.findMany({
                include :{
                    tb_sektor_nasional:{
                        include:{
                            sektor:{
                                include:{
                                    sektor_nasional_tr:true
                                }
                            }
                        }
                    },
                },
                where: {
                    status: 99,
                    id_adm_kabkot:Id,
                    tahun_pdrb: {
                        gte: currentYear - 4
                    }
                },
            });

            const detPdrb = pdrbs.reduce((acc, item) => {
                const year = item.tahun_pdrb;
                let sectorName = item.tb_sektor_nasional.sektor.nama;
                if (parameters.en) {
                    sectorName = item.tb_sektor_nasional.sektor.sektor_nasional_tr?.nama || item.tb_sektor_nasional.sektor.nama;

                }
                const value = Number(item.jumlah_pdrb); // Ensure value is a number

                // Initialize an object for the year if it doesn't exist
                if (!acc[year]) {
                    acc[year] = {};
                }

                // Check if the sector already exists for the current year
                if (!acc[year][sectorName]) {
                    acc[year][sectorName] = {
                        tahun: year,
                        nama_sektor: sectorName,
                        nilai: 0 // Initialize as 0 for summing
                    };
                }

                // Sum the values
                acc[year][sectorName].nilai += value; // Sum as number

                return acc;
            }, {});

            const formattedDetPdrb = Object.entries(detPdrb).reduce((acc, [year, sectors]) => {
                acc[year] = Object.values(sectors).map(sector => ({
                    ...sector,
                    nilai_text: `${sector.nama_sektor} : Rp ${numberFormat(sector.nilai)}` // Format for display
                }));
                return acc;
            }, {});

            const umr5 = await prisma.tb_umr_kabkot.findMany({
                where: {
                    id_adm_kabkot: Id,
                    status:99
                },
                orderBy:{
                    tahun: 'desc',
                },
                take:5
            })
            const detUmr = umr5.map((item) => {
                return {
                    tahun : item.tahun,
                    nilai : item.nilai,
                    nilai_text : `Rp. ${numberFormat(item.nilai)}`
                }
            })

            const demografi5 = await prisma.tb_demografi_kabkot.findMany({
                where:{
                    status:99,
                    id_adm_kabkot:Id,
                    tahun: {
                        gte: currentYear - 4
                    }
                }
            })

            const daerahDetilemografis = demografi5.map((item) => {
                return {
                    tahun : item.tahun,
                    jumlah_pria : item.jumlah_pria,
                    jumlah_wanita : item.jumlah_wanita,
                    jumlah_pria_text : `Pria : ${item.jumlah_pria} jiwa`,
                    jumlah_wanita_text : `Wanita : ${item.jumlah_wanita} jiwa`,
                }
            })

            const inves5 = await prisma.tb_investasi_kabkot.findMany({
                where: {
                    status: 99,
                    id_adm_kabkot: Id,
                    tahun: {
                        gte: currentYear - 4 // assuming `latestYear` is the most recent year available
                    }
                },
                orderBy: [
                    { tahun: 'desc' }, // Order by year descending
                    { id_jenis: 'asc' } // Order by id_jenis within each year
                ],
                select: {
                    id_jenis: true,
                    tahun: true,
                    jumlah_investasi: true
                }
            });
            const investasi5 = inves5.reduce((acc, curr) => {
                const jenis = curr.id_jenis === 1 ? 'PMA' : 'PMDN';
                const satuan = curr.id_jenis === 1 ? 'US$' : 'RP';

                // Ensure a sub-object for the current year exists
                if (!acc[curr.tahun]) {
                    acc[curr.tahun] = [];
                }

                // Find if id_jenis entry already exists for the current year
                const jenisEntry = acc[curr.tahun].find(entry => entry.id_jenis === curr.id_jenis);

                // If the entry exists, update its total_investasi, otherwise create a new entry
                if (jenisEntry) {
                    jenisEntry.total_investasi += curr.jumlah_investasi;
                } else {
                    acc[curr.tahun].push({
                        id_jenis: curr.id_jenis,
                        jenis: jenis,
                        satuan:satuan,
                        tahun:curr.tahun,
                        total_investasi: curr.jumlah_investasi,
                    });
                }

                return acc;
            }, {});

            // Convert the result to an array format
            const investasiPerYear = Object.entries(investasi5).map(([tahun, data]) => ({
                tahun: parseInt(tahun),
                data : data.map(entry => ({
                        jenis: entry.jenis,
                        total_investasi: entry.total_investasi,
                        total_investasi_text: `${entry.jenis} ${entry.tahun} : <b>${entry.satuan} ${numberFormat(entry.total_investasi)}</b>`
                    }))
            }));

            const inves1Year = await prisma.tb_investasi_kabkot.findFirst({
                where: {
                    status: 99,
                    id_adm_kabkot: Id,
                },
                orderBy:{
                    tahun: 'desc',
                },
                select:{
                    tahun:true
                }
            })

            let inves1;
            let groupedData = {};

            if (inves1Year) {
                inves1 = await prisma.tb_investasi_kabkot.findMany({
                    where: {
                        status: 99,
                        id_adm_kabkot: Id,
                        tahun: inves1Year.tahun
                    },
                    select: {
                        id_jenis: true,
                        tahun: true,
                        jumlah_investasi: true,
                        tb_investasi_sektor: {
                            select:{
                                nama:true,
                                tb_investasi_sektor_tr:{
                                    select:{
                                        nama:true
                                    }
                                }
                            }
                        }
                    }
                });



                inves1.forEach(item => {
                    let sectorName=''
                    if (!parameters.en) {

                        sectorName = item.tb_investasi_sektor.nama;
                    } else {
                         sectorName = item.tb_investasi_sektor.tb_investasi_sektor_tr[0]?.nama || item.tb_investasi_sektor.nama;
                    }

                    // If sector name does not exist in groupedData, initialize it with an empty array
                    if (!groupedData[sectorName]) {
                        groupedData[sectorName] = [];
                    }

                    const jenis = item.id_jenis === 1 ? 'PMA' : 'PMDN';
                    const satuan = item.id_jenis === 1 ? 'US$' : 'RP';


                    // Push the formatted object into the array for this sector
                    groupedData[sectorName].push({
                        id_jenis: item.id_jenis,
                        tahun: item.tahun,
                        jumlah_investasi_text : `${jenis} ${sectorName} : ${satuan} ${numberFormat(item.jumlah_investasi)}`,
                        jumlah_investasi: item.jumlah_investasi,
                        nama: sectorName
                    });
                });

                // Convert groupedData object back to an array of grouped results if needed

            }

            inves1 = Object.values(groupedData);


            const sektors = await prisma.tb_sektor_daerah.findMany({
                        where: {
                            status: 99,
                            id_adm_kabkot: Id,
                        },
                        include: {
                            sektor_nasional:{
                                include:{
                                    sektor : {
                                        include :{
                                            sub_sektor_nasional:{
                                                include:{
                                                    sub_sektor_ref:true
                                                }
                                            }
                                        }
                                    }

                                }
                            }

                        },
                    });

                    const dropdownSektor =  sektors.map((item) => {
                        return {
                            id_sektor : item.sektor_nasional.sektor?.id_sektor,
                            nama_sektor : item.sektor_nasional.sektor?.nama,
                            sub_sektor : item.sektor_nasional.sektor.sub_sektor_nasional.map((item2) => {
                                return {
                                    id_sektor : item.sektor_nasional.sektor.id,
                                    id_sub_sektor : item2.id_sub_sektor,
                                    nama_sub_sektor : item2.sub_sektor_ref.nama
                                }
                            })
                        }
                    })
            const daerahDetil = {
                pdrb:formattedDetPdrb,
                investasi : {
                        by_sektor : inves1,
                        by_year :investasiPerYear
                    },
                umr:detUmr,
                penduduk : daerahDetilemografis,
                komoditi : {
                                ref_sektor : dropdownSektor
                            }
                }
                let prov = kab?.tb_adm_provinsi.nama
                let kabkot = kab?.nama
                if (parameters.en) {
                    kabkot = kab?.tb_adm_kabkot_tr[0]?.nama || kab?.nama
                    prov= kab?.tb_adm_provinsi.tb_adm_provinsi_tr[0]?.nama || kab?.tb_adm_provinsi.nama
                }

            return {
                success : true,
                data : {
                    id_adm_kabkot :kab?.id_adm_kabkot,
                    id_adm_provinsi : kab?.id_adm_provinsi,
                    nama_kabkot: kabkot,
                    nama_provinsi:prov,
                    luas :`area : ${numberFormat(kab?.luas_wilayah)}  km\u00B2`,
                    icon: `${process.env.APP_URL}/uploads/daerah/${kab?.id_adm_kabkot}/${kab?.file_logo}`,
                    banner:`${process.env.APP_URL}/uploads/daerah/${kab?.id_adm_provinsi}/${kab?.file_image}`,
                    profil : strProfil,
                    lon:kab?.lon,
                    lat:kab?.lat,
                    sektorDaerah,
                    peluang_investasi,
                    daerahDetil
                    // detail_pdrb : formattedDetPdrb,
                    // detail_investasi : {
                    //                     by_sektor :inves1,
                    //                     by_year :investasiPerYear
                    //                 },
                    // detail_umr :detUmr,
                    // detail_demografi :daerahDetilemografis,
                    // detail_komoditi : {
                    //     sektor:dropdownSektor
                    // }
                }
            }
        // } catch (error) {
        //     console.error(error);
        //     return response.status(500).json({
        //         status: 'error',
        //         message: 'Error fetching data',
        //     });
        // }
    }


    public async kawasan_prov({ params,response,request }:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ success:false,error: 'Invalid ID provided' })
        }
        try {
            const kwss = await prisma.tb_kawasan_industri.findMany({
                    where: {
                        status:"99",
                         id_adm_kabkot:  {
                            gte: parseInt(`${Id}00`),
                            lt: parseInt(`${Id}99`)
                        },
                    },
                    include:{
                        tb_kawasan_industri_kategori:true,
                        tb_adm_kabkot:{
                            include:{
                                tb_adm_provinsi:{
                                    include:{
                                        tb_adm_provinsi_tr:true
                                    }
                                },
                                tb_adm_kabkot_tr:true
                            }
                        },
                        tb_kawasan_industri_occupancy:true,
                        tb_kawasan_industri_tr:true

                    }
                });
                const parameters = request.qs()
                const kws = await kwss.map((item) => {
                    if (!parameters.en) {
                        return {
                            id:item.id_kawasan_industri,
                            nama : item.nama,
                            id_kategori : item.id_kategori,
                            nama_kategori : item.tb_kawasan_industri_kategori?.nama,
                            provinsi : item.tb_adm_kabkot.tb_adm_provinsi.nama,
                            kabkot : item.tb_adm_kabkot.nama,
                            luas : ` ${item.luas} ${item.luas_satuan}`,
                            ocupansi : item.tb_kawasan_industri_occupancy?.nama,
                        }
                    }else{
                        return {
                            id:item.id_kawasan_industri,
                            nama :item.tb_kawasan_industri_tr[0]?.nama || item.nama,
                            id_kategori : item.id_kategori,
                            nama_kategori : item.tb_kawasan_industri_kategori?.nama,
                            provinsi : item.tb_adm_kabkot.tb_adm_provinsi.tb_adm_provinsi_tr[0]?.nama || item.tb_adm_kabkot.tb_adm_provinsi.nama,
                            kabkot : item.tb_adm_kabkot.tb_adm_kabkot_tr[0].nama || item.tb_adm_kabkot.nama,
                            luas : ` ${item.luas} ${item.luas_satuan}`,
                            ocupansi : item.tb_kawasan_industri_occupancy?.nama,
                        }
                    }
                })
                return {
                    success:true,
                    data:kws
                }
            } catch (error) {
            console.error(error);
            return response.status(500).json({
                status: 'error',
                message: 'Error fetching data',
            });
        }
    }
    public async kawasan_kabkot({ params,response }:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ success:false,error: 'Invalid ID provided' })
        }
        try {
            const kwss = await prisma.tb_kawasan_industri.findMany({
                    where: {
                        status:"99",
                         id_adm_kabkot:  Id,
                    },
                    include:{
                        tb_kawasan_industri_kategori:true,
                        tb_adm_kabkot:{
                            include:{
                                tb_adm_provinsi:true
                            }
                        },
                        tb_kawasan_industri_occupancy:true
                    }
                });
                const kws = await kwss.map((item) => {
                    return {
                        id:item.id_kawasan_industri,
                        nama : item.nama,
                        id_kategori : item.id_kategori,
                        nama_kategori : item.tb_kawasan_industri_kategori?.nama,
                        provinsi : item.tb_adm_kabkot.tb_adm_provinsi.nama,
                        kabkot : item.tb_adm_kabkot.nama,
                        luas : ` ${item.luas} ${item.luas_satuan}`,
                        ocupansi : item.tb_kawasan_industri_occupancy?.nama,
                    }
                })

                return {
                    success:true,
                    data:kws
                }
            } catch (error) {
            console.error(error);
            return response.status(500).json({
                status: 'error',
                message: 'Error fetching data',
            });
        }
    }


    public async detail_komoditi_prov({params,response}:HttpContext) {
        const id_adm_provinsi = parseInt(params.id_adm_provinsi, 10);
        const id_sub_sektor = parseInt(params.id_sub_sektor, 10);
        const tahun = parseInt(params.tahun, 10);

        // Check if all parameters are not numbers
        if (isNaN(id_adm_provinsi) && isNaN(id_sub_sektor) && isNaN(tahun)) {
        return response.status(400).send({ success: false, error: 'All parameters are invalid' });
        }


        const komoditis = await prisma.tb_komoditi_provinsi.findMany({
            include: {
                tb_komoditi_nasional_ref: true,
                tb_komoditi_satuan:true
            },
            where: {
                id_adm_provinsi: id_adm_provinsi,
                tahun: tahun,
                tb_komoditi_nasional_ref: {
                id_sub_sektor: id_sub_sektor
                }
            }
        });
        const komoditi = komoditis.map((item) => {
            return {
                nama_komoditi: item.tb_komoditi_nasional_ref.nama,
                nilai_produksi: item.nilai_produksi,
                nilai_produksi_text:` ${item.tb_komoditi_nasional_ref.nama} : ${numberFormat(item.nilai_produksi)} ${item.tb_komoditi_satuan.nama}`
            }
        })


      return {
                success:true,
                data:komoditi
            }

    }

    public async detail_komoditi_kabkot({params,response}:HttpContext) {
        const id_adm_kabkot = parseInt(params.id_adm_kabkot, 10);
        const id_sub_sektor = parseInt(params.id_sub_sektor, 10);
        const tahun = parseInt(params.tahun, 10);

        // Check if all parameters are not numbers
        if (isNaN(id_adm_kabkot) && isNaN(id_sub_sektor) && isNaN(tahun)) {
        return response.status(400).send({ success: false, error: 'All parameters are invalid' });
        }


        const komoditis = await prisma.tb_komoditi_kabkot.findMany({
            include: {
                tb_komoditi_nasional_ref: true,
                tb_komoditi_satuan:true
            },
            where: {
                id_adm_kabkot: id_adm_kabkot,
                tahun: tahun,
                tb_komoditi_nasional_ref: {
                id_sub_sektor: id_sub_sektor
                }
            }
        });
        const komoditi = komoditis.map((item) => {
            return {
                nama_komoditi: item.tb_komoditi_nasional_ref.nama,
                nilai_produksi: item.nilai_produksi,
                nilai_produksi_text:` ${item.tb_komoditi_nasional_ref.nama} : ${numberFormat(item.nilai_produksi)} ${item.tb_komoditi_satuan.nama}`
            }
        })


      return {
                success:true,
                data:komoditi
            }



    }

    public async detail_kawasan({params,response,request}:HttpContext) {
        const id_kawasan_industri = parseInt(params.id_kawasan_industri, 10);

        // Check if all parameters are not numbers
        if (isNaN(id_kawasan_industri)) {
        return response.status(400).send({ success: false, error: 'All parameters are invalid' });
        }

        const kawasans = await prisma.tb_kawasan_industri.findFirst({
            include: {
                tb_bandara:true,
                tb_pelabuhan:true,
                tb_adm_kabkot:{
                    include:{
                        tb_adm_provinsi:{
                            include:{
                                tb_adm_provinsi_tr:true
                            }
                        },
                        tb_adm_kabkot_tr:true
                    }
                },
                tb_kawasan_industri_occupancy:true,
                tb_kawasan_industri_blok:{
                    include:{
                        tb_kawasan_industri_blok_kondisi:true,
                        tb_kawasan_industri_blok_fungsi:true,
                        tb_kawasan_industri_blok_tr:true
                    }
                },
                tb_kawasan_industri_file:{
                    include:{
                        tb_kawasan_industri_file_tr:true
                    }
                },
                tb_kawasan_industri_peluang:{
                    include:{
                        tb_kawasan_industri_peluang_file:{
                            where:{
                                tipe:1
                            },
                        },
                        tb_kawasan_industri_peluang_tr:true
                    }
                },
                tb_kawasan_industri_kategori:{
                    include:{
                        tb_kawasan_industri_kategori_tr:true
                    }
                },
                tb_kawasan_industri_tr:true
            },
            where: {
               status:"99",
               id_kawasan_industri : id_kawasan_industri
            }
        });

        // return kawasans

        const kondisis = await prisma.tb_kawasan_industri_blok_kondisi.findMany({});
        const kondisi = kondisis.map((item)=> {
            return {
                id_kawasan_industri_blok_kondisi:item.id_kawasan_industri_blok_kondisi,
                nama:item.nama,
                jumlah : 0
            }
        })
        let kawasan
        if (kawasans) {
            let nama_kategori = kawasans?.tb_kawasan_industri_kategori?.nama
            let nama_kawasan = kawasans?.nama
            let nama_kabkot = kawasans?.tb_adm_kabkot.nama
            let nama_provinsi = kawasans?.tb_adm_kabkot.tb_adm_provinsi.nama
            const parameters = request.qs()

            if (parameters.en) {
                nama_kategori = kawasans?.tb_kawasan_industri_kategori?.tb_kawasan_industri_kategori_tr[0].nama|| kawasans?.tb_kawasan_industri_kategori?.nama
                nama_kawasan = kawasans?.tb_kawasan_industri_tr[0]?.nama != 'undefined' ? kawasans?.tb_kawasan_industri_tr[0]?.nama || kawasans?.nama : kawasans?.nama
                nama_kabkot = kawasans?.tb_adm_kabkot.tb_adm_kabkot_tr[0].nama || kawasans?.tb_adm_kabkot.nama
                nama_provinsi = kawasans?.tb_adm_kabkot.tb_adm_provinsi.tb_adm_provinsi_tr[0].nama || kawasans?.tb_adm_kabkot.tb_adm_provinsi.nama
            }
            kawasans.tb_kawasan_industri_blok.forEach((item) => {
                // Cari elemen dalam kondisi yang memiliki id yang sama dengan id_kondisi di item
                const kondisiItem = kondisi.find((kond) => kond.id_kawasan_industri_blok_kondisi === item.id_kondisi);

                // Jika ditemukan, tambahkan jumlahnya
                if (kondisiItem) {
                kondisiItem.jumlah++;
                }
            });

            const peluang = kawasans.tb_kawasan_industri_peluang.map((item) => {
                return {
                    id_kawasan_industri_peluang:item.id_kawasan_industri_peluang,
                    nama_kabkot: nama_kabkot,
                    nama_provinsi: nama_provinsi,
                    judul:item.judul,
                    deskripsi : item.deskripsi,
                    luas : `${item.luas_lahan} ha`,
                    luas_potensi : ` ${kawasans?.luas} ${kawasans?.luas_satuan}`,
                    nilai_investasi : ` Rp. ${numberFormat(item.nilai_investasi)} `,
                    image:   item.tb_kawasan_industri_peluang_file.map((item2) =>
                        `${process.env.APP_URL}/uploads/kawasan_industri/${kawasans?.id_kawasan_industri}/${item.id_kawasan_industri_peluang}/${item2.nama}`,
                    )
                }
            });
            const images = await prisma.tb_kawasan_industri_file.findMany({
                            where:{
                                tipe:1,
                                id_kawasan_industri:id_kawasan_industri
                            },

                            take:1
                        })
            const vidios = await prisma.tb_kawasan_industri_file.findMany({
                            where:{
                                tipe:2,
                                id_kawasan_industri:id_kawasan_industri

                            },
                            take:1
                        })
            let vidio
            if (vidios.length > 0) {
                const check = await checkFileOrUrl(vidios[0].nama)
                if (check == 'file') {
                    vidio  = `${process.env.APP_URL}/uploads/kawasan_industri/${kawasans?.id_kawasan_industri}/${vidios[0]?.nama} `

                } else {
                    vidio = vidios[0].nama
                }
            }
            const layers = await prisma.tb_kawasan_layers.findMany({
                            where:{
                                id_kawasan_industri:id_kawasan_industri
                            },
                            select:{
                                layeruid:true
                            }
                        })

            kawasan = {
                id_kawasan_industri: kawasans?.id_kawasan_industri,
                id_kategori:kawasans?.id_kategori,
                nama_kategori:nama_kategori,
                nama_kawasan: nama_kawasan,
                nama_kabkot: nama_kabkot,
                nama_provinsi: nama_provinsi,
                nama_kontak : kawasans?.cp,
                alamat_kontak : kawasans?.alamat,
                no_telp : kawasans?.no_telp,
                no_fax : kawasans?.no_fax,
                email : kawasans?.email,
                luas: `${kawasans?.luas} ${kawasans?.luas_satuan}`,
                okupansi_kawasan : kawasans?.tb_kawasan_industri_occupancy?.nama,
                bandara_terdekat : ` ${kawasans?.tb_bandara?.nama} ${kawasans?.jarak_bandara_terdekat} `,
                pelabuhan_terdekat : ` ${kawasans?.tb_pelabuhan?.nama} ${kawasans?.jarak_pelabuhan_terdekat} `,
                major_tenant : kawasans?.major_tenants ? kawasans?.major_tenants : "-",
                range_harga_blok : 'Negosiasi',
                peluang_investasi : peluang,
                keterangan : kawasans?.keterangan,
                kondisi : kondisi,
                lon:kawasans?.lon,
                lat:kawasans?.lat,
                image:  images[0]?.nama != undefined ? `${process.env.APP_URL}/uploads/kawasan_industri/${kawasans?.id_kawasan_industri}/${images[0]?.nama} ` : null,
                vidio,
                // terakir_diperbarui:kawasans?.
                galeri : kawasans?.tb_kawasan_industri_file.map((item) => {
                    if (item.tipe == 1) {

                        return {
                            image:  `${process.env.APP_URL}/uploads/kawasan_industri/${kawasans?.id_kawasan_industri}/${item.nama} `,

                        }
                    } return undefined;
                }).filter(item => item !== undefined),
                layers


            }
        }



      return {
                success:true,
                data:kawasan
            }

    }

    public async get_zona_waktu({request}:HttpContext) {
        const datas = await prisma.tb_zona_waktu.findMany({
            include:{
                tb_zona_waktu_tr:true
            }
        })
        const data = datas.map((item) => {
            const parameters = request.qs()
            if (parameters.en) {
                return {
                    id_zona_waktu : item.id_zona_waktu,
                    nama : item.tb_zona_waktu_tr[0].nama || item.nama,
                }
            }else{
                return {
                    id_zona_waktu : item.id_zona_waktu,
                    nama : item.nama,
                }
            }

        })

        return {
            success :true,
            data :data
        }
    }
    public async get_prov({params ,response,request}:HttpContext) {
        let Id = params.id_zona_waktu == ','? null : params.id_zona_waktu
        Id = parseInt(params.id_zona_waktu, 10)
        let search={}
        if (!isNaN(Id)) {
             search ={
                id_zona_waktu:Id
            }
        }

        const parameters = request.qs()
        const datas = await prisma.tb_adm_provinsi.findMany({
            where:search,
            orderBy:{
                id_adm_provinsi:'asc'
            },
            include:{
                tb_adm_provinsi_tr:true
            }
        })

        const data = datas.map((item) => {
            if (parameters.en) {
                return {
                    id_adm_provinsi : item.id_adm_provinsi,
                    nama : item.tb_adm_provinsi_tr[0]?.nama || item.nama,
                    lon:item.lon,
                    lat:item.lat
                }
            }else{
                return {
                    id_adm_provinsi : item.id_adm_provinsi,
                    nama : item.nama,
                    lon:item.lon,
                    lat:item.lat
                }
            }


        })

        return {
            success :true,
            data :data
        }
    }
    public async get_ppi_by_zonawaktu({params ,response,request}:HttpContext) {
        let Id = params.id_zona_waktu == ','? null : params.id_zona_waktu
        Id = parseInt(params.id_zona_waktu, 10)
        let search={}
        if (!isNaN(Id)) {
             search ={
                id_zona_waktu:Id
            }
        }

        const datas = await prisma.tb_peluang_kabkot.findMany({
            where: {
                id_prioritas: 1,
                status: "99",
                // OR: provinsiIds.map((Id) => ({
                //     id_adm_kabkot: {
                //         gte: parseInt(`${Id}00`),
                //         lt: parseInt(`${Id}99`)
                //     },
                // }))
                tb_adm_kabkot:{
                    tb_adm_provinsi:{
                        id_zona_waktu:Id
                    }
                }
            },
            include:{
                tb_peluang_kabkot_tr:true
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const parameters  = request.qs()
        const data = datas.map((item) => {
            if (!parameters.en) {
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    nama : item.nama,
                    lon:item.lon,
                    lat:item.lat
                }
            }else{
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    nama : item.tb_peluang_kabkot_tr[0]?.nama || item.nama,
                    lon:item.lon,
                    lat:item.lat
                }

            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_ppi_by_prov({params ,response,request}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_peluang_kabkot.findMany({
            where: {
                id_prioritas: 1,
                status: "99",
                id_adm_kabkot: {
                    gte: parseInt(`${Id}00`),
                    lt: parseInt(`${Id}99`)
                },
            },
            include:{
                tb_peluang_kabkot_tr:true
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const parameters  = request.qs()
        const data = datas.map((item) => {
            if (!parameters.en) {
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    nama : item.nama,
                    lon:item.lon,
                    lat:item.lat
                }
            }else{
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    nama : item.tb_peluang_kabkot_tr[0]?.nama || item.nama,
                    lon:item.lon,
                    lat:item.lat
                }

            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_ppi_by_kabkot({params ,response,request}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_peluang_kabkot.findMany({
            where: {
                id_prioritas: 1,
                status: "99",
                id_adm_kabkot: Id,
            },
            include:{
                tb_peluang_kabkot_tr:true
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });

        const parameters  = request.qs()
        const data = datas.map((item) => {
            if (!parameters.en) {
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    nama : item.nama,
                    lon:item.lon,
                    lat:item.lat
                }
            }else{
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot :  item.id_peluang_kabkot,
                    nama : item.tb_peluang_kabkot_tr[0]?.nama || item.nama,
                    lon:item.lon,
                    lat:item.lat
                }

            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_ipro_by_zonawaktu({params ,response,request}:HttpContext) {
        let Id = params.id_zona_waktu == ','? null : params.id_zona_waktu
        Id = parseInt(params.id_zona_waktu, 10)
        let search={}
        if (!isNaN(Id)) {
             search ={
                id_zona_waktu:Id
            }
        }
        // const prov = await prisma.tb_adm_provinsi.findMany({
        //     where:{
        //         id_zona_waktu : Id
        //     },
        //     orderBy:{
        //         id_adm_provinsi:'asc'
        //     }
        // })
        // const provinsiIds = [];
        // if (prov.length > 0) {
        //     prov.map((item) => {
        //         provinsiIds.push(item.id_adm_provinsi);
        //     })
        // }
        const datas = await prisma.tb_peluang_kabkot.findMany({
            where: {
                project_status_enum: "2",
                status: "99",
                // OR: provinsiIds.map((Id) => ({
                //     id_adm_kabkot: {
                //         gte: parseInt(`${Id}00`),
                //         lt: parseInt(`${Id}99`)
                //     },
                // }))
                tb_adm_kabkot:{
                    tb_adm_provinsi:{
                        id_zona_waktu:Id
                    }
                }
            },
            include:{
                tb_peluang_kabkot_tr:true
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const parameters = request.qs()
        const data = datas.map((item) => {

            if (!parameters.en) {
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    project_status_enum : item.project_status_enum,
                    nama : item.nama,
                    lon:item.lon,
                    lat:item.lat
                }
            }else{
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    project_status_enum : item.project_status_enum,
                    nama : item.tb_peluang_kabkot_tr[0].nama || item.nama,
                    lon:item.lon,
                    lat:item.lat
                }
            }


        })

        return {
            success :true,
            data :data
        }
    }
    public async get_ipro_by_prov({params ,response,request}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_peluang_kabkot.findMany({
            where: {
                project_status_enum: "2",
                status: "99",
                id_adm_kabkot: {
                    gte: parseInt(`${Id}00`),
                    lt: parseInt(`${Id}99`)
                },
            },
            include:{
                tb_peluang_kabkot_tr:true
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const parameters = request.qs()
        const data = datas.map((item) => {

            if (!parameters.en) {
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    project_status_enum : item.project_status_enum,
                    nama : item.nama,
                    lon:item.lon,
                    lat:item.lat
                }
            }else{
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    project_status_enum : item.project_status_enum,
                    nama : item.tb_peluang_kabkot_tr[0].nama || item.nama,
                    lon:item.lon,
                    lat:item.lat
                }
            }


        })
        return {
            success :true,
            data :data
        }
    }
    public async get_ipro_by_kabkot({params ,response,request}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_peluang_kabkot.findMany({
            where: {
                project_status_enum: "2",
                status: "99",
                id_adm_kabkot: Id,
            },
            include:{
                tb_peluang_kabkot_tr:true
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const parameters = request.qs()
        const data = datas.map((item) => {

            if (!parameters.en) {
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    project_status_enum : item.project_status_enum,
                    nama : item.nama,
                    lon:item.lon,
                    lat:item.lat
                }
            }else{
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    project_status_enum : item.project_status_enum,
                    nama : item.tb_peluang_kabkot_tr[0].nama || item.nama,
                    lon:item.lon,
                    lat:item.lat
                }
            }


        })
        return {
            success :true,
            data :data
        }
    }

    public async get_pid_by_zonawaktu({params ,response,request}:HttpContext) {
        let Id = params.id_zona_waktu == ','? null : params.id_zona_waktu
        Id = parseInt(params.id_zona_waktu, 10)
        let search={}
        if (!isNaN(Id)) {
             search ={
                id_zona_waktu:Id
            }
        }
        // const prov = await prisma.tb_adm_provinsi.findMany({
        //     where:{
        //         id_zona_waktu : Id
        //     },
        //     orderBy:{
        //         id_adm_provinsi:'asc'
        //     }
        // })
        // const provinsiIds = [];
        // if (prov.length > 0) {
        //     prov.map((item) => {
        //         provinsiIds.push(item.id_adm_provinsi);
        //     })

        // }
        const datas = await prisma.tb_peluang_daerah.findMany({
            where: {
                status: 99,
                OR: [
                    //   ...provinsiIds.map((Id) => ({
                    //     id_adm_kabkot: {
                    //         gte: parseInt(`${Id}00`),
                    //         lt: parseInt(`${Id}99`),
                    //     },
                    // })),
                    {
                        tb_adm_kabkot:{
                            tb_adm_provinsi:{
                                id_zona_waktu:Id
                            }
                        }
                    },
                    {
                        tb_adm_provinsi: {
                            id_zona_waktu:Id, // OR untuk id_adm_provinsi
                        },
                    },
                ],
            },
            include:{
                tb_peluang_daerah_tr:true
            }
        });
        const parameters = request.qs()
        const data = datas.map((item) => {

            if (!parameters.en) {
                return {
                    id_adm_provinsi :item.id_adm_kabkot? parseInt(item.id_adm_kabkot.toString().slice(0, 2)):item.id_adm_provinsi,
                    id_peluang_daerah : item.id_peluang_daerah,
                    nama : item.judul,
                    lon:item.lon,
                    lat:item.lat
                }
            }else{
                return {
                    id_adm_provinsi :item.id_adm_kabkot? parseInt(item.id_adm_kabkot.toString().slice(0, 2)):item.id_adm_provinsi,
                    id_peluang_daerah : item.id_peluang_daerah,
                    nama : item.tb_peluang_daerah_tr[0]?.judul|| item.judul,
                    lon:item.lon,
                    lat:item.lat
                }
            }

        })

        return {
            success :true,
            data :data
        }
    }
    public async get_pid_by_prov({params ,response,request}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_peluang_daerah.findMany({
            where: {
                status: 99,
                OR: [
                      { id_adm_kabkot: {
                            gte: parseInt(`${Id}00`),
                            lt: parseInt(`${Id}99`),
                        },
                        },
                        {

                            id_adm_provinsi:Id,
                        }
                ],
            },
            orderBy:{
                id_adm_kabkot:'asc'
            },
            include:{
                tb_peluang_daerah_tr:true
            }
        });
        const parameters = request.qs()
        const data = datas.map((item) => {

            if (!parameters.en) {
                return {
                    id_adm_provinsi :item.id_adm_kabkot? parseInt(item.id_adm_kabkot.toString().slice(0, 2)):item.id_adm_provinsi,
                    id_peluang_daerah : item.id_peluang_daerah,
                    nama : item.judul,
                    lon:item.lon,
                    lat:item.lat
                }
            }else{
                return {
                    id_adm_provinsi :item.id_adm_kabkot? parseInt(item.id_adm_kabkot.toString().slice(0, 2)):item.id_adm_provinsi,
                    id_peluang_daerah : item.id_peluang_daerah,
                    nama : item.tb_peluang_daerah_tr[0]?.judul|| item.judul,
                    lon:item.lon,
                    lat:item.lat
                }
            }

        })

        return {
            success :true,
            data :data
        }
    }
    public async get_pid_by_kabkot({params ,response,request}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_peluang_daerah.findMany({
            where: {
                status: 99,
                id_adm_kabkot:Id
            },
            orderBy:{
                id_adm_kabkot:'asc'
            },

            include:{
                tb_peluang_daerah_tr:true
            }
        });
        const parameters = request.qs()
        const data = datas.map((item) => {

            if (!parameters.en) {
                return {
                    id_adm_provinsi :item.id_adm_kabkot? parseInt(item.id_adm_kabkot.toString().slice(0, 2)):item.id_adm_provinsi,
                    id_peluang_daerah : item.id_peluang_daerah,
                    nama : item.judul,
                    lon:item.lon,
                    lat:item.lat
                }
            }else{
                return {
                    id_adm_provinsi :item.id_adm_kabkot? parseInt(item.id_adm_kabkot.toString().slice(0, 2)):item.id_adm_provinsi,
                    id_peluang_daerah : item.id_peluang_daerah,
                    nama : item.tb_peluang_daerah_tr[0]?.judul|| item.judul,
                    lon:item.lon,
                    lat:item.lat
                }
            }

        })

        return {
            success :true,
            data :data
        }
    }

    public async get_kawasan_by_zonawaktu({params ,response,request}:HttpContext) {
        let Id = params.id_zona_waktu == ','? null : params.id_zona_waktu
        Id = parseInt(params.id_zona_waktu, 10)
        let search={}
        if (!isNaN(Id)) {
             search ={
                id_zona_waktu:Id
            }
        }
        // const prov = await prisma.tb_adm_provinsi.findMany({
        //     where:{
        //         id_zona_waktu : Id
        //     },
        //     orderBy:{
        //         id_adm_provinsi:'asc'
        //     }
        // })
        // const provinsiIds = [];
        // if (prov.length > 0) {
        //     prov.map((item) => {
        //         provinsiIds.push(item.id_adm_provinsi);
        //     })

        // }
        const datas = await prisma.tb_kawasan_industri.findMany({
            where: {
                id_kategori : 3,
                status: "99",
                // OR: provinsiIds.map((Id) => ({
                //     id_adm_kabkot: {
                //         gte: parseInt(`${Id}00`),
                //         lt: parseInt(`${Id}99`)
                //     },
                // }))
                tb_adm_kabkot:{
                    tb_adm_provinsi:{
                        id_zona_waktu:Id
                    }
                }
            },
            orderBy:{
                id_adm_kabkot:'asc'
            },
            include:{
                tb_kawasan_industri_tr:true
            }
        });
        const parameters = request.qs()
        const data = datas.map((item) => {
            if (!parameters.en) {
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    project_status_enum : item.project_status_enum,
                    nama : item.nama,
                    lon:item.lon,
                    lat:item.lat

                }
            }else{
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    project_status_enum : item.project_status_enum,
                    nama :item.tb_kawasan_industri_tr[0]?.nama || item.nama,
                    lon:item.lon,
                    lat:item.lat

                }
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_kawasan_by_prov({params ,response,request}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_kawasan_industri.findMany({
            where: {
                id_kategori : 3,
                status: "99",
                id_adm_kabkot: {
                    gte: parseInt(`${Id}00`),
                    lt: parseInt(`${Id}99`)
                },
            },
            orderBy:{
                id_adm_kabkot:'asc'
            },
            include:{
                tb_kawasan_industri_tr:true
            }
        });
        const parameters = request.qs()
        const data = datas.map((item) => {
            if (!parameters.en) {
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    project_status_enum : item.project_status_enum,
                    nama : item.nama,
                    lon:item.lon,
                    lat:item.lat

                }
            }else{
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    project_status_enum : item.project_status_enum,
                    nama :item.tb_kawasan_industri_tr[0]?.nama || item.nama,
                    lon:item.lon,
                    lat:item.lat

                }
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_kawasan_by_kabkot({params ,response,request}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_kawasan_industri.findMany({
            where: {
                id_kategori : 3,
                status: "99",
                id_adm_kabkot: Id,
            },
            orderBy:{
                id_adm_kabkot:'asc'
            },
            include:{
                tb_kawasan_industri_tr:true
            }
        });
        const parameters = request.qs()
        const data = datas.map((item) => {
            if (!parameters.en) {
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    project_status_enum : item.project_status_enum,
                    nama : item.nama,
                    lon:item.lon,
                    lat:item.lat

                }
            }else{
                return {
                    id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                    id_peluang_kabkot : item.id_peluang_kabkot,
                    project_status_enum : item.project_status_enum,
                    nama :item.tb_kawasan_industri_tr[0]?.nama || item.nama,
                    lon:item.lon,
                    lat:item.lat

                }
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_kek_by_zonawaktu({params ,response}:HttpContext) {
        let Id = params.id_zona_waktu == ','? null : params.id_zona_waktu
        Id = parseInt(params.id_zona_waktu, 10)
        let search={}
        if (!isNaN(Id)) {
             search ={
                id_zona_waktu:Id
            }
        }
        // const prov = await prisma.tb_adm_provinsi.findMany({
        //     where:{
        //         id_zona_waktu : Id
        //     },
        //     orderBy:{
        //         id_adm_provinsi:'asc'
        //     }
        // })
        // const provinsiIds = [];
        // if (prov.length > 0) {
        //     prov.map((item) => {
        //         provinsiIds.push(item.id_adm_provinsi);
        //     })

        // }
        const datas = await prisma.tb_kawasan_industri.findMany({
            where: {
                id_kategori : 1,
                status: "99",
                // OR: provinsiIds.map((Id) => ({
                //     id_adm_kabkot: {
                //         gte: parseInt(`${Id}00`),
                //         lt: parseInt(`${Id}99`)
                //     },
                // }))
                tb_adm_kabkot:{
                    tb_adm_provinsi:{
                        id_zona_waktu:Id
                    }
                }
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat

            }

        })

        return {
            success :true,
            data :data
        }
    }
    public async get_kek_by_prov({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_kawasan_industri.findMany({
            where: {
                id_kategori : 1,
                status: "99",
                id_adm_kabkot: {
                    gte: parseInt(`${Id}00`),
                    lt: parseInt(`${Id}99`)
                },
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_kek_by_kabkot({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_kawasan_industri.findMany({
            where: {
                id_kategori : 1,
                status: "99",
                id_adm_kabkot: Id,
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }

    public async get_prov_by_zona_waktu({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }
        const provs = await prisma.tb_adm_provinsi.findMany({
            where:{
                id_zona_waktu : Id
            },
            orderBy:{
                id_adm_provinsi:'asc'
            }
        })
        const provinsiIds = [];
        if (provs.length > 0) {
            provs.map((item) => {
                provinsiIds.push(item.id_adm_provinsi);
            })

        }
        let prov = await prisma.tb_adm_provinsi.findMany({
                where: {
                    OR: provinsiIds.map((Id) => ({
                    id_adm_provinsi:Id,
                }))
                },
                select: {
                    id_adm_provinsi: true,
                    nama: true,
                    luas_wilayah:true,
                    file_image: true,
                    file_logo: true,
                    tb_adm_kabkot: {
                        select: {
                            _count: {
                                select: {
                                    tb_peluang_kabkot: {
                                        where: {
                                            status: "99"
                                        }
                                    }
                                }
                            }
                        }
                    },
                    tb_zona_waktu:{
                        select:{
                            nama:true
                        }
                    }
                },
                orderBy:{
                    id_adm_provinsi:'asc'
                }
            });
            const groupedResults = {};

                await Promise.all(
                    prov.map(item =>
                        limit(async () => {
                            try {
                                // Fetching data from Prisma
                                const latestYearData = await prisma.tb_investasi_provinsi.findMany({
                                    where: {
                                        status: 99,
                                        id_adm_provinsi: item.id_adm_provinsi
                                    },
                                    orderBy: [
                                        { id_jenis: 'asc' },
                                        { tahun: 'desc' }
                                    ],
                                    select: {
                                        id_jenis: true,
                                        tahun: true,
                                        jumlah_investasi: true
                                    }
                                });

                                const totalInvestasiPerJenis = latestYearData.reduce((acc, curr) => {
                                    const jenis = curr.id_jenis === 1 ? 'PMA' : 'PMDN';
                                    if (!acc[curr.id_jenis]) {
                                        acc[curr.id_jenis] = { id_jenis: curr.id_jenis, jenis: jenis, total_investasi: 0 };
                                    }
                                    acc[curr.id_jenis].total_investasi += curr.jumlah_investasi;
                                    return acc;
                                }, {});

                                const peluangs = item.tb_adm_kabkot.reduce((sum, item) => {
                                    return sum + item._count.tb_peluang_kabkot;
                                }, 0);

                                const realisasi = Object.values(totalInvestasiPerJenis).map((item) => {
                                    return {
                                        nilai: item.jenis === 'PMA' ? `PMA : (US$ Ribu) : ${numberFormat(item.total_investasi)}` : `PMDN : (RP Juta) : ${numberFormat(item.total_investasi)}`,
                                    };
                                });

                                // Group by tb_zona_waktu.nama
                                const zoneName = item.tb_zona_waktu.nama.toLowerCase(); // Convert to lowercase for consistency
                                if (!groupedResults[zoneName]) {
                                    groupedResults[zoneName] = [];
                                }

                                groupedResults[zoneName].push({
                                    id_adm_provinsi: item.id_adm_provinsi,
                                    nama: item.nama,
                                    luas_wilayah: item.luas_wilayah,
                                    image:  `${process.env.APP_URL}/uploads/daerah/${item.id_adm_provinsi}/${item.file_logo} `,
                                    peluang: peluangs,
                                    realisasi: realisasi
                                });

                            } catch (error) {
                                console.error(`Error processing province ${item.id_adm_provinsi}:`, error);
                                if (!groupedResults['error']) {
                                    groupedResults['error'] = [];
                                }
                                groupedResults['error'].push({
                                    id_adm_provinsi: item.id_adm_provinsi,
                                    error: true,
                                    message: 'Error fetching data'
                                });
                            }
                        })
                    )
                );

                // Convert the grouped results into the desired array format
                const resultArray = Object.keys(groupedResults).map(key => {
                    return groupedResults[key] ;
                });

            return {
                success : true,
                data : resultArray[0]? resultArray[0] : null,
            }
    }

    public async get_prov_by_prov({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }
        let prov = await prisma.tb_adm_provinsi.findMany({
                where: {
                    id_adm_provinsi:Id
                },
                select: {
                    id_adm_provinsi: true,
                    nama: true,
                    luas_wilayah:true,
                    file_image: true,
                    file_logo: true,
                    tb_adm_kabkot: {
                        select: {
                            _count: {
                                select: {
                                    tb_peluang_kabkot: {
                                        where: {
                                            status: "99"
                                        }
                                    }
                                }
                            }
                        }
                    },
                    tb_zona_waktu:{
                        select:{
                            nama:true
                        }
                    }
                },
                orderBy:{
                    id_adm_provinsi:'asc'
                }
            });
            const groupedResults = {};

                await Promise.all(
                    prov.map(item =>
                        limit(async () => {
                            try {
                                // Fetching data from Prisma
                                const latestYearData = await prisma.tb_investasi_provinsi.findMany({
                                    where: {
                                        status: 99,
                                        id_adm_provinsi: item.id_adm_provinsi
                                    },
                                    orderBy: [
                                        { id_jenis: 'asc' },
                                        { tahun: 'desc' }
                                    ],
                                    select: {
                                        id_jenis: true,
                                        tahun: true,
                                        jumlah_investasi: true
                                    }
                                });

                                const totalInvestasiPerJenis = latestYearData.reduce((acc, curr) => {
                                    const jenis = curr.id_jenis === 1 ? 'PMA' : 'PMDN';
                                    if (!acc[curr.id_jenis]) {
                                        acc[curr.id_jenis] = { id_jenis: curr.id_jenis, jenis: jenis, total_investasi: 0 };
                                    }
                                    acc[curr.id_jenis].total_investasi += curr.jumlah_investasi;
                                    return acc;
                                }, {});

                                const peluangs = item.tb_adm_kabkot.reduce((sum, item) => {
                                    return sum + item._count.tb_peluang_kabkot;
                                }, 0);

                                const realisasi = Object.values(totalInvestasiPerJenis).map((item) => {
                                    return {
                                        nilai: item.jenis === 'PMA' ? `PMA : (US$ Ribu) : ${numberFormat(item.total_investasi)}` : `PMDN : (RP Juta) : ${numberFormat(item.total_investasi)}`,
                                    };
                                });

                                // Group by tb_zona_waktu.nama
                                const zoneName = item.tb_zona_waktu.nama.toLowerCase(); // Convert to lowercase for consistency
                                if (!groupedResults[zoneName]) {
                                    groupedResults[zoneName] = [];
                                }

                                groupedResults[zoneName].push({
                                    id_adm_provinsi: item.id_adm_provinsi,
                                    nama: item.nama,
                                    luas_wilayah: item.luas_wilayah,
                                    image:  `${process.env.APP_URL}/uploads/daerah/${item.id_adm_provinsi}/${item.file_logo} `,
                                    peluang: peluangs,
                                    realisasi: realisasi
                                });

                            } catch (error) {
                                console.error(`Error processing province ${item.id_adm_provinsi}:`, error);
                                if (!groupedResults['error']) {
                                    groupedResults['error'] = [];
                                }
                                groupedResults['error'].push({
                                    id_adm_provinsi: item.id_adm_provinsi,
                                    error: true,
                                    message: 'Error fetching data'
                                });
                            }
                        })
                    )
                );

                // Convert the grouped results into the desired array format
                const resultArray = Object.keys(groupedResults).map(key => {
                    return groupedResults[key] ;
                });

            return {
                success : true,
                data : resultArray[0]? resultArray[0] : null,
            }
    }

    public async get_bandara_by_zonawaktu({params ,response}:HttpContext) {
        let Id = params.id_zona_waktu == ','? null : params.id_zona_waktu
        Id = parseInt(params.id_zona_waktu, 10)
        let search={}
        if (!isNaN(Id)) {
             search ={
                id_zona_waktu:Id
            }
        }

        const datas = await prisma.tb_bandara.findMany({
            select: {
                id_adm_kabkot: true,
                nama: true,
                lon: true,
                lat: true,
            },
            where: {
                status: 99,
                tb_adm_kabkot: {
                    tb_adm_provinsi: {
                        id_zona_waktu: Id,
                    },
                },
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }

        })

        return {
            success :true,
            data :data
        }
    }
    public async get_bandara_by_prov({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_bandara.findMany({
            where: {
                status: 99,
                id_adm_kabkot: {
                    gte: parseInt(`${Id}00`),
                    lt: parseInt(`${Id}99`)
                },
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_bandara_by_kabkot({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_bandara.findMany({
            where: {
                status: 99,
                id_adm_kabkot: Id,
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_hotel_by_zonawaktu({params ,response}:HttpContext) {
        const Id = parseInt(params.id_zona_waktu, 10) || 1;

        // Ambil daftar provinsi berdasarkan zona waktu
        // const provinsiIds = (await prisma.tb_adm_provinsi.findMany({
        //     where: { id_zona_waktu: Id },
        //     orderBy: { id_adm_provinsi: 'asc' }
        // })).map(prov => prov.id_adm_provinsi);

        // Ambil data hotel berdasarkan provinsi yang ditemukan
        const datas = await prisma.tb_hotel.findMany({
            select: {
                id_adm_kabkot: true,
                nama: true,
                lon: true,
                lat: true,
            },
            where: {
                status: 99,
                tb_adm_kabkot: {
                    tb_adm_provinsi: {
                        id_zona_waktu: Id,
                    },
                },
            },
            orderBy: { id_adm_kabkot: 'asc' },
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_hotel_by_prov({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_hotel.findMany({
            where: {
                status: 99,
                id_adm_kabkot: {
                    gte: parseInt(`${Id}00`),
                    lt: parseInt(`${Id}99`)
                },
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_hotel_by_kabkot({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_hotel.findMany({
            where: {
                status: 99,
                id_adm_kabkot: Id,
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_pelabuhan_by_zonawaktu({params ,response}:HttpContext) {
        let Id = params.id_zona_waktu == ','? null : params.id_zona_waktu
        Id = parseInt(params.id_zona_waktu, 10)
        let search={}
        if (!isNaN(Id)) {
             search ={
                id_zona_waktu:Id
            }
        }

        const datas = await prisma.tb_pelabuhan.findMany({
            select: {
                id_adm_kabkot: true,
                nama: true,
                lon: true,
                lat: true,
            },
            where: {
                status: 99,
                tb_adm_kabkot: {
                    tb_adm_provinsi: {
                        id_zona_waktu: Id,
                    },
                },
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }

        })

        return {
            success :true,
            data :data
        }
    }
    public async get_pelabuhan_by_prov({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_pelabuhan.findMany({
            where: {
                status: 99,
                id_adm_kabkot: {
                    gte: parseInt(`${Id}00`),
                    lt: parseInt(`${Id}99`)
                },
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_pelabuhan_by_kabkot({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_pelabuhan.findMany({
            where: {
                status: 99,
                id_adm_kabkot: Id,
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_pendidikan_by_zonawaktu({params ,response}:HttpContext) {
        const Id = parseInt(params.id_zona_waktu, 10) || 1;

        // Ambil daftar provinsi berdasarkan zona waktu
        // const provinsiIds = (await prisma.tb_adm_provinsi.findMany({
        //     where: { id_zona_waktu: Id },
        //     orderBy: { id_adm_provinsi: 'asc' }
        // })).map(prov => prov.id_adm_provinsi);

        // Ambil data hotel berdasarkan provinsi yang ditemukan
        const datas = await prisma.tb_pendidikan.findMany({
            select:{
                id_adm_kabkot:true,
                nama:true,
                lon:true,
                lat:true
            },
            where: {
                status: 99,
                tb_adm_kabkot: {
                    tb_adm_provinsi: {
                        id_zona_waktu: Id,
                    },
                },
            },
            orderBy: { id_adm_kabkot: 'asc' }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }

        })

        return {
            success :true,
            data :data
        }
    }
    public async get_pendidikan_by_prov({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_pendidikan.findMany({
            where: {
                status: 99,
                id_adm_kabkot: {
                    gte: parseInt(`${Id}00`),
                    lt: parseInt(`${Id}99`)
                },
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_pendidikan_by_kabkot({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_pendidikan.findMany({
            where: {
                status: 99,
                id_adm_kabkot: Id,
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_rumah_sakit_by_zonawaktu({params ,response}:HttpContext) {
        const Id = parseInt(params.id_zona_waktu, 10) || 1;

        // Ambil daftar provinsi berdasarkan zona waktu
        // const provinsiIds = (await prisma.tb_adm_provinsi.findMany({
        //     where: { id_zona_waktu: Id },
        //     orderBy: { id_adm_provinsi: 'asc' }
        // })).map(prov => prov.id_adm_provinsi);

        // Ambil data hotel berdasarkan provinsi yang ditemukan
        const datas = await prisma.tb_rumah_sakit.findMany({
            select:{
                id_adm_kabkot:true,
                nama:true,
                lon:true,
                lat:true
            },
            where: {
                status: 99,
                tb_adm_kabkot: {
                    tb_adm_provinsi: {
                        id_zona_waktu: Id,
                    },
                },
            },
            orderBy: { id_adm_kabkot: 'asc' }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }

        })

        return {
            success :true,
            data :data
        }
    }
    public async get_rumah_sakit_by_prov({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_rumah_sakit.findMany({
            where: {
                status: 99,
                id_adm_kabkot: {
                    gte: parseInt(`${Id}00`),
                    lt: parseInt(`${Id}99`)
                },
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_rumah_sakit_by_kabkot({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_rumah_sakit.findMany({
            where: {
                status: 99,
                id_adm_kabkot: Id,
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_rtrw_by_prov({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_adm_provinsi_layer_spasial.findMany({
            where: {
                status: 1,
                id_adm_provinsi:Id
            },
            orderBy:{
                id_adm_provinsi:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                nama : item.nama_layer,
                url : item.url_service,
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_rtrw_by_kabkot({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_adm_kabkot_layer_spasial.findMany({
            where: {
                status: 1,
                id_adm_kabkot: Id,
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                nama : item.nama_layer,
                url : item.url_service,
            }
        })

        return {
            success :true,
            data :data
        }
    }

    public async get_rdtr_by_prov({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_adm_provinsi_layer_spasial.findMany({
            where: {
                status: 2,
                id_adm_provinsi:Id
            },
            orderBy:{
                id_adm_provinsi:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                nama : item.nama_layer,
                url : item.url_service,
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_rdtr_by_kabkot({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_adm_kabkot_layer_spasial.findMany({
            where: {
                status: 2,
                id_adm_kabkot: Id,
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                nama : item.nama_layer,
                url : item.url_service,
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_komoditi_by_prov({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_sektor_daerah.findMany({
            where: {
                status: 99,
                id_adm_provinsi:Id
            },
            orderBy:{
                id_adm_provinsi:'asc'
            },
            include:{
                tb_adm_provinsi:{
                    include:{
                        tb_komoditi_provinsi:true
                    }
                },
                tb_sub_sektor_daerah:{
                    include:{
                        tb_sub_sektor_nasional:{
                            include:{
                                sektor:{
                                    include:{
                                        kategori_sektor:true
                                    }
                                },
                                sub_sektor_ref:true
                            }
                        },
                        tb_komoditi_daerah:{
                            include:{
                                tb_komoditi_nasional_ref:true
                            }
                        }
                    }
                }
            }
        });
        const data = datas.flatMap((item) => {
            return item.tb_sub_sektor_daerah.flatMap((i) => {
                return i.tb_komoditi_daerah.map((i2) => {
                    return {
                        id_komoditi: i2.id_komoditi,
                        nama: `${i.tb_sub_sektor_nasional.sektor.kategori_sektor?.nama.toUpperCase()} - ${i.tb_sub_sektor_nasional.sub_sektor_ref.nama} - ${i2.tb_komoditi_nasional_ref.nama}`,
                        lon:item.tb_adm_provinsi?.tb_komoditi_provinsi[0]?.lon,
                        lat:item.tb_adm_provinsi?.tb_komoditi_provinsi[0]?.lat,
                    };
                });
            });
        });


        return {
            success :true,
            data :data
        }
    }
    public async get_komoditi_by_kabkot({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_sektor_daerah.findMany({
            where: {
                // status: 99,
                id_adm_kabkot:Id
            },
            orderBy:{
                id_adm_kabkot:'asc'
            },
            include:{
                tb_adm_kabkot:{
                    include:{
                        tb_komoditi_kabkot:{
                            take:1
                        }
                    }
                },
                tb_sub_sektor_daerah:{
                    include:{
                        tb_sub_sektor_nasional:{
                            include:{
                                sektor:{
                                    include:{
                                        kategori_sektor:true
                                    }
                                },
                                sub_sektor_ref:true
                            }
                        },
                        tb_komoditi_daerah:{
                            include:{
                                tb_komoditi_nasional_ref:true
                            }
                        }
                    }
                }
            }
        });
        const data = datas.flatMap((item) => {
            return item.tb_sub_sektor_daerah.flatMap((i) => {
                return i.tb_komoditi_daerah.map((i2) => {
                    return {
                        id_komoditi: i2.id_komoditi,
                        nama: `${i.tb_sub_sektor_nasional.sektor.kategori_sektor?.nama.toUpperCase()} - ${i.tb_sub_sektor_nasional.sub_sektor_ref.nama} - ${i2.tb_komoditi_nasional_ref.nama}`,
                    };
                });
            });
        });


        return {
            success :true,
            data :data
        }
    }
    public async get_utilitas_by_prov({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }
        const utilitass = await prisma.tb_utilitas_provinsi.findFirst({
                where:{
                    id_adm_provinsi:Id,
                    status:99
                },
                orderBy:{
                    tahun:'desc'
                }

            })
        let data = {}
        if (utilitass) {
            data ={
                produksi_air_bersih : ` ${numberFormat(utilitass.produksi_air_bersih)} m<sup>3</sup>`,
                daya_terpasang : `${numberFormat(utilitass.daya_terpasang)} KW`,
                jumlah_bts : `${numberFormat(utilitass.jumlah_bts)}`
            }
        }

        return {
            success :true,
            data :data
        }
    }
    public async get_utilitas_by_kabkot({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const utilitass = await prisma.tb_utilitas_kabkot.findFirst({
                where:{
                    id_adm_kabkot:Id,
                    status:99
                },
                orderBy:{
                    tahun:'desc'
                }

            })
        let data = {}
        if (utilitass) {
            data ={
                produksi_air_bersih : ` ${numberFormat(utilitass.produksi_air_bersih)} m<sup>3</sup>`,
                daya_terpasang : `${numberFormat(utilitass.daya_terpasang)} KW`,
                jumlah_bts : `${numberFormat(utilitass.jumlah_bts)} Menara`
            }
        }

        return {
            success :true,
            data :data
        }
    }


    public filterSektorDaerah(data) {
        // Langkah 1: Kelompokkan data berdasarkan nama
        const groupedByNama = {};

        for (const item of data) {
          const nama = item.nama;
          if (!groupedByNama[nama]) {
            groupedByNama[nama] = [];
          }
          groupedByNama[nama].push(item);
        }

        // Langkah 2: Proses setiap grup untuk membuat nama unik dengan tahun terbaru
        const result = [];

        for (const nama in groupedByNama) {
          const items = groupedByNama[nama];

          // Hitung jumlah item tanpa tahun
          const itemsWithoutTahun = items.filter(
            (item) => item.tahun === undefined || item.tahun === null
          );

          // Jika ada lebih dari 1 item tanpa tahun, hapus semua item tersebut
          if (itemsWithoutTahun.length > 1) {
            continue; // Lewati grup ini
          }

          // Jika hanya ada 1 item dalam grup, masukkan langsung ke hasil
          if (items.length === 1) {
            result.push(items[0]);
            continue;
          }

          // Filter item yang memiliki tahun (abaikan yang undefined/null)
          const itemsWithTahun = items.filter(
            (item) => item.tahun !== undefined && item.tahun !== null
          );

          // Jika tidak ada item dengan tahun, masukkan item tanpa tahun (jika hanya 1)
          if (itemsWithTahun.length === 0) {
            if (itemsWithoutTahun.length === 1) {
              result.push(itemsWithoutTahun[0]);
            }
            continue;
          }

          // Urutkan berdasarkan tahun (terbaru ke terlama) dan ambil yang pertama
          const sortedItems = itemsWithTahun.sort((a, b) => b.tahun - a.tahun);
          result.push(sortedItems[0]);
        }

        return result;
      }

      public async get_listrik_by_prov({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_listrik.findMany({
            where: {
                status: 99,
                id_adm_kabkot: {
                    gte: parseInt(`${Id}00`),
                    lt: parseInt(`${Id}99`)
                },
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_listrik_by_kabkot({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_listrik.findMany({
            where: {
                status: 99,
                id_adm_kabkot: Id,
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }

    public async get_gas_by_prov({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_gas.findMany({
            where: {
                status: 99,
                id_adm_kabkot: {
                    gte: parseInt(`${Id}00`),
                    lt: parseInt(`${Id}99`)
                },
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_gas_by_kabkot({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_gas.findMany({
            where: {
                status: 99,
                id_adm_kabkot: Id,
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }

    public async get_air_by_prov({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_air.findMany({
            where: {
                status: 99,
                id_adm_kabkot: {
                    gte: parseInt(`${Id}00`),
                    lt: parseInt(`${Id}99`)
                },
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
    public async get_air_by_kabkot({params ,response}:HttpContext) {
        const Id = parseInt(params.id_adm_kabkot, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const datas = await prisma.tb_air.findMany({
            where: {
                status: 99,
                id_adm_kabkot: Id,
            },
            orderBy:{
                id_adm_kabkot:'asc'
            }
        });
        const data = datas.map((item) => {
            return {
                id_adm_provinsi : parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                nama : item.nama,
                lon:item.lon,
                lat:item.lat
            }
        })

        return {
            success :true,
            data :data
        }
    }
}




  // Jalankan filter