import { createMenuValidator, updateMenuValidator } from '#validators/menu';
import type { HttpContext } from '@adonisjs/core/http'
import { get_paginate } from '../helpers/global_helper.js';
import { createRoleMenuValidator } from '#validators/role_menu';
import { idText } from 'typescript';
import prisma from '../lib/prisma.js'
export default class RoleMenusController {
  /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.role_menu_access.findMany({
      include:{
        role:true,
        menu:true
      }
    });
    return {
        success : true,
        data : {data}
    }
  }

  /**
     * @get_paginate
     * @summary Get a list of Role with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          {
            role:{
              role_name: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
            },
            menu:{
              menu_name: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
            },
          },
        ],
      };
    }
    // Get total record count with the search condition applied
    const totalRecords = await prisma.role_menu_access.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.role_menu_access.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      include:{
        menu:true,
        role:true,
      }

    });
    const data = datas.map(({...rest }) => {
        return {
            id : rest.id,
            role_name: rest.role.role_name,
            menu_name: rest.menu.menu_name
        };
    });


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };
  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
     * @store
     * @paramQuery role_id - Masukan Id role - @type(string) @required
     * @paramQuery menu_id - Masukan Id Menu - @type(string)
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async store({ request, response ,auth}: HttpContext) {

      let inpData = request.input('data')
      if (typeof inpData === 'string') {
            inpData = JSON.parse(inpData);
        }
      // const data = await request.validateUsing(createRoleMenuValidator)
        try {
          if (Array.isArray(inpData) && inpData.length > 0) {
            // Ambil role_id unik dari inpData
            const roleIds = [...new Set(inpData.map((item) => parseInt(item.role_id)))];

            // Hapus semua data dengan role_id yang ada di inpData
            await prisma.role_menu_access.deleteMany({
              where: {
                role_id: { in: roleIds },
              },
            });

            // Masukkan data baru
            const insert = await Promise.all(
              inpData.map((item) => {
                const dataPost = {
                  role_id: parseInt(item.role_id),
                  menu_id: parseInt(item.menu_id),
                };
                return prisma.role_menu_access.create({ data: dataPost });
              })
            );

            response.status(201).json({ success: true, data: insert });
          } else{

            response.status(500).json({ success: false, message: 'Data Kosong' })
          }
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
    const Id = parseInt(id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ success:false,error: 'Invalid ID provided' })
    }
    const data = await prisma.role_menu_access.findUnique({
      where: {
        id: Id,
      },
    })

    return {
        success : true,
        data : {data}
    }
  }


  /**
   * Edit individual record
   */
  async edit({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
        const data = await prisma.role_menu_access.findUnique({
          where: {
            id: Id,
          },
        })

        return {
            success : true,
            data
        }
  }

   /**
     * @update
     * @paramQuery role_id - Masukan Id role - @type(string) @required
     * @paramQuery menu_id - Masukan Id Menu - @type(string)
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
  async update({ params, request ,response}: HttpContext) {
    const dataPost = await request.validateUsing(createRoleMenuValidator)
    const { role_id,menu_id} = dataPost

    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }
    const check_uniq = await prisma.role_menu_access.findFirst({
            where:
            {
              role_id,
              menu_id,
              id:{
                not: Id
              }
            }
          })

          if(check_uniq){
            return response.status(500).json({ success: false, message: 'role_id and menu_id is unique'})

          }
    try {
      const updatePost = await prisma.role_menu_access.update({
        where: {
          id: Id,
        },
        data: {
          role_id,
          menu_id
        },
      })
      return response.status(200).json({ success: true, data: updatePost })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data' })
    }
  }
  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.role_menu_access.delete({
        where: {
          id: Id,
        },
      })

      return response.status(200).send({ success: true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }
}