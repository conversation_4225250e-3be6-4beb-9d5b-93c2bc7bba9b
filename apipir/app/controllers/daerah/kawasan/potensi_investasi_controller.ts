import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';

import prisma from '../../../lib/prisma.js';
import uploadFile from '../../../helpers/file_uploader.js';



export default class KawasanPotensiInvestasiController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_kawasan_industri_peluang: { column: 'id_kawasan_industri_peluang', alias: 'id_kawasan_industri_peluang', type: 'int' },
                        id_kawasan_industri: { column: 'id_kawasan_industri', alias: 'id_kawasan_industri', type: 'int' },
                        judul: { column: 'judul', alias: 'judul', type: 'string' },
                        nilai_investasi: { column: 'nilai_investasi', alias: 'nilai_investasi', type: 'float' },
                        deskripsi: { column: 'deskripsi', alias: 'deskripsi', type: 'string' },
                        luas_lahan: { column: 'luas_lahan', alias: 'luas_lahan', type: 'float' }
                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        orderBy: {
                                id_kawasan_industri_peluang: 'desc', 
                        },
                        // include: {
                        //         tb_adm_provinsi: {
                        //                 select: {
                        //                         nama: true,
                        //                         jumlah_penduduk: true,
                        //                 }
                        //         },
                        // }
                }


                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                options['where'] = modelWhereAnd((await this.schemaData()).columns, queryParams);

                try {
                        const data = await prisma.tb_kawasan_industri_peluang.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_kawasan_industri_peluang.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                // const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        // status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }

        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {
                        include: {
                                tb_kawasan_industri_peluang_file:true,
                                tb_kawasan_industri_peluang_tr:true
                                // tb_sektor_nasional_ref: true,
                                // tb_adm_kabkot: true,
                                // tb_adm_provinsi: true
                        }
                };

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_kawasan_industri_peluang': parseInt(params.id)
                }

                const data = await prisma.tb_kawasan_industri_peluang.findFirst(options);

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }

        public async createOrUpdate({ request, response, params }: HttpContext) {
                const paramID = parseInt(params.id);
                const model = "kawasan_potensi_investasi";

                try {
                        const result = await prisma.$transaction(async (prisma) => {
                                let fileFotoName: string = "";
                                let fileDokumenName: string = "";
                                let resID: number | undefined;

                                const fileFoto = request.file("file_foto");
                                const fileDokumen = request.file("file_dokumen");

                                if (!paramID) {
                                        console.log("Sync tb_kawasan_industri_peluang id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_kawasan_industri_peluang"', 'id_kawasan_industri_peluang')),
                                                (SELECT (MAX("id_kawasan_industri_peluang") + 1) FROM "tb_kawasan_industri_peluang"),
                                                false) FROM "tb_kawasan_industri_peluang";
                                         `);

                                        const res = await prisma.tb_kawasan_industri_peluang.create({
                                                data: {
                                                        id_kawasan_industri: parseInt(request.input("id_kawasan_industri")) || 0,
                                                        judul: request.input("judul") || "",
                                                        nilai_investasi: parseFloat(request.input("nilai_investasi")) || 0,
                                                        deskripsi: request.input("deskripsi") || "",
                                                        luas_lahan: parseFloat(request.input("luas_lahan")) || 0,
                                                }
                                        });

                                        resID = res.id_kawasan_industri

                                        console.log("Sync tb_kawasan_industri_peluang_tr id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_kawasan_industri_peluang_tr"', 'id_kawasan_industri_peluang_tr')),
                                                (SELECT (MAX("id_kawasan_industri_peluang_tr") + 1) FROM "tb_kawasan_industri_peluang_tr"),
                                                false) FROM "tb_kawasan_industri_peluang_tr";
                                         `);


                                        const data_kawasan_industri_peluang_tr: any = {
                                                id_kawasan_industri_peluang: res.id_kawasan_industri_peluang,
                                                kd_bahasa: "en",
                                                judul: request.input("tr_judul") || "",
                                                deskripsi: request.input("tr_deskripsi") || "",
                                        }

                                        const res_data_kawasan_industri_peluang_tr = await prisma.tb_kawasan_industri_peluang_tr.create({ data: data_kawasan_industri_peluang_tr });


                                        let res_data_kawasan_industri_peluang_file_foto: any
                                        let res_data_kawasan_industri_peluang_file_dokumen: any
                                        if (fileFoto) {
                                                console.log("Sync tb_kawasan_industri_peluang_file id sequence",
                                                        await prisma.$executeRaw`
                                                        SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_kawasan_industri_peluang_file"', 'id_kawasan_industri_peluang_file')),
                                                        (SELECT (MAX("id_kawasan_industri_peluang_file") + 1) FROM "tb_kawasan_industri_peluang_file"),
                                                        false) FROM "tb_kawasan_industri_peluang_file";
                                                 `);

                                                await uploadFile(fileFoto, model, resID);
                                                fileFotoName = fileFoto.fileName ?? ""; // Set fileName if file exists

                                                const data_kawasan_industri_peluang_file: any = {
                                                        id_kawasan_industri_peluang: res.id_kawasan_industri_peluang,
                                                        tipe: parseInt(request.input("tipe_foto")) || 0,
                                                        jenis: 1,
                                                        nama: fileFotoName || "",
                                                        judul: request.input("judul_foto") || "",
                                                        keterangan: res.deskripsi || "",
                                                }
                                                res_data_kawasan_industri_peluang_file_foto = await prisma.tb_kawasan_industri_peluang_file.create({ data: data_kawasan_industri_peluang_file });
                                        }

                                        if (fileDokumen) {
                                                console.log("Sync tb_kawasan_industri_peluang_file id sequence",
                                                        await prisma.$executeRaw`
                                                        SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_kawasan_industri_peluang_file"', 'id_kawasan_industri_peluang_file')),
                                                        (SELECT (MAX("id_kawasan_industri_peluang_file") + 1) FROM "tb_kawasan_industri_peluang_file"),
                                                        false) FROM "tb_kawasan_industri_peluang_file";
                                                 `);

                                                await uploadFile(fileDokumen, model, resID);
                                                fileDokumenName = fileDokumen.fileName ?? ""; // Set fileName if file exists

                                                const data_kawasan_industri_peluang_file: any = {
                                                        id_kawasan_industri_peluang: res.id_kawasan_industri_peluang,
                                                        tipe: parseInt(request.input("tipe_dokumen")) || 0,
                                                        jenis: 2,
                                                        nama: fileDokumenName || "",
                                                        judul: request.input("judul_dokumen") || "",
                                                        keterangan: res.deskripsi || "",
                                                }
                                                res_data_kawasan_industri_peluang_file_dokumen = await prisma.tb_kawasan_industri_peluang_file.create({ data: data_kawasan_industri_peluang_file });
                                        }




                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: res,
                                                data_kawasan_industri_peluang_tr: res_data_kawasan_industri_peluang_tr,
                                                data_kawasan_industri_peluang_file_foto: res_data_kawasan_industri_peluang_file_foto,
                                                data_kawasan_industri_peluang_file_dokumen: res_data_kawasan_industri_peluang_file_dokumen,
                                        };


                                } else {
                                        // console.log(paramID);
                                        const update = await prisma.tb_kawasan_industri_peluang.update({
                                                where: { id_kawasan_industri_peluang: paramID },
                                                data: {
                                                        id_kawasan_industri: parseInt(request.input("id_kawasan_industri")) || 0,
                                                        judul: request.input("judul") || "",
                                                        nilai_investasi: parseFloat(request.input("nilai_investasi")) || 0,
                                                        deskripsi: request.input("deskripsi") || "",
                                                        luas_lahan: parseFloat(request.input("luas_lahan")) || 0,
                                                }
                                        });

                                        const data_kawasan_industri_peluang_tr: any = {
                                                id_kawasan_industri_peluang: update.id_kawasan_industri_peluang,
                                                kd_bahasa: "en",
                                                judul: request.input("tr_judul") || "",
                                                deskripsi: request.input("tr_deskripsi") || "",
                                        }

                                        const existing_data_kawasan_industri_peluang_tr = await prisma.tb_kawasan_industri_peluang_tr.findFirst({
                                                where: { id_kawasan_industri_peluang: update.id_kawasan_industri_peluang }
                                        });

                                        let res_data_kawasan_industri_peluang_tr: any
                                        if (existing_data_kawasan_industri_peluang_tr) {
                                                res_data_kawasan_industri_peluang_tr = await prisma.tb_kawasan_industri_peluang_tr.update({
                                                        where: { id_kawasan_industri_peluang_tr: existing_data_kawasan_industri_peluang_tr.id_kawasan_industri_peluang_tr },
                                                        data: data_kawasan_industri_peluang_tr,
                                                });
                                        }


                                        let res_data_kawasan_industri_peluang_file_foto: any
                                        let res_data_kawasan_industri_peluang_file_dokumen: any
                                        if (fileFoto) {
                                                const data_kawasan_industri_peluang_file: any = {
                                                        id_kawasan_industri_peluang: update.id_kawasan_industri_peluang,
                                                        tipe: parseInt(request.input("tipe_foto")) || 0,
                                                        jenis: 1,
                                                        nama: fileFotoName || "",
                                                        judul: request.input("judul_foto") || "",
                                                        keterangan: request.input("deskripsi") || "",
                                                }

                                                const existing_data_kawasan_industri_peluang_file = await prisma.tb_kawasan_industri_peluang_file.findFirst({
                                                        where: { id_kawasan_industri_peluang: update.id_kawasan_industri_peluang }
                                                });


                                                if (existing_data_kawasan_industri_peluang_file) {
                                                        res_data_kawasan_industri_peluang_file_foto = await prisma.tb_kawasan_industri_peluang_file.update({
                                                                where: { id_kawasan_industri_peluang_file: existing_data_kawasan_industri_peluang_file.id_kawasan_industri_peluang },
                                                                data: data_kawasan_industri_peluang_file,
                                                        });
                                                }
                                        }

                                        if (fileDokumen) {
                                                const data_kawasan_industri_peluang_file: any = {
                                                        id_kawasan_industri_peluang: update.id_kawasan_industri_peluang,
                                                        tipe: parseInt(request.input("tipe_dokumen")) || 0,
                                                        jenis: 2,
                                                        nama: fileDokumenName || "",
                                                        judul: request.input("judul_dokumen") || "",
                                                        keterangan: request.input("deskripsi") || "",
                                                }

                                                const existing_data_kawasan_industri_peluang_file = await prisma.tb_kawasan_industri_peluang_file.findFirst({
                                                        where: { id_kawasan_industri_peluang: update.id_kawasan_industri_peluang }
                                                });


                                                if (existing_data_kawasan_industri_peluang_file) {
                                                        res_data_kawasan_industri_peluang_file_dokumen = await prisma.tb_kawasan_industri_peluang_file.update({
                                                                where: { id_kawasan_industri_peluang_file: existing_data_kawasan_industri_peluang_file.id_kawasan_industri_peluang },
                                                                data: data_kawasan_industri_peluang_file,
                                                        });
                                                }
                                        }


                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update,
                                                data_kawasan_industri_peluang_tr: res_data_kawasan_industri_peluang_tr,
                                                data_kawasan_industri_peluang_file_foto: res_data_kawasan_industri_peluang_file_foto,
                                                data_kawasan_industri_peluang_file_dokumen: res_data_kawasan_industri_peluang_file_dokumen,
                                        };
                                }
                        });

                        return response.json(result);
                } catch (error) {
                        console.log(error);
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error,
                        });
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_kawasan_industri_peluang.delete({
                                where: { id_kawasan_industri_peluang: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }
}