import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../helpers/model_helper.js';

import uploadFile from '../../helpers/file_uploader.js';

import prisma from '../../lib/prisma.js'

export default class PeluangInvestasiDaerahController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_peluang_daerah: { column: 'id_peluang_daerah', alias: 'id_peluang_daerah', type: 'int' },
                        id_adm_provinsi: { column: 'id_adm_provinsi', alias: 'id_adm_provinsi', type: 'int' },
                        id_adm_kabkot: { column: 'id_adm_kabkot', alias: 'id_adm_kabkot', type: 'int' },
                        id_sub_sektor_daerah: { column: 'id_sub_sektor_daerah', alias: 'id_sub_sektor_daerah', type: 'int' },
                        status_peluang: { column: 'status_peluang', alias: 'status_peluang', type: 'int' },
                        status: { column: 'status', alias: 'status', type: 'int' },
                        id_bandara: { column: 'id_bandara', alias: 'id_bandara', type: 'int' },
                        jarak_bandara: { column: 'jarak_bandara', alias: 'jarak_bandara', type: 'int' },
                        id_pelabuhan: { column: 'id_pelabuhan', alias: 'id_pelabuhan', type: 'int' },
                        jarak_pelabuhan: { column: 'jarak_pelabuhan', alias: 'jarak_pelabuhan', type: 'int' },
                        jarak_ibukota: { column: 'jarak_ibukota', alias: 'jarak_ibukota', type: 'int' },
                        kode_kbli: { column: 'kode_kbli', alias: 'kode_kbli', type: 'string' },
                        judul: { column: 'judul', alias: 'judul', type: 'string' },
                        lokasi: { column: 'lokasi', alias: 'lokasi', type: 'string' },
                        tahun: { column: 'tahun', alias: 'tahun', type: 'int' },
                        keterangan: { column: 'keterangan', alias: 'keterangan', type: 'string' },
                        aspek_pasar: { column: 'aspek_pasar', alias: 'aspek_pasar', type: 'string' },
                        aspek_teknis: { column: 'aspek_teknis', alias: 'aspek_teknis', type: 'string' },
                        lon: { column: 'lon', alias: 'lon', type: 'float' },
                        lat: { column: 'lat', alias: 'lat', type: 'float' },
                        zoom_peta_default: { column: 'zoom_peta_default', alias: 'zoom_peta_default', type: 'int' },
                        is_ikn: { column: 'is_ikn', alias: 'is_ikn', type: 'boolean' },
                        project_status_enum: { column: 'project_status_enum', alias: 'project_status_enum', type: 'int' }
                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                if (request.input("is_get_table_admin") || request.input("is_get_table_admin") == "true") {
                        const page = parseInt(request.input("page", "1")) || 1;
                        const perPage = parseInt(request.input("per_page", "10")) || 10;
                        const offset = (page - 1) * perPage;

                        const searchQuery = request.input("q")?.trim();
                        let Wilayah = ""; // Awalnya valid
                        const params = request.qs();
                        let IdAdmProvinsi = params['id_adm_provinsi'];
                        let IdAdmKabkot = params['id_adm_kabkot'];

                        let totalCount = 0;
                        let data = [];

                        try {
                                const params = request.qs();
                                let order = params.order
                                const by = ['asc','desc'].includes(params.by) ? params.by : 'asc'
                                let orderBy=`ORDER BY tpd.id_peluang_daerah DESC`
                                const paramList = ['status','tahun']
                                if (order != undefined && paramList.includes(order)) {
                                        if (order == 'tahun') {
                                                orderBy = `order by tpd.tahun ${by}`
                                        }else if (order == 'status') {
                                                orderBy = `order by tpd.status ${by}`
                                        }
                                }
                                if (searchQuery) {
                                        if (IdAdmKabkot) {
                                                Wilayah += ` AND tak.id_adm_kabkot = ${IdAdmKabkot}`;
                                        }
                                        if (IdAdmProvinsi){
                                                Wilayah += ` AND (tap.id_adm_provinsi = ${IdAdmProvinsi} OR LEFT(tak.id_adm_kabkot::TEXT, 2) = LEFT(${IdAdmProvinsi}::TEXT, 2))`;
                                        }

                                        // Query untuk menghitung total data dengan filter `q`
                                        const totalCountResult = await prisma.$queryRawUnsafe(`
                                                SELECT COUNT(*) as count
                                                FROM tb_peluang_daerah tpd
                                                LEFT JOIN tb_adm_provinsi tap ON tpd.id_adm_provinsi = tap.id_adm_provinsi
                                                LEFT JOIN tb_adm_kabkot tak ON tpd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap2 ON tak.id_adm_provinsi = tap2.id_adm_provinsi
                                                LEFT JOIN tb_peluang_daerah_kelayakan tpdk ON tpdk.id_peluang_daerah = tpd.id_peluang_daerah
                                                left join tb_sub_sektor_daerah tssd on tpd.id_sub_sektor_daerah = tssd.id_sub_sektor_daerah
                                                left join tb_sub_sektor_nasional tssn on tssd.id_sub_sektor_nasional = tssn.id_sub_sektor_nasional
                                                left join tb_sektor_nasional_ref tsnr on tsnr.id_sektor = tssn.id_sektor_nasional
                                                WHERE
                                                        LOWER(CASE
                                                                WHEN tap.nama IS NULL THEN tap2.nama
                                                                ELSE tap.nama
                                                        END) ILIKE $1 OR
                                                        LOWER(tak.nama) ILIKE $1 OR
                                                        LOWER(CASE
                                                                WHEN tpd.status_peluang = 1 THEN 'Prioritas'
                                                                ELSE 'Pengembangan'
                                                        END) ILIKE $1 OR
                                                        tpdk.initial_invesment::TEXT ILIKE $1 OR
                                                        tpdk.irr::TEXT ILIKE $1 OR
                                                        tpdk.npv::TEXT ILIKE $1 OR
                                                        tpdk.pp::TEXT ILIKE $1 OR
                                                        tpd.id_peluang_daerah::TEXT ILIKE $1 OR
                                                        tpd.judul::TEXT ILIKE $1 OR
                                                        tpd.tahun::TEXT ILIKE $1 OR
                                                        tsnr.nama::TEXT ILIKE $1
                                                        ${Wilayah}
                                        `, `%${searchQuery}%`);

                                        totalCount = Number(totalCountResult[0]?.count || 0);

                                        // Query data dengan filter `q`, limit, dan offset
                                        data = await prisma.$queryRawUnsafe(`
                                                SELECT
                                                        tsnr.nama AS nama_sektor_peluang,
                                                        CASE
                                                        WHEN tap.nama IS NULL THEN tap2.nama
                                                        ELSE tap.nama
                                                        END AS nama_provinsi,
                                                        tak.nama AS nama_kabkot,
                                                        CASE
                                                        WHEN tpd.status_peluang = 1 THEN 'Prioritas'
                                                        ELSE 'Pengembangan'
                                                        END AS prioritas,
                                                        tpdk.initial_invesment,
                                                        tpdk.irr,
                                                        tpdk.npv,
                                                        tpdk.pp,
                                                        tpd.*
                                                FROM tb_peluang_daerah tpd
                                                LEFT JOIN tb_adm_provinsi tap ON tpd.id_adm_provinsi = tap.id_adm_provinsi
                                                LEFT JOIN tb_adm_kabkot tak ON tpd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap2 ON tak.id_adm_provinsi = tap2.id_adm_provinsi
                                                LEFT JOIN tb_peluang_daerah_kelayakan tpdk ON tpdk.id_peluang_daerah = tpd.id_peluang_daerah
                                                left join tb_sub_sektor_daerah tssd on tpd.id_sub_sektor_daerah = tssd.id_sub_sektor_daerah
                                                left join tb_sub_sektor_nasional tssn on tssd.id_sub_sektor_nasional = tssn.id_sub_sektor_nasional
                                                left join tb_sektor_nasional_ref tsnr on tsnr.id_sektor = tssn.id_sektor_nasional
                                                WHERE
                                                        LOWER(CASE
                                                                WHEN tap.nama IS NULL THEN tap2.nama
                                                                ELSE tap.nama
                                                        END) ILIKE $1 OR
                                                        LOWER(tak.nama) ILIKE $1 OR
                                                        LOWER(CASE
                                                                WHEN tpd.status_peluang = 1 THEN 'Prioritas'
                                                                ELSE 'Pengembangan'
                                                        END) ILIKE $1 OR
                                                        tpdk.initial_invesment::TEXT ILIKE $1 OR
                                                        tpdk.irr::TEXT ILIKE $1 OR
                                                        tpdk.npv::TEXT ILIKE $1 OR
                                                        tpdk.pp::TEXT ILIKE $1 OR
                                                        tpd.id_peluang_daerah::TEXT ILIKE $1 OR
                                                        tpd.judul::TEXT ILIKE $1 OR
                                                        tpd.tahun::TEXT ILIKE $1 OR
                                                        tsnr.nama::TEXT ILIKE $1
                                                        ${Wilayah}
                                                        ${orderBy}
                                                LIMIT $2 OFFSET $3
                                        `, `%${searchQuery}%`, perPage, offset);
                                } else {
                                        if (IdAdmKabkot) {
                                                Wilayah += ` where tak.id_adm_kabkot = ${IdAdmKabkot}`;
                                        }
                                        if (IdAdmProvinsi){
                                                Wilayah += ` where (tap.id_adm_provinsi = ${IdAdmProvinsi} OR LEFT(tak.id_adm_kabkot::TEXT, 2) = LEFT(${IdAdmProvinsi}::TEXT, 2))`;
                                        }
                                        console.log(`
                                                        SELECT COUNT(*) as count
                                                        FROM tb_peluang_daerah tpd
                                                        LEFT JOIN tb_adm_provinsi tap ON tpd.id_adm_provinsi = tap.id_adm_provinsi
                                                        LEFT JOIN tb_adm_kabkot tak ON tpd.id_adm_kabkot = tak.id_adm_kabkot
                                                        LEFT JOIN tb_adm_provinsi tap2 ON tak.id_adm_provinsi = tap2.id_adm_provinsi
                                                        LEFT JOIN tb_peluang_daerah_kelayakan tpdk ON tpdk.id_peluang_daerah = tpd.id_peluang_daerah
                                                        ${Wilayah}
                                                `);
                                        // Query untuk menghitung total data tanpa filter `q`
                                        const totalCountResult =  await prisma.$queryRawUnsafe(`
                                                SELECT COUNT(*) as count
                                                FROM tb_peluang_daerah tpd
                                                LEFT JOIN tb_adm_provinsi tap ON tpd.id_adm_provinsi = tap.id_adm_provinsi
                                                LEFT JOIN tb_adm_kabkot tak ON tpd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap2 ON tak.id_adm_provinsi = tap2.id_adm_provinsi
                                                LEFT JOIN tb_peluang_daerah_kelayakan tpdk ON tpdk.id_peluang_daerah = tpd.id_peluang_daerah
                                                ${Wilayah}
                                        `);

                                        totalCount = Number(totalCountResult[0]?.count || 0);

                                        // Query data tanpa filter `q`, limit, dan offset
                                        data = await prisma.$queryRawUnsafe(`
                                                SELECT
                                                        tsnr.nama AS nama_sektor_peluang,
                                                        CASE
                                                        WHEN tap.nama IS NULL THEN tap2.nama
                                                        ELSE tap.nama
                                                        END AS nama_provinsi,
                                                        tak.nama AS nama_kabkot,
                                                        CASE
                                                        WHEN tpd.status_peluang = 1 THEN 'Prioritas'
                                                        ELSE 'Pengembangan'
                                                        END AS prioritas,
                                                        tpdk.initial_invesment,
                                                        tpdk.irr,
                                                        tpdk.npv,
                                                        tpdk.pp,
                                                        tpd.*
                                                FROM tb_peluang_daerah tpd
                                                LEFT JOIN tb_adm_provinsi tap ON tpd.id_adm_provinsi = tap.id_adm_provinsi
                                                LEFT JOIN tb_adm_kabkot tak ON tpd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap2 ON tak.id_adm_provinsi = tap2.id_adm_provinsi
                                                LEFT JOIN tb_peluang_daerah_kelayakan tpdk ON tpdk.id_peluang_daerah = tpd.id_peluang_daerah
                                                left join tb_sub_sektor_daerah tssd on tpd.id_sub_sektor_daerah = tssd.id_sub_sektor_daerah
                                                left join tb_sub_sektor_nasional tssn on tssd.id_sub_sektor_nasional = tssn.id_sub_sektor_nasional
                                                left join tb_sektor_nasional_ref tsnr on tsnr.id_sektor = tssn.id_sektor_nasional
                                                ${Wilayah}
                                                ${orderBy}
                                                LIMIT ${perPage} OFFSET ${offset}
                                        `);
                                }

                                // Format response
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: perPage,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / perPage),
                                        },
                                        data: data,
                                });
                        } catch (error) {
                                console.error(error);
                                return response.status(500).json({
                                        success: false,
                                        message: "An error occurred while fetching data",
                                        error: error.message,
                                });
                        }
                }

                let isAll = false;

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                }


                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                options['where'] = modelWhereAnd((await this.schemaData()).columns, queryParams);

                try {
                        const data = await prisma.tb_peluang_daerah.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_peluang_daerah.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }

        public async getById({ request, params, response }: HttpContext) {
                if (request.input("is_kelayakan") || request.input("is_kelayakan") == "true") {
                        let kelayakan = await prisma.tb_peluang_daerah_kelayakan.findFirst({
                                where: {
                                        id_peluang_daerah: parseInt(params.id)
                                }
                        });

                        let kelayakanDetail = await prisma.tb_peluang_daerah_kelayakan_detail.findMany({
                                where: {
                                        id_peluang_daerah: parseInt(params.id)
                                }
                        });

                        const resultKelayakanDetail : any = [];

                        // Iterasi data berdasarkan tahun
                        kelayakanDetail.forEach(item => {
                            // Cari tahun di dalam resultKelayakanDetail
                            let yearObj = resultKelayakanDetail[item.tahun];
                            if (!yearObj) {
                                // Jika tidak ada, buat objek baru
                                yearObj = {
                                    pendapatan: 0,
                                    biaya_operasional: 0,
                                    ebitda: 0,
                                    arus_kas_bersih: 0,
                                    akumulasi_arus_kas: 0,
                                    nilai_residu: 0
                                };
                                resultKelayakanDetail[item.tahun] = yearObj;
                            }

                            // Map nilai berdasarkan jenis
                            switch (item.jenis) {
                                case 1:
                                    yearObj.pendapatan = item.nilai;
                                    break;
                                case 2:
                                    yearObj.biaya_operasional = item.nilai;
                                    break;
                                case 3:
                                    yearObj.ebitda = item.nilai;
                                    break;
                                case 4:
                                    yearObj.arus_kas_bersih = item.nilai;
                                    break;
                                case 5:
                                    yearObj.akumulasi_arus_kas = item.nilai;
                                    break;
                                case 6:
                                    yearObj.nilai_residu = item.nilai;
                                    break;
                            }
                        });

                        // Filter untuk menghilangkan elemen undefined
                        const finalResult = resultKelayakanDetail.filter(item => item !== undefined);

                        let kelayakanFile = await prisma.tb_peluang_daerah_file.findMany({
                                where: {
                                        id_peluang_daerah: parseInt(params.id),
                                        tipe: 3,
                                        jenis: 1
                                }
                        });

                        kelayakanFile = kelayakanFile.map((item) => {
                                return {
                                        ...item, // Spread item untuk mempertahankan properti yang ada
                                        // path: `${process.env.APP_URL}/uploads/peluang_investasi_daerah/${item.jenis}/${item.nama}`,
                                        path: `${process.env.APP_URL}/uploads/peluang_investasi_daerah/${item.id_peluang_daerah}/${item.nama}`,
                                };
                        });

                        return response.status(200).json({
                                success: true,
                                message: 'Success Retrieve Data',
                                data: {
                                        'kelayakan': kelayakan,
                                        'kelayakan_detail': finalResult,
                                        'kelayakan_file': kelayakanFile
                                }
                        });
                }

                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {
                         include: {
                                tb_adm_kabkot: true,
                                tb_adm_provinsi: true,
                                tb_sub_sektor_daerah: {
                                        include: {
                                                tb_sub_sektor_nasional: {
                                                        include: {
                                                                sektor: true,
                                                                sub_sektor_ref: true
                                                        }
                                                },
                                        }
                                },
                                tb_peluang_daerah_file: {
                                        include: {
                                                tb_peluang_daerah_file_tr: true
                                        }
                                },
                                tb_peluang_daerah_kelayakan: true,
                                tb_peluang_daerah_komoditi: {
                                        include: {
                                                tb_komoditi_daerah: true
                                        }
                                },
                                tb_peluang_daerah_tr: true,
                                tb_peluang_daerah_layer_spasial:true,
                                tb_peluang_daerah_kontak:true
                        }
                };

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_peluang_daerah': parseInt(params.id)
                }

                const data = await prisma.tb_peluang_daerah.findFirst(options);
                data['nama_bandara'] = null;
                if (data['id_bandara']) {
                        let bandara = await prisma.tb_bandara.findFirst({
                                where: {
                                        id_bandara: data['id_bandara']
                                }
                        });

                        data['nama_bandara'] = bandara?.nama;
                }

                data['nama_pelabuhan'] = null;
                if (data['id_pelabuhan']) {
                        let pelabuhan = await prisma.tb_pelabuhan.findFirst({
                                where: {
                                        id_pelabuhan: data['id_pelabuhan']
                                }
                        });

                        data['nama_pelabuhan'] = pelabuhan?.nama;
                }

                let sniids = await prisma.tb_peluang_daerah_insentif.findMany({
                        where: {
                                id_peluang_daerah: parseInt(params.id)
                        }
                });

                data['sektor_nasional_insentif_ids'] = sniids;

                let peluangDaerahKelayakan = await prisma.tb_peluang_daerah_kelayakan.findFirst({
                        where: {
                                id_peluang_daerah: parseInt(params.id)
                        }
                });

                data['peluang_daerah_kelayakan'] = peluangDaerahKelayakan;

                data['tb_peluang_daerah_file'] = data['tb_peluang_daerah_file'].map((item) => {
                        return {
                                ...item, // Spread item untuk mempertahankan properti yang ada
                                // path: `${process.env.APP_URL}/uploads/peluang_daerah/${item.tipe}${item.jenis}/${item.nama}`,
                                path: `${process.env.APP_URL}/uploads/peluang_daerah/${item.id_peluang_daerah}/${item.nama}`,
                        };
                });

                data['tb_peluang_daerah_file'] = data['tb_peluang_daerah_file'].map((val) => {
                        val['tb_peluang_daerah_file_tr'] = val['tb_peluang_daerah_file_tr'].map((item) => {
                                return {
                                        ...item, // Spread item untuk mempertahankan properti yang ada
                                        // path: `${process.env.APP_URL}/uploads/peluang_daerah/${val.tipe}${val.jenis}/${item.nama}`,
                                        path: `${process.env.APP_URL}/uploads/peluang_daerah/${item.id_peluang_daerah}/${item.nama}`,
                                };
                        });

                        return {
                                ...val, // Spread item untuk mempertahankan properti yang ada
                        };
                });

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }

        public async createOrUpdate({ request, params, response }: HttpContext) {
                let reqBody: any = request.body()
                try {
                        const result = await prisma.$transaction(async (prisma) => {
                                const paramID = parseInt(reqBody.id_peluang_daerah || params.id);
                                const model = "peluang_investasi_daerah";

                                // console.log(paramID);
                                if (!paramID) {
                                        console.log(
                                                "Sync tb_peluang_daerah id sequence",
                                                await prisma.$executeRaw`
                                                    SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_peluang_daerah"', 'id_peluang_daerah')),
                                                        (SELECT (MAX("id_peluang_daerah") + 1) FROM "tb_peluang_daerah"),
                                                        false) FROM "tb_peluang_daerah";
                                                `
                                            );

                                        const res = await prisma.tb_peluang_daerah.create({
                                        data: {
                                                id_adm_provinsi: request.input("id_adm_provinsi") ? parseInt(request.input("id_adm_provinsi")) : null,
                                                id_adm_kabkot: request.input("id_adm_kabkot") ? parseInt(request.input("id_adm_kabkot")) : null,
                                                id_sub_sektor_daerah: request.input("id_sub_sektor_daerah") ? parseInt(request.input("id_sub_sektor_daerah")) : 274,
                                                tahun: request.input("tahun") ? parseInt(request.input("tahun")) : 0,
                                                judul: request.input("judul") || "",
                                                status_peluang: request.input("status_peluang") ? parseInt(request.input("status_peluang")) : 0,
                                                status: request.input("status") ? parseInt(request.input("status")) : 0,
                                                id_bandara: request.input("id_bandara") ? parseInt(request.input("id_bandara")) : undefined,
                                                jarak_bandara: request.input("jarak_bandara") ? parseInt(request.input("jarak_bandara")) : undefined,
                                                id_pelabuhan: request.input("id_pelabuhan") ? parseInt(request.input("id_pelabuhan")) : undefined,
                                                jarak_pelabuhan: request.input("jarak_pelabuhan") ? parseInt(request.input("jarak_pelabuhan")) : undefined,
                                                jarak_ibukota: request.input("jarak_ibukota") ? parseInt(request.input("jarak_ibukota")) : undefined,
                                                kode_kbli: request.input("kode_kbli") || undefined,
                                                lokasi: request.input("lokasi") || undefined,
                                                keterangan: request.input("keterangan") || undefined,
                                                aspek_pasar: request.input("aspek_pasar") || undefined,
                                                aspek_teknis: request.input("aspek_teknis") || undefined,
                                                lon: request.input("lon") ? parseFloat(request.input("lon")) : undefined,
                                                lat: request.input("lat") ? parseFloat(request.input("lat")) : undefined,
                                                zoom_peta_default: request.input("zoom_peta_default") ? parseInt(request.input("zoom_peta_default")) : undefined,
                                                is_ikn: request.input("is_ikn") ? Boolean(request.input("is_ikn")) : undefined,
                                                project_status_enum: request.input("project_status_enum") ? parseInt(request.input("project_status_enum")) : 2,
                                        },
                                        });

                                        if (reqBody['peluang_kontak']) {
                                                for (let kontak of reqBody['peluang_kontak']) {
                                                        const kontakInsert =await prisma.tb_peluang_daerah_kontak.create({
                                                                data:{
                                                                        id_peluang_daerah:res.id_peluang_daerah,
                                                                        id_peluang_kontak:parseInt(kontak)
                                                                }
                                                        })
                                                        console.log(`kontak ${kontak}`);
                                                }
                                        }

                                        // Proses `sektor_nasional_insentif_ids`
                                        if (reqBody.sektor_nasional_insentif_ids) {
                                                if (Array.isArray(reqBody.sektor_nasional_insentif_ids)) {
                                                        for (const sektorNasionalId of reqBody.sektor_nasional_insentif_ids) {
                                                                await prisma.tb_peluang_daerah_insentif.create({
                                                                        data: {
                                                                                id_peluang_daerah: res.id_peluang_daerah,
                                                                                id_sektor_nasional_insentif: parseInt(sektorNasionalId),
                                                                                id_sektor_daerah_insentif: null, // Set null untuk sektor_daerah_insentif
                                                                        },
                                                                });
                                                        }
                                                } else if (typeof reqBody.sektor_nasional_insentif_ids === "string") {
                                                        reqBody.sektor_nasional_insentif_ids = reqBody.sektor_nasional_insentif_ids.split(",").map(Number);
                                                        for (const sektorNasionalId of reqBody.sektor_nasional_insentif_ids) {
                                                                await prisma.tb_peluang_daerah_insentif.create({
                                                                        data: {
                                                                                id_peluang_daerah: res.id_peluang_daerah,
                                                                                id_sektor_nasional_insentif: parseInt(sektorNasionalId),
                                                                                id_sektor_daerah_insentif: null, // Set null untuk sektor_daerah_insentif
                                                                        },
                                                                });
                                                        }
                                                } else {
                                                        console.log("Tipe data tidak diketahui:", typeof reqBody.sektor_nasional_insentif_ids);
                                                }
                                        }

                                        // Proses `sektor_daerah_insentif_ids`
                                        if (reqBody.sektor_daerah_insentif_ids) {
                                                if (Array.isArray(reqBody.sektor_daerah_insentif_ids)) {
                                                        for (const sektorDaerahId of reqBody.sektor_daerah_insentif_ids) {
                                                                await prisma.tb_peluang_daerah_insentif.create({
                                                                        data: {
                                                                                id_peluang_daerah: res.id_peluang_daerah,
                                                                                id_sektor_nasional_insentif: null,
                                                                                id_sektor_daerah_insentif: parseInt(sektorDaerahId),
                                                                        },
                                                                });
                                                        }
                                                } else if (typeof reqBody.sektor_daerah_insentif_ids === "string") {
                                                        reqBody.sektor_daerah_insentif_ids = reqBody.sektor_daerah_insentif_ids.split(",").map(Number);
                                                        for (const sektorDaerahId of reqBody.sektor_daerah_insentif_ids) {
                                                                await prisma.tb_peluang_daerah_insentif.create({
                                                                        data: {
                                                                                id_peluang_daerah: res.id_peluang_daerah,
                                                                                id_sektor_nasional_insentif: null,
                                                                                id_sektor_daerah_insentif: parseInt(sektorDaerahId),
                                                                        },
                                                                });
                                                        }
                                                } else {
                                                        console.log("Tipe data tidak diketahui:", typeof reqBody.sektor_daerah_insentif_ids);
                                                }
                                        }

                                        console.log(
                                                "Sync tb_peluang_daerah_komoditi id sequence",
                                                await prisma.$executeRaw`
                                                    SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_peluang_daerah_komoditi"', 'id_peluang_daerah_komoditi')),
                                                        (SELECT (MAX("id_peluang_daerah_komoditi") + 1) FROM "tb_peluang_daerah_komoditi"),
                                                        false) FROM "tb_peluang_daerah_komoditi";
                                                `
                                            );
                                        if (reqBody.komoditi_ids) {
                                                if (Array.isArray(reqBody.komoditi_ids)) {
                                                        for (const komoditiId of reqBody.komoditi_ids) {
                                                                await prisma.tb_peluang_daerah_komoditi.create({
                                                                        data: {
                                                                                id_peluang_daerah: res.id_peluang_daerah,
                                                                                id_komoditi_daerah: parseInt(komoditiId),
                                                                                jenis: '-',
                                                                                manfaat: '-'
                                                                        },
                                                                });
                                                        }
                                                } else if (typeof reqBody.komoditi_ids === "string") {
                                                        reqBody.komoditi_ids = reqBody.komoditi_ids.split(",").map(Number);
                                                        for (const komoditiId of reqBody.komoditi_ids) {
                                                                await prisma.tb_peluang_daerah_komoditi.create({
                                                                        data: {
                                                                                id_peluang_daerah: res.id_peluang_daerah,
                                                                                id_komoditi_daerah: parseInt(komoditiId),
                                                                                jenis: '-',
                                                                                manfaat: '-'
                                                                        },
                                                                });
                                                        }
                                                } else {
                                                        console.log("Tipe data tidak diketahui:", typeof reqBody.komoditi_ids);
                                                }
                                        }

                                        let n = 0;

                                        // Lampiran -> tipe = 3, jenis = 1
                                        if (reqBody['lampiran']) {
                                                for (let lampiranParams of reqBody['lampiran']) {
                                                        let lampiranFile = request.file(`lampiran[${n}].file`);
                                                        if (lampiranFile) {
                                                                let filename: string = "";

                                                                if (lampiranFile) {
                                                                        let uploadFileToServer = await uploadFile(lampiranFile, model, res.id_peluang_daerah);
                                                                        let filenameFromServer = '';
                                                                        if (uploadFileToServer) {
                                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                                        }
                                                                        filename = filenameFromServer; // Set fileName if file exists
                                                                }
                                                                await prisma.tb_peluang_daerah_file.create({
                                                                        data: {
                                                                                id_peluang_daerah: res.id_peluang_daerah,
                                                                                tipe: 3,
                                                                                jenis: 1,
                                                                                nama: filename,
                                                                                keterangan: lampiranParams['keterangan'] ?? '-',
                                                                                judul: lampiranParams['judul'] ?? '-'
                                                                        },
                                                                });
                                                        }
                                                        n++;
                                                }
                                        }

                                        // Images -> tipe = 1, jenis = 3
                                        n = 0;
                                        if (reqBody['images']) {
                                                for (let imagesParams of reqBody['images']) {
                                                        let imagesFile = request.file(`images[${n}].file`);
                                                        if (imagesFile) {
                                                                let filename: string = "";

                                                                if (imagesFile) {
                                                                        let uploadFileToServer = await uploadFile(imagesFile, model, res.id_peluang_daerah);
                                                                        let filenameFromServer = '';
                                                                        if (uploadFileToServer) {
                                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                                        }
                                                                        filename = filenameFromServer; // Set fileName if file exists
                                                                }
                                                                await prisma.tb_peluang_daerah_file.create({
                                                                        data: {
                                                                                id_peluang_daerah: res.id_peluang_daerah,
                                                                                tipe: 1,
                                                                                jenis: 3,
                                                                                nama: filename,
                                                                                keterangan: imagesParams['keterangan'] ?? '-',
                                                                                judul: imagesParams['judul'] ?? '-'
                                                                        },
                                                                });
                                                        }
                                                        n++;
                                                }
                                        }

                                        // Infografis -> tipe = 4, jenis = 4
                                        n = 0;
                                        if (reqBody['infografis']) {
                                                for (let infografisParams of reqBody['infografis']) {
                                                        let infografisFile = request.file(`infografis[${n}].file`);
                                                        let infografisTrFile = request.file(`infografis_tr[${n}].file`);
                                                        let infografisTrJudul = request.input(`infografis_tr[${n}].judul`);
                                                        let infografisTrKeterangan = request.input(`infografis_tr[${n}].keterangan`);

                                                        if (infografisFile) {
                                                                let filename: string = "";
                                                                let filenameTr: string = "";

                                                                if (infografisFile) {
                                                                        let uploadFileToServer = await uploadFile(infografisFile, model, res.id_peluang_daerah);
                                                                        let filenameFromServer = '';
                                                                        if (uploadFileToServer) {
                                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                                        }
                                                                        filename = filenameFromServer; // Set fileName if file exists
                                                                }

                                                                if (infografisTrFile) {
                                                                        let uploadFileToServer = await uploadFile(infografisTrFile, model, 44);
                                                                        let filenameFromServer = '';
                                                                        if (uploadFileToServer) {
                                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                                        }
                                                                        filenameTr = filenameFromServer; // Set fileName if file exists
                                                                }

                                                                let saveFile = await prisma.tb_peluang_daerah_file.create({
                                                                        data: {
                                                                                id_peluang_daerah: res.id_peluang_daerah,
                                                                                tipe: 4,
                                                                                jenis: 4,
                                                                                nama: filename,
                                                                                keterangan: infografisParams['keterangan'] ?? '-',
                                                                                judul: infografisParams['judul'] ?? '-'
                                                                        },
                                                                });

                                                                if (infografisTrFile) {
                                                                        await prisma.tb_peluang_daerah_file_tr.create({
                                                                                data: {
                                                                                        id_peluang_daerah_file: saveFile.id_peluang_daerah_file,
                                                                                        kd_bahasa: 'en',
                                                                                        nama: filenameTr,
                                                                                        judul: infografisTrJudul ?? '-',
                                                                                        keterangan: infografisTrKeterangan ?? '-',
                                                                                },
                                                                        });
                                                                }
                                                        }
                                                        n++;
                                                }
                                        }

                                        // Dokumen -> tipe = 3, jenis = 5
                                        n = 0;
                                        if (reqBody['dokumen']) {
                                                for (let dokumenParams of reqBody['dokumen']) {
                                                        let dokumenFile = request.file(`dokumen[${n}].file`);
                                                        if (dokumenFile) {
                                                                let filename: string = "";

                                                                if (dokumenFile) {
                                                                        let uploadFileToServer = await uploadFile(dokumenFile, model, res.id_peluang_daerah);
                                                                        let filenameFromServer = '';
                                                                        if (uploadFileToServer) {
                                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                                        }
                                                                        filename = filenameFromServer; // Set fileName if file exists
                                                                }
                                                                await prisma.tb_peluang_daerah_file.create({
                                                                        data: {
                                                                                id_peluang_daerah: res.id_peluang_daerah,
                                                                                tipe: 3,
                                                                                jenis: 5,
                                                                                nama: filename,
                                                                                keterangan: dokumenParams['keterangan'] ?? '-',
                                                                                judul: dokumenParams['judul'] ?? '-'
                                                                        },
                                                                });
                                                        }
                                                        n++;
                                                }
                                        }

                                        // Video -> tipe = 2, jenis = 6
                                        n = 0;
                                        if (reqBody['video']) {
                                                for (let videoParams of reqBody['video']) {
                                                        let videoFile = request.file(`video[${n}].file`);
                                                        if (videoFile) {
                                                                let filename: string = "";

                                                                if (videoFile) {
                                                                        let uploadFileToServer = await uploadFile(videoFile, model, res.id_peluang_daerah);
                                                                        let filenameFromServer = '';
                                                                        if (uploadFileToServer) {
                                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                                        }
                                                                        filename = filenameFromServer; // Set fileName if file exists
                                                                }
                                                                await prisma.tb_peluang_daerah_file.create({
                                                                        data: {
                                                                                id_peluang_daerah: res.id_peluang_daerah,
                                                                                tipe: 2,
                                                                                jenis: 6,
                                                                                nama: filename,
                                                                                keterangan: videoParams['keterangan'] ?? '-',
                                                                                judul: videoParams['judul'] ?? '-'
                                                                        },
                                                                });
                                                        }
                                                        n++;
                                                }
                                        }

                                        await prisma.tb_peluang_daerah_tr.deleteMany({
                                                where: {
                                                        id_peluang_daerah: res.id_peluang_daerah,
                                                },
                                        });

                                        await prisma.tb_peluang_daerah_tr.create({
                                                data: {
                                                        kd_bahasa: 'en',
                                                        id_peluang_daerah: res.id_peluang_daerah,
                                                        judul: request.input("judul_tr") ?? '-',
                                                        lokasi: request.input("lokasi_tr") ?? '-',
                                                        keterangan: request.input("keterangan_tr") ?? '-',
                                                        aspek_teknis: request.input("aspek_teknis_tr") ?? '-',
                                                },
                                        });

                                        return {
                                                status: 'success',
                                                message: 'Successfully Create Data',
                                                data: res
                                        };
                                } else {
                                        let dataUpdate = {
                                                id_adm_provinsi: request.input("id_adm_provinsi") ? parseInt(request.input("id_adm_provinsi")) : undefined,
                                                id_adm_kabkot: request.input("id_adm_kabkot") ? parseInt(request.input("id_adm_kabkot")) : undefined,
                                                id_sub_sektor_daerah: request.input("id_sub_sektor_daerah") ? parseInt(request.input("id_sub_sektor_daerah")) : 0,
                                                status_peluang: request.input("status_peluang") ? parseInt(request.input("status_peluang")) : 0,
                                                status: request.input("status") ? parseInt(request.input("status")) : 0,
                                                id_bandara: request.input("id_bandara") ? parseInt(request.input("id_bandara")) : undefined,
                                                jarak_bandara: request.input("jarak_bandara") ? parseInt(request.input("jarak_bandara")) : undefined,
                                                id_pelabuhan: request.input("id_pelabuhan") ? parseInt(request.input("id_pelabuhan")) : undefined,
                                                jarak_pelabuhan: request.input("jarak_pelabuhan") ? parseInt(request.input("jarak_pelabuhan")) : undefined,
                                                jarak_ibukota: request.input("jarak_ibukota") ? parseInt(request.input("jarak_ibukota")) : undefined,
                                                kode_kbli: request.input("kode_kbli") || undefined,
                                                judul: request.input("judul") || undefined,
                                                lokasi: request.input("lokasi") || undefined,
                                                tahun: request.input("tahun") ? parseInt(request.input("tahun")) : undefined,
                                                keterangan: request.input("keterangan") || undefined,
                                                aspek_pasar: request.input("aspek_pasar") || undefined,
                                                aspek_teknis: request.input("aspek_teknis") || undefined,
                                                lon: request.input("lon") ? parseFloat(request.input("lon")) : undefined,
                                                lat: request.input("lat") ? parseFloat(request.input("lat")) : undefined,
                                                zoom_peta_default: request.input("zoom_peta_default") ? parseInt(request.input("zoom_peta_default")) : undefined,
                                                is_ikn: request.input("is_ikn") ? Boolean(request.input("is_ikn")) : undefined,
                                                project_status_enum: request.input("project_status_enum") ? parseInt(request.input("project_status_enum")) : 0,
                                        };

                                        if (!dataUpdate['id_sub_sektor_daerah']) {
                                                delete dataUpdate['id_sub_sektor_daerah'];
                                        }

                                        if (!dataUpdate['id_bandara']) {
                                                delete dataUpdate['id_bandara'];
                                        }

                                        if (!dataUpdate['id_pelabuhan']) {
                                                delete dataUpdate['id_pelabuhan'];
                                        }

                                        if (!dataUpdate['kode_kbli'] || dataUpdate['kode_kbli'] == 'undefined') {
                                                delete dataUpdate['kode_kbli'];
                                        }

                                        if (!dataUpdate['lokasi'] || dataUpdate['lokasi'] == 'null') {
                                                delete dataUpdate['lokasi'];
                                        }

                                        if (!dataUpdate['tahun']) {
                                                delete dataUpdate['tahun'];
                                        }

                                        if (!dataUpdate['keterangan']) {
                                                delete dataUpdate['keterangan'];
                                        }

                                        if (!dataUpdate['aspek_pasar']) {
                                                delete dataUpdate['aspek_pasar'];
                                        }

                                        if (!dataUpdate['aspek_teknis']) {
                                                delete dataUpdate['aspek_teknis'];
                                        }

                                        if (!dataUpdate['lon']) {
                                                delete dataUpdate['lon'];
                                        }

                                        if (!dataUpdate['lat']) {
                                                delete dataUpdate['lat'];
                                        }

                                        const update = await prisma.tb_peluang_daerah.update({
                                                where: { id_peluang_daerah: paramID },
                                                data: dataUpdate
                                        });

                                        if (reqBody['peluang_kontak']) {
                                                await prisma.tb_peluang_daerah_kontak.deleteMany({
                                                        where:{
                                                                id_peluang_daerah:update.id_peluang_daerah
                                                        }
                                                })
                                                for (let kontak of reqBody['peluang_kontak']) {
                                                        const kontakInsert =await prisma.tb_peluang_daerah_kontak.create({
                                                                data:{
                                                                        id_peluang_daerah:update.id_peluang_daerah,
                                                                        id_peluang_kontak:parseInt(kontak)
                                                                }
                                                        })
                                                }
                                        }

                                        // Hapus semua data berdasarkan id_peluang_daerah
                                        await prisma.tb_peluang_daerah_insentif.deleteMany({
                                                where: {
                                                        id_peluang_daerah: paramID,
                                                },
                                        });

                                        await prisma.tb_peluang_daerah_komoditi.deleteMany({
                                                where: {
                                                        id_peluang_daerah: paramID,
                                                },
                                        });

                                        if (reqBody.komoditi_ids) {
                                                if (Array.isArray(reqBody.komoditi_ids)) {
                                                        for (const komoditiId of reqBody.komoditi_ids) {
                                                                await prisma.tb_peluang_daerah_komoditi.create({
                                                                        data: {
                                                                                id_peluang_daerah: paramID,
                                                                                id_komoditi_daerah: komoditiId,
                                                                                jenis: '-',
                                                                                manfaat: '-'
                                                                        },
                                                                });
                                                        }
                                                } else if (typeof reqBody.komoditi_ids === "string") {
                                                        reqBody.komoditi_ids = reqBody.komoditi_ids.split(",").map(Number);
                                                        for (const komoditiId of reqBody.komoditi_ids) {
                                                                await prisma.tb_peluang_daerah_komoditi.create({
                                                                        data: {
                                                                                id_peluang_daerah: paramID,
                                                                                id_komoditi_daerah: komoditiId,
                                                                                jenis: '-',
                                                                                manfaat: '-'
                                                                        },
                                                                });
                                                        }
                                                } else {
                                                        console.log("Tipe data tidak diketahui:", typeof reqBody.komoditi_ids);
                                                }
                                        }

                                        // Proses `sektor_nasional_insentif_ids`
                                        if (reqBody.sektor_nasional_insentif_ids) {
                                                if (Array.isArray(reqBody.sektor_nasional_insentif_ids)) {
                                                        for (const sektorNasionalId of reqBody.sektor_nasional_insentif_ids) {
                                                                await prisma.tb_peluang_daerah_insentif.create({
                                                                        data: {
                                                                                id_peluang_daerah: paramID,
                                                                                id_sektor_nasional_insentif: parseInt(sektorNasionalId),
                                                                                id_sektor_daerah_insentif: null, // Set null untuk sektor_daerah_insentif
                                                                        },
                                                                });
                                                        }
                                                } else if (typeof reqBody.sektor_nasional_insentif_ids === "string") {
                                                        reqBody.sektor_nasional_insentif_ids = reqBody.sektor_nasional_insentif_ids.split(",").map(Number);
                                                        for (const sektorNasionalId of reqBody.sektor_nasional_insentif_ids) {
                                                                await prisma.tb_peluang_daerah_insentif.create({
                                                                        data: {
                                                                                id_peluang_daerah: paramID,
                                                                                id_sektor_nasional_insentif: parseInt(sektorNasionalId),
                                                                                id_sektor_daerah_insentif: null, // Set null untuk sektor_daerah_insentif
                                                                        },
                                                                });
                                                        }
                                                } else {
                                                        console.log("Tipe data tidak diketahui:", typeof reqBody.sektor_nasional_insentif_ids);
                                                }
                                        }

                                        // Proses `sektor_daerah_insentif_ids`
                                        if (reqBody.sektor_daerah_insentif_ids) {
                                                if (Array.isArray(reqBody.sektor_daerah_insentif_ids)) {
                                                        for (const sektorDaerahId of reqBody.sektor_daerah_insentif_ids) {
                                                                await prisma.tb_peluang_daerah_insentif.create({
                                                                        data: {
                                                                                id_peluang_daerah: paramID,
                                                                                id_sektor_nasional_insentif: null,
                                                                                id_sektor_daerah_insentif: parseInt(sektorDaerahId),
                                                                        },
                                                                });
                                                        }
                                                } else if (typeof reqBody.sektor_daerah_insentif_ids === "string") {
                                                        reqBody.sektor_daerah_insentif_ids = reqBody.sektor_daerah_insentif_ids.split(",").map(Number);
                                                        for (const sektorDaerahId of reqBody.sektor_daerah_insentif_ids) {
                                                                await prisma.tb_peluang_daerah_insentif.create({
                                                                        data: {
                                                                                id_peluang_daerah: paramID,
                                                                                id_sektor_nasional_insentif: null,
                                                                                id_sektor_daerah_insentif: parseInt(sektorDaerahId),
                                                                        },
                                                                });
                                                        }
                                                } else {
                                                        console.log("Tipe data tidak diketahui:", typeof reqBody.sektor_daerah_insentif_ids);
                                                }
                                        }

                                        await prisma.tb_peluang_daerah_file.deleteMany({
                                                where: {
                                                        id_peluang_daerah: paramID,
                                                },
                                        });
                                        let n = 0;

                                        // Lampiran -> tipe = 3, jenis = 1
                                        if (reqBody['lampiran']) {
                                                for (let lampiranParams of reqBody['lampiran']) {
                                                        let lampiranFile = request.file(`lampiran[${n}].file`);
                                                        if (lampiranFile) {
                                                                let filename: string = "";

                                                                if (lampiranFile) {
                                                                        let uploadFileToServer = await uploadFile(lampiranFile, model, paramID);
                                                                        let filenameFromServer = '';
                                                                        if (uploadFileToServer) {
                                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                                        }
                                                                        filename = filenameFromServer; // Set fileName if file exists
                                                                }
                                                                await prisma.tb_peluang_daerah_file.create({
                                                                        data: {
                                                                                id_peluang_daerah: paramID,
                                                                                tipe: 3,
                                                                                jenis: 1,
                                                                                nama: filename,
                                                                                keterangan: lampiranParams['keterangan'] ?? '-',
                                                                                judul: lampiranParams['judul'] ?? '-'
                                                                        },
                                                                });
                                                        }
                                                        n++;
                                                }
                                        }

                                        // Images -> tipe = 1, jenis = 3
                                        n = 0;
                                        if (reqBody['images']) {
                                                for (let imagesParams of reqBody['images']) {
                                                        let imagesFile = request.file(`images[${n}].file`);
                                                        if (imagesFile) {
                                                                let filename: string = "";

                                                                if (imagesFile) {
                                                                        let uploadFileToServer = await uploadFile(imagesFile, model, paramID);
                                                                        let filenameFromServer = '';
                                                                        if (uploadFileToServer) {
                                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                                        }
                                                                        filename = filenameFromServer; // Set fileName if file exists
                                                                }
                                                                await prisma.tb_peluang_daerah_file.create({
                                                                        data: {
                                                                                id_peluang_daerah: paramID,
                                                                                tipe: 1,
                                                                                jenis: 3,
                                                                                nama: filename,
                                                                                keterangan: imagesParams['keterangan'] ?? '-',
                                                                                judul: imagesParams['judul'] ?? '-'
                                                                        },
                                                                });
                                                        }
                                                        n++;
                                                }
                                        }

                                        // Infografis -> tipe = 4, jenis = 4
                                        n = 0;
                                        if (reqBody['infografis']) {
                                                for (let infografisParams of reqBody['infografis']) {
                                                        let infografisFile = request.file(`infografis[${n}].file`);
                                                        if (infografisFile) {
                                                                let filename: string = "";

                                                                if (infografisFile) {
                                                                        let uploadFileToServer = await uploadFile(infografisFile, model, paramID);
                                                                        let filenameFromServer = '';
                                                                        if (uploadFileToServer) {
                                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                                        }
                                                                        filename = filenameFromServer; // Set fileName if file exists
                                                                }
                                                                await prisma.tb_peluang_daerah_file.create({
                                                                        data: {
                                                                                id_peluang_daerah: paramID,
                                                                                tipe: 4,
                                                                                jenis: 4,
                                                                                nama: filename,
                                                                                keterangan: infografisParams['keterangan'] ?? '-',
                                                                                judul: infografisParams['judul'] ?? '-'
                                                                        },
                                                                });
                                                        }
                                                        n++;
                                                }
                                        }

                                        // Dokumen -> tipe = 3, jenis = 5
                                        n = 0;
                                        if (reqBody['dokumen']) {
                                                for (let dokumenParams of reqBody['dokumen']) {
                                                        let dokumenFile = request.file(`dokumen[${n}].file`);
                                                        if (dokumenFile) {
                                                                let filename: string = "";

                                                                if (dokumenFile) {
                                                                        let uploadFileToServer = await uploadFile(dokumenFile, model, paramID);
                                                                        let filenameFromServer = '';
                                                                        if (uploadFileToServer) {
                                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                                        }
                                                                        filename = filenameFromServer; // Set fileName if file exists
                                                                }
                                                                await prisma.tb_peluang_daerah_file.create({
                                                                        data: {
                                                                                id_peluang_daerah: paramID,
                                                                                tipe: 3,
                                                                                jenis: 5,
                                                                                nama: filename,
                                                                                keterangan: dokumenParams['keterangan'] ?? '-',
                                                                                judul: dokumenParams['judul'] ?? '-'
                                                                        },
                                                                });
                                                        }
                                                        n++;
                                                }
                                        }

                                        // Video -> tipe = 2, jenis = 6
                                        n = 0;
                                        if (reqBody['video']) {
                                                for (let videoParams of reqBody['video']) {
                                                        let videoFile = request.file(`video[${n}].file`);
                                                        if (videoFile) {
                                                                let filename: string = "";

                                                                if (videoFile) {
                                                                        let uploadFileToServer = await uploadFile(videoFile, model, paramID);
                                                                        let filenameFromServer = '';
                                                                        if (uploadFileToServer) {
                                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                                        }
                                                                        filename = filenameFromServer; // Set fileName if file exists
                                                                }
                                                                await prisma.tb_peluang_daerah_file.create({
                                                                        data: {
                                                                                id_peluang_daerah: paramID,
                                                                                tipe: 2,
                                                                                jenis: 6,
                                                                                nama: filename,
                                                                                keterangan: videoParams['keterangan'] ?? '-',
                                                                                judul: videoParams['judul'] ?? '-'
                                                                        },
                                                                });
                                                        }
                                                        n++;
                                                }
                                        }

                                        await prisma.tb_peluang_daerah_tr.deleteMany({
                                                where: {
                                                        id_peluang_daerah: paramID,
                                                },
                                        });

                                        await prisma.tb_peluang_daerah_tr.create({
                                                data: {
                                                        kd_bahasa: 'en',
                                                        id_peluang_daerah: paramID,
                                                        judul: request.input("judul_tr") ?? '-',
                                                        lokasi: request.input("lokasi_tr") ?? '-',
                                                        keterangan: request.input("keterangan_tr") ?? '-',
                                                        aspek_teknis: request.input("aspek_teknis_tr") ?? '-',
                                                },
                                        });
                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update
                                        };
                                }
                        });

                        return response.json(result);
                } catch (error) {
                        console.log(error);
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error,
                        });
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_peluang_daerah.delete({
                                where: { id_peluang_daerah: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

                public async toggleStatus({ request, params, response }: HttpContext) {
                        const Id = parseInt(params.id, 10)
                        const statusId = parseInt(request.input('status') ?? 0);

                        if (isNaN(Id)) {
                                return response.status(400).send({ error: 'Invalid ID provided' })
                        }

                        if (isNaN(statusId)) {
                                return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                        }

                        try {
                                const toggleStatus = await prisma.tb_peluang_daerah.update({
                                        where: {
                                                id_peluang_daerah: Id,
                                        },
                                        data: {
                                                status: request.input('status') ?? 0
                                        },
                                })

                                return response.status(200).json({
                                        status: 'success',
                                        message: 'Success Toggle Status',
                                        data: toggleStatus,
                                })
                        } catch (error) {
                                return response.status(500).send({ error: 'Error Toggle Status' })
                        }
                }

                public async hitungKelayakan({ request, response }: HttpContext) {
                        let params = request.qs();

                        let sukuBunga = parseFloat(params['suku_bunga'] ?? 0);
                        let periodeProyek = params['periode_proyek']; //Tahun
                        let pendapatanAwal = parseFloat(params['pendapatan_awal'] ?? 0);
                        let pertumbuhanPendapatan = parseFloat(params['pertumbuhan_pendapatan'] ?? 0);
                        let opexBiayaOperasional = parseFloat(params['opex_biaya_operasional'] ?? 0);
                        let tingkatInflasi = parseFloat(params['tingkat_inflasi'] ?? 0);
                        let investasiAwal = parseFloat(params['investasi_awal'] ?? 0);
                        let persentasePenyusutan = parseFloat(params['persentase_penyusutan'] ?? 0);

                        let data = {};
                        data['details'] = [];

                        for (let i = 0; i <= parseInt(periodeProyek); i++) {
                                // Pastikan setiap elemen data['details'][i] adalah objek
                                if (!data['details'][i]) {
                                        data['details'][i] = {};
                                }

                                // Penyesuaian nilai awal
                                if (i === 0) {
                                        data['details'][i]['pendapatan'] = 0;
                                        data['details'][i]['biaya_operasional'] = 0;
                                        data['details'][i]['ebitda'] = 0;
                                        data['details'][i]['arus_kas_bersih'] = 0 - investasiAwal;
                                        data['details'][i]['akumulasi_arus_kas'] = 0 - investasiAwal;
                                } else if (i === 1) {
                                        data['details'][i]['pendapatan'] = pendapatanAwal;
                                        data['details'][i]['biaya_operasional'] = opexBiayaOperasional;
                                } else {
                                        data['details'][i]['pendapatan'] = pendapatanAwal * Math.pow(1 + (pertumbuhanPendapatan / 100), i - 1);
                                        data['details'][i]['biaya_operasional'] = opexBiayaOperasional * Math.pow(1 + (tingkatInflasi / 100), i - 1);
                                }

                                // Nilai residu
                                if (i === parseInt(periodeProyek)) {
                                        data['details'][i]['nilai_residu'] = investasiAwal * (persentasePenyusutan / 100);
                                } else {
                                        data['details'][i]['nilai_residu'] = 0;
                                }

                                if (i > 0) {
                                        data['details'][i]['ebitda'] = data['details'][i]['pendapatan'] - data['details'][i]['biaya_operasional'];
                                        data['details'][i]['arus_kas_bersih'] = data['details'][i]['ebitda'] + data['details'][i]['nilai_residu'];
                                        // console.log(data['details'][i]['ebitda']);

                                        let akumulasiArusKas = 0;
                                        for (let n = 0; n <= i; n++) {
                                                akumulasiArusKas += data['details'][n]['arus_kas_bersih'];
                                        }

                                        data['details'][i]['akumulasi_arus_kas'] = akumulasiArusKas;
                                }
                        }

                        // ========== TAMBAHKAN FUNGSI INI ==========
            // Ekstrak array arus kas bersih untuk perhitungan
            const cashFlows = data['details'].map((d: any) => d.arus_kas_bersih);

            // 1. Hitung NPV
            const calculateNPV = (flows: number[], rate: number) => {
                return flows.reduce((acc, cash, t) =>
                    acc + (cash / Math.pow(1 + rate, t)), 0);
            };
            data['npv'] = calculateNPV(cashFlows, sukuBunga/100);

            // 2. Hitung IRR dengan Newton-Raphson
            const calculateIRR = (flows: number[], guess = 0.1) => {
                const maxIter = 100;
                const epsilon = 0.0001;
                let x = guess;

                for (let i = 0; i < maxIter; i++) {
                    let npv = 0;
                    let dNpv = 0;

                    flows.forEach((cash, t) => {
                        npv += cash / Math.pow(1 + x, t);
                        dNpv -= t * cash / Math.pow(1 + x, t + 1);
                    });

                    const newX = x - npv/dNpv;
                    if (Math.abs(newX - x) < epsilon) {
                        return (newX * 100).toFixed(2);
                    }
                    x = newX;
                }
                return (x * 100).toFixed(2);
            };
            data['irr'] = calculateIRR(cashFlows);

            // 3. Hitung Payback Period
            const calculatePayback = (flows: number[]) => {
                let cumulative = 0;
                let paybackYear = 0;

                for (let t = 0; t < flows.length; t++) {
                    cumulative += flows[t];
                    if (cumulative >= 0) {
                        const prevCumulative = cumulative - flows[t];
                        const payback = t - 1 + (-prevCumulative / flows[t]);
                        return Math.ceil(payback); // Bulatkan ke atas di sini
                }
                }
                // Jika belum balik modal
                // return '> ' + (flows.length - 1).toString();
                return 0
            };
            data['payback_period'] = calculatePayback(cashFlows);


                        return response.json(data);
                }
}