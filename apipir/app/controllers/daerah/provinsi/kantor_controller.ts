import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../../../lib/prisma.js'
import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';
import uploadFile from '../../../helpers/file_uploader.js';



export default class ProvinsiKantorController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_adm_provinsi_kantor: { column: ' id_adm_provinsi_kantor', alias: ' id_adm_provinsi_kantor', type: 'int' },
                        jenis: { column: 'jenis', alias: 'jenis', type: 'string' },
                        id_adm_provinsi: { column: 'id_adm_provinsi', alias: ' id_adm_provinsi', type: 'int' },
                        nama_pic: { column: 'nama_pic', alias: 'nama_pic', type: 'string' },
                        file_foto_pic: { column: 'file_foto_pic', alias: 'file_foto_pic', type: 'string' },
                        nama: { column: 'nama', alias: 'nama', type: 'string' },
                        alamat: { column: ' alamat', alias: ' alamat', type: 'string' },
                        no_telp: { column: ' no_telp', alias: ' no_telp', type: 'string' },
                        no_fax: { column: 'no_fax', alias: 'no_fax', type: 'string' },
                        url_web: { column: ' url_web', alias: ' url_web', type: 'string' },
                        status: { column: ' status', alias: ' status', type: 'int' },
                        lon: { column: 'lon', alias: 'lon', type: 'int' },
                        lat: { column: ' lat', alias: 'lat', type: 'int' },
                        created_by: { column: ' created_by', alias: 'created_by', type: 'string' },
                        created_date: { column: 'created_date', alias: 'created_date', type: 'string' },
                        updated_by: { column: 'updated_by', alias: 'updated_by', type: 'string' },
                        updated_date: { column: ' updated_date', alias: ' updated_date', type: 'string' },
                        'tb_adm_provinsi.nama': { column: 'tb_adm_provinsi.nama', alias: 'tb_adm_provinsi.nama', type: 'string' },

                }

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;
                let q = '';

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        orderBy?: { [key: string]: string };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                if (request.input('q')) {
                        q = request.input('q');
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let whereDefault = {}
                
                if (q) {
                        whereDefault = {
                                OR: [
                                    {
                                        tb_adm_provinsi: {
                                            nama: {
                                                contains: q,
                                                mode: 'insensitive', // Mengabaikan besar kecil huruf
                                            },
                                        },
                                    },
                                    {
                                        jenis: {
                                                contains: q,
                                                mode: 'insensitive'
                                        }
                                    },
                                    {
                                        nama: {
                                                contains: q,
                                                mode: 'insensitive'
                                        }
                                    },
                                    {
                                        alamat: {
                                                contains: q,
                                                mode: 'insensitive'
                                        }
                                    },
                                    {
                                        nama_pic: {
                                                contains: q,
                                                mode: 'insensitive'
                                        }
                                    }
                                ],
                            };
                }

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        include: {
                                tb_adm_provinsi: {
                                        select: {
                                                nama: true,
                                                jumlah_penduduk: true,
                                        }
                                },
                        },
                        where: whereDefault,
                }

                let order = queryParams.order 
                const by = ['asc','desc'].includes(queryParams.by) ? queryParams.by : 'asc'
                const paramList = ['status']
                if (order != undefined && paramList.includes(order)) {
                        options.orderBy = {[order]:by}
                }

                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                let where = modelWhereAnd((await this.schemaData()).columns, queryParams);

                if (Object.keys(where).length !== 0) {
                        options['where'] = {
                            AND: [
                                whereDefault, // Kondisi pencarian `OR`
                                where,        // Kondisi tambahan dari `modelWhereAnd`
                            ],
                        };
                }

                try {
                        const data = await prisma.tb_adm_provinsi_kantor.findMany(options);



                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        } else if (options['include'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_adm_provinsi_kantor.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                                error : error.message
                        });
                }
        }
        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {
                        include: {
                                tb_adm_provinsi: {
                                        select: {
                                                nama: true,
                                                jumlah_penduduk: true,
                                        }
                                },
                        }
                };

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_adm_provinsi_kantor': parseInt(params.id)
                }

                let data = await prisma.tb_adm_provinsi_kantor.findFirst(options);
                const path = `${process.env.APP_URL}/uploads/kantor/${data?.id_adm_provinsi_kantor}/${data?.file_foto_pic}`;
                data['path'] = path;

                return response.status(200).json({
                        success: true,
                        data: data,
                        path: path
                });
        }

        public async createOrUpdate({ request, response, params }: HttpContext) {
                let reqBody: any = request.all();
                const model = "daerah_provinsi_kantor";
                const paramID = parseInt(params.id)


                try {
                        const result = await prisma.$transaction(async (prisma) => {
                                let fileName1: string = "";
                                // let filePath: string = "";
                                let resID: number | undefined;

                                const file1 = request.file("file_foto_pic");
                                
                              

                                const currentTimestamp = new Date();
                                const localTime = currentTimestamp.toLocaleString("en-US", {
                                        timeZone: "Asia/Jakarta", // Replace with your local timezone
                                });


                                if (!paramID) {
                                        // console.log("create")
                                        console.log("Sync tb_adm_provinsi_kantor id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_adm_provinsi_kantor"', 'id_adm_provinsi_kantor')),
                                                        (SELECT (MAX("id_adm_provinsi_kantor") + 1) FROM "tb_adm_provinsi_kantor"),
                                                        false) FROM "tb_adm_provinsi_kantor";
                                                `);

                                        const res = await prisma.tb_adm_provinsi_kantor.create({
                                                data: {
                                                        jenis: request.input("jenis") || "",
                                                        id_adm_provinsi: request.input("id_adm_provinsi") ? parseInt(request.input("id_adm_provinsi")) : 0,
                                                        nama_pic: "-",
                                                        file_foto_pic: fileName1 || "",
                                                        nama: request.input("nama") || "",
                                                        alamat: request.input("alamat") || "",
                                                        no_telp: request.input("no_telp") || "",
                                                        no_fax: request.input("no_fax") || "",
                                                        url_web: request.input("url_web") || "",
                                                        status: request.input("status") ? parseInt(request.input("status")) : 0,
                                                        lon: request.input("lon") ? parseFloat(request.input("lon")) : 0,
                                                        lat: request.input("lat") ? parseFloat(request.input("lat")) : 0,
                                                        created_by: request.input("created_by") ? parseInt(request.input("created_by")) : 0,
                                                        created_date: currentTimestamp,
                                                }
                                        })

                                        resID = res.id_adm_provinsi_kantor;

                                        if (file1) {
                                                let uploadFileToServer = await uploadFile(file1, model, resID);
                                                
                                                let filenameFromServer = '';
                                                if (uploadFileToServer) {
                                                        filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                }
                                                await prisma.tb_adm_provinsi_kantor.update({
                                                        where:{
                                                                id_adm_provinsi_kantor:resID
                                                        },
                                                        data:{
                                                                file_foto_pic : filenameFromServer    
                                                        }
                                                })
                                        }


                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: { ...res, created_date: localTime },
                                        }
                                } else {
                                        let old = await prisma.tb_adm_provinsi_kantor.findFirst({
                                                where: {
                                                        'id_adm_provinsi_kantor': parseInt(params.id)                    
                                                }
                                        });

                                        if (file1) {
                                                let uploadFileToServer = await uploadFile(file1, model, parseInt(params.id));
                                                let filenameFromServer = '';
                                                if (uploadFileToServer) {
                                                        filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                }
                                                fileName1 = filenameFromServer; // Set fileName if file exists
                                        }

                                        reqBody['id_adm_provinsi'] = parseInt(reqBody['id_adm_provinsi']);
                                        reqBody['lon'] = parseFloat(reqBody['lon']);
                                        reqBody['lat'] = parseFloat(reqBody['lat']);
                                        reqBody['created_by'] = parseInt(reqBody['created_by']);
                                        // Jika `id` ada, lakukan operasi update
                                        const update = await prisma.tb_adm_provinsi_kantor.update({
                                                where: { id_adm_provinsi_kantor: paramID },
                                                data: {
                                                        ...reqBody,
                                                        nama_pic: "-",
                                                        file_foto_pic: fileName1 ? fileName1 : (old['file_foto_pic'] ?? ''),
                                                        updated_date: currentTimestamp,
                                                        status: request.input("status") ? parseInt(request.input("status")) : 0,
                                                },
                                        })
                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: { ...update, updated_date: localTime },
                                        }
                                }
                        })

                        return response.json(result)

                } catch (error) {
                        // Handle error jika ada kesalahan dalam transaksi
                        console.log(error)
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error.message,
                        })
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_adm_provinsi_kantor.delete({
                                where: { id_adm_provinsi_kantor: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response }: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }

                try {
                        const toggleStatus = await prisma.tb_adm_provinsi_kantor.update({
                                where: {
                                        id_adm_provinsi_kantor: Id,
                                },
                                data: {
                                        status: request.input('status') ?? 0
                                },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error Toggle Status' })
                }
        }
}

