import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';

import prisma from '../../../lib/prisma.js';



export default class ProvinsiInvestasiController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_investasi_provinsi: { column: 'id_investasi_provinsi', alias: 'id_investasi_provinsi', type: 'int' },
                        id_adm_provinsi: { column: 'id_adm_provinsi', alias: 'id_adm_provinsi', type: 'int' },
                        id_sumber_data: { column: 'id_sumber_data', alias: 'id_sumber_data', type: 'int' },
                        id_sektor: { column: 'id_sektor', alias: 'id_sektor', type: 'int' },
                        id_jenis: { column: 'id_jenis', alias: 'id_jenis', type: 'int' },
                        id_jenis_data: { column: 'id_jenis_data', alias: 'id_jenis_data', type: 'int' },
                        tahun: { column: 'tahun', alias: 'tahun', type: 'int' },
                        jumlah_proyek: { column: 'jumlah_proyek', alias: 'jumlah_proyek', type: 'int' },
                        jumlah_investasi: { column: 'jumlah_investasi', alias: 'jumlah_investasi', type: 'float' },
                        status: { column: 'status', alias: 'status', type: 'int' },
                        'tb_adm_provinsi.nama': { column: 'tb_adm_provinsi.nama', alias: 'tb_adm_provinsi.nama', type: 'string' },
                        'tb_investasi_jenis.nama': { column: 'tb_investasi_jenis.nama', alias: 'tb_investasi_jenis.nama', type: 'string' },
                        'tb_investasi_sektor.nama': { column: 'tb_investasi_sektor.nama', alias: 'tb_investasi_sektor.nama', type: 'string' },
                        'tb_investasi_jenis_data.nama': { column: 'tb_investasi_jenis_data.nama', alias: 'tb_investasi_jenis_data.nama', type: 'string' },

                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;
                let q = '';

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        orderBy?: { [key: string]: string };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                if (request.input('q')) {
                        q = request.input('q');
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let whereDefault = {}
                
                if (q) {
                        whereDefault = {
                                OR: [
                                        {
                                                tb_adm_provinsi: {
                                                    nama: {
                                                        contains: q,
                                                        mode: 'insensitive', // Mengabaikan besar kecil huruf
                                                    },
                                                },
                                        },
                                        {
                                                tb_investasi_jenis: {
                                                    nama: {
                                                        contains: q,
                                                        mode: 'insensitive', // Mengabaikan besar kecil huruf
                                                    },
                                                },
                                        },
                                        {
                                                tb_investasi_sektor: {
                                                    nama: {
                                                        contains: q,
                                                        mode: 'insensitive', // Mengabaikan besar kecil huruf
                                                    },
                                                },
                                        },
                                        {
                                                tb_investasi_jenis_data: {
                                                    nama: {
                                                        contains: q,
                                                        mode: 'insensitive', // Mengabaikan besar kecil huruf
                                                    },
                                                },
                                        },
                                        {
                                                tahun: {
                                                        equals: isNaN(q) ? undefined : parseInt(q),
                                                },
                                         },
                                ],
                            };
                }

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        include: {
                                tb_adm_provinsi: {
                                        select: {
                                                id_adm_provinsi: true,
                                                nama: true,
                                                jumlah_penduduk: true,
                                        }
                                },
                                tb_investasi_jenis: true,
                                tb_investasi_sektor: true,
                                tb_investasi_jenis_data: true,
                                tb_sumber_data:{
                                        include:{
                                                tb_sumber_data_judul:true
                                        }
                                }
                        },
                        where: whereDefault,
                };

                let order = queryParams.order 
                const by = ['asc','desc'].includes(queryParams.by) ? queryParams.by : 'asc'
                const paramList = ['status','tahun']
                if (order != undefined && paramList.includes(order)) {
                        options.orderBy = {[order]:by}
                }
                
                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                let where = modelWhereAnd((await this.schemaData()).columns, queryParams);

                if (Object.keys(where).length !== 0) {
                        options['where'] = {
                            AND: [
                                whereDefault, // Kondisi pencarian `OR`
                                where,        // Kondisi tambahan dari `modelWhereAnd`
                            ],
                        };
                }

                try {
                        const data = await prisma.tb_investasi_provinsi.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_investasi_provinsi.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }
        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {
                        include: {
                                tb_adm_provinsi: {
                                        select: {
                                                id_adm_provinsi: true,
                                                nama: true,
                                                jumlah_penduduk: true,
                                        }
                                },
                                tb_investasi_jenis: true,
                                tb_investasi_sektor: true,
                                tb_investasi_jenis_data: true
                        }
                };

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_investasi_provinsi': parseInt(params.id)
                }

                const data = await prisma.tb_investasi_provinsi.findFirst(options);

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }

        public async createOrUpdate({ request, response, params }: HttpContext) {
                let reqBody: any = request.body();
                const paramID = parseInt(params.id)
                try {

                        const result = await prisma.$transaction(async (prisma) => {
                                if (params.id) {
                                        // Jika `id` ada, lakukan operasi update
                                        const update = await prisma.tb_investasi_provinsi.update({
                                                where: { id_investasi_provinsi: paramID },
                                                data: reqBody,
                                        })
                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update,
                                        }
                                } else {
                                        // console.log("create")
                                        // console.log("Sync tb_investasi_provinsi id sequence",
                                        //         await prisma.$executeRaw`
                                        //         SELECT setval((
                                        //                 SELECT PG_GET_SERIAL_SEQUENCE('"tb_investasi_provinsi"', 'id_investasi_provinsi')),
                                        //                 (SELECT (MAX("id_investasi_provinsi") + 1) FROM "tb_investasi_provinsi"),
                                        //                 false) FROM "tb_investasi_provinsi";
                                        //         `);

                                        const save = await prisma.tb_investasi_provinsi.create({ data: reqBody })


                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: save,
                                        }
                                }
                        })

                        return response.json(result)

                } catch (error) {
                        // Handle error jika ada kesalahan dalam transaksi
                        console.log(error)
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error,
                        })
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_investasi_provinsi.delete({
                                where: { id_investasi_provinsi: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response }: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }

                try {
                        const toggleStatus = await prisma.tb_investasi_provinsi.update({
                                where: {
                                        id_investasi_provinsi: Id,
                                },
                                data: {
                                        status: request.input('status') ?? 0
                                },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error Toggle Status' })
                }
        }
}