import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../helpers/model_helper.js';

import prisma from '../../lib/prisma.js';
import uploadFile from '../../helpers/file_uploader.js';



export default class PeluangDaerahKelayakanController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_peluang_daerah_kelayakan: { column: 'id_peluang_daerah_kelayakan', alias: 'id_peluang_daerah_kelayakan', type: 'int' },
                        id_peluang_daerah: {column: 'id_peluang_daerah', alias: 'id_peluang_daerah', type: 'int'},
                        pct_cost_capital: {column: 'pct_cost_capital', alias: 'pct_cost_capital', type: 'int'},
                        year_period: {column: 'year_period', alias: 'year_period', type: 'int'},
                        base_revenue: {column: 'base_revenue', alias: 'base_revenue', type: 'int'},
                        pct_revenue_growth: {column: 'pct_revenue_growth', alias: 'pct_revenue_growth', type: 'int'},
                        base_opex: {column: 'base_opex', alias: 'base_opex', type: 'int'},
                        pct_inflation: {column: 'pct_inflation', alias: 'pct_inflation', type: 'int'},
                        initial_invesment: {column: 'initial_invesment', alias: 'initial_invesment', type: 'int'},
                        pct_salvage_value: {column: 'pct_salvage_value', alias: 'pct_salvage_value', type: 'int'},
                        irr: {column: 'irr', alias: 'irr', type: 'int'},
                        npv: {column: 'npv', alias: 'npv', type: 'int'},
                        pp: {column: 'pp', alias: 'pp', type: 'int'},
                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                }

                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                options['where'] = modelWhereAnd((await this.schemaData()).columns, queryParams);

                try {
                        const data = await prisma.tb_peluang_daerah_kelayakan.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_peluang_daerah_kelayakan.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: data,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: data,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }

        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {};

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_peluang_daerah': parseInt(params.id)
                }

                const data = await prisma.tb_peluang_daerah_kelayakan.findFirst(options);

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }

        public async createOrUpdate({ request, response }: HttpContext) {
                const reqBody: any = request.body();
            
                const idPeluangDaerah = reqBody['id_peluang_daerah'];
                const yearPeriod = parseInt(reqBody['periode_proyek'] ?? 0);
                const baseRevenue = parseFloat(reqBody['pendapatan_awal'] ?? 0);
                const pctRevenueGrowth = parseFloat(reqBody['pertumbuhan_pendapatan'] ?? 0);
                const baseOpex = parseFloat(reqBody['opex_biaya_operasional'] ?? 0);
                const pctInflation = parseFloat(reqBody['tingkat_inflasi'] ?? 0);
                const initialInvestment = parseFloat(reqBody['investasi_awal'] ?? 0);
                const pctSalvageValue = parseFloat(reqBody['persentase_penyusutan'] ?? 0);
                const irr = parseFloat(reqBody['irr'] ?? 0);
                const npv = parseFloat(reqBody['npv'] ?? 0);
                const paybackPeriod = parseFloat(reqBody['payback_period'] ?? 0);
                const details = reqBody['details'] ?? [];
            
                const jenisMapping = {
                  pendapatan: 1,
                  biaya_operasional: 2,
                  ebitda: 3,
                  nilai_residu: 4,
                  arus_kas_bersih: 5,
                  akumulasi_arus_kas: 6,
                };
            
                try {
                        console.log("Sync tb_peluang_daerah_kelayakan id sequence",
                                await prisma.$executeRaw`
                                SELECT setval((
                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_peluang_daerah_kelayakan"', 'id_peluang_daerah_kelayakan')),
                                (SELECT (MAX("id_peluang_daerah_kelayakan") + 1) FROM "tb_peluang_daerah_kelayakan"),
                                false) FROM "tb_peluang_daerah_kelayakan";
                                `);
                  const result = await prisma.$transaction(async (prisma) => {
                    const kelayakan = await prisma.tb_peluang_daerah_kelayakan.create({
                      data: {
                        id_peluang_daerah: parseInt(idPeluangDaerah),
                        pct_cost_capital: parseInt(reqBody['suku_bunga']),
                        year_period: yearPeriod,
                        base_revenue: baseRevenue,
                        pct_revenue_growth: pctRevenueGrowth,
                        base_opex: baseOpex,
                        pct_inflation: pctInflation,
                        initial_invesment: initialInvestment,
                        pct_salvage_value: pctSalvageValue,
                        irr: irr,
                        npv: npv,
                        pp: paybackPeriod,
                      },
                    });
            
                    const kelayakanId = kelayakan.id_peluang_daerah_kelayakan;
                    
                    const detailEntries = details.flatMap((detail: any, index: number) => [
                      {
                        // id_peluang_daerah_kelayakan: kelayakanId,
                        id_peluang_daerah: idPeluangDaerah,
                        jenis: jenisMapping['pendapatan'],
                        tahun: index,
                        nilai: detail['pendapatan'],
                      },
                      {
                        // id_peluang_daerah_kelayakan: kelayakanId,
                        id_peluang_daerah: idPeluangDaerah,
                        jenis: jenisMapping['biaya_operasional'],
                        tahun: index,
                        nilai: detail['biaya_operasional'],
                      },
                      {
                        // id_peluang_daerah_kelayakan: kelayakanId,
                        id_peluang_daerah: idPeluangDaerah,
                        jenis: jenisMapping['ebitda'],
                        tahun: index,
                        nilai: detail['ebitda'],
                      },
                      {
                        // id_peluang_daerah_kelayakan: kelayakanId,
                        id_peluang_daerah: idPeluangDaerah,
                        jenis: jenisMapping['nilai_residu'],
                        tahun: index,
                        nilai: detail['nilai_residu'],
                      },
                      {
                        // id_peluang_daerah_kelayakan: kelayakanId,
                        id_peluang_daerah: idPeluangDaerah,
                        jenis: jenisMapping['arus_kas_bersih'],
                        tahun: index,
                        nilai: detail['arus_kas_bersih'],
                      },
                      {
                        // id_peluang_daerah_kelayakan: kelayakanId,
                        id_peluang_daerah: idPeluangDaerah,
                        jenis: jenisMapping['akumulasi_arus_kas'],
                        tahun: index,
                        nilai: detail['akumulasi_arus_kas'],
                      },
                    ]);
            
                    console.log("Sync tb_peluang_daerah_kelayakan_detail id sequence",
                        await prisma.$executeRaw`
                        SELECT setval((
                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_peluang_daerah_kelayakan_detail"', 'id_peluang_daerah_kelayakan_detai')),
                        (SELECT (MAX("id_peluang_daerah_kelayakan_detai") + 1) FROM "tb_peluang_daerah_kelayakan_detail"),
                        false) FROM "tb_peluang_daerah_kelayakan_detail";
                        `);

                    await prisma.tb_peluang_daerah_kelayakan_detail.createMany({
                      data: detailEntries,
                    });
            
                    return kelayakan;
                  });
            
                  return response.status(200).json({
                    status: 'success',
                    message: 'Data has been processed successfully',
                    data: result,
                  });
                } catch (error) {
                  console.error(error);
                  return response.status(500).json({
                    status: 'error',
                    message: 'An error occurred while processing data',
                    error: error.message,
                  });
                }
              }

        public async deleteById({ params, response }: HttpContext) {
                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_peluang_daerah_kelayakan.delete({
                                where: { id_peluang_daerah: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }
}