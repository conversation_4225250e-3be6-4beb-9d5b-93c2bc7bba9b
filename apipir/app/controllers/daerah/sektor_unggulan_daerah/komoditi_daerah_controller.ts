import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';

import prisma from '../../../lib/prisma.js';
// import { configUpload, img_to_webp } from '../../../helpers/global_helper.js';
// import vine from '@vinejs/vine';
import uploadFile from '../../../helpers/file_uploader.js';



export default class SUDKomoditiDaerahController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_komoditi_daerah: { column: 'id_komoditi_daerah', alias: 'id_komoditi_daerah', type: 'int' },
                        id_komoditi: { column: 'id_komoditi', alias: 'id_komoditi', type: 'int' },
                        id_sub_sektor_daerah: { column: 'id_sub_sektor_daerah', alias: 'id_sub_sektor_daerah', type: 'int' },
                        deskripsi_singkat: { column: 'deskripsi_singkat', alias: 'deskripsi_singkat', type: 'string' },
                        deskripsi: { column: 'deskripsi', alias: 'deskripsi', type: 'string' },
                        file_icon: { column: 'file_icon', alias: 'file_icon', type: 'string' },
                        sentra_produksi: { column: 'sentra_produksi', alias: 'sentra_produksi', type: 'string' },
                        status: { column: 'status', alias: 'status', type: 'int' }
                };

                const joins = {

                };

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                if (request.input("is_get_table_admin") || request.input("is_get_table_admin") == "true") {
                        const page = parseInt(request.input("page", "1")) || 1;
                        const perPage = parseInt(request.input("per_page", "10")) || 10;
                        const offset = (page - 1) * perPage;

                        const searchQuery = request.input("q")?.trim() || request.input("filter")?.trim(); // Trim untuk menghapus spasi ekstra
                        let totalCount = 0;
                        let data = [];
                        let Wilayah = ''
                        const params = request.qs();

                        try {
                                let IdAdmProvinsi = params['id_adm_provinsi'];
                                let IdAdmKabkot = params['id_adm_kabkot'];
                                let status = params['status'];

                                let order = params.order 
                                const by = ['asc','desc'].includes(params.by) ? params.by : 'asc'
                                let orderBy=`ORDER BY tkd.id_komoditi_daerah DESC`
                                const paramList = ['status','nama_komoditi']
                                if (order != undefined && paramList.includes(order)) {
                                        if (order == 'nama_komoditi') {
                                                orderBy = `order by tknr.nama ${by}`
                                        }else if (order == 'status') {
                                                orderBy = `order by tkd.status ${by}`
                                        }
                                }
                                if (searchQuery) {
                                        if (IdAdmKabkot) {
                                                Wilayah += ` AND tak.id_adm_kabkot = ${IdAdmKabkot}`;
                                        }
                                        if (IdAdmProvinsi){
                                                Wilayah += ` AND (tap.id_adm_provinsi = ${IdAdmProvinsi} 
                                                                OR LEFT(tak.id_adm_kabkot::TEXT, 2) = LEFT(${IdAdmProvinsi}::TEXT, 2))`;
                                        }
                                        if (status != undefined  && !isNaN(status)){
                                                Wilayah += ` AND tsd.status = ${status}` 
                                        }
                                        // Query untuk menghitung total data dengan filter `q`
                                        const totalCountResult = await prisma.$queryRawUnsafe(`
                                                SELECT COUNT(*) as count
                                                FROM tb_komoditi_daerah tkd
                                                LEFT JOIN tb_komoditi_nasional_ref tknr ON tknr.id_komoditi = tkd.id_komoditi
                                                LEFT JOIN tb_sub_sektor_daerah tssd ON tkd.id_sub_sektor_daerah = tssd.id_sub_sektor_daerah
                                                LEFT JOIN tb_sektor_daerah tsd ON tsd.id_sektor_daerah = tssd.id_sektor_daerah
                                                LEFT JOIN tb_sektor_nasional tsn ON tsd.id_sektor_nasional = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsnr.id_sektor = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional tssn ON tssn.id_sub_sektor_nasional = tssd.id_sub_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional_ref tssnr ON tssnr.id_sub_sektor = tssn.id_sub_sektor
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tsd.id_adm_provinsi
                                                LEFT JOIN tb_adm_provinsi tap2 ON tap2.id_adm_provinsi = tak.id_adm_provinsi
                                                WHERE 
                                                        LOWER(tsnr.nama) ILIKE $1 OR
                                                        LOWER(tssnr.nama) ILIKE $1 OR
                                                        LOWER(tknr.nama) ILIKE $1 OR
                                                        LOWER(tap.nama) ILIKE $1 OR
                                                        LOWER(tak.nama) ILIKE $1 OR
                                                        LOWER(tkd.sentra_produksi) ILIKE $1 OR
                                                        tkd.id_komoditi_daerah::TEXT ILIKE $1
                                                ${Wilayah}
                                        `, `%${searchQuery}%`);

                                        totalCount = Number(totalCountResult[0]?.count || 0);

                                        // Query data dengan filter `q`, limit, dan offset
                                        data = await prisma.$queryRawUnsafe(`
                                                SELECT 
                                                        tkd.id_komoditi_daerah AS id_komoditi_daerah,
                                                        tkd.status AS status,
                                                        tsnr.nama AS sektor,
                                                        tssnr.nama AS sub_sektor,
                                                        tknr.nama AS nama_komoditi,
                                                        case 
                                                                when tap.nama is null then tap2.nama
                                                                else 
                                                                tap.nama 
                                                        end AS provinsi,
                                                        tak.nama AS kabkota,
                                                        tkd.sentra_produksi
                                                FROM tb_komoditi_daerah tkd
                                                LEFT JOIN tb_komoditi_nasional_ref tknr ON tknr.id_komoditi = tkd.id_komoditi
                                                LEFT JOIN tb_sub_sektor_daerah tssd ON tkd.id_sub_sektor_daerah = tssd.id_sub_sektor_daerah
                                                LEFT JOIN tb_sektor_daerah tsd ON tsd.id_sektor_daerah = tssd.id_sektor_daerah
                                                LEFT JOIN tb_sektor_nasional tsn ON tsd.id_sektor_nasional = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsnr.id_sektor = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional tssn ON tssn.id_sub_sektor_nasional = tssd.id_sub_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional_ref tssnr ON tssnr.id_sub_sektor = tssn.id_sub_sektor
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tsd.id_adm_provinsi
                                                LEFT JOIN tb_adm_provinsi tap2 ON tap2.id_adm_provinsi = tak.id_adm_provinsi
                                                WHERE 
                                                        LOWER(tsnr.nama) ILIKE $1 OR
                                                        LOWER(tssnr.nama) ILIKE $1 OR
                                                        LOWER(tknr.nama) ILIKE $1 OR
                                                        LOWER(tap.nama) ILIKE $1 OR
                                                        LOWER(tak.nama) ILIKE $1 OR
                                                        LOWER(tkd.sentra_produksi) ILIKE $1 OR
                                                        tkd.id_komoditi_daerah::TEXT ILIKE $1
                                                        ${Wilayah}
                                                        ${orderBy}
                                                LIMIT $2 OFFSET $3
                                        `, `%${searchQuery}%`, perPage, offset);
                                } else {
                                        if (IdAdmKabkot) {
                                                Wilayah += ` where tak.id_adm_kabkot = ${IdAdmKabkot}`;
                                                if (status != undefined  && !isNaN(status)){
                                                        Wilayah += ` AND tsd.status = ${status}` 
                                                }
                                        }
                                        if (IdAdmProvinsi){
                                                Wilayah += ` where (tap.id_adm_provinsi = ${IdAdmProvinsi} 
                                                                      OR LEFT(tak.id_adm_kabkot::TEXT, 2) = LEFT(${IdAdmProvinsi}::TEXT, 2))`;
                                                if (status != undefined  && !isNaN(status)){
                                                        Wilayah += ` AND tsd.status = ${status}` 
                                                }
                                        }
                                        if (status != undefined && Wilayah == ''){
                                                Wilayah += ` where tsd.status = ${status}` 
                                        }
                                        // Query untuk menghitung total data tanpa filter `q`
                                        const totalCountResult = await prisma.$queryRawUnsafe(`
                                        SELECT COUNT(*) as count
                                        FROM tb_komoditi_daerah tkd
                                                LEFT JOIN tb_komoditi_nasional_ref tknr ON tknr.id_komoditi = tkd.id_komoditi
                                                LEFT JOIN tb_sub_sektor_daerah tssd ON tkd.id_sub_sektor_daerah = tssd.id_sub_sektor_daerah
                                                LEFT JOIN tb_sektor_daerah tsd ON tsd.id_sektor_daerah = tssd.id_sektor_daerah
                                                LEFT JOIN tb_sektor_nasional tsn ON tsd.id_sektor_nasional = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsnr.id_sektor = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional tssn ON tssn.id_sub_sektor_nasional = tssd.id_sub_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional_ref tssnr ON tssnr.id_sub_sektor = tssn.id_sub_sektor
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tsd.id_adm_provinsi
                                                LEFT JOIN tb_adm_provinsi tap2 ON tap2.id_adm_provinsi = tak.id_adm_provinsi
                                                ${Wilayah}
                                                `);

                                        totalCount = Number(totalCountResult[0]?.count || 0);

                                        // Query data tanpa filter `q`, limit, dan offset
                                        data = await prisma.$queryRawUnsafe(`
                                                SELECT 
                                                        tkd.id_komoditi_daerah AS id_komoditi_daerah,
                                                        tkd.status AS status,
                                                        tsnr.nama AS sektor,
                                                        tssnr.nama AS sub_sektor,
                                                        tknr.nama AS nama_komoditi,
                                                        case 
                                                                when tap.nama is null then tap2.nama
                                                                else 
                                                                tap.nama 
                                                        end AS provinsi,
                                                        tak.nama AS kabkota,
                                                        tkd.sentra_produksi
                                                FROM tb_komoditi_daerah tkd
                                                LEFT JOIN tb_komoditi_nasional_ref tknr ON tknr.id_komoditi = tkd.id_komoditi
                                                LEFT JOIN tb_sub_sektor_daerah tssd ON tkd.id_sub_sektor_daerah = tssd.id_sub_sektor_daerah
                                                LEFT JOIN tb_sektor_daerah tsd ON tsd.id_sektor_daerah = tssd.id_sektor_daerah
                                                LEFT JOIN tb_sektor_nasional tsn ON tsd.id_sektor_nasional = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsnr.id_sektor = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional tssn ON tssn.id_sub_sektor_nasional = tssd.id_sub_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional_ref tssnr ON tssnr.id_sub_sektor = tssn.id_sub_sektor
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tsd.id_adm_provinsi
                                                LEFT JOIN tb_adm_provinsi tap2 ON tap2.id_adm_provinsi = tak.id_adm_provinsi
                                                ${Wilayah}
                                                ${orderBy}
                                                LIMIT ${perPage} OFFSET ${offset}
                                        `);
                                }

                                // Format response
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: perPage,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / perPage),
                                        },
                                        data: data,
                                });
                        } catch (error) {
                                console.error(error);
                                return response.status(500).json({
                                        success: false,
                                        message: "An error occurred while fetching data",
                                        error: error.message,
                                });
                        }
                }

                let isAll = false;

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        // where: {
                        //         tb_adm_provinsi: {
                        //                 nama: {
                        //                         equals: queryParams
                        //                 },
                        //         }
                        // },
                        include: {
                                tb_sub_sektor_daerah: true,
                                tb_komoditi_nasional_ref: true,
                                tb_komoditi_daerah_value: true,
                                tb_komoditi_daerah_file: true,
                        }
                }


                if (isAll) {
                        delete options['skip'];
                        delete options['take'];

                        if (request.input("is_simple") || request.input("is_simple") == "true") {
                                delete options['include'];
                                options = {
                                        select: {
                                                id_komoditi_daerah: true,
                                                tb_komoditi_nasional_ref: {
                                                        select: {
                                                                nama: true
                                                        }
                                                }
                                        }
                                }
                        }
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                options['where'] = modelWhereAnd((await this.schemaData()).columns, queryParams);

                try {
                        const data = await prisma.tb_komoditi_daerah.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_komoditi_daerah.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                // const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        // status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }
        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {};

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_komoditi_daerah': parseInt(params.id)
                }

                const data = await prisma.tb_komoditi_daerah.findFirst({
                        where: { id_komoditi_daerah: parseInt(params.id) },
                        include: {
                                tb_sub_sektor_daerah: {
                                        include: {
                                                tb_sub_sektor_nasional: {
                                                        include: {
                                                                sub_sektor_ref: true
                                                        }
                                                },
                                                tb_sektor_daerah: {
                                                        include: {
                                                                sektor_nasional: {
                                                                        include: {
                                                                                sektor: true
                                                                        }
                                                                },
                                                                tb_adm_kabkot: true
                                                        }
                                                },
                                        }
                                },
                                tb_komoditi_nasional_ref: true,
                                tb_komoditi_daerah_file: {
                                        include: {
                                                tb_komoditi_daerah_file_tr: true
                                        }
                                },
                                tb_komoditi_daerah_value: {
                                        include: {
                                                tb_komoditi_daerah_value_detail: true,
                                                tb_komoditi_daerah_value_tr: true
                                        }
                                },
                                tb_peluang_daerah_komoditi: true,
                                tb_komoditi_daerah_tr: true,
                        }
                });
                const sanitizeFileName = (fileName: string): string => {
                        const fileBaseName = fileName.replace(/\s+/g, "_").replace(/\.[^/.]+$/, ""); // Remove spaces and extension
                        const fileExtension = fileName.split('.').pop()?.toLowerCase() || "";
                        return fileExtension === "pdf" ? `${fileBaseName}.pdf` : `${fileBaseName}.webp`; // Replace non-PDF extensions with .webp
                };
                const getFoto = data?.file_icon ? sanitizeFileName(data.file_icon) : "";
                const pathFoto = `${process.env.APP_URL}/uploads/sektor/${getFoto}`;

                data['tb_komoditi_daerah_file'] = data['tb_komoditi_daerah_file'].map((item) => {
                        const tb_komoditi_daerah_file_tr = item.tb_komoditi_daerah_file_tr.map((items) => {
                                return {
                                        ...items,
                                        path: `${process.env.APP_URL}/uploads/sektor/${items.nama}`,
                                }
                        }) 
                        return {
                                ...item, // Spread item untuk mempertahankan properti yang ada
                                path: `${process.env.APP_URL}/uploads/sektor/${item.nama}`,
                                tb_komoditi_daerah_file_tr
                        };
                });
                data['icon_tr'] = data['tb_komoditi_daerah_tr'].map((item) => {
                        let pathFotoTr: string | undefined;
                        if (item.file_icon != "") {
                                pathFotoTr = `${process.env.APP_URL}/uploads/sektor/${item.file_icon}`
                                
                        }
                        return pathFotoTr
                })[0] || "";


                return response.status(200).json({
                        success: true,
                        data: { ...data, pathFoto: pathFoto },
                });
        }



        public async createOrUpdate({ request, response, params }: HttpContext) {
                let reqBody: any = request.body()

                const paramID = parseInt(params.id || parseInt(request.input("id_komoditi_daerah")));
                const model = "sud_komoditi_daerah";
                try {
                        const result = await prisma.$transaction(async (prisma) => {
                                let fileName: string | undefined;
                                let fileNameTr: string | undefined;
                                let resID: number | undefined;
                                // Handle file upload if provided
                                // const file = request.file("icon");
                                // const fileTr = request.file("icon_tr");

                                const file = request.file("file_icon");
                                const fileTr = request.file("icon_tr");

                                if (file) {
                                        let uploadFileToServer = await uploadFile(file, model, resID);
                                        let filenameFromServer = '';
                                        if (uploadFileToServer) {
                                          filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                          filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                        }
                                        fileName = filenameFromServer;
                                }
                                if (fileTr) {
                                        let uploadFileToServer = await uploadFile(fileTr, model, resID);
                                        let filenameFromServer = '';
                                        if (uploadFileToServer) {
                                          filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                          filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                        }
                                        fileNameTr = filenameFromServer;
                                }

                                if (!paramID) {
                                        const checkUnique = await prisma.tb_komoditi_daerah.count({
                                                where: {
                                                        id_komoditi: request.input("id_komoditi") ? parseInt(request.input("id_komoditi")) : undefined,
                                                        id_sub_sektor_daerah: request.input("id_sub_sektor_daerah") ? parseInt(request.input("id_sub_sektor_daerah")) : undefined,
                                                }
                                        })

                                        console.log(checkUnique);
                                        if (checkUnique > 0) {
                                                return {
                                                        status: 'error',
                                                        message: 'Data Duplikat! Sektor Daerah dan Komoditi Sudah Pernah Dibuat Pada Daerah Yang Sama!',
                                                        error: null,
                                                };
                                        }
                                        console.log("Sync tb_komoditi_daerah id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_komoditi_daerah"', 'id_komoditi_daerah')),
                                                (SELECT (MAX("id_komoditi_daerah") + 1) FROM "tb_komoditi_daerah"),
                                                false) FROM "tb_komoditi_daerah";
                                         `);

                                        const res = await prisma.tb_komoditi_daerah.create({
                                                data: {
                                                        id_komoditi: request.input("id_komoditi") ? parseInt(request.input("id_komoditi")) : undefined,
                                                        id_sub_sektor_daerah: request.input("id_sub_sektor_daerah") ? parseInt(request.input("id_sub_sektor_daerah")) : undefined,
                                                        deskripsi_singkat: request.input("deskripsi_singkat") ? request.input("deskripsi_singkat") : "",
                                                        deskripsi: request.input("deskripsi") ? request.input("deskripsi") : "",
                                                        file_icon: fileName || "",
                                                        sentra_produksi: request.input("sentra_produksi") ? request.input("sentra_produksi") : "",
                                                        status: request.input("status") ? parseInt(request.input("status")) : 0,
                                                }
                                        });

                                        resID = res.id_komoditi_daerah

                                        await prisma.tb_komoditi_daerah_tr.deleteMany({
                                                where: {
                                                        id_komoditi_daerah: resID,
                                                },
                                        });

                                        
                                        await prisma.tb_komoditi_daerah_tr.create({
                                                data: {
                                                        id_komoditi_daerah: resID,
                                                        kd_bahasa: 'en',
                                                        deskripsi_singkat: '',
                                                        deskripsi: '',
                                                        file_icon: fileNameTr || '',
                                                        sentra_produksi: request.input("sentra_produksi_tr") ? request.input("sentra_produksi_tr") : '-',
                                                }
                                        });

                                        await prisma.tb_komoditi_daerah_value.deleteMany({
                                                where: {
                                                        id_komoditi_daerah: resID,
                                                },
                                        });

                                        let saveFileIds = [];
                                        for (let n in request.files('diagram_nilai')) {
                                                let fileParam : any = {};
                                                let file = request.files('diagram_nilai')[n];
                                                let judulFile = request.input('diagram_nilai_detail')[n]['judul'] ?? '';
                                                let keteranganFile = request.input('diagram_nilai_detail')[n]['keterangan'] ?? '';
                                                if (file) {
                                                        let uploadFileToServer = await uploadFile(file, model, 1);
                                                        let filenameFromServer = '';
                                                        if (uploadFileToServer) {
                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                        }
                                                        let filename = filenameFromServer; // Set fileName if file exists
                                                        fileParam['nama'] = filename;
                                                        fileParam['tipe'] = 1;
                                                        fileParam['jenis'] = 1;
                                                        fileParam['id_komoditi_daerah'] = resID;
                                                        fileParam['judul'] = judulFile;
                                                        fileParam['keterangan'] = keteranganFile;

                                                        let saveFile = await prisma.tb_komoditi_daerah_file.create({
                                                                data: fileParam
                                                        });

                                                        saveFileIds.push(saveFile.id_komoditi_daerah_file);
                                                }
                                        }

                                        for (let n in request.input('diagram_nilai_tr')) {
                                                let fileParam : any = {};
                                                let file = request.file('diagram_nilai_tr['+n+'][file]');
                                                let judulFile = request.input('diagram_nilai_tr['+n+'][judul]');
                                                let keteranganFile = request.input('diagram_nilai_tr['+n+'][keterangan]');

                                                if (file) {
                                                        let uploadFileToServer = await uploadFile(file, model, 2);
                                                        let filenameFromServer = '';
                                                        if (uploadFileToServer) {
                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                        }
                                                        let filename = filenameFromServer; // Set fileName if file exists
                                                        fileParam['kd_bahasa'] = 'en';
                                                        fileParam['nama'] = filename;
                                                        fileParam['tb_komoditi_daerah_file'] = {
                                                                'connect': {
                                                                        'id_komoditi_daerah_file': saveFileIds[n]
                                                                }
                                                        }
                                                        fileParam['judul'] = judulFile ?? '-';
                                                        fileParam['keterangan'] = keteranganFile ?? '-';

                                                        await prisma.tb_komoditi_daerah_file_tr.create({
                                                                data: fileParam
                                                        });
                                                }
                                        }

                                        if (Array.isArray(request.input("parameter_data"))) {
                                                console.log("Sync tb_komoditi_daerah_value id sequence",
                                                        await prisma.$executeRaw`
                                                        SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_komoditi_daerah_value"', 'id_komoditi_daerah_value')),
                                                        (SELECT (MAX("id_komoditi_daerah_value") + 1) FROM "tb_komoditi_daerah_value"),
                                                        false) FROM "tb_komoditi_daerah_value";
                                                 `);

                                                 console.log("Sync tb_komoditi_daerah_value_tr id sequence",
                                                        await prisma.$executeRaw`
                                                        SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_komoditi_daerah_value_tr"', 'id_komoditi_daerah_value_tr')),
                                                        (SELECT (MAX("id_komoditi_daerah_value_tr") + 1) FROM "tb_komoditi_daerah_value_tr"),
                                                        false) FROM "tb_komoditi_daerah_value_tr";
                                                 `);

                                                 console.log("Sync tb_komoditi_daerah_value_detail id sequence",
                                                        await prisma.$executeRaw`
                                                        SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_komoditi_daerah_value_detail"', 'id_komoditi_daerah_value_detail')),
                                                        (SELECT (MAX("id_komoditi_daerah_value_detail") + 1) FROM "tb_komoditi_daerah_value_detail"),
                                                        false) FROM "tb_komoditi_daerah_value_detail";
                                                 `);
                                                for (const item of request.input("parameter_data")) {
                                                        if (item.nama) {
                                                                let itemValue: any = {};
                                                                itemValue.id_komoditi_daerah = res.id_komoditi_daerah;
                                                                itemValue.nama = item.nama;
                                                                itemValue.satuan = item.satuan;
                                                                itemValue.tipe = 1;
                                                                let saveKomoditiDaerahValue = await prisma.tb_komoditi_daerah_value.create({
                                                                        data: itemValue,
                                                                });
        
                                                                let itemValueTr: any = {};
                                                                itemValueTr.id_komoditi_daerah_value = saveKomoditiDaerahValue.id_komoditi_daerah_value;
                                                                itemValueTr.nama = item.nama_tr;
                                                                itemValueTr.satuan = item.satuan_tr;
                                                                itemValueTr.kd_bahasa = 'en';
        
                                                                await prisma.tb_komoditi_daerah_value_tr.create({
                                                                        data: itemValueTr,
                                                                });
        
                                                                for (const itemDetail of item['details']) {
                                                                        let itemValueDetail: any = {};
                                                                        itemValueDetail.id_komoditi_daerah_value = saveKomoditiDaerahValue.id_komoditi_daerah_value;
                                                                        itemValueDetail.tahun = parseInt(itemDetail.tahun);
                                                                        itemValueDetail.numeric_value = parseFloat(itemDetail.numeric_value);
                                                                        if (!isNaN(Number(itemDetail.tahun)) && !isNaN(Number(itemDetail.numeric_value))) {
                                                                                await prisma.tb_komoditi_daerah_value_detail.create({
                                                                                        data: itemValueDetail,
                                                                                });
                                                                        } 
                                                                };
                                                        }
                                                }
                                        }

                                        const response = await prisma.tb_komoditi_daerah.findUnique({
                                                where: { id_komoditi_daerah: resID },
                                        })

                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: response
                                        };



                                } else {
                                        let old = await prisma.tb_komoditi_daerah.findFirst({
                                                where: {
                                                        id_komoditi_daerah: paramID
                                                },
                                                include:{
                                                        tb_komoditi_daerah_tr:true
                                                }
                                        });

                                        await prisma.tb_komoditi_daerah.update({
                                                where: { id_komoditi_daerah: paramID },
                                                data: {
                                                        id_komoditi: request.input("id_komoditi") ? parseInt(request.input("id_komoditi")) : 0,
                                                        id_sub_sektor_daerah: request.input("id_sub_sektor_daerah") ? parseInt(request.input("id_sub_sektor_daerah")) : 0,
                                                        deskripsi_singkat: request.input("deskripsi_singkat") ? request.input("deskripsi_singkat") : "",
                                                        deskripsi: request.input("deskripsi") ? request.input("deskripsi") : "",
                                                        file_icon: fileName ? fileName : (old['file_icon'] ?? ''),
                                                        sentra_produksi: request.input("sentra_produksi") ? request.input("sentra_produksi") : "",
                                                        status: request.input("status") ? parseInt(request.input("status")) : 0,
                                                }
                                        });

                                        resID = paramID

                                        await prisma.tb_komoditi_daerah_tr.deleteMany({
                                                where: {
                                                        id_komoditi_daerah: resID,
                                                },
                                        });

                                        
                                        await prisma.tb_komoditi_daerah_tr.create({
                                                data: {
                                                        id_komoditi_daerah: resID,
                                                        kd_bahasa: 'en',
                                                        deskripsi_singkat: '',
                                                        deskripsi: '',
                                                        file_icon: fileNameTr ? fileNameTr : (old?.tb_komoditi_daerah_tr[0].file_icon ?? ''),
                                                        sentra_produksi: request.input("sentra_produksi_tr") ? request.input("sentra_produksi_tr") : '-',
                                                }
                                        });

                                        await prisma.tb_komoditi_daerah_value.deleteMany({
                                                where: {
                                                        id_komoditi_daerah: resID,
                                                },
                                        });
                                        await prisma.tb_komoditi_daerah_file.deleteMany({
                                                where: {
                                                        id_komoditi_daerah: resID,
                                                },
                                        });
                                        
                                        let saveFileIds = [];
                                        if (reqBody['diagram_nilai']) {
                                        for (let n = 0; n < reqBody['diagram_nilai'].length; n++) {
                                                let fileParam : any = {};
                                                // Use the correct path to access the file
                                                let file = request.file(`diagram_nilai[${n}][file]`);
                                                let judulFile = request.input(`diagram_nilai[${n}][judul]`) ?? '';
                                                let keteranganFile = request.input(`diagram_nilai[${n}][keterangan]`) ?? '';
                                                console.log('dg atas');
                                                console.log(file);
                                                
                                                if (file) {
                                                let uploadFileToServer = await uploadFile(file, model, 1);
                                                let filenameFromServer = '';
                                                if (uploadFileToServer) {
                                                        filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                        filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                }
                                                let filename = filenameFromServer; // Set fileName if file exists
                                                fileParam['nama'] = filename;
                                                fileParam['tipe'] = 1;
                                                fileParam['jenis'] = 1;
                                                fileParam['id_komoditi_daerah'] = resID;
                                                fileParam['judul'] = judulFile;
                                                fileParam['keterangan'] = keteranganFile;
                                                
                                                let saveFile = await prisma.tb_komoditi_daerah_file.create({
                                                        data: fileParam
                                                });
                                                
                                                console.log('dg if');
                                                console.log(`ids : ${saveFile.id_komoditi_daerah_file}`);
                                                saveFileIds.push(saveFile.id_komoditi_daerah_file);
                                                }
                                        }
                                        }

                                        // Second loop - Process translations
                                        if (reqBody['diagram_nilai_tr']) {
                                        for (let n = 0; n < reqBody['diagram_nilai_tr'].length; n++) {
                                                try {
                                                // Only proceed if we have a corresponding file ID
                                                if (saveFileIds[n]) {
                                                        let fileParam : any = {};
                                                        let file = request.file(`diagram_nilai_tr[${n}][file]`);
                                                        let judulFile = request.input(`diagram_nilai_tr[${n}][judul]`);
                                                        let keteranganFile = request.input(`diagram_nilai_tr[${n}][keterangan]`);

                                                        if (file) {
                                                        let uploadFileToServer = await uploadFile(file, model, 2);
                                                        let filenameFromServer = '';
                                                        if (uploadFileToServer) {
                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                        }
                                                        let filename = filenameFromServer; // Set fileName if file exists
                                                        fileParam['kd_bahasa'] = 'en';
                                                        fileParam['nama'] = filename;
                                                        fileParam['tb_komoditi_daerah_file'] = {
                                                                'connect': {
                                                                'id_komoditi_daerah_file': saveFileIds[n]
                                                                }
                                                        };
                                                        fileParam['judul'] = judulFile ?? '-';
                                                        fileParam['keterangan'] = keteranganFile ?? '-';

                                                        await prisma.tb_komoditi_daerah_file_tr.create({
                                                                data: fileParam
                                                        });
                                                        }
                                                } else {
                                                        console.log(`Skipping translation for index ${n} - no corresponding file ID found`);
                                                }
                                                } catch (error) {
                                                console.error(`Error processing translation at index ${n}:`, error);
                                                }
                                        }
                                        }

                                      
                                        if (Array.isArray(request.input("parameter_data"))) {
                                                console.log("Sync tb_komoditi_daerah_value id sequence",
                                                        await prisma.$executeRaw`
                                                        SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_komoditi_daerah_value"', 'id_komoditi_daerah_value')),
                                                        (SELECT (MAX("id_komoditi_daerah_value") + 1) FROM "tb_komoditi_daerah_value"),
                                                        false) FROM "tb_komoditi_daerah_value";
                                                 `);

                                                 console.log("Sync tb_komoditi_daerah_value_tr id sequence",
                                                        await prisma.$executeRaw`
                                                        SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_komoditi_daerah_value_tr"', 'id_komoditi_daerah_value_tr')),
                                                        (SELECT (MAX("id_komoditi_daerah_value_tr") + 1) FROM "tb_komoditi_daerah_value_tr"),
                                                        false) FROM "tb_komoditi_daerah_value_tr";
                                                 `);

                                                 console.log("Sync tb_komoditi_daerah_value_detail id sequence",
                                                        await prisma.$executeRaw`
                                                        SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_komoditi_daerah_value_detail"', 'id_komoditi_daerah_value_detail')),
                                                        (SELECT (MAX("id_komoditi_daerah_value_detail") + 1) FROM "tb_komoditi_daerah_value_detail"),
                                                        false) FROM "tb_komoditi_daerah_value_detail";
                                                 `);
                                                for (const item of request.input("parameter_data")) {
                                                        if (item.nama) {
                                                                let itemValue: any = {};
                                                                itemValue.id_komoditi_daerah = resID;
                                                                itemValue.nama = item.nama;
                                                                itemValue.satuan = item.satuan;
                                                                itemValue.tipe = 1;
                                                                let saveKomoditiDaerahValue = await prisma.tb_komoditi_daerah_value.create({
                                                                        data: itemValue,
                                                                });
        
                                                                let itemValueTr: any = {};
                                                                itemValueTr.id_komoditi_daerah_value = saveKomoditiDaerahValue.id_komoditi_daerah_value;
                                                                itemValueTr.nama = item.nama_tr;
                                                                itemValueTr.satuan = item.satuan_tr;
                                                                itemValueTr.kd_bahasa = 'en';
        
                                                                await prisma.tb_komoditi_daerah_value_tr.create({
                                                                        data: itemValueTr,
                                                                });
        
                                                                for (const itemDetail of item['details']) {
                                                                        let itemValueDetail: any = {};
                                                                        itemValueDetail.id_komoditi_daerah_value = saveKomoditiDaerahValue.id_komoditi_daerah_value;
                                                                        itemValueDetail.tahun = parseInt(itemDetail.tahun);
                                                                        itemValueDetail.numeric_value = parseFloat(itemDetail.numeric_value);
                
                                                                        if (!isNaN(Number(itemDetail.tahun)) && !isNaN(Number(itemDetail.numeric_value))) {
                                                                                await prisma.tb_komoditi_daerah_value_detail.create({
                                                                                        data: itemValueDetail,
                                                                                });
                                                                        } 
                                                                };
                                                        }
                                                }
                                        }

                                        const response = await prisma.tb_komoditi_daerah.findUnique({
                                                where: { id_komoditi_daerah: resID },
                                        })

                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: response
                                        };
                                }
                        });

                        return response.json(result);
                } catch (error) {
                        console.log(error);
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error.message,
                        });
                }
        }



        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_komoditi_daerah.delete({
                                where: { id_komoditi_daerah: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response }: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }

                try {
                        const toggleStatus = await prisma.tb_komoditi_daerah.update({
                                where: {
                                        id_komoditi_daerah: Id,
                                },
                                data: {
                                        status: request.input('status') ?? 0
                                },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error Toggle Status' })
                }
        }

}









