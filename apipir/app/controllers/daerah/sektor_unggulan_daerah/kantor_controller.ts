import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../../../lib/prisma.js'
import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';



export default class SUDKantorController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_adm_provinsi_kantor: { column: ' id_adm_provinsi_kantor', alias: ' id_adm_provinsi_kantor', type: 'int' },
                        jenis: { column: 'jenis', alias: 'jenis', type: 'string' },
                        id_adm_provinsi: { column: ' id_adm_provinsi', alias: ' id_adm_provinsi', type: 'int' },
                        nama_pic: { column: 'nama_pic', alias: 'nama_pic', type: 'string' },
                        file_foto_pic: { column: 'file_foto_pic', alias: 'file_foto_pic', type: 'string' },
                        nama: { column: 'nama', alias: 'nama', type: 'string' },
                        alamat: { column: ' alamat', alias: ' alamat', type: 'string' },
                        no_telp: { column: ' no_telp', alias: ' no_telp', type: 'string' },
                        no_fax: { column: 'no_fax', alias: 'no_fax', type: 'string' },
                        url_web: { column: ' url_web', alias: ' url_web', type: 'string' },
                        status: { column: ' status', alias: ' status', type: 'int' },
                        lon: { column: 'lon', alias: 'lon', type: 'int' },
                        lat: { column: ' lat', alias: 'lat', type: 'int' },
                        created_by: { column: ' created_by', alias: 'created_by', type: 'string' },
                        created_date: { column: 'created_date', alias: 'created_date', type: 'string' },
                        updated_by: { column: 'updated_by', alias: 'updated_by', type: 'string' },
                        updated_date: { column: ' updated_date', alias: ' updated_date', type: 'string' },
                }

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        // where: {
                        //         tb_adm_provinsi: {
                        //                 nama: {
                        //                         equals: queryParams
                        //                 },
                        //         }
                        // },
                        // include: {
                        //         tb_adm_provinsi: {
                        //                 select: {
                        //                         nama: true,
                        //                         jumlah_penduduk: true,
                        //                 }
                        //         },
                        // }
                }


                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                options['where'] = modelWhereAnd((await this.schemaData()).columns, queryParams);

                try {
                        const data = await prisma.tb_adm_provinsi_kantor.findMany(options);



                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        } else if (options['include'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_adm_provinsi_kantor.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        // params: queryParams,
                                        //
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }
}

