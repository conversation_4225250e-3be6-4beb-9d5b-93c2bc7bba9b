import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';

import prisma from '../../../lib/prisma.js';



export default class SarprasPendidikanController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_pendidikan: { column: 'id_pendidikan', alias: 'id_pendidikan', type: 'int' },
                        id_adm_kabkot: { column: 'id_adm_kabkot', alias: 'id_adm_kabkot', type: 'int' },
                        id_sumber_data: { column: 'id_sumber_data', alias: 'id_sumber_data', type: 'int' },
                        nama: { column: 'nama', alias: 'nama', type: 'string' },
                        keterangan: { column: 'keterangan', alias: 'keterangan', type: 'string' },
                        id_kategori: { column: 'id_kategori', alias: 'id_kategori', type: 'int' },
                        id_jenjang: { column: 'id_jenjang', alias: 'id_jenjang', type: 'int' },
                        alamat: { column: 'alamat', alias: 'alamat', type: 'string' },
                        no_telp: { column: 'no_telp', alias: 'no_telp', type: 'string' },
                        no_fax: { column: 'no_fax', alias: 'no_fax', type: 'string' },
                        email: { column: 'email', alias: 'email', type: 'string' },
                        url_web: { column: 'url_web', alias: 'url_web', type: 'string' },
                        status: { column: 'status', alias: 'status', type: 'int' },
                        lon: { column: 'lon', alias: 'lon', type: 'float' },
                        lat: { column: 'lat', alias: 'lat', type: 'float' },
                        is_ikn: { column: 'is_ikn', alias: 'is_ikn', type: 'boolean' },
                        id_kategori_infrastruktur: { column: 'id_kategori_infrastruktur', alias: 'id_kategori_infrastruktur', type: 'int' },

                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;
                let q = '';

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                if (request.input('q')) {
                        q = request.input('q');
                }

                let whereDefault = {};

                if (q) {
                        whereDefault = {
                                OR: [
                                    {
                                        nama: {
                                                contains: q,
                                                mode: 'insensitive', // Mengabaikan besar kecil huruf
                                        },
                                    },
                                    {
                                        tb_adm_kabkot: {
                                            nama: {
                                                contains: q,
                                                mode: 'insensitive',
                                            },
                                        },
                                    },
                                    {
                                        tb_adm_kabkot: {
                                                tb_adm_provinsi:{

                                                        nama: {
                                                                contains: q,
                                                                mode: 'insensitive',
                                                        },
                                                }
                                        },
                                    },
                                    {
                                        tb_pendidikan_jenjang: {
                                            nama: {
                                                contains: q,
                                                mode: 'insensitive',
                                            },
                                        },
                                    },
                                    {
                                        tb_pendidikan_kategori: {
                                            nama: {
                                                contains: q,
                                                mode: 'insensitive',
                                            },
                                        },
                                    },
                                ],
                            };
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        orderBy: {
                                id_pendidikan: 'desc', 
                        },
                        include: {
                                tb_adm_kabkot: {
                                        include:{
                                                tb_adm_provinsi:true
                                        }
                                },
                                tb_sumber_data: true,
                                tb_pendidikan_jenjang: true,
                                tb_pendidikan_kategori: true,
                                tb_pendidikan_tr: true
                        },
                        where: whereDefault,
                }


                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                let where = modelWhereAnd((await this.schemaData()).columns, queryParams);
                const p = request.qs();
                let IdAdmProvinsi = p['id_adm_provinsi'];
                if (IdAdmProvinsi) {
                        
                        where['id_adm_kabkot']  = {
                                gte: parseInt(`${IdAdmProvinsi}00`),
                                lt: parseInt(`${IdAdmProvinsi}99`)
                            }
                }
                if (Object.keys(where).length !== 0) {
                        options['where'] = {
                            AND: [
                                whereDefault, 
                                where,        
                            ],
                        };
                    }
                const req = request.qs()
                let order = req.order 
                const by = ['asc','desc'].includes(req.by) ? req.by : 'asc'
                const paramList = ['nama','status','provinsi','kabkot']                
                if (order != undefined && paramList.includes(order)) {
                        if (order == 'provinsi') {
                                options.orderBy = {tb_adm_kabkot:{tb_adm_provinsi:{nama:by}}}
                        }else if (order == 'kabkot') {
                                options.orderBy = {tb_adm_kabkot:{nama:by}}
                        }else  {
                                options.orderBy = {[order]:by}
                        }
                }
                try {
                        const data = await prisma.tb_pendidikan.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(200).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada",
                                        pagination: {
                                                page: 1,
                                                per_page: 10,
                                                total_count: 0,
                                                total_pages: 0
                                                },
                                                data: []
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_pendidikan.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }

        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {
                        include: {
                                tb_adm_kabkot: true,
                                tb_sumber_data: true,
                                tb_pendidikan_jenjang: true,
                                tb_pendidikan_kategori: true,
                                tb_pendidikan_tr: true
                        }
                };

                options['where'] = {
                        'id_pendidikan': parseInt(params.id)
                }

                const data = await prisma.tb_pendidikan.findFirst(options);

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }

        public async createOrUpdate({ request, response, params,auth }: HttpContext) {
                let reqBody: any = request.body();
                const paramID = parseInt(params.id)
                try {

                        const result = await prisma.$transaction(async (prisma) => {
                                const {
                                        id_adm_kabkot,
                                        id_sumber_data,
                                        nama,
                                        keterangan,
                                        id_kategori,
                                        id_jenjang,
                                        alamat,
                                        no_telp,
                                        no_fax,
                                        email,
                                        url_web,
                                        status,
                                        lon,
                                        lat,
                                        is_ikn,
                                        kd_bahasa,
                                        nama_tr,
                                        keterangan_tr,
                                        id_kategori_infrastruktur
                                } = reqBody;

                                if (params.id) {
                                        // Jika `id` ada, lakukan operasi update
                                        const update = await prisma.tb_pendidikan.update({
                                                where: { id_pendidikan: paramID },
                                                data: {
                                                        id_adm_kabkot,
                                                        id_sumber_data,
                                                        nama,
                                                        keterangan,
                                                        id_kategori,
                                                        id_jenjang,
                                                        alamat,
                                                        no_telp,
                                                        no_fax,
                                                        email,
                                                        url_web,
                                                        status,
                                                        lon,
                                                        lat,
                                                        is_ikn,
                                                        id_kategori_infrastruktur
                                                }
                                        })
                                        const dataTr = {
                                                id_pendidikan: update.id_pendidikan,
                                                kd_bahasa,
                                                nama: nama_tr,
                                                keterangan: keterangan_tr,

                                        };

                                        const existingID = await prisma.tb_pendidikan_tr.findFirst({
                                                where: {
                                                        id_pendidikan: paramID,
                                                },
                                        });

                                        let updateTr
                                        if (existingID) {
                                                updateTr = await prisma.tb_pendidikan_tr.update({
                                                        where: { id_pendidikan_tr: existingID.id_pendidikan_tr },
                                                        data: dataTr,
                                                });
                                        }

                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update,
                                                data_translated: updateTr,
                                        }
                                } else {
                                        // console.log("create")
                                        console.log("Sync tb_pendidikan id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_pendidikan"', 'id_pendidikan')),
                                                        (SELECT (MAX("id_pendidikan") + 1) FROM "tb_pendidikan"),
                                                        false) FROM "tb_pendidikan";
                                                `);
                                        const res = await prisma.tb_pendidikan.create({
                                                data:
                                                {
                                                        id_adm_kabkot,
                                                        id_sumber_data,
                                                        nama,
                                                        keterangan,
                                                        id_kategori,
                                                        id_jenjang,
                                                        alamat,
                                                        no_telp,
                                                        no_fax,
                                                        email,
                                                        url_web,
                                                        status,
                                                        lon,
                                                        lat,
                                                        is_ikn,
                                                        id_kategori_infrastruktur
                                                }
                                        })
                                        const dataTr = {
                                                id_pendidikan: res.id_pendidikan,
                                                kd_bahasa,
                                                nama: nama_tr,
                                                keterangan: keterangan_tr,
                                        };

                                        console.log("Sync tb_pendidikan_tr id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_pendidikan_tr"', 'id_pendidikan_tr')),
                                                        (SELECT (MAX("id_pendidikan_tr") + 1) FROM "tb_pendidikan_tr"),
                                                        false) FROM "tb_pendidikan_tr";
                                                `);

                                        await auth.check()
                                        await prisma.tb_pendidikan_status.create({
                                                data: {
                                                id_pendidikan: res.id_pendidikan,
                                                status: 0,
                                                status_proses: 0,
                                                keterangan: 'Dokumen baru',
                                                created_by:auth.user?.id,
                                                updated_by:auth.user?.id,
                                                created_date:new Date()
                                                },
                                        });

                                        const createTr = await prisma.tb_pendidikan_tr.create({ data: dataTr })


                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: res,
                                                data_tr: createTr
                                        }
                                }
                        })

                        return response.json(result)

                } catch (error) {
                        // Handle error jika ada kesalahan dalam transaksi
                        console.log(error)
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error,
                        })
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        // Hapus dependent records di tb_pendidikan_tr terlebih dahulu
                        await prisma.tb_pendidikan_tr.deleteMany({
                                where: { id_pendidikan: Id },
                        });

                        // Hapus record utama di tb_pendidikan
                        const deletePost = await prisma.tb_pendidikan.delete({
                                where: { id_pendidikan: Id },
                        });

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        console.log(error)
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response,auth }: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }

                try {
                        const toggleStatus = await prisma.tb_pendidikan.update({
                                where: {
                                        id_pendidikan: Id,
                                },
                                data: {
                                        status: parseInt(request.input('status')) ?? 0
                                },
                        })
                        
                        await auth.check()
                        const insert = await prisma.tb_pendidikan_status.create({
                                data: {
                                    id_pendidikan: Id,
                                    status: parseInt(request.input('status')),
                                    status_proses: parseInt(request.input('status')),
                                    keterangan: request.input('keterangan'),
                                    created_by:auth.user?.id,
                                    updated_by:auth.user?.id,
                                    created_date:new Date()
                                },
                            });


                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error Toggle Status' })
                }
        }
}