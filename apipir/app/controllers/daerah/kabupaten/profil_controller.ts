import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';

import prisma from '../../../lib/prisma.js';
import uploadFile from '../../../helpers/file_uploader.js';



export default class KabupatenProfilController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_adm_kabkot: { column: 'id_adm_kabkot', alias: 'id_adm_kabkot', type: 'int' },
                        id_adm_provinsi: { column: 'id_adm_provinsi', alias: 'id_adm_provinsi', type: 'int' },
                        kd_adm: { column: 'kd_adm', alias: 'kd_adm', type: 'string' },
                        jenis: { column: 'jenis', alias: 'jenis', type: 'string' },
                        nama: { column: 'nama', alias: 'nama', type: 'string' },
                        nama_ibukota: { column: 'nama_ibukota', alias: 'nama_ibukota', type: 'string' },
                        file_logo: { column: 'file_logo', alias: 'file_logo', type: 'string' },
                        file_image: { column: 'file_image', alias: 'file_image', type: 'string' },
                        deskripsi: { column: 'deskripsi', alias: 'deskripsi', type: 'string' },
                        luas_wilayah: { column: 'luas_wilayah', alias: 'luas_wilayah', type: 'float' },
                        jumlah_penduduk: { column: 'jumlah_penduduk', alias: 'jumlah_penduduk', type: 'int' },
                        alamat: { column: 'alamat', alias: 'alamat', type: 'string' },
                        no_telp: { column: 'no_telp', alias: 'no_telp', type: 'string' },
                        no_fax: { column: 'no_fax', alias: 'no_fax', type: 'string' },
                        url_web: { column: 'url_web', alias: 'url_web', type: 'string' },
                        lon: { column: 'lon', alias: 'lon', type: 'float' },
                        lat: { column: 'lat', alias: 'lat', type: 'float' },
                        shape: { column: 'shape', alias: 'shape', type: 'string' }
                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;
                let q = '';

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        orderBy?: { [key: string]: string };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                if (request.input('q')) {
                        q = request.input('q');
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let whereDefault = {}
                
                if (q) {
                        whereDefault = {
                                OR: [
                                        {
                                                tb_adm_provinsi: {
                                                        nama: {
                                                            contains: q,
                                                            mode: 'insensitive', // Mengabaikan besar kecil huruf
                                                        },
                                                    },
                                        },
                                        {
                                                nama: {
                                                        contains: q,
                                                        mode: 'insensitive', // Mengabaikan besar kecil huruf
                                                    },
                                        }
                                ],
                            };
                }

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        // where: {
                        //         tb_adm_provinsi: {
                        //                 nama: {
                        //                         equals: queryParams
                        //                 },
                        //         }
                        // },
                        include: {
                                tb_adm_provinsi: {
                                        select: {
                                                nama: true,
                                                jumlah_penduduk: true,
                                        }
                                },
                        },
                        where: whereDefault,
                }
                // let order = queryParams.order 
                // const by = ['asc','desc'].includes(queryParams.by) ? queryParams.by : 'asc'
                // const paramList = ['status']
                // if (order != undefined && paramList.includes(order)) {
                //         options.orderBy = {[order]:by}
                // }

                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                let where = modelWhereAnd((await this.schemaData()).columns, queryParams);

                if (Object.keys(where).length !== 0) {
                        options['where'] = {
                            AND: [
                                whereDefault, // Kondisi pencarian `OR`
                                where,        // Kondisi tambahan dari `modelWhereAnd`
                            ],
                        };
                }
                try {
                        const data = await prisma.tb_adm_kabkot.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_adm_kabkot.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                // const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        // status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }
        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {
                        include: {
                                tb_adm_provinsi: {
                                        select: {
                                                id_adm_provinsi: true,
                                                nama: true,
                                                jumlah_penduduk: true,
                                        }
                                },
                        }
                };

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_adm_kabkot': parseInt(params.id)
                }

                const data = await prisma.tb_adm_kabkot.findFirst(options);
                let dataLayer = await prisma.tb_adm_kabkot_layer_spasial.findMany({
                        where: {
                                id_adm_kabkot: parseInt(params.id)
                        }
                });

                const fileLogoPath = `${process.env.APP_URL}/uploads/daerah/${data?.id_adm_kabkot}/${data?.file_logo}`;
                const fileImagePath = `${process.env.APP_URL}/uploads/daerah/${data?.id_adm_kabkot}/${data?.file_image}`;
                data["file_logo_path"] = fileLogoPath;
                data["file_image_path"] = fileImagePath;
                data['map_services'] = dataLayer;

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }

        public async createOrUpdate({ request, response, params }: HttpContext) {
                let reqBody: any = request.body();
                const paramID = parseInt(params.id)
                const model = "kabupaten_profil";

                try {

                        const result = await prisma.$transaction(async (prisma) => {
                                let fileName1: string = "";
                                let fileName2: string = "";

                                let resID: number | undefined;

                                const file1 = request.file("file_logo");
                                const file2 = request.file("file_image");

                                if (!paramID) {
                                        
                                        console.log("Sync tb_adm_kabkot id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_adm_kabkot"', 'id_adm_kabkot')),
                                                        (SELECT (MAX("id_adm_kabkot") + 1) FROM "tb_adm_kabkot"),
                                                        false) FROM "tb_adm_kabkot";
                                                `);

                                        const is_daerah_tertinggal = request.input("is_daerah_tertinggal") === "true" ? true : false
                                        const save = await prisma.tb_adm_kabkot.create({
                                                data: {
                                                        id_adm_provinsi: parseInt(request.input("id_adm_provinsi")) || 0,
                                                        kd_adm: request.input("kd_adm") || "",
                                                        jenis: request.input("jenis") || "",
                                                        nama: request.input("nama") || "",
                                                        nama_ibukota: request.input("nama_ibukota") || "",
                                                        file_logo: fileName1 || "",
                                                        file_image: fileName2 || "",
                                                        deskripsi: request.input("deskripsi") || "",
                                                        luas_wilayah: parseFloat(request.input("luas_wilayah")) || 0,
                                                        jumlah_penduduk: parseInt(request.input("jumlah_penduduk")) || 0,
                                                        alamat: request.input("alamat") || "",
                                                        no_telp: request.input("no_telp") || "",
                                                        no_fax: request.input("no_fax") || "",
                                                        url_web: request.input("url_web") || "",
                                                        lon: parseFloat(request.input("lon")) || 0,
                                                        lat: parseFloat(request.input("lat")) || 0,
                                                        shape: request.input("shape") || "",
                                                        is_daerah_tertinggal: is_daerah_tertinggal
                                                }
                                        })

                                        resID = save.id_adm_kabkot
                                        if (file1) {
                                                let uploadFileToServer = await uploadFile(file1, model, resID);
                                                let filenameFromServer = '';
                                                if (uploadFileToServer) {
                                                        filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                }
                                                await prisma.tb_adm_kabkot.update({
                                                        where:{
                                                                id_adm_kabkot:resID
                                                        },
                                                        data:{
                                                           file_logo:filenameFromServer      
                                                        }
                                                })
                                                fileName1 = filenameFromServer; // Set fileName if file exists
                                        }

                                        if (file2) {
                                                let uploadFileToServer = await uploadFile(file2, model, resID);
                                                let filenameFromServer = '';
                                                if (uploadFileToServer) {
                                                        filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                }
                                                await prisma.tb_adm_kabkot.update({
                                                        where:{
                                                                id_adm_kabkot:resID
                                                        },
                                                        data:{
                                                           file_image:filenameFromServer      
                                                        }
                                                })
                                                fileName2 = filenameFromServer; // Set fileName if file exists
                                        }

                                        if (Array.isArray(reqBody['map_services'])) {
                                                console.log("Sync tb_adm_kabkot_layer_spasial id sequence",
                                                        await prisma.$executeRaw`
                                                        SELECT setval((
                                                            SELECT PG_GET_SERIAL_SEQUENCE('"tb_adm_kabkot_layer_spasial"', 'id_adm_kabkot_layer_spasial')),
                                                            (SELECT (MAX("id_adm_kabkot_layer_spasial") + 1) FROM "tb_adm_kabkot_layer_spasial"),
                                                            false) FROM "tb_adm_kabkot_layer_spasial";
                                                        `);
                                                        
                                                for (const item of reqBody['map_services']) {
                                                        item['id_adm_kabkot'] = resID;
                                                        item['nama_layer'] = item['nama_layer'] ?? '-';
                                                        item['tipe'] = parseInt(item['tipe'] ?? '1');
                                                        item['url_service'] = item['layer_uid'] ?? '';
                                                        item['status'] = parseInt(item['status'] ?? '1');
                                                        item['is_active'] = Boolean(item['is_active'] ?? false);
                                                        delete item['layer_uid'];
                                                        delete item['keterangan'];
                                                        await prisma.tb_adm_kabkot_layer_spasial.create({
                                                                data: item,
                                                        });
                                                }
                                        }

                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: save,
                                        }

                                } else {
                                        let old = await prisma.tb_adm_kabkot.findFirst({
                                                where: {
                                                        'id_adm_kabkot': parseInt(params.id)
                                                }
                                        });

                                        // Jika `id` ada, lakukan operasi update

                                        const update = await prisma.tb_adm_kabkot.update({
                                                where: { id_adm_kabkot: paramID },
                                                data: {
                                                        id_adm_provinsi: request.input('id_adm_provinsi') ? parseInt(request.input("id_adm_provinsi")) : undefined,
                                                        kd_adm: request.input("kd_adm") || "",
                                                        jenis: request.input("jenis") || "",
                                                        nama: request.input("nama") || "",
                                                        nama_ibukota: request.input("nama_ibukota") || "",
                                                        file_logo: fileName1 ? fileName1 : (old['file_logo'] ?? ''),
                                                        file_image: fileName2 ? fileName2 : (old['file_image'] ?? ''),
                                                        deskripsi: request.input("deskripsi") || "",
                                                        luas_wilayah: parseFloat(request.input("luas_wilayah")) || 0,
                                                        jumlah_penduduk: parseInt(request.input("jumlah_penduduk")) || 0,
                                                        alamat: request.input("alamat") || "",
                                                        no_telp: request.input("no_telp") || "",
                                                        no_fax: request.input("no_fax") || "",
                                                        url_web: request.input("url_web") || "",
                                                        lon: parseFloat(request.input("lon")) || 0,
                                                        lat: parseFloat(request.input("lat")) || 0,
                                                        shape: request.input("shape") || "",
                                                        is_daerah_tertinggal: request.input("is_daerah_tertinggal") || false
                                                },
                                        });
                                        resID = update.id_adm_kabkot
                                        if (file1) {
                                                let uploadFileToServer = await uploadFile(file1, model, resID);
                                                let filenameFromServer = '';
                                                if (uploadFileToServer) {
                                                        filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                }
                                                await prisma.tb_adm_kabkot.update({
                                                        where:{
                                                                id_adm_kabkot:resID
                                                        },
                                                        data:{
                                                           file_logo:filenameFromServer      
                                                        }
                                                })
                                                fileName1 = filenameFromServer; // Set fileName if file exists
                                        }

                                        if (file2) {
                                                let uploadFileToServer = await uploadFile(file2, model, resID);
                                                let filenameFromServer = '';
                                                if (uploadFileToServer) {
                                                        filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                }
                                                await prisma.tb_adm_kabkot.update({
                                                        where:{
                                                                id_adm_kabkot:resID
                                                        },
                                                        data:{
                                                           file_image:filenameFromServer      
                                                        }
                                                })
                                                fileName2 = filenameFromServer; // Set fileName if file exists
                                        }

                                        if (Array.isArray(reqBody['map_services'])) {
                                                await prisma.tb_adm_kabkot_layer_spasial.deleteMany({
                                                        where: {
                                                                id_adm_kabkot: parseInt(params.id),
                                                        },
                                                });
                                        
                                                console.log("Sync tb_adm_kabkot_layer_spasial id sequence",
                                                        await prisma.$executeRaw`
                                                        SELECT setval((
                                                            SELECT PG_GET_SERIAL_SEQUENCE('"tb_adm_kabkot_layer_spasial"', 'id_adm_kabkot_layer_spasial')),
                                                            (SELECT (MAX("id_adm_kabkot_layer_spasial") + 1) FROM "tb_adm_kabkot_layer_spasial"),
                                                            false) FROM "tb_adm_kabkot_layer_spasial";
                                                        `);
                                                        
                                                for (const item of reqBody['map_services']) {
                                                        item['id_adm_kabkot'] = parseInt(params.id);
                                                        item['nama_layer'] = item['nama_layer'] ?? '-';
                                                        item['tipe'] = parseInt(item['tipe'] ?? '1');
                                                        item['url_service'] = item['layer_uid'] ?? '';
                                                        item['status'] = parseInt(item['status'] ?? '1');
                                                        item['is_active'] = Boolean(item['is_active'] ?? false);
                                                        delete item['layer_uid'];
                                                        delete item['keterangan'];
                                                        await prisma.tb_adm_kabkot_layer_spasial.create({
                                                                data: item,
                                                        });
                                                }
                                        }

                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update,
                                        }
                                }
                        })

                        return response.json(result)

                } catch (error) {
                        // Handle error jika ada kesalahan dalam transaksi
                        console.log(error)
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error,
                        })
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_adm_kabkot.delete({
                                where: { id_adm_kabkot: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }
}