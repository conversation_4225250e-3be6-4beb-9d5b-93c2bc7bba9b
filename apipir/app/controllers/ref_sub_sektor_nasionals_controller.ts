import { createUmkmValidator, updateUmkmValidator } from '#validators/umkm'
import { createUmrProvValidator } from '#validators/umr'
import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'
import { configUpload, img_to_webp } from '../helpers/global_helper.js'
import env from '#start/env'
import { createSNRefValidator, createSSNRefValidator, updateSNRefValidator, updateSSNRefValidator } from '#validators/referensi'

export default class RefSubSektorNasionalController {
    
  async index({}: HttpContext) {
    const data = await prisma.tb_sub_sektor_nasional_ref.findMany({})
    return { 
        success : true,
        data : data
    }
  }
   /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
   async get_paginate({params,response,request}:HttpContext) {
    const parameters =  request.qs()
    let order = parameters.order 
    const by = ['asc','desc'].includes(parameters.by) ? parameters.by : 'asc'
    let orderBy={}
    const paramList = ['nama_sektor','nama_sub_sektor','nama_sub_sektor_tr']
    if (order != undefined && paramList.includes(order)) {
        if (order == 'nama_sub_sektor') {
            orderBy = {nama:by}
        }else if (order == 'nama_sub_sektor_tr') {
          orderBy = {sub_sektor_nasional_tr:{nama:by}}
        }else if (order == 'nama_sektor') {
          orderBy = {sektor_nasional_ref:{nama:by}}
        }else{
            orderBy = {[order]:by}
        }
    }
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          { nama: { contains: search, mode: 'insensitive' } }, 
          {
            sub_sektor_nasional_ref_tr: {
                nama: {
                  contains: search,mode: "insensitive"
              }
            }
          },
          {
            sektor_nasional_ref :{
              nama : { contains: search, mode: 'insensitive' } }
            }
        ],
      };
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_sub_sektor_nasional_ref.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_sub_sektor_nasional_ref.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      orderBy:orderBy,
      include:{
        sub_sektor_nasional_ref_tr:true,
        sektor_nasional_ref:true,
      }
    });
    const data = datas.map((item =>{
        return {
            id_sektor_nasional_ref : item.id_sub_sektor,
            nama_sektor : item.sektor_nasional_ref.nama,
            nama_sub_sektor : item.nama,
            nama_sub_sektor_tr : item?.sub_sektor_nasional_ref_tr?.nama,
        }
    }))
    

    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
     * @store
     * @summary Insert Referensi Sektor Nasional Provinsi
     * @paramQuery nama - nama - @type(string) @required
     * @paramQuery id_sektor - Id Sektor - @type(string) @required
     * @paramQuery nama_tr - nama_tr - @type(string) @required
     * @paramQuery kd_bahasa - kd_bahasa - @type(string) @required
     * @paramQuery image - image - @type(file) @required
  */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createSSNRefValidator)
        try {
            const dataPost = {
              id_sektor:data.id_sektor,
              nama : data.nama,
            }
            const insert = await prisma.tb_sub_sektor_nasional_ref.create({data:dataPost})
            const dataPostTr = {
              id_sub_sektor  : insert.id_sub_sektor,
              nama : data.nama_tr,
              kd_bahasa : data.kd_bahasa,
            }
            const insertTr = await prisma.tb_sub_sektor_nasional_ref_tr.create({data:dataPostTr})
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
    const data = await prisma.tb_sub_sektor_nasional_ref.findUnique({
      where: {
        id_sub_sektor: Id,
      },
      include:{
        sub_sektor_nasional_ref_tr:true
      }
    })


    return { 
      success : true,
      data : data
    }
  }


  /**
     * @update
     * @paramQuery nama - nama - @type(string) @required
     * @paramQuery id_sektor - Id Sektor - @type(string) @required
     * @paramQuery nama_tr - nama_tr - @type(string) @required
     * @paramQuery kd_bahasa - kd_bahasa - @type(string) @required
     * @paramQuery image - image - @type(file) @required
     */
  async update({ params, request ,response}: HttpContext) {

    
    const Id = parseInt(params.id, 10)
    
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }
    try {
      const data = await request.validateUsing(updateSSNRefValidator)
      const dataPost = {
          id_sektor : data.id_sektor,
          nama : data.nama,
        }
        const update = await prisma.tb_sub_sektor_nasional_ref.update({
          data:dataPost,
          where:{
            id_sub_sektor : Id,
          }
        })
      let tr = request.input('tr')
      if (typeof tr === 'string') {
          tr = JSON.parse(tr);
      }
      if (Array.isArray(tr)) {
          // Jika panjang array JSON dan file gambar tidak sesuai, kembalikan error
          await Promise.all(
            tr.map(item => {
              const dataPostTr = {
                nama: item.nama,
                kd_bahasa: item.kd_bahasa,
                id_sub_sektor : update.id_sub_sektor
              };
              
              return prisma.tb_sub_sektor_nasional_ref_tr.update({
                data: dataPostTr,
                where: {
                  id_sub_sektor: parseInt(item.id_sub_sektor),
                },
              });
            })
          );
              
      }

      
      return response.status(200).json({ success: true, data: update })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data' })
    }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_sub_sektor_nasional_ref.delete({
        where: {
          id_sub_sektor: Id,
        },
      })
      return response.status(200).send({success:true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }
  async get_by_sektor({ params,response }: HttpContext) {
      const Id = parseInt(params.id_sektor_nasional, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_sub_sektor_nasional_ref.findMany({
        where: {
            id_sektor: Id,
        },
        
        });

        return { 
            success : true,
            data : data
        }
   }
}