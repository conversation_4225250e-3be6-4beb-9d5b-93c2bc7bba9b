import { createMenuValidator, updateMenuValidator } from '#validators/menu';
import type { HttpContext } from '@adonisjs/core/http'
import { configUpload, get_paginate, img_to_webp, upload } from '../helpers/global_helper.js';
import { createAppSLiderPeluangValidator } from '#validators/add_slider_peluang';
import { createSNInsentifValidator, updateSNInsentifValidator } from '#validators/sektor_nasional';
import { aproveValidator } from '#validators/aprove';
import { DateTime } from 'luxon';
import env from '#start/env';
import prisma from '../lib/prisma.js'
export default class SektorNasionalInsentifsController {

    /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.tb_sektor_nasional_insentif.findMany({

    });

      return {
          success : true,
          data : {data}
      }
  }

  /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response,request}:HttpContext) {
    const parameters =  request.qs()
    let order = parameters.order
    const by = ['asc','desc'].includes(parameters.by) ? parameters.by : 'asc'
    let orderBy={}
    const paramList = ['status','nama_sektor']
    if (order != undefined && paramList.includes(order)) {
        if (order == 'nama_sektor') {
            orderBy = {sektor_nasional:{sektor:{nama:by}}}
        }else{
            orderBy = {[order]:by}
        }
    }
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          {
            sektor_nasional: {
              sektor: {
                nama: { contains: search, mode: 'insensitive' }, // Search in sektor.nama
              },
            },
          },
          {
            nama: { contains: search, mode: 'insensitive' }, // Search in tb_sektor_nasional_insentif.nama
          },
        ],
      };


    }
    const req = request.qs()
    if (req.status) {
        (searchCondition.AND ??= []).push(
          { status: parseInt(req.status) }
  
        );
    }
    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_sektor_nasional_insentif.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_sektor_nasional_insentif.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      orderBy:orderBy,
      include: {
          sektor_nasional:{
            include:{
              sektor:true
            }
          }
        },
    });
    const data = datas.map(({...rest }) => {
        return {
            id_sektor_nasional_insentif : rest.id_sektor_nasional_insentif,
            nama_sektor: rest.sektor_nasional.sektor.nama,
            nama: rest.nama,
            status : rest.status
        };
    });


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };
  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {

  }

    /**
     * @store
     * @paramQuery id_sektor_nasional - Id Sektor Nasional - @type(number) @required
     * @paramQuery nama - Nama  - @type(string)
     * @paramQuery deskripsi - Deskripsi - @type(string) @required
     * @paramQuery kd_bahasa - Kode Bahasa - @type(string) @required
     * @paramQuery nama_tr - Nama Translate - @type(string) @required
     * @paramQuery deskripsi_tr - Deskripsi Translate - @type(string) @required
     * @paramQuery dokumen - dokumen - @type(file)
     */
    public async store({ request, response ,auth}: HttpContext) {


      let data = await request.validateUsing(createSNInsentifValidator)
      const configDoc = await configUpload('doc')
        const dokumen = request.files('dokumen',configDoc);
        let dokumenDetail = request.input('dokumen_detail');

        try {
            const dataPost = {
                id_sektor_nasional: data.id_sektor_nasional,
                nama: data.nama,
                deskripsi: '-',
                status : 0
            }
            const insert = await prisma.tb_sektor_nasional_insentif.create({data:dataPost})
            const dataPostTr = {
                id_sektor_nasional_insentif: insert.id_sektor_nasional_insentif,
                nama: data.nama_tr,
                deskripsi: '-',
                kd_bahasa: "en",
            }

            await prisma.tb_sektor_nasional_insentif_tr.create({data:dataPostTr})
            if (typeof dokumenDetail === 'string') {
                  dokumenDetail = JSON.parse(dokumenDetail);
              }
            if (dokumen && dokumen.length > 0) {
                if (Array.isArray(dokumenDetail)) {
                    const uploadPromises = dokumenDetail.map(async (detail) => {
                        const matchingDokumen = dokumen.find(
                            (doc) => doc.clientName === detail.fileName
                        );

                        if (matchingDokumen) {
                            const uploadPath = `uploads/sektor/`;

                            // Upload file
                            const upDoc = await upload(matchingDokumen, uploadPath);

                            // Simpan ke database
                            const newFileE = await prisma.tb_sektor_nasional_insentif_file.create({
                                data: {
                                    id_sektor_nasional_insentif: insert.id_sektor_nasional_insentif,
                                    nama: upDoc.data?.filename, // Nama file setelah upload
                                    tipe: 3,
                                    jenis: 0,
                                    judul: detail.judul, // Ambil judul dari detail_dokumen
                                    keterangan: "-", // Tetap sebagai "-"
                                },
                            });
                        } else {
                            console.log(`Dokumen tidak ditemukan untuk: ${detail.fileName}`);
                        }
                    });

                    // Tunggu semua proses upload selesai
                    await Promise.all(uploadPromises);
                } else {
                    console.error("dokumenDetail bukan array.");
                }
            }
            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_sektor_nasional_insentif.findUnique({
          where: {
            id_sektor_nasional_insentif: Id,
          },
          include: {
            sektor_nasional_insentif_tr: true,
            sektor_nasional_insentif_file: true,
          },
        })

        if (data?.sektor_nasional_insentif_file) {
            data.sektor_nasional_insentif_file = data?.sektor_nasional_insentif_file.map((item)=> {
              return {
                ...item,
                nama :`${env.get('APP_URL')}/uploads/sektor/${item?.nama}`,
              }
            })

        }

        return {
            success : true,
            data : {data}
        }
  }

  /**
   * Edit individual record
   */
  async edit({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_sektor_nasional_insentif.findUnique({
          where: {
            id_sektor_nasional_insentif: Id,
          },
        })

        return {
            success : true,
            data
        }
  }

  /**
     * @update
     * @paramQuery id_sektor_nasional - Id Sektor Nasional - @type(number) @required
     * @paramQuery nama - Nama  - @type(string)
     * @paramQuery deskripsi - Deskripsi - @type(string) @required
     * @paramQuery kd_bahasa - Kode Bahasa - @type(string) @required
     * @paramQuery nama_tr - Nama Translate - @type(string) @required
     * @paramQuery deskripsi_tr - Deskripsi Translate - @type(string) @required
     * @paramQuery dokumen - dokumen - @type(file)
     */
  async update({ params, request ,response}: HttpContext) {

      const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }

      const data = await request.validateUsing(updateSNInsentifValidator)
        try {

          const dataPost = {
                id_sektor_nasional: data.id_sektor_nasional,
                nama: data.nama,
                deskripsi: data.deskripsi,
                status : 0
            }

            const insert = await prisma.tb_sektor_nasional_insentif.update({
              data:dataPost,
              where:{
                id_sektor_nasional_insentif:Id
              }
            })

            const dataPostTr = {
                nama: data.nama_tr,
                deskripsi: data.deskripsi_tr,
                kd_bahasa: "en",
            }
            const tr = await prisma.tb_sektor_nasional_insentif_tr.findFirst({
              where:{
                id_sektor_nasional_insentif:Id
              },
              select :{
                id_sektor_nasional_insentif_tr:true
              }
            })

            const updateTr = await prisma.tb_sektor_nasional_insentif_tr.update({
              data:dataPostTr,
              where:{
                id_sektor_nasional_insentif_tr:tr?.id_sektor_nasional_insentif_tr
              }
            })
            const configDoc = await configUpload('doc')
            const dokumen = request.files('dokumen',configDoc);
            let dokumenDetail = request.input('dokumen_detail');
            if (typeof dokumenDetail === 'string') {
                dokumenDetail = JSON.parse(dokumenDetail);
            }
            const file = await prisma.tb_sektor_nasional_insentif_file.findFirst({
              where:{
                id_sektor_nasional_insentif:Id
              },
              select :{
                id_sektor_nasional_insentif_file:true
              }
            })

            if (dokumenDetail && dokumenDetail.length > 0) {
                if (Array.isArray(dokumenDetail)) {
                    const uploadPromises = dokumenDetail.map(async (detail) => {
                        // Jika detail memiliki fileName, lakukan pencarian dokumen yang cocok

                        if (detail.fileName) {
                            const matchingDokumen = dokumen.find(
                                (doc) => doc.clientName === detail.fileName
                            );

                            if (matchingDokumen) {

                                const uploadPath = `uploads/sektor/`;

                                // Upload file
                                const upDoc = await upload(matchingDokumen, uploadPath);

                                // Simpan ke database setelah upload
                                if (detail.id_sektor_nasional_insentif_file) {

                                  await prisma.tb_sektor_nasional_insentif_file.update({
                                      where: {
                                        id_sektor_nasional_insentif_file: parseInt(detail.id_sektor_nasional_insentif_file), // Gunakan ID jika tersedia
                                      },
                                      data: {
                                        nama: upDoc.data?.filename, // Update nama file
                                        judul: detail.judul, // Update judul
                                      },

                                  });
                                }else{
                                  await prisma.tb_sektor_nasional_insentif_file.create({
                                      data:  {
                                        id_sektor_nasional_insentif: insert.id_sektor_nasional_insentif,
                                        nama: upDoc.data?.filename, // Nama file setelah upload
                                        tipe: 3,
                                        jenis: 0,
                                        judul: detail.judul, // Judul dari detail dokumen
                                        keterangan: "-", // Tetap sebagai "-"
                                    }
                                  })
                                }
                            } else {
                                if (detail.id_sektor_nasional_insentif_file) {

                                  await prisma.tb_sektor_nasional_insentif_file.update({
                                      where: {
                                        id_sektor_nasional_insentif_file: parseInt(detail.id_sektor_nasional_insentif_file), // Gunakan ID jika tersedia
                                      },
                                      data: {
                                        judul: detail.judul, // Update judul
                                      },

                                  });
                                }
                            }
                        } else {
                            // Jika fileName tidak ada, hanya update tabel
                            await prisma.tb_sektor_nasional_insentif_file.update({
                                where: {
                                    id_sektor_nasional_insentif_file: detail.id_sektor_nasional_insentif_file,
                                },
                                data: {
                                    judul: detail.judul, // Update judul
                                },
                            });
                        }
                    });

                    // Tunggu semua proses upload dan update selesai
                    await Promise.all(uploadPromises);
                } else {
                    console.error("dokumenDetail bukan array.");
                }
            } else {
                console.error("dokumen kosong.");
            }



            response.status(201).json({ success: true,data:insert })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_sektor_nasional_insentif.delete({
        where: {
          id_sektor_nasional_insentif: Id,
        },
      })

      return response.status(200).send({ success: true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }

  /**
     * @aprove
     * @paramQuery status - status - @type(number) @required
     * @paramQuery keterangan - keterangan - @type(string)  @required
     */
    async aprove({ params, request, response ,auth}: HttpContext) {
        // Ambil dan parse ID dari parameter URL
        const Id = parseInt(params.id_sektor_nasional_insentif, 10);
        // Validasi ID
        if (isNaN(Id)) {
            return response.status(400).json({ error: 'Invalid ID provided' });
        }
        try {
            // Validasi input dengan validator yang telah dibuat
            const dataPost = await request.validateUsing(aproveValidator);

            // Update data di tb_sektor_nasional
            const update = await prisma.tb_sektor_nasional_insentif.update({
            where: {
                id_sektor_nasional_insentif: Id
            },
                data: {
                    status: dataPost.status
                },
            });
            // Insert ke tb_sektor_nasional_status
            const insert = await prisma.tb_sektor_nasional_insentif_status.create({
                data: {
                    id_sektor_nasional_insentif: Id,
                    status: dataPost.status,
                    status_proses: dataPost.status,
                    keterangan: dataPost.keterangan,
                    created_by:auth.user?.id,
                    updated_by:auth.user?.id,
                    created_date: DateTime.now().toISO()
                },
            });
            // Berikan respon sukses dengan data update
            return response.status(201).json({ success: true, data: update });
        } catch (error) {
            // Tangani error dan kirimkan respon gagal
            return response.status(500).json({ success: false, message: error.message });
        }
    }
}