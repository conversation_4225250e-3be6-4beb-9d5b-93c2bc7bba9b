import type { HttpContext } from '@adonisjs/core/http'

import fs from 'fs';
// import { Prisma } from '@prisma/client';
import { backupTableValidator } from '#validators/backup_db';
import { getValidator, globalMenuValidator, globalRoleValidator, globalTableValidator } from '#validators/global';
import path from 'path';
import { createObjectCsvStringifier } from 'csv-writer';

import { configUpload, img_to_webp, send_mail, upload_file, upload_img_to_webp } from '../helpers/global_helper.js';
import env from '#start/env';
import { base64 } from '@adonisjs/core/helpers';

import prisma from '../lib/prisma.js';

export default class CustomController {

 /**
     * @backup
     * @summary Bakcup Table
     * @paramQuery table - Masukan Nama Table - @type(string) @required
     */
//    public async backup({ request,response }: HttpContext) {
//     const dataPost = await request.validateUsing(backupTableValidator)
//     const {table} = dataPost
//     const models = Prisma.dmmf.datamodel.models.map(model => model.name);
//     if (!models.includes(table)) {
//       throw new Error(`Model '${table}' is not exist.`);
//     }
//      try {
//       if (!prisma[table] || typeof prisma[table].findMany !== 'function') {
//         return response.status(400).send({ error: 'Invalid table name' });
//       }

//       const data = await prisma[table].findMany({});
//       // const data = datas.map(({ id, ...rest }) => rest);

//       const backupFilePath = path.join('database', 'backup', `${table}.json`);
//       fs.writeFileSync(backupFilePath, JSON.stringify(data, null, 2));

//       // Automatically create the seeder
//       const seederFilePath = path.join('database', 'seeders', `${table}_seeder.ts`);
//       const seederContent = `
// import { BaseSeeder } from '@adonisjs/lucid/seeders'
// import fs from 'fs'
// import path from 'path'
// import prisma from '../lib/prisma.js'

// 

// export default class restore_${table}_seeder extends BaseSeeder {
//     public async run() {
//         const data = JSON.parse(fs.readFileSync(path.join('database', 'backup', '${table}.json'), 'utf-8'));
//         await prisma.${table}.createMany({ data, skipDuplicates: true });
//     }
// }
// `;
//         fs.writeFileSync(seederFilePath, seederContent.trim());

//             response.send({ message: `Backup of ${table} table completed successfully, and seeder file created.` });
//           } catch (error) {
//             console.error(error);
//             response.status(500).send({ error: `Failed to backup ${table} table` });
//           } finally {
//             await prisma.$disconnect();
//           }
//     }


    public async sendEmail({ request,response }: HttpContext) {
            const send_mails = await send_mail('<EMAIL>','Judul Email','<h1>Isi Email</h1>')
            return send_mails

    }
    public async getAnyModel({ params,response }: HttpContext) {
      const table = params.model
      if (!prisma[table] || typeof prisma[table].findMany !== 'function') {
        return response.status(400).send({ error: 'Invalid table name' });
      }

      const data = await prisma[table].findMany({});
      return data
    }

    public async configUpload(params:string) {

      const conf = await prisma.config_upload.findFirst({
          where: {
            jenis:params
          },
          select:{
            size:true,
            extnames:true
          }
        })
      const config = {
        ...conf,
        extnames: JSON.parse(conf.extnames)
      };

      return config
    }


    /**
     * @image
     * @paramQuery upload_file - Masukan Gambar - @type(file) @required
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async image({ request,response }: HttpContext) {
      const configData = this.configUpload('img')
      const uploadPath = 'uploads/post'
      return  upload_file([],request,configData,'file_upload',uploadPath)

    }

     /**
     * @vidio
     * @paramQuery upload_file - Masukan Gambar - @type(file) @required
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async vidio({ request,response }: HttpContext) {
      const configData = JSON.parse(env.get('VIDIO_CONFIG'))
      const uploadPath = 'uploads/post'
      return  upload_file([],request,configData,'file_upload')

    }

     /**
     * @doc
     * @paramQuery img - Masukan Gambar - @type(file) @required
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async doc({ request,response }: HttpContext) {
      const configData = this.configUpload('doc')
      const uploadPath = 'uploads/post'
      return  upload_file([],request,configData,'file_upload')

    }

     /**
     * @doc
     * @paramQuery img - Masukan Gambar - @type(file) @required
     * @responseBody 200 - {"fieldname": {"type":"string", "format": "email"}}
     */
    public async image_to_webp({ request,response }: HttpContext) {
      const configData = await configUpload('img')
      const uploadPath = 'uploads/post'
      return  upload_img_to_webp([],request,configData,'file_upload',uploadPath)

    }

    /**
     * @insertData
     * @paramQuery table - nama tabel  - @type(string)
     * @paramQuery dataPost - data Json - @type(json)
     */
    public async insertData({ request, response }:HttpContext) {
      const { table, dataPost } = request.only(['table', 'dataPost'])

      if (!table || !dataPost) {
        return response.status(400).send({ error: 'Invalid data' });
      }

      const data= JSON.parse(dataPost)

      const ins = `
      INSERT INTO ${table} (${Object.keys(data).join(', ')})
      VALUES (${Object.values(data).map(value => `'${value}'`).join(', ')})
      `

      const result = await prisma.$executeRawUnsafe(ins)

      return response.status(201).json({
        message: 'Data inserted successfully',
        result,
      })

    }
    /**
     * @insertData2
     * @paramQuery table - nama tabel  - @type(string)
     * @paramQuery dataPost - data Json - @type(json)
     */
    public async insertData2({ request, response }:HttpContext) {
      const { table, dataPost } = request.only(['table', 'dataPost'])

      if (!table || !dataPost) {
        return response.status(400).send({ error: 'Invalid data' });
      }
      // try {
        const Table = await globalTableValidator.validate({ table });
        const tableName = Table.table
        const validator =  getValidator(tableName)
        const data = await validator.validate(dataPost);
        const insert = await prisma[tableName].create({data:data});

        return response.status(201).json({
          message: 'Data inserted successfully',
          insert,
        })

      // } catch (err) {
      //   return response.status(400).send({ error: 'insert data failed', details: err });
      // }


    }
    /**
     * @updateData
     * @paramQuery table - nama tabel  - @type(string)
     * @paramQuery dataPost - data Json - @type(json)
     * @paramQuery idName - nama id - @type(string)
     * @paramQuery id - id - @type(number)
     */
    public async updateData({ request, response }: HttpContext) {
      const { table, dataPost, id ,idName} = request.only(['table', 'dataPost', 'id','idName']);

      // Validate input
      if (!table || !dataPost || !id) {
        return response.status(400).send({ error: 'Invalid data' });
      }

      const data = JSON.parse(dataPost);


      // Prepare the SET clause for the update query
      const setClause = Object.keys(data).map(key => `${key} = '${data[key]}'`).join(', ');

      // Construct the update query with direct value insertion
      const updateQuery = `
        UPDATE ${table}
        SET ${setClause}
        WHERE ${idName} = '${id}'
      `;

      try {
        // Execute the update query
        await prisma.$executeRawUnsafe(updateQuery);

        return response.status(200).json({
          message: 'Data updated successfully',
        });
      } catch (error) {
        console.error('Error updating data:', error);
        return response.status(500).json({
          message: 'Failed to update data',
          error: error.message,
        });
      }
    }


    /**
     * @deleteData
     * @paramQuery table - nama tabel  - @type(string)
     * @paramQuery idName - nama id - @type(string)
     * @paramQuery id - id - @type(number)
     */
    public async deleteData({ request, response }: HttpContext) {
      const { table, id ,idName} = request.only(['table', 'id','idName']);

      // Validate input
      if (!table || !id) {
        return response.status(400).send({ error: 'Invalid data' });
      }

      // Construct the delete query
      const deleteQuery = `
        DELETE FROM ${table}
        WHERE ${idName} = '${id}'
      `;


      try {
        // Execute the delete query
        await prisma.$executeRawUnsafe(deleteQuery);

        return response.status(200).json({
          message: 'Data deleted successfully',
        });
      } catch (error) {
        console.error('Error deleting data:', error);
        return response.status(500).json({
          message: 'Failed to delete data',
          error: error.message,
        });
      }
    }

    /**
     * @getAllData
     * @paramQuery table - nama tabel  - @type(string) @requires
     * @paramQuery page - halaman ke  - @type(string)
     * @paramQuery pageSize - jumlah per halaman - @type(string)
     * @paramQuery searchQuery - data pendarian - @type(string)
     * @paramQuery ids - Daftar ID dalam array - @type([string])
     * @paramQuery orderBy - Daftar ID dalam array - @type([string])
     * @paramQuery orderDirection - Daftar ID dalam array - @type([string])
     */
    public async getAllData({ request, response }: HttpContext) {
      const { table, page = 1, pageSize = 10, searchQuery = '', orderBy = '', orderDirection = 'ASC' } = request.only(['table', 'page', 'pageSize', 'searchQuery', 'orderBy', 'orderDirection']);

      // Calculate offset based on page and pageSize
      const offset = (page - 1) * pageSize;

      try {
        // Build the query to fetch columns from the table
        const queryColumn = `SELECT column_name
                            FROM information_schema.columns
                            WHERE table_name = '${table}';`

        const columns = await prisma.$queryRawUnsafe(queryColumn);

        // Final output
        let countQuery = `SELECT COUNT(*) AS totalRecords FROM ${table}`;

        // Add search condition if searchQuery is provided
        if (searchQuery) {
          const conditions = columns.map(col => {
            return `${col.column_name}::text LIKE '%${searchQuery}%'`;
          });
          countQuery += ` WHERE ${conditions.join(' OR ')}`;
        }

        // Execute the count query to get total number of records
        const countResult = await prisma.$queryRawUnsafe(countQuery);
        const totalRecords = parseInt(countResult[0]?.totalrecords) || 0;

        // Calculate total pages based on total records and page size
        const totalPages = Math.ceil(totalRecords / pageSize);

        // Build the main query for fetching data with pagination and search
        let query = `SELECT * FROM ${table}`;

        if (searchQuery) {
          const conditions = columns.map(col => {
            return `${col.column_name}::text LIKE '%${searchQuery}%'`;
          });
          query += ` WHERE ${conditions.join(' OR ')}`;
        }

        // Add ORDER BY clause if orderBy is provided
        if (orderBy) {
          query += ` ORDER BY ${orderBy} ${orderDirection === 'DESC' ? 'DESC' : 'ASC'}`;
        }

        // Add LIMIT and OFFSET for pagination
        query += ` LIMIT ${pageSize} OFFSET ${offset}`;

        // Execute the main query
        const data = await prisma.$queryRawUnsafe(query);

        return response.status(200).json({
          message: 'Data fetched successfully',
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: totalPages,
          totalRecords: totalRecords,
          data: data,
          searchQuery: searchQuery,
          orderBy: orderBy,
          orderDirection: orderDirection,
        });

      } catch (error) {
        console.error('Error fetching data:', error);
        return response.status(500).json({
          message: 'Failed to fetch data',
          error: error.message,
        });
      }
    }

    /**
     * @getReferensi
     */
    public async getReferensi({ params, response }: HttpContext) {
      const table= params.table
      try {
        if (!table || !/^[a-zA-Z0-9_]+$/.test(table)) {
          return response.status(400).json({
            success: false,
            message: 'Invalid table name'
          });
        }

        const data = await prisma.$queryRawUnsafe(`SELECT * FROM "${table}";`);
        return {
          success:true,
          data
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        return response.status(500).json({
          message: 'Failed to fetch data',
          error: error.message,
        });
      }
    }

    /**
     * @getReferensi
     */
   public async getSebaranKomoditi({ params, response }: HttpContext) {
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
    const id_prov = parseInt(params.id_prov) || 11;
    // const prov = await prisma.tb_adm_provinsi.findFirst({
    //   where: {
    //     id_adm_provinsi: id_prov // Ensure `id_prov` matches the type expected for `id_adm_provinsi`
    //   }
    // });
    const prov = await prisma.tb_adm_provinsi.findMany()

    let data = [];
    const komoditi = await prisma.tb_komoditi_nasional_ref.findMany({
    });

    for (const item2 of prov) {
        for (const item of komoditi) {
        // Generate the URL for each komoditi

        let url = `https://regionalinvestment.bkpm.go.id/api/api/Webs/GetSebaranKomoditiProvinsis?idKomoditi=${item.id_komoditi}&idProvinsi=${item2.id_adm_provinsi}`;

        try {
            // Fetch the data asynchronously
            const get = await fetch(url);
            const datas = await get.json();

            // Only push if data is available
            if (datas.data.length > 0) {
                data.push(...datas.data); // Spread operator to push all data entries into the array
            }
        } catch (error) {
            console.error(`Failed to fetch data for komoditi ${item.id_komoditi}:`, error);
        }
      }
    }

    // Prepare CSV Stringifier (in-memory generation of CSV)
    const csvStringifier = createObjectCsvStringifier({
        header: [
            { id: 'idKomoditi', title: 'ID Komoditi' },
            { id: 'idAdmProvinsi', title: 'ID Provinsi' },
            { id: 'idAdmKabkot', title: 'ID Kabupaten/Kota' },
            { id: 'sektor', title: 'Sektor' },
            { id: 'komoditi', title: 'Komoditi' },
            { id: 'provinsi', title: 'Provinsi' },
            { id: 'jenisKabkot', title: 'Jenis Kabupaten/Kota' },
            { id: 'kabkot', title: 'Kabupaten/Kota' },
            { id: 'tahunData', title: 'Tahun' },
            { id: 'nilaiProduksi', title: 'Nilai Produksi' },
            { id: 'satuan', title: 'Satuan' },
            { id: 'lat', title: 'Latitude' },
            { id: 'lon', title: 'Longitude' }
        ]
    });

    // Create CSV content from data
    const csvContent = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(data);

    // Set the response headers to force download
    response.header('Content-Type', 'text/csv');
    const filename = `sebaran_komoditi.csv`;
    response.header('Content-Disposition', `attachment; filename="${filename}"`);


    // Return the CSV file content
    response.send(csvContent);
    setTimeout(() => {
        response.send({ message: 'Download successful' });
    }, 1000); // Ganti 1000 dengan delay yang diinginkan dalam milidet
}

    /**
     * @getReferensi
     */
   public async updateTes({ request,params, response }: HttpContext) {
      let potensiDetail = request.input('potensi_detail')
      const configImg = await configUpload('img');
      const potensi = request.files('potensi', configImg);


      if (typeof potensiDetail === 'string') {
          potensiDetail = JSON.parse(potensiDetail);
      }
      if (potensi && Array.isArray(potensiDetail)) {
          // Jika panjang array JSON dan file gambar tidak sesuai, kembalikan error
          if (potensi.length !== potensiDetail.length) {
              return response.badRequest('Jumlah gambar dan detail tidak sesuai');
          }

          const uploadPromisesUpImg = potensi.map(async (item, index) => {
              const uploadPath = `uploads/tessektor/`;
              const upImg = await img_to_webp(item, uploadPath); // Mengupload gambar ke WebP
              const fileName = upImg.data.filename; // Mendapatkan nama file gambar

              // Menghubungkan gambar yang diupload dengan detail dari JSON
              const detail = potensiDetail[index]; // Mendapatkan detail JSON berdasarkan index

              // Lakukan proses insert ke database atau logika apapun yang kamu inginkan
              await prisma.tb_sektor_nasional_file.create({
                  data: {
                      id_sektor_nasional : 12,
                      tipe : 1,
                      jenis : 1,
                      nama: fileName, // Nama dari JSON
                      judul: detail.judul, // Judul dari JSON
                      keterangan: detail.keterangan, // Nama file yang sudah diupload
                  },
              });
          });

          await Promise.all(uploadPromisesUpImg); // Menjalankan semua upload dan insert secara paralel
      }
}

   public async postAllFiles({ request }: HttpContext) {
        const data = request.allFiles()
        return {data : data}
   }

  public async download({ params, response }: HttpContext) {
    const { id } = params;
    // const fixId = base64.decode(id)
    const fixId = Buffer.from(id, 'base64').toString('utf-8');
    const zipFilePath = path.join('uploads', `${fixId}`);
    const fileName = path.basename(zipFilePath);
    // return zipFilePath
    // Tentukan path file zip langsung berdasarkan ID
    // Periksa apakah file ada
    if (!fs.existsSync(zipFilePath)) {
      return response.status(404).send('File tidak ditemukan');
    }

    // Kirim file untuk di-download
    response.header('Content-Disposition', `attachment; filename=${fileName}`);
    return response.download(zipFilePath);
  }


  // public async get_status2({response}:HttpContext) {
  //   try {
  //     const result = await prisma.$queryRaw`
  //       SELECT table_name, data_type
  //       FROM information_schema.columns
  //       WHERE column_name = 'status'
  //       AND table_name LIKE '%tb_%'
  //       AND table_name not LIKE '%_file%'
  //       AND table_name not LIKE '%_layer_%'
  //       AND table_name not LIKE '%_blok_%'
  //       AND table_schema = 'public'
  //     `
  //     const hasil = await Promise.all(
  //       result.map(async (item) => {
  //         // Tentukan nilai status berdasarkan tipe data
  //         const statusValue = (item.data_type === 'character varying' || item.data_type === 'text') ? '2' : 2

  //         // Jalankan query dinamis untuk menghitung baris dengan status yang sesuai
  //         const cek = await prisma.$queryRawUnsafe(`
  //           SELECT COUNT(*) as count
  //           FROM ${item.table_name}
  //           WHERE status = $1
  //         `, statusValue)

  //         // Jika ada data yang sesuai, kembalikan nama tabel
  //         if (cek[0].count > 0) {
  //           return { name: item.table_name }
  //         }
  //         return null // Kembalikan null jika tidak ada data
  //       })
  //     )

  //     // Filter hasil untuk menghapus null dan kembalikan hanya tabel yang memiliki data
  //     const filteredHasil = hasil.filter((item) => item !== null)

  //     return response.json(filteredHasil)

  //   } catch (error) {
  //     return response.status(500).json({ error: error.message })
  //   }
  // }
}

