import type { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'

import { configUpload, get_paginate, img_to_webp, upload, upload_img_to_webp } from '../helpers/global_helper.js';
import { createPeluangValidator } from '#validators/peluang';
import { createSektorNasionalValidator, updateSektorNasionalValidator, updateSNasionalValidator } from '#validators/sektor_nasional';
import { aproveValidator } from '#validators/aprove';
import { auth } from '@adonisjs/auth'
import env from '#start/env';
import striptags from 'striptags';

export default class SektorNasionalsController {

    /**
     * @sektor_nasional_detail
     * @summary List Detail Data Sektor Unggulan Nasional
     */
    public async sektor_nasional_detail({ params ,response}: HttpContext) {
        const Id = parseInt(params.id, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }
        const peluang = await prisma.tb_peluang_daerah.findMany({
            where: {
                id_adm_provinsi: Id,
            },
        })
        const umr = await prisma.tb_umr_provinsi.findMany({
            where: {
                id_adm_provinsi: Id,
            },
            orderBy:{
                tahun: 'desc',
            }
        })

        const pdrb = await prisma.tb_sektor_daerah_pdrb.findMany({
            where: {
                id_adm_provinsi: Id,
            },
            orderBy:{
                tahun_pdrb: 'desc',
            }
        })
        const pdrbNilai = pdrb.reduce((sum, record) => {
            const jumlah = record.jumlah_pdrb ? record.jumlah_pdrb.toNumber() : 0;
            return sum + jumlah;
        }, 0);


        const data ={
            peluang:peluang.length,
            umr : umr.length > 0 ? umr[0].nilai : null,
            jumlah_penduduk:null ,
            prdb:pdrbNilai,
            realisasi_investasi:null,

        }
        return {
        success : true,
        data : data
        }
    }

    /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.tb_sektor_nasional.findMany({
        take: 100,
        include: {
            sektor_nasional_kontak: true, // Ganti dengan nama relasi yang benar
            sektor_nasional_value: {
                include: {
                    sektor_nasional_value_detail: true,
                    sektor_nasional_value_tr: true
                }
            }
        }
    });

    return {
        success : true,
        data : {data}
    }
  }

  async get_paginate({params,response,request}:HttpContext) {
    const parameters =  request.qs()
    let order = parameters.order
    const by = ['asc','desc'].includes(parameters.by) ? parameters.by : 'asc'
    let orderBy={}
    const paramList = ['status','nama_sektor']
    if (order != undefined && paramList.includes(order)) {
        if (order == 'nama_sektor') {
            orderBy = {sektor:{nama:by}}
        }else{
            orderBy = {[order]:by}
        }
    }
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
        //   { potensi_pasar: { contains: search.replaceAll('%20',' '), mode: 'insensitive' } }, // Search in tb_app_slider.judul
          { potensi_pasar: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
          { deskripsi_singkat: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
          {
            sektor: {
              nama:  { contains: search, mode: 'insensitive' }, // Search in tb_app_slider_tr.deskripsi
            },
          },
        ],
      };
    }
    const req = request.qs()
    if (req.status) {
        (searchCondition.AND ??= []).push(
          { status: parseInt(req.status) }
  
        );
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_sektor_nasional.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_sektor_nasional.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      orderBy:orderBy,
      where: searchCondition, // Apply searchCondition only if it's not empty
      include: {
            sektor_nasional_kontak: true, // Ganti dengan nama relasi yang benar
            sektor_nasional_value: {
                include: {
                    sektor_nasional_value_detail: true,
                    sektor_nasional_value_tr: true
                }
            },
            sektor_nasional_file:true,
            sektor:true
        }
    });
    const data = datas.map((item =>{
        const status_text = item.status == 99 ? "Aprove" : "Delete"
        return {
            id_sektor_nasional : item.id_sektor_nasional,
            nama_sektor : item.sektor.nama,
            potensi_pasar : striptags(item.potensi_pasar),
            deskripsi_singkat : striptags(item.deskripsi_singkat),
            deskripsi : striptags(item.deskripsi),
            status:item.status,
            status_text
        }
    }))


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }

    /**
     * @store
     * @paramQuery id_adm_kabkot - id_adm_kabkot - @type(number) @required
     * @paramQuery id_adm_provinsi - id_adm_provinsi - @type(number) @required
     * @paramQuery nama - id_sektor - @type(string)  @required
     * @paramQuery bidang_usaha - tahun - @type(string) @required
     * @paramQuery email - email - @type(string) @required
     * @paramQuery alamat - alamat - @type(string) @required
     * @paramQuery nama_kabkot - nama kabkot - @type(string)
     * @paramQuery nama_provinsi - nama provinsi - @type(string)
     * @paramQuery image - image - @type(file)
     * @paramQuery lon - lon - @type(number)
     * @paramQuery lat - lat - @type(number)
     */

    public async store({ request, response ,auth}: HttpContext) {

      const data = request.all();


      let datas = await request.validateUsing(createSektorNasionalValidator)
      let parameterData = request.input('parameter_data')
      let kontakData = request.input('kontak')
      let potensiDetail = request.input('potensi_detail')
      let infografisDetail = request.input('infografis_detail')
      let sumberData = request.input('sumber_data');
        try {
            const configImg = await configUpload('img');

            let icon_name;
            let cover_name;
            let icon_name_tr;
            let cover_name_tr;

            const icon = request.files('icon', configImg);
            const icon_tr = request.files('icon_tr', configImg);
            if (icon) {
                const uploadPromisesUpImg = icon.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    icon_name = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }
            if (icon_tr) {
                const uploadPromisesUpImg = icon_tr.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    icon_name_tr = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }

            const cover = request.files('cover', configImg);
            const cover_tr = request.files('cover_tr', configImg);
            if (cover) {
                const uploadPromisesUpImg = cover.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    cover_name = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }
            if (cover_tr) {
                const uploadPromisesUpImg = cover_tr.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    cover_name_tr = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }

            const potensi = request.files('potensi', configImg);
            const potensiTr = request.files('potensi_tr', configImg);
            const infografis = request.files('infografis', configImg);
            const infografisTr = request.files('infografis_tr', configImg);

            datas['file_icon'] = icon_name;
            datas['file_image'] = cover_name;

            // Jalankan semua operasi database dalam transaction Prisma
            await prisma.$transaction(async (prisma) => {
                // Insert pertama untuk tb_sektor_nasional
                const dataPost = {
                    id_sektor_nasional : datas.id_sektor_nasional,
                    deskripsi : datas.deskripsi,
                    deskripsi_singkat : datas.deskripsi_singkat,
                    potensi_pasar : datas.potensi_pasar,
                    status : 0,
                    file_icon : icon_name,
                    file_image : cover_name,
                }

                const insert = await prisma.tb_sektor_nasional.create({ data: dataPost });
                const id_sektor_nasional = insert.id_sektor_nasional;
                const dataPostTr = {
                    id_sektor_nasional : id_sektor_nasional,
                    deskripsi : datas.deskripsi_tr,
                    deskripsi_singkat : datas.deskripsi_singkat_tr,
                    potensi_pasar : datas.potensi_pasar_tr || '-',
                    file_icon : icon_name_tr,
                    file_image : cover_name_tr,
                    kd_bahasa : "en"
                }
                const insertTr = await prisma.tb_sektor_nasional_tr.create({ data: dataPostTr });

                if (typeof sumberData === 'string') {
                    sumberData = JSON.parse(sumberData);
                }

                if (Array.isArray(sumberData) && sumberData.length > 0) {
                    await Promise.all(
                        sumberData.map(id =>
                            prisma.tb_sektor_nasional_sumber_data.create({
                                data: {
                                    id_sektor_nasional: id_sektor_nasional,
                                    id_sumber_data: parseInt(id),
                                },
                            })
                        )
                    );
                }


                if (typeof kontakData === 'string') {
                    kontakData = JSON.parse(kontakData);
                }
                kontakData['id_sektor_nasional'] = id_sektor_nasional;
                kontakData.status = "99";
                kontakData.lon = parseFloat(kontakData.lon);
                kontakData.lat = parseFloat(kontakData.lat);

                // Insert untuk tb_sektor_nasional_kontak
                await prisma.tb_sektor_nasional_kontak.create({ data: kontakData });

                if (typeof parameterData === 'string') {
                    parameterData = JSON.parse(parameterData);
                }

                for (const el of parameterData) {
                    const value = {
                        id_sektor_nasional: id_sektor_nasional,
                        nama: el.nama,
                        tipe: 1,
                        satuan: el.satuan,
                    };

                    const insertValue = await prisma.tb_sektor_nasional_value.create({ data: value });
                    const value_tr = {
                        id_sektor_nasional_value: insertValue?.id_sektor_nasional_value,
                        nama: el.nama_tr,
                        satuan: el.satuan_tr,
                        kd_bahasa:"en"
                    };
                    await prisma.tb_sektor_nasional_value_tr.create({ data: value_tr });

                    // Insert untuk tb_sektor_nasional_value_detail
                    for (const el2 of el.detail) {
                        const value_detail = {
                            id_sektor_nasional_value: insertValue?.id_sektor_nasional_value,
                            tahun: parseInt(el2.tahun),
                            numeric_value: parseFloat(el2.numeric_value),
                        };

                        await prisma.tb_sektor_nasional_value_detail.create({ data: value_detail });
                    }
                }
                if (typeof potensiDetail === 'string') {
                    potensiDetail = JSON.parse(potensiDetail);
                }
                if (potensi && Array.isArray(potensiDetail)) {
                    // Validasi jumlah array
                    if (potensi.length !== potensiDetail.length) {
                        return response.badRequest('Jumlah gambar dan detail tidak sesuai');
                    }

                    // Proses upload dan insert data
                    const uploadPromisesUpImg = potensi.map(async (item, index) => {
                        const uploadPath = `uploads/sektor/`;
                        const upImg = await img_to_webp(item, uploadPath); // Upload file utama ke WebP
                        const fileName = upImg.data.filename; // Nama file utama yang diupload
                        const detail = potensiDetail[index]; // Detail berdasarkan indeks

                        // Insert data ke tabel utama
                        const insertedFile = await prisma.tb_sektor_nasional_file.create({
                            data: {
                                id_sektor_nasional: id_sektor_nasional,
                                tipe: 1,
                                jenis: 1,
                                nama: fileName,
                                judul: detail.judul,
                                keterangan: detail.keterangan,
                            },
                        });

                        // Jika ada data `tr` dalam detail
                        if (detail.tr && potensiTr) {
                            const trMatch = potensiTr.find(trItem => trItem.clientName === detail.tr.fileName); // Mencocokkan fileName

                            if (trMatch) {
                                const trFileName = trMatch.fileName === detail.fileName
                                    ? fileName // Gunakan file utama jika sama
                                    : (await img_to_webp(trMatch, uploadPath)).data.filename; // Upload file baru jika berbeda

                                // Insert ke tabel translate
                                await prisma.tb_sektor_nasional_file_tr.create({
                                    data: {
                                        id_sektor_nasional_file: insertedFile.id_sektor_nasional_file, // Relasi ke file utama
                                        kd_bahasa: "en", // Sesuaikan kode bahasa
                                        nama: trFileName,
                                        judul: detail.tr.judul,
                                        keterangan: detail.tr.keterangan,
                                    },
                                });
                            } else {
                                console.log(`File translate tidak ditemukan untuk ${detail.tr.fileName}`);
                            }
                        }
                    });

                    await Promise.all(uploadPromisesUpImg); // Jalankan semua proses secara paralel
                }


                if (typeof infografisDetail === 'string') {
                    infografisDetail = JSON.parse(infografisDetail);
                }
                if (infografis && Array.isArray(infografisDetail)) {
                        // Validasi jumlah array
                    if (infografis.length !== infografisDetail.length) {
                        return response.badRequest('Jumlah gambar dan detail tidak sesuai');
                    }

                    // Upload file gambar dari infografis
                    const uploadPromisesUpImg = infografis.map(async (item, index) => {
                        const uploadPath = `uploads/sektor/`;
                        const upImg = await img_to_webp(item, uploadPath); // Upload file
                        const fileName = upImg.data.filename; // Nama file yang diunggah
                        const detail = infografisDetail[index]; // Detail JSON berdasarkan indeks

                        // Insert ke database untuk infografis utama
                        const insertedFile = await prisma.tb_sektor_nasional_file.create({
                            data: {
                                id_sektor_nasional: id_sektor_nasional,
                                tipe: 1,
                                jenis: 2,
                                nama: fileName,
                                judul: detail.judul,
                                keterangan: detail.keterangan,
                            },
                        });

                        // Loop untuk bagian translate jika ada
                        if (detail.tr && infografisTr) {
                            const trMatch = infografisTr.find(trItem => trItem.clientName === detail.tr.fileName);
                            if (trMatch) {
                                const trFileName = trMatch.fileName === detail.fileName
                                    ? fileName // Gunakan file utama jika sama
                                    : (await img_to_webp(trMatch, uploadPath)).data.filename; // Upload file baru jika berbeda

                                // Insert ke tabel translate
                                await prisma.tb_sektor_nasional_file_tr.create({
                                    data: {
                                        id_sektor_nasional_file: insertedFile.id_sektor_nasional_file, // Relasi ke file utama
                                        kd_bahasa: "en", // Ganti dengan kode bahasa yang sesuai
                                        nama: trFileName,
                                        judul: detail.tr.judul,
                                        keterangan: detail.tr.keterangan,
                                    },
                                });
                            } else {
                                console.log(`File translate tidak ditemukan untuk ${detail.tr.fileName}`);
                            }
                        }
                    });

                    await Promise.all(uploadPromisesUpImg); // Jalankan semua secara paralel
                }


                response.status(201).json({ success: true,data:insert })
            });
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

    /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_sektor_nasional.findUnique({
        where: {
            id_sektor_nasional: Id,
        },
        include: {
            sektor_nasional_kontak: true, // Ganti dengan nama relasi yang benar
            sektor_nasional_value: {
                include: {
                    sektor_nasional_value_detail: true,
                    sektor_nasional_value_tr: true
                }
            },
            sektor_nasional_file:{
                include:{
                    sektor_nasional_file_tr:true
                }
            },
            sektor_nasional_sumber_data:true,
            sektor_nasional_tr:true
            }
        });

        if (data) {
            data.file_icon = `${env.get('APP_URL')}/uploads/sektor/${data.file_icon}`
            data.file_image = `${env.get('APP_URL')}/uploads/sektor/${data.file_image}`
            if (data.sektor_nasional_tr) {
                data.sektor_nasional_tr = data.sektor_nasional_tr
                                            .map((item) => {
                                                return {...item,
                                                    file_icon : `${env.get('APP_URL')}/uploads/sektor/${item.file_icon}`,
                                                    file_image : `${env.get('APP_URL')}/uploads/sektor/${item.file_image}`
                                                }
                                            })
            }
            if (data.sektor_nasional_file) {
                data.sektor_nasional_file = data.sektor_nasional_file
                                            .map((item) => {
                                                let fileTr = []
                                                if (item.sektor_nasional_file_tr) {
                                                    item.sektor_nasional_file_tr.map((items) =>
                                                            fileTr.push({
                                                                ...items,
                                                                nama : `${env.get('APP_URL')}/uploads/sektor/${items.nama}`,
                                                            })
                                                    )
                                                }
                                                return {
                                                    ...item,
                                                    nama : `${env.get('APP_URL')}/uploads/sektor/${item.nama}`,
                                                    sektor_nasional_file_tr : fileTr


                                                }
                                            })

            }
        }

        return {
            success : true,
            data : {data}
        }
  }


  /**
     * @update
     * @paramQuery id_adm_kabkot - id_adm_kabkot - @type(number) @required
     * @paramQuery id_adm_provinsi - id_adm_provinsi - @type(number) @required
     * @paramQuery nama - id_sektor - @type(string)  @required
     * @paramQuery bidang_usaha - tahun - @type(string) @required
     * @paramQuery email - email - @type(string) @required
     * @paramQuery alamat - alamat - @type(string) @required
     * @paramQuery nama_kabkot - nama kabkot - @type(string)
     * @paramQuery nama_provinsi - nama provinsi - @type(string)
     * @paramQuery image - image - @type(file)
     * @paramQuery lon - lon - @type(number)
     * @paramQuery lat - lat - @type(number)
     */
    async update({ params, request ,response}: HttpContext) {

      const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }

      const data = request.all();
      let datas = await request.validateUsing(updateSNasionalValidator)
      let parameterData = request.input('parameter_data')
      let kontakData = request.input('kontak')
      let potensiDetail = request.input('potensi_detail')
      let infografisDetail = request.input('infografis_detail')
      let sumberData = request.input('sumber_data')
      let tr = request.input('tr')
        const configImg = await configUpload('img');
        const potensi = request.files('potensi', configImg);
        const potensiTr = request.files('potensi_tr', configImg);
        const infografis = request.files('infografis', configImg);
        const infografisTr = request.files('infografis_tr', configImg);

        try {

            let icon_name;
            let cover_name;
            let icon_name_tr;
            let cover_name_tr;

            const icon = request.files('icon', configImg);
            if (icon) {
                const uploadPromisesUpImg = icon.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    icon_name = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }

            const iconTr = request.files('icon_tr', configImg);
            if (iconTr) {
                const uploadPromisesUpImg = iconTr.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                   icon_name_tr = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }

            const cover = request.files('cover', configImg);
            if (cover) {
                const uploadPromisesUpImg = cover.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    cover_name = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }
            const coverTr = request.files('cover_tr', configImg);
            if (coverTr) {
                const uploadPromisesUpImg = coverTr.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    cover_name_tr = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }


            if (typeof tr === 'string') {
                tr = JSON.parse(tr);
            }

            // Jalankan semua operasi database dalam transaction Prisma
            await prisma.$transaction(async (prisma) => {
                // Insert pertama untuk tb_sektor_nasional
                const dataPost = {
                    deskripsi : datas.deskripsi,
                    deskripsi_singkat : datas.deskripsi_singkat,
                    potensi_pasar : datas.potensi_pasar,
                    file_icon : icon_name,
                    file_image : cover_name,
                    status : 0
                }

                const update = await prisma.tb_sektor_nasional.update({
                    where : {
                        id_sektor_nasional: Id,
                    },
                    data: dataPost },
                );

                const dataPostTr = {
                    deskripsi : tr.deskripsi,
                    deskripsi_singkat : tr.deskripsi_singkat,
                    potensi_pasar : tr.potensi_pasar,
                    file_icon : icon_name_tr,
                    file_image : cover_name_tr,
                }
                const updateTr = await prisma.tb_sektor_nasional_tr.update({
                    where : {
                        id_sektor_nasional_tr: parseInt(tr.id_sektor_nasional_tr),
                    },
                    data: dataPostTr },

                );
                const id_sektor_nasional = update.id_sektor_nasional;
                if (typeof sumberData === 'string') {
                    sumberData = JSON.parse(sumberData);
                }


                if (Array.isArray(sumberData) && sumberData.length > 0) {
                    // 1. Ambil semua id_sumber_data yang sudah ada di database
                    const existingData = await prisma.tb_sektor_nasional_sumber_data.findMany({
                        where: {
                            id_sektor_nasional: id_sektor_nasional,
                        },
                        select: {
                            id_sumber_data: true, // Ambil hanya kolom id_sumber_data
                        },
                    });

                    // 2. Buat array berisi id_sumber_data yang sudah ada
                    const existingIds = existingData.map(item => item.id_sumber_data);

                    // 4. Tambahkan data baru


                    // 5. Hapus data yang tidak ada di sumberData
                    if (existingIds.length > 0) {
                        await prisma.tb_sektor_nasional_sumber_data.deleteMany({
                            where: {
                                id_sektor_nasional: id_sektor_nasional,
                                id_sumber_data: { in: existingIds },
                            },
                        });
                    }

                    if (sumberData.length > 0) {
                        await Promise.all(
                            sumberData.map(id =>
                                prisma.tb_sektor_nasional_sumber_data.create({
                                    data: {
                                        id_sektor_nasional: id_sektor_nasional,
                                        id_sumber_data: parseInt(id),
                                    },
                                })
                            )
                        );
                    }
                }

                if (typeof kontakData === 'string') {
                    kontakData = JSON.parse(kontakData);
                }
                kontakData['id_sektor_nasional'] = id_sektor_nasional;
                kontakData.status = "99";
                kontakData.lon = parseFloat(kontakData.lon);
                kontakData.lat = parseFloat(kontakData.lat);
                kontakData.id_sektor_nasional_kontak = parseInt(kontakData.id_sektor_nasional_kontak)

                // Insert untuk tb_sektor_nasional_kontak
                await prisma.tb_sektor_nasional_kontak.update(
                    {
                        where :{
                            id_sektor_nasional_kontak : parseInt(kontakData.id_sektor_nasional_kontak)
                        },
                         data: kontakData
                    }
                );

                if (typeof parameterData === 'string') {
                    parameterData = JSON.parse(parameterData);
                }

                // Hapus data di tb_sektor_nasional_value yang tidak ada di parameterData
                const existingValues = await prisma.tb_sektor_nasional_value.findMany({
                    where: { id_sektor_nasional: id_sektor_nasional },
                    include: {
                        sektor_nasional_value_detail: true,
                    },
                });

                const parameterValueIds = parameterData
                    .filter(el => parseInt(el.id_sektor_nasional_value))
                    .map(el => parseInt(el.id_sektor_nasional_value));

                const valuesToDelete = existingValues.filter(el => !parameterValueIds.includes(el.id_sektor_nasional_value));

                for (const value of valuesToDelete) {
                    // Hapus detail terlebih dahulu
                    await prisma.tb_sektor_nasional_value_detail.deleteMany({
                        where: { id_sektor_nasional_value: value.id_sektor_nasional_value },
                    });

                    // Hapus value
                    await prisma.tb_sektor_nasional_value.delete({
                        where: { id_sektor_nasional_value: value.id_sektor_nasional_value },
                    });
                }

                // Hapus data di tb_sektor_nasional_value_detail yang tidak ada di el.detail
                for (const el of parameterData) {
                    if (el.id_sektor_nasional_value) {
                        const existingDetails = await prisma.tb_sektor_nasional_value_detail.findMany({
                            where: { id_sektor_nasional_value: parseInt(el.id_sektor_nasional_value) },
                        });

                        const detailIds = el.detail
                            .filter(el2 => parseInt(el2.id_sektor_nasional_value_detail))
                            .map(el2 => parseInt(el2.id_sektor_nasional_value_detail));

                        const detailsToDelete = existingDetails.filter(detail => !detailIds.includes(detail.id_sektor_nasional_value_detail));

                        for (const detail of detailsToDelete) {
                            await prisma.tb_sektor_nasional_value_detail.delete({
                                where: { id_sektor_nasional_value_detail: detail.id_sektor_nasional_value_detail },
                            });
                        }
                    }
                }

                for (const el of parameterData) {
                    const value = {
                        id_sektor_nasional: id_sektor_nasional,
                        nama: el.nama,
                        tipe: 1,
                        satuan: el.satuan,
                    };

                    let insertValue;

                    // Cek jika id_sektor_nasional_value ada, lakukan update, jika tidak lakukan insert
                    if (!el.id_sektor_nasional_value) {
                        insertValue = await prisma.tb_sektor_nasional_value.create({
                            data: value,
                        });
                    } else {
                        const valExist = await prisma.tb_sektor_nasional_value.findUnique({
                            where: {
                                id_sektor_nasional_value: parseInt(el.id_sektor_nasional_value)
                            }
                        });
                        if (!valExist) {
                                return response.status(500).json({ success: false, message: `id_sektor_nasional_value ${el.id_sektor_nasional_value} Required!`,data:el })

                        }
                        insertValue = await prisma.tb_sektor_nasional_value.update({
                            where: { id_sektor_nasional_value: parseInt(el.id_sektor_nasional_value) },
                            data: value,
                        });
                    }

                    // Insert atau update tb_sektor_nasional_value_detail
                    for (const el2 of el.detail) {
                        const value_detail = {
                            id_sektor_nasional_value: parseInt(insertValue?.id_sektor_nasional_value),
                            tahun: parseInt(el2.tahun),
                            numeric_value: parseFloat(el2.numeric_value),
                        };

                        if (el2.id_sektor_nasional_value_detail) {
                            const valExist = await prisma.tb_sektor_nasional_value_detail.findUnique({
                                where: {
                                    id_sektor_nasional_value_detail: parseInt(el2.id_sektor_nasional_value_detail)
                                }
                            });
                            if (!valExist) {
                                    return response.status(500).json({ success: false, message: `id_sektor_nasional_value_detail ${el2.id_sektor_nasional_value} Required!`,data:el2 })

                            }
                            await prisma.tb_sektor_nasional_value_detail.update({
                                where: {
                                    id_sektor_nasional_value_detail: parseInt(el2.id_sektor_nasional_value_detail),
                                },
                                data: value_detail,
                            });
                        } else {
                            await prisma.tb_sektor_nasional_value_detail.create({
                                data: value_detail,
                            });
                        }
                    }
                    if (el.tr) {
                        const value_tr = {
                            id_sektor_nasional_value: parseInt(insertValue?.id_sektor_nasional_value),
                            nama: el.tr.nama,
                            satuan:el.tr.satuan,
                            kd_bahasa:"en"
                        };

                        if (el.tr.id_sektor_nasional_value_tr) {
                            const valExist = await prisma.tb_sektor_nasional_value_tr.findUnique({
                                where: {
                                    id_sektor_nasional_value_tr: parseInt(el.tr.id_sektor_nasional_value_tr)
                                }
                            });
                            if (!valExist) {
                                    return response.status(500).json({ success: false, message: `id_sektor_nasional_value_tr ${el.tr.id_sektor_nasional_value} Required!`,data:el.tr })

                            }
                            await prisma.tb_sektor_nasional_value_tr.update({
                                where: {
                                    id_sektor_nasional_value_tr: parseInt(el.tr.id_sektor_nasional_value_tr),
                                },
                                data: value_tr,
                            });
                        } else {
                            await prisma.tb_sektor_nasional_value_tr.create({
                                data: value_tr,
                            });
                        }
                    }
                }



                if (typeof potensiDetail === 'string') {
                    potensiDetail = JSON.parse(potensiDetail);
                }
                // return response.status(201).json({ success: true,data:potensi })

                if (potensiDetail && Array.isArray(potensiDetail)) {
                    // Ambil semua data yang sudah ada di database berdasarkan id_sektor_nasional
                    const existingFiles = await prisma.tb_sektor_nasional_file.findMany({
                        where: {
                            id_sektor_nasional: id_sektor_nasional,
                            jenis: 1 // Sesuaikan jenis jika diperlukan
                        }
                    });

                    // Buat array dari ID file yang ada di detail saat ini
                    const currentFileIds = potensiDetail
                        .filter(detail => parseInt(detail.id_sektor_nasional_file)) // Pastikan id_sektor_nasional_file ada
                        .map(detail => parseInt(detail.id_sektor_nasional_file)); // Ambil ID dari detail

                    // Cari ID yang ada di database tapi tidak ada di `currentFileIds`
                    const filesToDelete = existingFiles.filter(existingFile => !currentFileIds.includes(existingFile.id_sektor_nasional_file));

                    // Hapus file yang tidak ada di data detail saat ini
                    if (filesToDelete.length > 0) {
                        const deletePromises = filesToDelete.map(file =>
                            prisma.tb_sektor_nasional_file.delete({
                                where: { id_sektor_nasional_file: file.id_sektor_nasional_file }
                            })
                        );
                        await Promise.all(deletePromises); // Hapus semua file yang tidak ada di potensiDetail
                    }
                    // Lanjutkan dengan proses upload dan update/insert
                    for (const detail of potensiDetail) {
                        // Cari padanan gambar di potensi berdasarkan nama file yang cocok
                        const item = potensi.find(pot => pot.clientName === detail.fileName);

                        let id_sektor_nasional_file = 0
                        // Jika ada gambar yang cocok di potensi, upload dan ambil nama file baru
                        if (item) {
                            const uploadPath = `uploads/sektor/`;
                            const upImg = await img_to_webp(item, uploadPath); // Mengupload gambar ke WebP
                            if (detail.id_sektor_nasional_file) {
                                id_sektor_nasional_file = detail.id_sektor_nasional_file
                                // Jika `id_sektor_nasional_file` ada, lakukan update
                                await prisma.tb_sektor_nasional_file.update({
                                    where: {
                                        id_sektor_nasional_file: parseInt(detail.id_sektor_nasional_file), // Gunakan ID dari detail
                                    },
                                    data: {
                                        id_sektor_nasional: id_sektor_nasional,
                                        tipe: 1,
                                        jenis: 1,
                                        nama: upImg.data.filename, // Nama file dari hasil upload atau default
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                            } else {
                                const insert = await prisma.tb_sektor_nasional_file.create({
                                    data: {
                                        id_sektor_nasional: id_sektor_nasional,
                                        tipe: 1,
                                        jenis: 1,
                                        nama: upImg.data.filename, // Nama file baru atau dari detail
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                                id_sektor_nasional_file = insert.id_sektor_nasional_file
                            }

                        }else{


                            if (detail.id_sektor_nasional_file) {
                                const sektorFile = await prisma.tb_sektor_nasional_file.findMany({
                                    where: {
                                        id_sektor_nasional_file: parseInt(detail.id_sektor_nasional_file)
                                    }
                                });
                                if (!sektorFile) {
                                // Jika record tidak ditemukan, lemparkan error atau tangani sesuai logika Anda
                                throw new Error(` id_sektor_nasional_file ${detail.id_sektor_nasional_file} not found`);
                                }
                                await prisma.tb_sektor_nasional_file.update({
                                    where: {
                                        id_sektor_nasional_file: parseInt(detail.id_sektor_nasional_file), // Gunakan ID dari detail
                                    },
                                    data: {
                                        id_sektor_nasional: id_sektor_nasional,
                                        tipe: 1,
                                        jenis: 1,
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                            } else {
                                return response.status(500).json({ success: false, message: 'File Gambar Potensi Required!',data:detail })

                            }
                        }

                        if (detail.tr && potensiTr) {
                            const trMatch = potensiTr.find(trItem => trItem.clientName === detail.tr.fileName);

                            if (trMatch) {
                                const uploadPath = `uploads/sektor/`;
                                // Periksa apakah fileName sama atau upload file baru jika berbeda
                                const trFileName = trMatch.fileName === detail.fileName
                                ? trMatch.fileName // Gunakan file utama jika sama
                                : (await img_to_webp(trMatch, uploadPath)).data.filename; // Upload file baru jika berbeda

                                if (detail.tr.id_sektor_nasional_file_tr) {
                                // UPDATE jika membawa id_sektor_nasional_file_tr
                                await prisma.tb_sektor_nasional_file_tr.update({
                                    where: { id_sektor_nasional_file_tr: parseInt(detail.tr.id_sektor_nasional_file_tr) },
                                    data: {
                                    nama: trFileName,
                                    judul: detail.tr.judul,
                                    keterangan: detail.tr.keterangan,
                                    },
                                });
                                console.log(`File translate ID ${detail.tr.id_sektor_nasional_file_tr} berhasil diupdate`);
                                } else {
                                // CREATE jika tidak membawa id_sektor_nasional_file_tr
                                await prisma.tb_sektor_nasional_file_tr.create({
                                    data: {
                                    id_sektor_nasional_file: id_sektor_nasional_file, // Relasi ke file utama
                                    kd_bahasa: "en", // Ganti dengan kode bahasa yang sesuai
                                    nama: trFileName,
                                    judul: detail.tr.judul,
                                    keterangan: detail.tr.keterangan,
                                    },
                                });
                                console.log("File translate baru berhasil ditambahkan");
                                }
                            } else {
                                 await prisma.tb_sektor_nasional_file_tr.update({
                                    where: { id_sektor_nasional_file_tr: parseInt(detail.tr.id_sektor_nasional_file_tr) },
                                    data: {
                                    judul: detail.tr.judul,
                                    keterangan: detail.tr.keterangan,
                                    },
                                });
                                console.log(`File translate tidak ditemukan untuk ${detail.tr.fileName}`);
                            }
                        }

                    }
                }


                if (typeof infografisDetail === 'string') {
                    infografisDetail = JSON.parse(infografisDetail);
                }
                if (infografisDetail && Array.isArray(infografisDetail)) {
                    // Ambil semua data yang sudah ada di database berdasarkan id_sektor_nasional
                    const existingFiles = await prisma.tb_sektor_nasional_file.findMany({
                        where: {
                            id_sektor_nasional: id_sektor_nasional,
                            jenis: 2 // Sesuaikan jenis jika dibutuhkan
                        }
                    });

                    // Buat array dari ID file yang ada di detail saat ini
                    const currentFileIds = infografisDetail
                        .filter(detail => parseInt(detail.id_sektor_nasional_file))
                        .map(detail => parseInt(detail.id_sektor_nasional_file));

                    // Cari ID yang ada di database tapi tidak ada di `currentFileIds`
                    const filesToDelete = existingFiles.filter(existingFile => !currentFileIds.includes(existingFile.id_sektor_nasional_file));

                    // Hapus file yang tidak ada di data detail saat ini
                    if (filesToDelete.length > 0) {
                        const deletePromises = filesToDelete.map(file =>
                            prisma.tb_sektor_nasional_file.delete({
                                where: { id_sektor_nasional_file: file.id_sektor_nasional_file }
                            })
                        );
                        await Promise.all(deletePromises);
                    }

                    // Lanjutkan dengan proses upload dan update/insert
                    const uploadPromisesUpImg = infografisDetail.map(async (detail) => {
                        // Cari padanan gambar di infografis berdasarkan nama file yang cocok
                        const item = infografis.find(infografisItem => infografisItem.clientName === detail.fileName);

                        let fileName = detail.fileName; // Default value if no matching image is found
                        let id_sektor_nasional_file = 0
                        // Jika ada gambar yang cocok di infografis, upload dan ambil nama file baru
                        if (item) {
                            const uploadPath = `uploads/sektor/`;
                            const upImg = await img_to_webp(item, uploadPath); // Mengupload gambar ke WebP
                            fileName = upImg.data.filename; // Mendapatkan nama file gambar yang diupload
                            if (detail.id_sektor_nasional_file) {
                                id_sektor_nasional_file = detail.id_sektor_nasional_file
                                // Jika `id_sektor_nasional_file` ada, lakukan update
                                await prisma.tb_sektor_nasional_file.update({
                                    where: {
                                        id_sektor_nasional_file: parseInt(detail.id_sektor_nasional_file),
                                    },
                                    data: {
                                        id_sektor_nasional: id_sektor_nasional,
                                        tipe: 1,
                                        jenis: 2,
                                        nama: fileName, // Nama file baru jika ada upload, atau dari detail jika tidak ada
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                            } else {
                                // Jika `id_sektor_nasional_file` tidak ada, lakukan insert
                                const insert = await prisma.tb_sektor_nasional_file.create({
                                    data: {
                                        id_sektor_nasional: id_sektor_nasional,
                                        tipe: 1,
                                        jenis: 2,
                                        nama: fileName, // Nama file baru atau dari detail
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                                id_sektor_nasional_file = insert.id_sektor_nasional_file
                            }
                        }else{
                            if (detail.id_sektor_nasional_file) {
                                // Jika `id_sektor_nasional_file` ada, lakukan update
                                await prisma.tb_sektor_nasional_file.update({
                                    where: {
                                        id_sektor_nasional_file: parseInt(detail.id_sektor_nasional_file),
                                    },
                                    data: {
                                        id_sektor_nasional: id_sektor_nasional,
                                        tipe: 1,
                                        jenis: 2,
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                            } else {
                                return response.status(500).json({ success: false, message: 'File Infografis Required!' })
                            }
                        }

                        if (detail.tr && infografisTr) {
                            const trMatch = infografisTr.find(trItem => trItem.clientName === detail.tr.fileName);

                            if (trMatch) {
                                const uploadPath = `uploads/sektor/`;
                                // Periksa apakah fileName sama atau upload file baru jika berbeda
                                const trFileName = trMatch.fileName === detail.fileName
                                ? trMatch.fileName // Gunakan file utama jika sama
                                : (await img_to_webp(trMatch, uploadPath)).data.filename; // Upload file baru jika berbeda

                                if (detail.tr.id_sektor_nasional_file_tr) {
                                // UPDATE jika membawa id_sektor_nasional_file_tr
                                await prisma.tb_sektor_nasional_file_tr.update({
                                    where: { id_sektor_nasional_file_tr: parseInt(detail.tr.id_sektor_nasional_file_tr) },
                                    data: {
                                    nama: trFileName,
                                    judul: detail.tr.judul,
                                    keterangan: detail.tr.keterangan,
                                    },
                                });
                                console.log(`File translate ID ${detail.tr.id_sektor_nasional_file_tr} berhasil diupdate`);
                                } else {
                                // CREATE jika tidak membawa id_sektor_nasional_file_tr
                                await prisma.tb_sektor_nasional_file_tr.create({
                                    data: {
                                    id_sektor_nasional_file: id_sektor_nasional_file, // Relasi ke file utama
                                    kd_bahasa: "en", // Ganti dengan kode bahasa yang sesuai
                                    nama: trFileName,
                                    judul: detail.tr.judul,
                                    keterangan: detail.tr.keterangan,
                                    },
                                });
                                console.log("File translate baru berhasil ditambahkan");
                                }
                            } else {
                                await prisma.tb_sektor_nasional_file_tr.update({
                                    where: { id_sektor_nasional_file_tr: parseInt(detail.tr.id_sektor_nasional_file_tr) },
                                    data: {
                                    judul: detail.tr.judul,
                                    keterangan: detail.tr.keterangan,
                                    },
                                });
                                console.log(`File translate tidak ditemukan untuk ${detail.tr.fileName}`);
                            }
                        }


                    });

                    await Promise.all(uploadPromisesUpImg); // Menjalankan semua upload dan update/insert secara paralel
                }


                response.status(201).json({ success: true,data:update })
            },{timeout : 30000});
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

    async destroy({ params,response }: HttpContext) {
        const Id = parseInt(params.id, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        try {
        const deletePost = await prisma.tb_sektor_nasional.delete({
            where: {
            id_sektor_nasional: Id,
            },
        })
        return response.status(200).send({success:true,data:deletePost})
        } catch (error) {
        return response.status(500).send({ error: 'Error deleting data' })
        }
    }

    /**
     * @aprove
     * @paramQuery status - status - @type(number) @required
     * @paramQuery keterangan - keterangan - @type(string)  @required
     */
    async aprove({ params, request, response ,auth}: HttpContext) {
        // Ambil dan parse ID dari parameter URL
        const Id = parseInt(params.id_sektor_nasional, 10);
        // Validasi ID
        if (isNaN(Id)) {
            return response.status(400).json({ error: 'Invalid ID provided' });
        }
        try {
            // Validasi input dengan validator yang telah dibuat
            const dataPost = await request.validateUsing(aproveValidator);

            // Update data di tb_sektor_nasional
            const update = await prisma.tb_sektor_nasional.update({
            where: {
                id_sektor_nasional: Id
            },
                data: {
                    status: dataPost.status
                },
            });
            // Insert ke tb_sektor_nasional_status
            await auth.check()
            const insert = await prisma.tb_sektor_nasional_status.create({
                data: {
                    id_sektor_nasional: Id,
                    status: dataPost.status,
                    status_proses: dataPost.status,
                    keterangan: dataPost.keterangan,
                    created_by:auth.user?.id,
                    updated_by:auth.user?.id
                },
            });
            // Berikan respon sukses dengan data update
            return response.status(201).json({ success: true, data: update });
        } catch (error) {
            // Tangani error dan kirimkan respon gagal
            return response.status(500).json({ success: false, message: error.message });
        }
    }

}