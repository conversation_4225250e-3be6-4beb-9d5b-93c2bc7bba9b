import { createUmkmValidator, updateUmkmValidator } from '#validators/umkm'
import { createUmrProvValidator } from '#validators/umr'
import type { HttpContext } from '@adonisjs/core/http'
import { configUpload, img_to_webp } from '../helpers/global_helper.js'
import env from '#start/env'
import { createSNRefValidator, updateSNRefValidator } from '#validators/referensi'
import { createGlossaryValidator } from '#validators/glossary'
import prisma from '../lib/prisma.js'
export default class FaqController {

  async index({}: HttpContext) {
    const data = await prisma.tb_glossary.findMany({})
    return {
        success : true,
        data : data
    }
  }
   /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
  async get_paginate({params,response}:HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {
      AND: [
        { glossary_type: 'FAQ' }, // Kondisi wajib
      ],
    }; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        AND: [
          { glossary_type: { equals: 'FAQ' } }, // Gunakan operator `equals` untuk pencocokan
          {
            OR: [
              { title: { contains: search, mode: 'insensitive' } },
              { description: { contains: search, mode: 'insensitive' } },
              {
                tb_glossary_tr: {
                  some: {
                    OR: [
                      { title: { contains: search, mode: 'insensitive' } },
                      { description: { contains: search, mode: 'insensitive' } }
                    ]
                  }
                }
              }
            ]
          }
        ]
      };
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_glossary.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_glossary.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      include:{
        tb_glossary_tr:true
      }
    });
    const data = datas.map((item =>{
        return {
            id_glosary : item.id_glossary,
            judul : item.title,
            deskripsi : item.description,
            judul_tr : item.tb_glossary_tr[0].title,
            deskripsi_tr : item.tb_glossary_tr[0].description
          }
    }))


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
     * @store
     * @summary Insert Referensi Sektor Nasional Provinsi
     * @paramQuery title - judul - @type(string) @required
     * @paramQuery title_tr - judul translate - @type(string) @required
     * @paramQuery description - deskripsi - @type(string) @required
     * @paramQuery description_tr - deskripsi translate - @type(string) @required
     * @paramQuery kode_bahasa - kode bahasa - @type(string)
  */
    public async store({ request, response ,auth}: HttpContext) {
      const data = await request.validateUsing(createGlossaryValidator)
        try {
            await prisma.$transaction(async (prisma) => {

              const dataPost = {
                title : data.title,
                description : data.description,
                glossary_type : 'FAQ',
              }
              const insert = await prisma.tb_glossary.create({data:dataPost})
              const dataPostTr = {
                id_glossary : insert.id_glossary,
                title : data.title_tr,
                kode_bahasa : data.kode_bahasa || 'en',
                description : data.description_tr
              }
              const insertTr = await prisma.tb_glossary_tr.create({data:dataPostTr})
              response.status(201).json({ success: true,data:insert })
            })
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
    const data = await prisma.tb_glossary.findUnique({
      where: {
        id_glossary: Id,
      },
      include:{
        tb_glossary_tr:true
      }
    })

    return {
      success : true,
      data : data
    }
  }


  /**
     * @update
     * @paramQuery title - judul - @type(string) @required
     * @paramQuery title_tr - judul translate - @type(string) @required
     * @paramQuery description - deskripsi - @type(string) @required
     * @paramQuery description_tr - deskripsi translate - @type(string) @required
     */
  async update({ params, request ,response}: HttpContext) {


    const Id = parseInt(params.id, 10)

    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }
    const data = await request.validateUsing(createGlossaryValidator)
    try {
      const dataPost = {
          title : data.title,
          description : data.description
        }

      const update = await prisma.tb_glossary.update({
          data:dataPost,
          where:{
            id_glossary : Id,
          }
      })

      const dataPostTr = {
              title : data.title_tr,
              kode_bahasa : data.kode_bahasa || 'en',
              description : data.description_tr
            }
      const idTr = await prisma.tb_glossary_tr.findFirst({
        where:{
          id_glossary : Id
        },
        select:{
          id_glossary_tr:true
        }
      })
      const updateTr = await prisma.tb_glossary_tr.update({
        where:{
          id_glossary_tr: idTr?.id_glossary_tr
        },
        data:dataPostTr})

      return response.status(200).json({ success: true, data: update })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data' })
    }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_glossary.delete({
        where: {
          id_glossary: Id,
        },
      })
      return response.status(200).send({success:true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }
}