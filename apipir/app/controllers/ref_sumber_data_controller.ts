import { createUmkmValidator, updateUmkmValidator } from '#validators/umkm'
import { createUmrProvValidator } from '#validators/umr'
import type { HttpContext } from '@adonisjs/core/http'
import { configUpload, img_to_webp } from '../helpers/global_helper.js'
import env from '#start/env'
import { createPSRefValidator, createSDRefValidator, createSNRefValidator, updateSDRefValidator, updateSNRefValidator } from '#validators/referensi'
import prisma from '../lib/prisma.js'
export default class RefSumberDataController {

  async index({}: HttpContext) {
    const data = await prisma.tb_sumber_data.findMany({})
    return {
        success : true,
        data : data
    }
  }
   /**
     * @get_paginate
     * @summary Get a list of menu with pagination
     */
   async get_paginate({params,response,request}:HttpContext) {
    const parameters =  request.qs()
    let order = parameters.order
    const by = ['asc','desc'].includes(parameters.by) ? parameters.by : 'asc'
    let orderBy={}
    const paramList = ['tahun','nama_instansi']
    if (order != undefined && paramList.includes(order)) {
        if (order == 'tahun') {
            orderBy = {tahun_rumus:by}
        }else if (order == 'nama_instansi') {
          orderBy = {tb_sumber_data_judul:{tb_sumber_data_instansi:{nama:by}}}
        }else{
            orderBy = {[order]:by}
        }
    }

    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '' && search !== '%7Bsearch%7D') {
      searchCondition = {
      OR: [
        {
          tb_sumber_data_judul: {
            judul: {
              contains: search,
              mode: "insensitive", // Pencarian tidak sensitif huruf besar/kecil
            },
          },
        },
        {
          tb_sumber_data_judul: {
            tb_sumber_data_instansi: {
              nama: {
                contains: search,
                mode: "insensitive",
              },
            },
          },
        },
        !isNaN(Number(search)) && {
          tahun_rumus: {
            equals: Number(search),
          },
        },
      ].filter(Boolean),
    }
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_sumber_data.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_sumber_data.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      orderBy:orderBy,
      include:{
        tb_sumber_data_judul:{
          include:{
            tb_sumber_data_instansi:true
          }
        }
      }
    });
    const data = datas.map((item =>{
        return {
          id_sumber_data : item.id_sumber_data,
          tahun : item.tahun_rumus,
          judul : item.tb_sumber_data_judul.judul,
          nama_instansi : item.tb_sumber_data_judul.tb_sumber_data_instansi.nama,
          keterangan : item.tb_sumber_data_judul.keterangan ,
        }
    }))


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };


  }
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
     * @store
     * @summary Insert Referensi Sektor Nasional Provinsi
     * @paramQuery nama - nama - @type(string) @required
     * @paramQuery nama_tr - nama_tr - @type(string) @required
     * @paramQuery kd_bahasa - kd_bahasa - @type(string) @required
     * @paramQuery image - image - @type(file) @required
  */
    public async store({ request, response ,auth}: HttpContext) {


      const data = await request.validateUsing(createSDRefValidator)

        try {
          await prisma.$transaction(async (prisma) => {
            const dataPostJudul = {
              id_sumber_data_instansi : parseInt(data.id_sumber_data_instansi),
              judul : data.judul,
              keterangan : data.keterangan,
            }
            const insertJudul = await prisma.tb_sumber_data_judul.create({data:dataPostJudul})
            const dataPostJudulTr= {
              id_sumber_data_judul : insertJudul.id_sumber_data_judul,
              judul : data.judul_tr,
              keterangan : data.keterangan_tr,
              kd_bahasa : data.kd_bahasa,
            }
            const insertJudulTr = await prisma.tb_sumber_data_judul_tr.create({data:dataPostJudulTr})
            const dataPost = {
              id_sumber_data_judul : insertJudul.id_sumber_data_judul,
              tahun_rumus : data.tahun_rumus
            }

            const insert = await prisma.tb_sumber_data.create({data:dataPost})

            response.status(201).json({ success: true,data:insert })
          });
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

  /**
   * Show individual record
   */
  async show({ params ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
    const data = await prisma.tb_sumber_data.findUnique({
      where: {
        id_sumber_data: Id,
      },
      include:{
        tb_sumber_data_judul:
        {
          include:{
            tb_sumber_data_instansi:true,
            tb_sumber_data_judul_tr:true
          }
        }
      }
    })




    return {
      success : true,
      data : data
    }
  }


  /**
     * @update
     * @paramQuery nama - nama - @type(string) @required
     * @paramQuery nama_tr - nama_tr - @type(string) @required
     * @paramQuery kd_bahasa - kd_bahasa - @type(string) @required
     * @paramQuery image - image - @type(file) @required
     */
  async update({ params, request ,response}: HttpContext) {
    const Id = parseInt(params.id, 10)

    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }
    // try {
      const data = await request.validateUsing(updateSDRefValidator)


      const dataPost = {
        id_sumber_data_judul : data.id_sumber_data_judul,
        tahun_rumus : data.tahun_rumus,
      }
      const update = await prisma.tb_sumber_data.update({
                data: dataPost,
        where: {
          id_sumber_data: Id,
        },
      });
      let sumber_data_judul = request.input('sumber_data_judul')
      if (typeof sumber_data_judul === 'string') {
          sumber_data_judul = JSON.parse(sumber_data_judul);
      }
      if (Array.isArray(sumber_data_judul)) {
        await Promise.all(
          sumber_data_judul.map(async (item) => {  // Jadikan callback sebagai async
            const dataPostJudul = {
              id_sumber_data_instansi: parseInt(item.id_sumber_data_instansi),
              judul: item.judul,
              keterangan: item.keterangan,
            };

            await prisma.tb_sumber_data_judul.update({
              data: dataPostJudul,
              where: {
                id_sumber_data_judul: parseInt(item.id_sumber_data_judul),
              },
            });

            await Promise.all(
              item.tr.map(async (item2) => {  // Jadikan inner callback async juga
                const dataPostJudulTr = {
                  id_sumber_data_judul: parseInt(item2.id_sumber_data_judul),
                  judul: item2.judul,
                  keterangan: item2.keterangan,
                  kd_bahasa: item2.kd_bahasa,
                };

                await prisma.tb_sumber_data_judul_tr.update({
                  data: dataPostJudulTr,
                  where: {
                    id_sumber_data_judul_tr: parseInt(item2.id_sumber_data_judul_tr),
                  },
                });
              })
            );
          })
        );
      }




      return response.status(200).json({ success: true, data: update })
    // } catch (error) {
    //   return response.status(500).send({ error: 'Error updating data' })
    // }
  }

  /**
   * Delete record
   */
  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_sumber_data.delete({
        where: {
          id_sumber_data: Id,
        },
      })
      return response.status(200).send({success:true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }
}