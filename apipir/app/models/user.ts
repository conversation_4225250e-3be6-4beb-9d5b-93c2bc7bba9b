import { DateTime } from 'luxon'
import hash from '@adonisjs/core/services/hash'
import { compose } from '@adonisjs/core/helpers'
import { BaseModel, column } from '@adonisjs/lucid/orm'
import { withAuthFinder } from '@adonisjs/auth/mixins/lucid'
import { DbAccessTokensProvider } from '@adonisjs/auth/access_tokens'

const AuthFinder = withAuthFinder(() => hash.use('scrypt'), {
  uids: ['login_name'],
  passwordColumnName: 'password',
})

export default class User extends compose(BaseModel, AuthFinder) {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare fullName: string | null
  
  @column()
  declare role_id: number | null

  @column()
  declare email: string
  
  @column()
  declare full_name: string
  
  @column()
  declare login_name : string

  
  @column({ serializeAs: null })
  declare password: string
  
  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime
  
  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime | null
  
  @column()
  declare first_name : string

  @column()
  declare middle_name : string

  @column()
  declare last_name : string

  @column()
  declare address : string

  @column()
  declare phone_number : string

  @column()
  declare mobile_number : string

  @column()
  declare file_image : string

  @column()
  declare status : number

  @column()
  declare last_login : DateTime

  @column()
  declare updated_by : number

  @column()
  declare code_forgot : string

  @column()
  declare code_expired : string

  @column()
  declare keterangan : string

  @column()
  declare user_token : string

  @column()
  declare jabatan : string

  @column()
  declare instansi : string

  @column()
  declare user_api_key : string

  
  // static accessTokens = DbAccessTokensProvider.forModel(User)
  static accessTokens = DbAccessTokensProvider.forModel(User, {
    expiresIn: '150m',
    prefix: 'oat_',
    table: 'auth_access_tokens',
    type: 'auth_token',
    tokenSecretLength: 329,
  })
}