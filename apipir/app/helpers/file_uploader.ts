import { MultipartFile } from "@adonisjs/core/bodyparser";
import app from "@adonisjs/core/services/app";
import sharp from "sharp";
import path from "path";
import fs from "fs"; // Node.js fs.promises API
import env from "#start/env";


const pathMap = {
        "kajian": `${env.get('APP_CONTAINER')}uploads/kajian/{id}/`,
        "daerah_sarpras_hotel": `${env.get('APP_CONTAINER')}uploads/daerah/sarana_prasarana/hotel/`,
        "artikel_input_data": `${env.get('APP_CONTAINER')}uploads/berita/{id}/`,
        "informasi_kebijakan": `${env.get('APP_CONTAINER')}uploads/kebijakan/`,
        "informasi_insentif": `${env.get('APP_CONTAINER')}uploads/jenis/insentif/{id}/`,
        "peluang_investasi_daerah": `${env.get('APP_CONTAINER')}uploads/peluang_daerah/{id}/`,
        // "peluang_investasi_daerah": `${env.get('APP_CONTAINER')}uploads/peluang_daerah/`,
        "roadmap_input_data_table": `${env.get('APP_CONTAINER')}uploads/roadmap/`,
        // "sud_sub_sektor_daerah": `${env.get('APP_CONTAINER')}uploads/sud/sub_sektor_daerah/`,
        "sud_sub_sektor_daerah": `${env.get('APP_CONTAINER')}uploads/sektor/`,
        "sud_sektor_daerah_insentif": `${env.get('APP_CONTAINER')}uploads/sektor/`,
        "sud_komoditi_daerah": `${env.get('APP_CONTAINER')}uploads/sektor/`,
        // "sud_komoditi_daerah": `${env.get('APP_CONTAINER')}uploads/komoditi_daerah/`,
        // "sud_sektor_daerah": `${env.get('APP_CONTAINER')}uploads/sektor/{id}/`,
        "sud_sektor_daerah": `${env.get('APP_CONTAINER')}uploads/sektor/`,
        "kawasan_potensi_investasi": `${env.get('APP_CONTAINER')}uploads/kawasan_industri/{id}`,
        "kabupaten_profil": `${env.get('APP_CONTAINER')}uploads/daerah/{id}/`,
        "daerah_provinsi_kantor": `${env.get('APP_CONTAINER')}uploads/kantor/{id}`,
        "daerah_provinsi_profil": `${env.get('APP_CONTAINER')}uploads/daerah/{id}/`,
        "daerah_kabkot_kantor": `${env.get('APP_CONTAINER')}uploads/kantor/{id}`,
} as const;

export default async function uploadFile(file: MultipartFile, model: keyof typeof pathMap, id_path?: number) {
        let pathName = pathMap[model] ? pathMap[model] : "";

        if ((model === "kajian" || model === "artikel_input_data" || model === "informasi_insentif" || model === "peluang_investasi_daerah" || model === "sud_sektor_daerah" || model === "kawasan_potensi_investasi" || model === "daerah_provinsi_kantor" || model === "daerah_kabkot_kantor" || model === "daerah_provinsi_profil" || model === "kabupaten_profil") && id_path) {
                pathName = pathName.replace("{id}", id_path.toString());
        }

        const targetPath = app.makePath(pathName);
        let originalFileName = file?.clientName || "";
        originalFileName = originalFileName.replace(/\s+/g, "_");
        const fileBaseName = path.parse(originalFileName).name; // File name without extension
        const fileExtension = path.extname(originalFileName).toLowerCase();
        const webpFileName = `${fileBaseName}.webp`;
        const webpFilePath = path.join(targetPath, webpFileName);
        const pdfFilePath = path.join(targetPath, originalFileName); // Path to save PDF
        const videoFilePath = path.join(targetPath, originalFileName); // Path to save PDF

        // Ensure the target path exists using fs.promises.mkdir
        try {
                await fs.promises.mkdir(targetPath, { recursive: true, mode: 0o755 }); // Create the directory if it doesn't exist
        } catch (err) {
                console.error("Failed to create directory:", err);
                throw new Error("Directory creation failed");
        }

        if (file) {
                try {
                        // Move the original file to the target folder
                        const filePath = `${targetPath}/${originalFileName}`;
                        await file.move(targetPath, {
                                name: originalFileName, // Save original file
                                overwrite: true,
                        });

                        // Ensure the file has been moved before processing
                        const movedFilePath = path.join(targetPath, originalFileName);

                        try {
                                await fs.promises.access(movedFilePath); // Check if the file exists
                        } catch (err) {
                                console.error(`File does not exist: ${movedFilePath}`);
                                throw new Error(`Moved file not found: ${movedFilePath}`);
                        }
                        
                        // Handle different file types
                        if (fileExtension === ".pdf") {
                                // For PDFs, we simply move the file and don't convert it
                                console.log(`PDF file saved: ${pdfFilePath}`);
                                file.filePath = pdfFilePath; // Assign the PDF file path to the file object

                                return pdfFilePath;
                        } else if (fileExtension === ".mp4" || fileExtension === ".mov") {
                                // For Movies, we simply move the file and don't convert it
                                console.log(`Movie file saved: ${videoFilePath}`);
                                file.filePath = videoFilePath; // Assign the PDF file path to the file object

                                return videoFilePath;
                        } else if (fileExtension === ".jpg" || fileExtension === ".jpeg" || fileExtension === ".png") {
                                // For image files, convert to WebP format
                                await sharp(movedFilePath)
                                        .toFormat("webp")
                                        .toFile(webpFilePath);

                                // Delete the original file after conversion
                                await fs.promises.unlink(movedFilePath); // Remove the original image after conversion

                                console.log(`Original image saved: ${filePath}`);
                                console.log(`WebP image saved: ${webpFilePath}`);
                                console.log(`Original image deleted: ${movedFilePath}`);

                                // Assign details for logging or further use
                                file.fileName = originalFileName;
                                file.filePath = filePath;

                                return webpFilePath;
                        } else if (fileExtension === ".webp") {
                                // Assign details for logging or further use
                                file.fileName = originalFileName;
                                file.filePath = filePath;

                                return filePath;
                        } else {
                                throw new Error("Unsupported file type");
                        }
                } catch (err) {
                        console.error("Error processing the file:", err);
                        // throw new Error("Failed to upload and process the file.");
                        throw new Error(err.message);
                }
        }
}
