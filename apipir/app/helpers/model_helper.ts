// import app from "@adonisjs/core/services/app"
// import { PrismaClient } from '@prisma/client'

// const prisma = new PrismaClient()

export function modelSelect(columnSchema: { [key: string]: { column: string; alias: string; type: string } }, selectParam: string | string[]): { [key: string]: boolean } {
  let result: { [key: string]: boolean } = {};

  if (Array.isArray(selectParam)) {
    selectParam.forEach((col) => {
      const trimmedCol = col.trim();
      if (columnSchema[trimmedCol]) {
        result[trimmedCol] = true;
      }
    });
    return result;
  }

  if (typeof selectParam === 'string') {
    return selectParam.split(',').reduce((acc, col) => {
      const trimmedCol = col.trim();
      if (columnSchema[trimmedCol]) { 
        acc[trimmedCol] = true;
      }
      return acc;
    }, {} as { [key: string]: boolean });
  }

  return result;
}

export function modelWhereAnd(
  columnSchema: { [key: string]: { column: string; alias: string; type: string } },
  queryParams: { [key: string]: string | number }
): { [key: string]: any } {
  const where: { [key: string]: any } = {};

  for (const [key, value] of Object.entries(queryParams)) {
    if (columnSchema[key]) {
      const columnType = columnSchema[key].type;
      const columnName = columnSchema[key].column; // Ambil nama kolom dari schema

      if (columnType === 'string') {
        where[columnName] = {
          contains: value,
          mode: 'insensitive',
        };
      }

      else if (columnType === 'int') {
        where[columnName] = typeof value === 'string' ? parseInt(value, 10) : value;
      }
    }
  }

  return where;
}



