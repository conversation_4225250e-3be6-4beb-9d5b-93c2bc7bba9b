import { MultipartFile } from "@adonisjs/core/bodyparser"
import app from "@adonisjs/core/services/app"
import sharp from "sharp"
import path from "path"
import fs from "fs/promises"
import { randomUUID } from "crypto"
import env from "#start/env"

// Whitelist of allowed file extensions
const ALLOWED_IMAGE_EXTENSIONS = new Set(['.jpg', '.jpeg', '.png', '.webp', '.gif'])
const ALLOWED_DOCUMENT_EXTENSIONS = new Set(['.pdf', '.doc', '.docx', '.txt'])
const ALLOWED_VIDEO_EXTENSIONS = new Set(['.mp4', '.mov', '.avi', '.webm'])

// Whitelist of allowed MIME types
const ALLOWED_MIME_TYPES = new Set([
  'image/jpeg',
  'image/png', 
  'image/webp',
  'image/gif',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
  'video/mp4',
  'video/quicktime',
  'video/x-msvideo',
  'video/webm'
])

// Whitelist of allowed upload models/paths
const ALLOWED_UPLOAD_MODELS = new Set([
  'kajian',
  'daerah_sarpras_hotel',
  'artikel_input_data',
  'informasi_kebijakan',
  'informasi_insentif',
  'peluang_investasi_daerah',
  'roadmap_input_data_table',
  'sud_sub_sektor_daerah',
  'sud_sektor_daerah_insentif',
  'sud_komoditi_daerah',
  'sud_sektor_daerah',
  'kawasan_potensi_investasi',
  'kabupaten_profil',
  'daerah_provinsi_kantor',
  'daerah_provinsi_profil',
  'daerah_kabkot_kantor'
])

// Maximum file sizes (in bytes)
const MAX_FILE_SIZES = {
  image: 5 * 1024 * 1024,    // 5MB for images
  document: 10 * 1024 * 1024, // 10MB for documents
  video: 50 * 1024 * 1024     // 50MB for videos
}

interface UploadResult {
  success: boolean
  filePath?: string
  fileName?: string
  originalName?: string
  error?: string
}

interface SecurityScanResult {
  isSafe: boolean
  threats: string[]
}

export class SecureFileUploader {
  
  /**
   * Validates file extension against whitelist
   */
  private static validateFileExtension(fileName: string): boolean {
    const ext = path.extname(fileName).toLowerCase()
    return ALLOWED_IMAGE_EXTENSIONS.has(ext) || 
           ALLOWED_DOCUMENT_EXTENSIONS.has(ext) || 
           ALLOWED_VIDEO_EXTENSIONS.has(ext)
  }

  /**
   * Validates MIME type against whitelist
   */
  private static validateMimeType(mimeType: string): boolean {
    return ALLOWED_MIME_TYPES.has(mimeType.toLowerCase())
  }

  /**
   * Validates upload model against whitelist
   */
  private static validateUploadModel(model: string): boolean {
    return ALLOWED_UPLOAD_MODELS.has(model)
  }

  /**
   * Gets file category based on extension
   */
  private static getFileCategory(fileName: string): 'image' | 'document' | 'video' | null {
    const ext = path.extname(fileName).toLowerCase()
    
    if (ALLOWED_IMAGE_EXTENSIONS.has(ext)) return 'image'
    if (ALLOWED_DOCUMENT_EXTENSIONS.has(ext)) return 'document'
    if (ALLOWED_VIDEO_EXTENSIONS.has(ext)) return 'video'
    
    return null
  }

  /**
   * Validates file size based on category
   */
  private static validateFileSize(file: MultipartFile): boolean {
    const category = this.getFileCategory(file.clientName || '')
    if (!category) return false
    
    const maxSize = MAX_FILE_SIZES[category]
    return file.size <= maxSize
  }

  /**
   * Generates secure file name using UUID
   */
  private static generateSecureFileName(originalName: string): string {
    const ext = path.extname(originalName).toLowerCase()
    const uuid = randomUUID()
    return `${uuid}${ext}`
  }

  /**
   * Validates and sanitizes the upload path
   */
  private static validateUploadPath(model: string, idPath?: number): string {
    if (!this.validateUploadModel(model)) {
      throw new Error('Invalid upload model')
    }

    // Base upload directory (outside web root for security)
    const baseUploadDir = env.get('SECURE_UPLOAD_DIR', 'secure_uploads')
    
    // Sanitize ID path if provided
    let sanitizedIdPath = ''
    if (idPath !== undefined) {
      const numericId = parseInt(idPath.toString(), 10)
      if (isNaN(numericId) || numericId < 0 || numericId > 999999999) {
        throw new Error('Invalid ID path')
      }
      sanitizedIdPath = numericId.toString()
    }

    // Construct secure path
    const pathComponents = [baseUploadDir, model]
    if (sanitizedIdPath) {
      pathComponents.push(sanitizedIdPath)
    }

    // Ensure path doesn't contain traversal attempts
    const finalPath = path.join(...pathComponents)
    const normalizedPath = path.normalize(finalPath)
    
    // Verify the normalized path is still within our base directory
    if (!normalizedPath.startsWith(path.normalize(baseUploadDir))) {
      throw new Error('Path traversal attempt detected')
    }

    return normalizedPath
  }

  /**
   * Performs security scan on file content
   */
  private static async performSecurityScan(filePath: string): Promise<SecurityScanResult> {
    try {
      const fileBuffer = await fs.readFile(filePath)
      const threats: string[] = []

      // Check for executable signatures
      const executableSignatures = [
        Buffer.from([0x4D, 0x5A]), // PE executable (MZ)
        Buffer.from([0x7F, 0x45, 0x4C, 0x46]), // ELF executable
        Buffer.from([0xCA, 0xFE, 0xBA, 0xBE]), // Mach-O executable
        Buffer.from([0xFE, 0xED, 0xFA, 0xCE]), // Mach-O executable (reverse)
      ]

      for (const signature of executableSignatures) {
        if (fileBuffer.subarray(0, signature.length).equals(signature)) {
          threats.push('executable_file_detected')
          break
        }
      }

      // Check for script content in files
      const fileContent = fileBuffer.toString('utf8', 0, Math.min(fileBuffer.length, 8192))
      const scriptPatterns = [
        /<script[\s\S]*?>/i,
        /javascript:/i,
        /<\?php/i,
        /<%[\s\S]*?%>/,
        /\beval\s*\(/i,
        /\bexec\s*\(/i,
        /\bsystem\s*\(/i,
        /\bshell_exec\s*\(/i
      ]

      for (const pattern of scriptPatterns) {
        if (pattern.test(fileContent)) {
          threats.push('script_content_detected')
          break
        }
      }

      // Check for suspicious file headers that don't match extension
      const fileName = path.basename(filePath)
      const ext = path.extname(fileName).toLowerCase()
      
      if (ALLOWED_IMAGE_EXTENSIONS.has(ext)) {
        // Verify image file headers
        const imageSignatures = [
          { ext: '.jpg', signature: Buffer.from([0xFF, 0xD8, 0xFF]) },
          { ext: '.jpeg', signature: Buffer.from([0xFF, 0xD8, 0xFF]) },
          { ext: '.png', signature: Buffer.from([0x89, 0x50, 0x4E, 0x47]) },
          { ext: '.gif', signature: Buffer.from([0x47, 0x49, 0x46, 0x38]) },
          { ext: '.webp', signature: Buffer.from([0x52, 0x49, 0x46, 0x46]) }
        ]

        const matchingSignature = imageSignatures.find(sig => sig.ext === ext)
        if (matchingSignature && !fileBuffer.subarray(0, matchingSignature.signature.length).equals(matchingSignature.signature)) {
          threats.push('file_header_mismatch')
        }
      }

      return {
        isSafe: threats.length === 0,
        threats
      }
    } catch (error) {
      return {
        isSafe: false,
        threats: ['scan_error']
      }
    }
  }

  /**
   * Sanitizes file name to prevent issues
   */
  private static sanitizeFileName(fileName: string): string {
    if (!fileName || typeof fileName !== 'string') {
      return 'unknown'
    }

    // Remove path separators and dangerous characters
    return fileName
      .replace(/[<>:"/\\|?*\x00-\x1f]/g, '') // Remove dangerous characters
      .replace(/\.\./g, '') // Remove path traversal attempts
      .replace(/^\.+/, '') // Remove leading dots
      .trim()
      .substring(0, 255) // Limit length
  }

  /**
   * Main upload method with comprehensive security
   */
  static async uploadFile(
    file: MultipartFile, 
    model: string, 
    idPath?: number
  ): Promise<UploadResult> {
    try {
      // Validate file object
      if (!file || !file.isValid) {
        return {
          success: false,
          error: 'Invalid file or file validation failed'
        }
      }

      // Sanitize original file name
      const sanitizedOriginalName = this.sanitizeFileName(file.clientName || 'unknown')

      // Validate file extension
      if (!this.validateFileExtension(sanitizedOriginalName)) {
        return {
          success: false,
          error: 'File type not allowed'
        }
      }

      // Validate MIME type
      if (!this.validateMimeType(file.type || '')) {
        return {
          success: false,
          error: 'MIME type not allowed'
        }
      }

      // Validate file size
      if (!this.validateFileSize(file)) {
        return {
          success: false,
          error: 'File size exceeds limit'
        }
      }

      // Generate secure upload path
      const uploadPath = this.validateUploadPath(model, idPath)

      // Create directory if it doesn't exist
      await fs.mkdir(uploadPath, { recursive: true, mode: 0o755 })

      // Generate secure file name
      const secureFileName = this.generateSecureFileName(sanitizedOriginalName)
      const finalPath = path.join(uploadPath, secureFileName)

      // Move file to secure location
      await file.move(uploadPath, {
        name: secureFileName,
        overwrite: false // Never overwrite existing files
      })

      // Perform security scan
      const scanResult = await this.performSecurityScan(finalPath)
      if (!scanResult.isSafe) {
        // Delete the file if it's not safe
        try {
          await fs.unlink(finalPath)
        } catch (deleteError) {
          console.error('Failed to delete unsafe file:', deleteError)
        }
        
        return {
          success: false,
          error: `File failed security scan: ${scanResult.threats.join(', ')}`
        }
      }

      // Process image files (convert to WebP for security and optimization)
      const category = this.getFileCategory(sanitizedOriginalName)
      if (category === 'image' && !sanitizedOriginalName.toLowerCase().endsWith('.webp')) {
        try {
          const webpFileName = secureFileName.replace(/\.[^.]+$/, '.webp')
          const webpPath = path.join(uploadPath, webpFileName)

          await sharp(finalPath)
            .webp({ quality: 85 })
            .toFile(webpPath)

          // Delete original image file
          await fs.unlink(finalPath)

          return {
            success: true,
            filePath: webpPath,
            fileName: webpFileName,
            originalName: sanitizedOriginalName
          }
        } catch (conversionError) {
          console.error('Image conversion failed:', conversionError)
          // Keep original file if conversion fails
        }
      }

      return {
        success: true,
        filePath: finalPath,
        fileName: secureFileName,
        originalName: sanitizedOriginalName
      }

    } catch (error) {
      console.error('File upload error:', error)
      return {
        success: false,
        error: error.message || 'Upload failed'
      }
    }
  }

  /**
   * Validates if a file path is safe for serving
   */
  static validateFileAccess(requestedPath: string): boolean {
    try {
      const baseUploadDir = env.get('SECURE_UPLOAD_DIR', 'secure_uploads')
      const normalizedPath = path.normalize(requestedPath)
      const basePath = path.normalize(baseUploadDir)
      
      // Ensure the requested path is within the upload directory
      return normalizedPath.startsWith(basePath)
    } catch (error) {
      return false
    }
  }

  /**
   * Securely serves files with access control
   */
  static async serveFile(requestedPath: string, userRole?: string): Promise<{
    success: boolean
    filePath?: string
    error?: string
  }> {
    try {
      // Validate file access
      if (!this.validateFileAccess(requestedPath)) {
        return {
          success: false,
          error: 'Access denied - invalid file path'
        }
      }

      // Check if file exists
      try {
        await fs.access(requestedPath)
      } catch (error) {
        return {
          success: false,
          error: 'File not found'
        }
      }

      // Additional access control based on user role could be implemented here
      // For now, we'll allow access to validated paths

      return {
        success: true,
        filePath: requestedPath
      }
    } catch (error) {
      return {
        success: false,
        error: 'File access error'
      }
    }
  }

  /**
   * Deletes a file securely
   */
  static async deleteFile(filePath: string): Promise<boolean> {
    try {
      if (!this.validateFileAccess(filePath)) {
        return false
      }

      await fs.unlink(filePath)
      return true
    } catch (error) {
      console.error('File deletion error:', error)
      return false
    }
  }
} 