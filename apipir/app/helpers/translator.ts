// const { Translate } = require('@google-cloud/translate').v2;

import { Translate } from "@google-cloud/translate/build/src/v2/index.js";

// Create a Google Translate client
const translate = new Translate();

// Function to translate text
export default async function TranslateText(text: any, targetLanguage = 'en') {
        try {
                const [translation] = await translate.translate(text, targetLanguage);
                return translation; // Return the translated text
        } catch (error) {
                console.error('Error during translation:', error);
                throw new Error('Translation failed');
        }
}

