import { HttpContext } from "@adonisjs/core/http"
import { DateTime } from "luxon"
import app from "@adonisjs/core/services/app"
import { PrismaClient } from '@prisma/client'
const prisma = new PrismaClient()
import hash from "@adonisjs/core/services/hash"
import mail from "@adonisjs/mail/services/main"
import env from "#start/env"
import sharp from 'sharp';
import path from "path"
import fs from "fs"
import { log } from "console"




export function upload_file({request,response}:HttpContext,xfiles:string,configData:string,requestName:string='img',uploadPath:string='uploads/',fileName='') {
  const files = xfiles.file(requestName,configData)
    if (files.errors.length > 0) {
      return {
            status:false,
            message:files.errors[0].message
        }
    }
    const filename = `${DateTime.now().toFormat('yyyyMMddHHmmss')}${fileName}.${files.extname}`
    const outputPath = path.join(uploadPath, filename)


    files.move(app.makePath(uploadPath),{
      name: filename
    })

    return {
      status: true,
      message: 'File uploaded successfully',
      data: {
        filename: filename,
        originalName: files.clientName,
        size: files.size,
        type: files.type,
        subtype: files.subtype,
        path: `${uploadPath}/${filename}`
      }
    }
}
export function upload_img_to_webp({request,response}:HttpContext,xfiles:string,requestName:string='img',uploadPath:string='uploads/',fileName='') {
  const configData = configUpload('img')
    const files = xfiles.file(requestName, configData)

    if (files.errors.length > 0) {
      return {
        status: false,
        message: files.errors[0].message
      }
    }

    // Tentukan batas ukuran 300KB (dalam byte, 300KB = 300 * 1024)
    const sizeLimit = 300 * 1024
    const quality = files.size > sizeLimit ? 50 : 100 // Set kualitas berdasarkan ukuran file

    const filename = `${DateTime.now().toFormat('yyyyMMddHHmmss')}${fileName}.webp`
    const outputPath = path.join(uploadPath, filename)

    const up = sharp(files.tmpPath!)
      .webp({ quality })  // Kompresi menggunakan kualitas yang ditentukan
      .toFile(outputPath)

    return {
      status: true,
      message: 'File uploaded successfully',
      data: {
        filename: filename,
        originalName: files.clientName,
        path: `${uploadPath}/${filename}`
      }
    }

}

  export async function get_paginate(page: sumber, pageSize: number,model:string,configCustom:{},response:HttpContext) {

    let totalRows;
    let totalPages;
    const skip = (page - 1) * pageSize;

    const config = {
                  skip: skip,
                  take: pageSize
                }
                Object.assign(config, configCustom)
    try {
        let data = await prisma.users.findMany(config);

        totalRows = await model.count();
        totalPages = Math.ceil(totalRows / pageSize);
        return {
                  success : true,
                  data :
                        {
                          page: page || 1,
                          pageSize: pageSize || totalRows,
                          totalPages,
                          totalRows,
                          data: data,
                        }
              };
    } catch (error) {
      console.error(error);
      return {
        message: 'An error occurred while fetching data.',
      };
    }
  }

  export async function send_mail(to:string,subject:string,body:string) {

    const from = env.get('SMTP_USERNAME')
    await mail.send((message) => {
        message
          .to(to)
          .from(from)
          .subject(subject)
          .html(body)
      })
    return {success:true,message:`Mail to ${to} has been sent!`}
  }

  export function rupiah(number:number) {

   return  Intl.NumberFormat('id-ID', {
      style: 'currency',    // Ubah menjadi 'decimal' jika tidak butuh simbol mata uang
      currency: 'IDR',      // Ganti 'IDR' dengan simbol mata uang yang diinginkan
      minimumFractionDigits: 0, // Jumlah minimum desimal
      maximumFractionDigits: 2, // Jumlah maksimum desimal
    }).format( number || 0);
  }
  export function numberFormat(number:number) {

   return new Intl.NumberFormat('id-ID', {
      style: 'decimal',        // Gunakan 'decimal' untuk format angka tanpa mata uang
      minimumFractionDigits: 0, // Jumlah minimum desimal
      maximumFractionDigits: 2, // Jumlah maksimum desimal (0 untuk menghindari desimal)
    }).format(number || 0);
  }

  export function numberFormatRpSingkatan(value:number) {
    if (value >= 1_000_000_000_000) {
      // Jika triliunan
      return `Rp ${(value / 1_000_000_000_000).toFixed(1)}T`; // Contoh: 1.000.000.000.000 -> 1.0T
    } else if (value >= 1_000_000_000) {
      // Jika miliaran
      return `Rp ${(value / 1_000_000_000).toFixed(1)}M`; // Contoh: 1.000.000.000 -> 1.0M
    } else if (value >= 1_000_000) {
      // Jika jutaan
      return `Rp ${(value / 1_000_000).toFixed(1)}Jt`; // Contoh: 1.000.000 -> 1.0Jt
    } else if (value >= 1_000) {
      // Jika ribuan
      return `Rp ${(value / 1_000).toFixed(1)}Rb`; // Contoh: 1.000 -> 1.0Rb
    } else {
      // Jika kurang dari ribuan, biarkan seperti biasa
      const num = parseInt(value, 10);
       if (isNaN(num)) {
        return "0"
      }
      return value.toString(); // Contoh: 500 -> 500
    }
  }

  export function numberFormatRpLengkap(value:number | null | undefined): string {
    if (value == null || value === undefined) {
      return "-"; // Nilai default jika value tidak valid
    }
    if (value >= 1_000_000_000_000_000) {
      // Jika triliunan
      return `Rp ${(value / 1_000_000_000_000_000).toFixed(1)} Kuadriliun`; // Contoh: 1.000.000.000.000.000 -> 1.0T
    } else if (value >= 1_000_000_000_000) {
      // Jika triliunan
      return `Rp ${(value / 1_000_000_000_000).toFixed(1)} Triliun`; // Contoh: 1.000.000.000.000 -> 1.0T
    } else if (value >= 1_000_000_000) {
      // Jika miliaran
      return `Rp ${(value / 1_000_000_000).toFixed(1)} Miliar`; // Contoh: 1.000.000.000 -> 1.0M
    } else if (value >= 1_000_000) {
      // Jika jutaan
      return `Rp ${(value / 1_000_000).toFixed(1)} Juta`; // Contoh: 1.000.000 -> 1.0Jt
    } else if (value >= 1_000) {
      // Jika ribuan
      return `Rp ${(value / 1_000).toFixed(1)} Ribu`; // Contoh: 1.000 -> 1.0Rb
    } else {
      // Jika kurang dari ribuan, biarkan seperti biasa
      return value.toString(); // Contoh: 500 -> 500
    }
  }
  export function numberFormatRpLengkapEn(value: number| null | undefined): string {
    if (value == null || value === undefined) {
      return "-"; // Nilai default jika value tidak valid
    }
    if (value >= 1_000_000_000_000_000) {
      // If quadrillion
      return `Rp ${(value / 1_000_000_000_000_000).toFixed(1)} Quadrillion`; // Example: 1,000,000,000,000,000 -> 1.0 Quadrillion
    } else if (value >= 1_000_000_000_000) {
      // If trillion
      return `Rp ${(value / 1_000_000_000_000).toFixed(1)} Trillion`; // Example: 1,000,000,000,000 -> 1.0T
    } else if (value >= 1_000_000_000) {
      // If billion
      return `Rp ${(value / 1_000_000_000).toFixed(1)} Billion`; // Example: 1,000,000,000 -> 1.0B
    } else if (value >= 1_000_000) {
      // If million
      return `Rp ${(value / 1_000_000).toFixed(1)} Million`; // Example: 1,000,000 -> 1.0M
    } else if (value >= 1_000) {
      // If thousand
      return `Rp ${(value / 1_000).toFixed(1)} Thousand`; // Example: 1,000 -> 1.0K
    } else {
      // If less than a thousand, return as is
      return value.toString(); // Example: 500 -> 500
    }
  }


  export function numberFormatLengkap(value:number| null | undefined,koma:number = 0): string {
    if (value == null || value === undefined) {
      return "-"; // Nilai default jika value tidak valid
    }
    if (value >= 1_000_000_000_000_000) {
      // Jika triliunan
      return `${(value / 1_000_000_000_000_000).toFixed(koma)} Kuadriliun`; // Contoh: 1.000.000.000.000.000 -> 1.0T
    } else if (value >= 1_000_000_000_000) {
      // Jika triliunan
      return `${(value / 1_000_000_000_000).toFixed(koma)} Triliun`; // Contoh: 1.000.000.000.000 -> 1.0T
    } else if (value >= 1_000_000_000) {
      // Jika miliaran
      return `${(value / 1_000_000_000).toFixed(koma)} Miliar`; // Contoh: 1.000.000.000 -> 1.0M
    } else if (value >= 1_000_000) {
      // Jika jutaan
      return `${(value / 1_000_000).toFixed(koma)} Juta`; // Contoh: 1.000.000 -> 1.0Jt
    } else if (value >= 1_000) {
      // Jika ribuan
      return `${(value / 1_000).toFixed(koma)} Ribu`; // Contoh: 1.000 -> 1.0Rb
    } else {
      // Jika kurang dari ribuan, biarkan seperti biasa
      return value.toString(); // Contoh: 500 -> 500
    }
  }

  export function numberFormatLengkapEn(value:number| null | undefined,koma : number = 0): string {
    if (value == null || value === undefined) {
      return "-"; // Default value if invalid
    }
    if (value >= 1_000_000_000_000_000) {
      // If quadrillion
      return `${(value / 1_000_000_000_000_000).toFixed(koma)} Quadrillion`; 
    } else if (value >= 1_000_000_000_000) {
      // If trillion
      return `${(value / 1_000_000_000_000).toFixed(koma)} Trillion`; 
    } else if (value >= 1_000_000_000) {
      // If billion
      return `${(value / 1_000_000_000).toFixed(koma)} Billion`; 
    } else if (value >= 1_000_000) {
      // If million
      return `${(value / 1_000_000).toFixed(koma)} Million`; 
    } else if (value >= 1_000) {
      // If thousand
      return `${(value / 1_000).toFixed(koma)} Thousand`; 
    } else {
      // If less than thousand
      return value.toString(); 
    }
}

  export function satuanNumber(value:number) {
    if (value >= 1_000_000_000_000_000) {
      // Jika triliunan
      return `Rp Kuadriliun :} `; // Contoh: 1.000.000.000.000.000 -> 1.0T
    } else if (value >= 1_000_000_000_000) {
      // Jika triliunan
      return `Rp Triliun : `; // Contoh: 1.000.000.000.000 -> 1.0T
    } else if (value >= 1_000_000_000) {
      // Jika miliaran
      return `Rp Miliar : `; // Contoh: 1.000.000.000 -> 1.0M
    } else if (value >= 1_000_000) {
      // Jika jutaan
      return `Rp Juta :`; // Contoh: 1.000.000 -> 1.0Jt
    } else if (value >= 1_000) {
      // Jika ribuan
      return `Rp Ribu : `; // Contoh: 1.000 -> 1.0Rb
    } else {
      // Jika kurang dari ribuan, biarkan seperti biasa
      return value.toString(); // Contoh: 500 -> 500
    }
  }

  export function satuanNumberEn(value: number) {
    if (value >= 1_000_000_000_000_000) {
      // Quadrillion
      return `Rp Quadrillion : ${value / 1_000_000_000_000_000}Q`;
    } else if (value >= 1_000_000_000_000) {
      // Trillion
      return `Rp Trillion : ${value / 1_000_000_000_000}T`;
    } else if (value >= 1_000_000_000) {
      // Billion
      return `Rp Billion : ${value / 1_000_000_000}B`;
    } else if (value >= 1_000_000) {
      // Million
      return `Rp Million : ${value / 1_000_000}M`;
    } else if (value >= 1_000) {
      // Thousand
      return `Rp Thousand : ${value / 1_000}K`;
    } else {
      // Less than thousand
      return value.toString();
    }
  }


  export async function configUpload(params:string) {

      const conf = await prisma.config_upload.findFirst({
          where: {
            jenis:params
          },
          select:{
            size:true,
            extnames:true
          }
        })
      const config = {
        ...conf,
        extnames: JSON.parse(conf.extnames.replace(/'/g, '"'))
      };

      return config
    }

    export async function img_to_webp(file, uploadPath = 'uploads', response: HttpContext) {
      if (!file.isValid) {
        throw {
          success: false,
          message: file.errors.map((err) => ({
            fieldName: err.fieldName,
            clientName: err.clientName,
            message: err.message,
            type: err.type,
          })),
        };
      }

      const invalidFileNamePattern = /[<>:"/\\|?*]/;
      if (invalidFileNamePattern.test(file.clientName)) {
        throw {
          success: false,
          message: `Nama File Tidak Valid! File: ${file.clientName}`,
        };
      }

      // Baca file sebagai Buffer (bukan teks)
      const fileContent = fs.readFileSync(file.tmpPath); // Buffer untuk file biner
      const maliciousCheck = await containsMaliciousCode(fileContent);
      console.log('maliciousCheck');
      console.log(maliciousCheck);
        if (maliciousCheck.hasMaliciousCode) {
          throw {
            success: false,
            message: `File Berpotensi Berbahaya! File: ${file.clientName}`,
            details: maliciousCheck.detectedPatterns
          };
        }

      const filename = `${DateTime.now().toFormat('yyyyMMddHHmmss')}${Math.floor(1000 + Math.random() * 9000)}.webp`;
      const outputPath = path.join(uploadPath, filename);
      if (!fs.existsSync(uploadPath)) {
        fs.mkdirSync(uploadPath, { recursive: true, mode: 0o755 });
      }

      const compressFile = await compressImage(file.tmpPath, outputPath, 200);

      return {
        status: true,
        message: 'File uploaded successfully',
        data: {
          filename: compressFile.filename.split('/').pop(),
          originalName: file.clientName,
          path: outputPath,
        },
      };
    }



      async function compressImage(filePath, outputPath, maxSizeKB) {
        let quality = 100; // Start with the highest quality
        let compressedImage;
        do {
          compressedImage = await sharp(filePath)
            .webp({ quality })
            .toBuffer();
          const sizeKB = compressedImage.length / 1024;

          if (sizeKB > maxSizeKB) {
            quality -= 5;
          }

        } while (compressedImage.length / 1024 > maxSizeKB && quality > 0); // Loop until size is below the max limit

        await fs.promises.writeFile(outputPath, compressedImage);
        return {
          filename : outputPath
        }
      }



      // async function containsMaliciousCode(content) {
      //   const patterns = [
      //     /<\s*script[^>]*>/i,
      //     /<\/\s*script\s*>/i,
      //     /\bfunction\b/i,
      //     /\bimport\b|\bexport\b|\bclass\b/i,
      //     /\bif\s*\(|\belse\b|\bfor\s*\(|\bwhile\s*\(/i,
      //     /console\.log\(/,
      //     /#include\s*<[^>]+>/,
      //     /;$/,
      //     /<\?php/i,
      //     /\becho\b/i,
      //     /\binclude\b|\brequire\b/i,
      //     /\bvar_dump\b|\bprint_r\b/i,
      //     /\bisset\b|\bempty\b/i,
      //     /\barray\s*\(/i,
      //     /\bmysql_|pdo_|mysqli_/i,
      //     /\bpublic\b|\bprivate\b|\bprotected\b|\bnamespace\b|\buse\b/i,
      //   ];
      //   return patterns.some((pattern) => pattern.test(content));
      // }

      async function containsMaliciousCode(content) {
        if (Buffer.isBuffer(content)) {
          content = content.toString('utf-8');
        }
      
        const contentString = String(content);
        const patterns = [
          { pattern: /<\s*script[^>]*>/i, name: 'Script tag opening' },
          { pattern: /<\/\s*script\s*>/i, name: 'Script tag closing' },
          { pattern: /\bfunction\b/i, name: 'Function keyword' },
          { pattern: /\bimport\b|\bexport\b|\bclass\b/i, name: 'Import/Export/Class keywords' },
          // { pattern: /\bif\s*\(|\belse\b|\bfor\s*\(|\bwhile\s*\(/i, name: 'Control flow statements' },
          { pattern: /console\.log\(/, name: 'Console.log' },
          { pattern: /#include\s*<[^>]+>/, name: 'C/C++ include' },
          { pattern: /;$/, name: 'Semicolon at end' },
          { pattern: /<\?php/i, name: 'PHP opening tag' },
          { pattern: /\becho\b/i, name: 'Echo statement' },
          { pattern: /\binclude\b|\brequire\b/i, name: 'Include/Require statements' },
          { pattern: /\bvar_dump\b|\bprint_r\b/i, name: 'PHP debug functions' },
          { pattern: /\bisset\b|\bempty\b/i, name: 'PHP check functions' },
          { pattern: /\barray\s*\(/i, name: 'Array declaration' },
          { pattern: /\bmysql_|pdo_|mysqli_/i, name: 'Database functions' },
          { pattern: /\bpublic\b|\bprivate\b|\bprotected\b|\bnamespace\b/i, name: 'OOP keywords' }
        ];
      
        const foundPatterns = [];
        
        for (const { pattern, name } of patterns) {
          if (pattern.test(contentString)) {
            foundPatterns.push({
              name,
              match: contentString.match(pattern)?.[0] || 'matched'
            });
          }
        }
      
      
        if (foundPatterns.length > 0) {
          return {
            hasMaliciousCode: true,
            detectedPatterns: foundPatterns
          };
        }
      
        return {
          hasMaliciousCode: false,
          detectedPatterns: []
        };
      }


      export async function upload(file,uploadPath:string='uploads') {
        const randomNumbers = Math.floor(10000 + Math.random() * 90000);
        const filename = `${DateTime.now().toFormat('yyyyMMddHHmmss')}${randomNumbers}.${file.extname}`
        const outputPath = path.join(uploadPath, filename)
        if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true, mode: 0o755 });
        }


          // Check if the file is valid before proceeding
          if (!file.isValid) {
              return {
                  status: false,
                  message: 'File is not valid',
                  errors: file.errors // Include any validation errors
              };
          }

          // Simulating file move - in actual implementation this may differ based on how you receive the file
          // Assuming file is a stream, if you are using request.file(), handle it accordingly
          const fileStream = file; // Adjust this if you are using file stream from the request

          // Move the file to the designated output path
          await fileStream.move(uploadPath, {
              name: filename,
              overwrite: true // Optionally overwrite if the file already exists
          });
          return {
            status: true,
            message: 'File uploaded successfully',
            data: {
              filename:file.fileName, // Ambil nama file dari path
              originalName: file.clientName,
              path: outputPath
            }
          };

      }

       export async function checkFileOrUrl(input) {
          const fileRegex = /^[^\\/:*?"<>|]+\.[a-zA-Z0-9]+$/;
          const urlRegex = /^(https?:\/\/)?([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(\/\S*)?$/;

          if (fileRegex.test(input)) {
              return "file";
          } else {
            return "url";
          }
      }

      export function normalStr(): RegExp {
       return /^[a-zA-Z0-9.,!?;:()'"“”\-]+$/
      }


/**
 * Mengubah string menjadi format title case dengan mengganti tanda - atau _ menjadi spasi
 * Contoh: "aku_menjadi-NORMAL" menjadi "Aku Menjadi Normal"
 *
 * @param str String yang akan diubah
 * @returns String yang sudah diformat
 */
export function titleCase(str: string): string {
  if (!str) return '';

  // Ganti tanda - atau _ dengan spasi
  const stringWithSpaces = str.replace(/[-_]/g, ' ');

  // Ubah string menjadi lowercase terlebih dahulu
  const lowerCaseString = stringWithSpaces.toLowerCase();

  // Ubah huruf pertama setiap kata menjadi uppercase
  return lowerCaseString.replace(
    /\b\w/g,
    (char) => char.toUpperCase()
  );
}

export async function get_detail_ip(clientIp:string) {

    try {
      const apiUrl = `https://ipapi.co/${clientIp}/country_name`;
      console.log(`Fetching data from: ${apiUrl}`);
      
      const fetchResponse = await fetch(apiUrl, {
          headers: {
              'User-Agent': 'Mozilla/5.0 (compatible; YourApp/1.0)'
          }
      });
      
      if (!fetchResponse.ok) {
          console.error(`Error status: ${fetchResponse.status}`);
          console.error(`Response text: ${await fetchResponse.text()}`);
          throw new Error(`HTTP error! status: ${fetchResponse.status}`);
      }
      
      return  await fetchResponse.text()
    } catch (error) {
      
      return null
    }
}