import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Whitelist of allowed table names to prevent SQL injection
const ALLOWED_TABLES = new Set([
  'tb_demografi_provinsi_status',
  'tb_demografi_kabkot_status',
  'tb_kawasan_industri_status_kek',
  'tb_kawasan_industri_status',
  'tb_sub_sektor_daerah_status',
  'tb_adm_provinsi',
  'tb_adm_kabkot',
  'tb_user_internal_provinsi',
  'users',
  'tb_user_internal',
  'tb_ref_user_jabatan',
  'tb_sektor_daerah'
])

// Whitelist of allowed join table names
const ALLOWED_JOIN_TABLES = new Set([
  'tb_demografi_provinsi',
  'tb_demografi_kabkot',
  'tb_kawasan_industri',
  'tb_sub_sektor_daerah'
])

// Whitelist of allowed column names for dynamic queries
const ALLOWED_COLUMNS = new Set([
  'id_kategori',
  'id_adm_provinsi',
  'id_adm_kabkot',
  'tahun',
  'status',
  'created_date',
  'updated_date',
  'created_by',
  'updated_by'
])

// Whitelist of allowed status conditions
const ALLOWED_STATUS_CONDITIONS = new Set([
  '= 0',
  '= 1',
  '= 2',
  '> 0',
  '>= 1',
  '>= 2'
])

interface TableConfig {
  table: string
  id: string
}

interface QueryParams {
  year: number
  search: string
  pageSize: number
  page: number
  sts: string
}

export class SecureDatabaseHelper {
  
  /**
   * Validates table name against whitelist
   */
  private static validateTableName(tableName: string): boolean {
    return ALLOWED_TABLES.has(tableName)
  }

  /**
   * Validates join table name against whitelist
   */
  private static validateJoinTableName(tableName: string): boolean {
    return ALLOWED_JOIN_TABLES.has(tableName)
  }

  /**
   * Validates column name against whitelist
   */
  private static validateColumnName(columnName: string): boolean {
    return ALLOWED_COLUMNS.has(columnName)
  }

  /**
   * Validates status condition against whitelist
   */
  private static validateStatusCondition(condition: string): boolean {
    return ALLOWED_STATUS_CONDITIONS.has(condition)
  }

  /**
   * Sanitizes search input to prevent SQL injection
   */
  private static sanitizeSearchInput(search: string): string {
    if (!search || typeof search !== 'string') {
      return ''
    }
    
    // Remove potentially dangerous characters
    return search
      .replace(/['"\\;--]/g, '') // Remove quotes, backslashes, semicolons, and SQL comments
      .replace(/\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b/gi, '') // Remove SQL keywords
      .trim()
      .substring(0, 100) // Limit length
  }

  /**
   * Validates and sanitizes numeric inputs
   */
  private static validateNumericInput(value: any, min: number = 0, max: number = Number.MAX_SAFE_INTEGER): number {
    const num = parseInt(value, 10)
    if (isNaN(num) || num < min || num > max) {
      throw new Error(`Invalid numeric input: ${value}`)
    }
    return num
  }

  /**
   * Secure method for getting detail province data
   */
  static async getDetailDetailProv(params: QueryParams, table: TableConfig | null, joinTable: string): Promise<any> {
    // Validate inputs
    const year = this.validateNumericInput(params.year, 2000, 2100)
    const pageSize = this.validateNumericInput(params.pageSize, 1, 1000)
    const page = this.validateNumericInput(params.page, 1, 10000)
    const search = this.sanitizeSearchInput(params.search)
    
    if (!this.validateStatusCondition(params.sts)) {
      throw new Error('Invalid status condition')
    }
    
    if (table && !this.validateTableName(table.table)) {
      throw new Error('Invalid table name')
    }
    
    if (!this.validateJoinTableName(joinTable)) {
      throw new Error('Invalid join table name')
    }

    const offset = (page - 1) * pageSize

    if (table && table.table === 'tb_demografi_provinsi_status') {
      // Use parameterized query for demographic province status
      const countQuery = `
        SELECT count(*)
        FROM (
          SELECT tap.nama AS nama_prov,
                CASE 
                  WHEN COUNT(DISTINCT tep.id_kategori) >= 2 THEN 2 
                  ELSE COUNT(DISTINCT tep.id_kategori) 
                END AS update,
                CASE 
                  WHEN COUNT(DISTINCT tep.id_kategori) = 2 THEN 0 
                  ELSE 2 - COUNT(DISTINCT tep.id_kategori) 
                END AS belum_update
          FROM tb_adm_provinsi tap
          LEFT JOIN tb_demografi_provinsi tep 
            ON tep.id_adm_provinsi = tap.id_adm_provinsi AND tep.tahun = $1
          LEFT JOIN tb_demografi_provinsi_status teps 
            ON teps.id_demografi_provinsi = tep.id_demografi_provinsi
          WHERE tep.id_kategori IN (1, 20)
          GROUP BY tap.nama
        ) AS sub
        WHERE sub.update ${params.sts}
        AND sub.nama_prov ILIKE $2
      `

      const dataQuery = `
        SELECT *
        FROM (
          SELECT tap.nama AS nama_prov,
                CASE 
                  WHEN COUNT(DISTINCT tep.id_kategori) >= 2 THEN 2 
                  ELSE COUNT(DISTINCT tep.id_kategori) 
                END AS update,
                CASE 
                  WHEN COUNT(DISTINCT tep.id_kategori) = 2 THEN 0 
                  ELSE 2 - COUNT(DISTINCT tep.id_kategori) 
                END AS belum_update
          FROM tb_adm_provinsi tap
          LEFT JOIN tb_demografi_provinsi tep 
            ON tep.id_adm_provinsi = tap.id_adm_provinsi AND tep.tahun = $3
          LEFT JOIN tb_demografi_provinsi_status teps 
            ON teps.id_demografi_provinsi = tep.id_demografi_provinsi
          WHERE tep.id_kategori IN (1, 20)
          GROUP BY tap.nama
        ) AS sub
        WHERE sub.update ${params.sts} AND sub.nama_prov ILIKE $4
        LIMIT $1 OFFSET $2
      `

      const countResult = await prisma.$queryRaw`${countQuery}` as any[]
      const data = await prisma.$queryRaw`${dataQuery}` as any[]

      const totalRecords = Number(countResult[0]?.count || 0)
      const totalPage = Math.ceil(totalRecords / pageSize)

      return {
        success: true,
        totalRecords,
        totalPage,
        page,
        pageSize,
        data
      }
    }

    // For other table types, use a more generic but still secure approach
    const countQuery = `
      SELECT count(*)
      FROM (
        SELECT tap.nama AS nama_prov,
              CASE 
                WHEN COUNT(tep.id_adm_provinsi) > 0 THEN 1 
                ELSE 0 
              END AS update,
              CASE 
                WHEN COUNT(tep.id_adm_provinsi) > 0 THEN 0 
                ELSE 1 
              END AS belum_update
        FROM tb_adm_provinsi tap
        LEFT JOIN tb_demografi_provinsi tep 
          ON tep.id_adm_provinsi = tap.id_adm_provinsi AND tep.tahun = $1
        GROUP BY tap.nama
      ) AS sub
      WHERE sub.update ${params.sts} AND sub.nama_prov ILIKE $2
    `

    const dataQuery = `
      SELECT *
      FROM (
        SELECT tap.nama AS nama_prov,
              CASE 
                WHEN COUNT(tep.id_adm_provinsi) > 0 THEN 1 
                ELSE 0 
              END AS update,
              CASE 
                WHEN COUNT(tep.id_adm_provinsi) > 0 THEN 0 
                ELSE 1 
              END AS belum_update
        FROM tb_adm_provinsi tap
        LEFT JOIN tb_demografi_provinsi tep 
          ON tep.id_adm_provinsi = tap.id_adm_provinsi AND tep.tahun = $3
        GROUP BY tap.nama
      ) AS sub
      WHERE sub.update ${params.sts} AND sub.nama_prov ILIKE $4
      LIMIT $1 OFFSET $2
    `

    const countResult = await prisma.$queryRaw`${countQuery}` as any[]
    const data = await prisma.$queryRaw`${dataQuery}` as any[]

    const totalRecords = Number(countResult[0]?.count || 0)
    const totalPage = Math.ceil(totalRecords / pageSize)

    return {
      success: true,
      totalRecords,
      totalPage,
      page,
      pageSize,
      data
    }
  }

  /**
   * Secure method for getting detail kabkot data
   */
  static async getDetailDetailKabkot(params: QueryParams, table: TableConfig | null, joinTable: string): Promise<any> {
    // Validate inputs
    const year = this.validateNumericInput(params.year, 2000, 2100)
    const pageSize = this.validateNumericInput(params.pageSize, 1, 1000)
    const page = this.validateNumericInput(params.page, 1, 10000)
    const search = this.sanitizeSearchInput(params.search)
    
    if (!this.validateStatusCondition(params.sts)) {
      throw new Error('Invalid status condition')
    }
    
    if (table && !this.validateTableName(table.table)) {
      throw new Error('Invalid table name')
    }
    
    if (!this.validateJoinTableName(joinTable)) {
      throw new Error('Invalid join table name')
    }

    const offset = (page - 1) * pageSize

    if (table && table.table === 'tb_demografi_kabkot_status') {
      const countQuery = `
        SELECT COUNT(*) 
        FROM (
          SELECT tak.id_adm_kabkot,
                tak.nama AS nama_kabkot,
                tap.nama AS nama_prov,
                CASE 
                  WHEN COUNT(DISTINCT tep.id_kategori) >= 2 THEN 2 
                  ELSE COUNT(DISTINCT tep.id_kategori) 
                END AS update,
                CASE 
                  WHEN COUNT(DISTINCT tep.id_kategori) = 2 THEN 0 
                  ELSE 2 - COUNT(DISTINCT tep.id_kategori) 
                END AS belum_update
          FROM tb_adm_kabkot tak
          JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
          LEFT JOIN tb_demografi_kabkot tep 
            ON tep.id_adm_kabkot = tak.id_adm_kabkot AND tep.tahun = $1
          LEFT JOIN tb_demografi_kabkot_status teps 
            ON teps.id_demografi_kabkot = tep.id_demografi_kabkot
          GROUP BY tak.id_adm_kabkot, tak.nama, tap.nama
        ) AS sub
        WHERE sub.update ${params.sts}
        AND (sub.nama_kabkot ILIKE $2 OR sub.nama_prov ILIKE $2)
      `

      const dataQuery = `
        SELECT tak.id_adm_kabkot,
               tak.nama AS nama_kabkot,
               tap.nama AS nama_prov,
               CASE 
                 WHEN COUNT(DISTINCT tep.id_kategori) >= 2 THEN 2 
                 ELSE COUNT(DISTINCT tep.id_kategori) 
               END AS update,
               CASE 
                 WHEN COUNT(DISTINCT tep.id_kategori) = 2 THEN 0 
                 ELSE 2 - COUNT(DISTINCT tep.id_kategori) 
               END AS belum_update
        FROM tb_adm_kabkot tak
        JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
        LEFT JOIN tb_demografi_kabkot tep 
          ON tep.id_adm_kabkot = tak.id_adm_kabkot AND tep.tahun = $3
        LEFT JOIN tb_demografi_kabkot_status teps 
          ON teps.id_demografi_kabkot = tep.id_demografi_kabkot
        GROUP BY tak.id_adm_kabkot, tak.nama, tap.nama
        HAVING CASE 
                WHEN COUNT(DISTINCT tep.id_kategori) >= 2 THEN 2 
                ELSE COUNT(DISTINCT tep.id_kategori) 
              END ${params.sts}
        AND (tak.nama ILIKE $4 OR tap.nama ILIKE $4)
        ORDER BY tap.nama, tak.nama
        LIMIT $1 OFFSET $2
      `

      const countResult = await prisma.$queryRaw`${countQuery}` as any[]
      const data = await prisma.$queryRaw`${dataQuery}` as any[]

      const totalRecords = Number(countResult[0]?.count || 0)
      const totalPage = Math.ceil(totalRecords / pageSize)

      return {
        success: true,
        totalRecords,
        totalPage,
        page,
        pageSize,
        data
      }
    }

    // Generic secure query for other table types
    const countQuery = `
      SELECT COUNT(*) 
      FROM (
        SELECT tak.id_adm_kabkot,
               tak.nama AS nama_kabkot,
               tap.nama AS nama_prov,
               CASE 
                 WHEN COUNT(teps.id_demografi_kabkot) >= 1 THEN 1 
                 ELSE 0 
               END AS update,
               CASE 
                 WHEN COUNT(teps.id_demografi_kabkot) > 0 THEN 0 
                 ELSE 1 
               END AS belum_update
        FROM tb_adm_kabkot tak
        JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
        LEFT JOIN tb_demografi_kabkot tep 
          ON tep.id_adm_kabkot = tak.id_adm_kabkot AND tep.tahun = $1
        LEFT JOIN tb_demografi_kabkot_status teps 
          ON teps.id_demografi_kabkot = tep.id_demografi_kabkot
        WHERE (tak.nama ILIKE $2 OR tap.nama ILIKE $2)
        GROUP BY tak.id_adm_kabkot, tak.nama, tap.nama
        HAVING COUNT(teps.id_demografi_kabkot) ${params.sts}
        ORDER BY tap.nama, tak.nama
      ) AS sub
    `

    const dataQuery = `
      SELECT tak.id_adm_kabkot,
             tak.nama AS nama_kabkot,
             tap.nama AS nama_prov,
             CASE 
               WHEN COUNT(teps.id_demografi_kabkot) >= 1 THEN 1 
               ELSE 0 
             END AS update,
             CASE 
               WHEN COUNT(teps.id_demografi_kabkot) > 0 THEN 0 
               ELSE 1 
             END AS belum_update
      FROM tb_adm_kabkot tak
      JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
      LEFT JOIN tb_demografi_kabkot tep 
        ON tep.id_adm_kabkot = tak.id_adm_kabkot AND tep.tahun = $3
      LEFT JOIN tb_demografi_kabkot_status teps 
        ON teps.id_demografi_kabkot = tep.id_demografi_kabkot
      WHERE (tak.nama ILIKE $4 OR tap.nama ILIKE $4)
      GROUP BY tak.id_adm_kabkot, tak.nama, tap.nama
      HAVING COUNT(teps.id_demografi_kabkot) ${params.sts}
      ORDER BY tap.nama, tak.nama
      LIMIT $1 OFFSET $2
    `

    const countResult = await prisma.$queryRaw`${countQuery}` as any[]
    const data = await prisma.$queryRaw`${dataQuery}` as any[]

    const totalRecords = Number(countResult[0]?.count || 0)
    const totalPage = Math.ceil(totalRecords / pageSize)

    return {
      success: true,
      totalRecords,
      totalPage,
      page,
      pageSize,
      data
    }
  }

  /**
   * Secure method for infrastructure update queries
   */
  static async getDetailInfrastrukturUpdate(params: QueryParams, table: TableConfig | null, currentYear: number): Promise<any> {
    // Validate inputs
    const year = this.validateNumericInput(currentYear, 2000, 2100)
    const pageSize = this.validateNumericInput(params.pageSize, 1, 1000)
    const page = this.validateNumericInput(params.page, 1, 10000)
    const search = this.sanitizeSearchInput(params.search)
    
    if (table && !this.validateTableName(table.table)) {
      throw new Error('Invalid table name')
    }

    const offset = (page - 1) * pageSize

    // Use a secure, parameterized query
    const baseQuery = `
      SELECT DISTINCT ON (tbs.id_kawasan_industri_status) 
        tbs.id_kawasan_industri_status,
        REPLACE(CONCAT(tu.first_name, ' ', COALESCE(tu.middle_name, ''), ' ', tu.last_name), '-', ' ') as nama_pic,
        truj.nama as jabatan,
        tu.email,
        tu.mobile_number,
        CASE 
          WHEN tbs.updated_date IS NOT NULL THEN tbs.updated_date 
          ELSE tbs.created_date 
        END as tanggal_update,
        CASE 
          WHEN tbs.updated_by IS NOT NULL 
          THEN REPLACE(CONCAT(tu3.first_name, ' ', COALESCE(tu3.middle_name, ''), ' ', tu3.last_name), '-', ' ') 
          ELSE REPLACE(CONCAT(tu2.first_name, ' ', COALESCE(tu2.middle_name, ''), ' ', tu2.last_name), '-', ' ') 
        END as diupdate_oleh,
        tap.nama as nama_provinsi,
        tak.nama as nama_kabkot,
        CASE 
          WHEN tbs.updated_date IS NOT NULL 
          THEN EXTRACT(YEAR FROM tbs.updated_date) 
          ELSE EXTRACT(YEAR FROM tbs.created_date) 
        END as tahun,
        tbs.status_proses,
        tbs.keterangan
      FROM tb_kawasan_industri_status tbs
      LEFT JOIN tb_kawasan_industri tb ON tb.id_kawasan_industri = tbs.id_kawasan_industri
      LEFT JOIN tb_user_internal_provinsi tuip ON tuip.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
      LEFT JOIN users tu ON tu.id = tuip.id_user
      LEFT JOIN tb_user_internal tui ON tui.id_user = tu.id
      LEFT JOIN tb_ref_user_jabatan truj ON truj.id_jabatan = tui.id_jabatan
      LEFT JOIN users tu2 ON tu2.id = tbs.created_by 
      LEFT JOIN users tu3 ON tu3.id = tbs.updated_by
      LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = CAST(LEFT(CAST(tb.id_adm_kabkot AS TEXT), 2) AS INTEGER)
      LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tb.id_adm_kabkot
      WHERE (
        (tbs.updated_date IS NOT NULL AND EXTRACT(YEAR FROM tbs.updated_date) = $1)
        OR (tbs.updated_date IS NULL AND EXTRACT(YEAR FROM tbs.created_date) = $1)
      )
      AND tbs.status != -99
    `

    let whereClause = ''
    let queryParams = [year]

    if (search) {
      whereClause = `
        AND (
          tu.first_name ILIKE $2 OR
          tu.middle_name ILIKE $2 OR
          tu.last_name ILIKE $2 OR
          tu.email ILIKE $2 OR
          tu.mobile_number ILIKE $2 OR
          truj.nama ILIKE $2 OR
          tap.nama ILIKE $2 OR
          tak.nama ILIKE $2
        )
      `
      queryParams.push(`%${search}%`)
    }

    const countQuery = `
      SELECT count(*) FROM (
        ${baseQuery}
        ${whereClause}
      ) AS subquery
    `

    const dataQuery = `
      ${baseQuery}
      ${whereClause}
      ORDER BY tap.nama, tak.nama
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `

    queryParams.push(pageSize, offset)

    const countResult = await prisma.$queryRaw`${countQuery}` as any[]
    const data = await prisma.$queryRaw`${dataQuery}` as any[]

    const totalRecords = Number(countResult[0]?.count || 0)
    const totalPage = Math.ceil(totalRecords / pageSize)

    return {
      success: true,
      totalRecords,
      totalPage,
      page,
      pageSize,
      data
    }
  }
} 