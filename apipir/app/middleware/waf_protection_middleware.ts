import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'

interface ThreatSignature {
  name: string
  pattern: RegExp
  severity: 'low' | 'medium' | 'high' | 'critical'
  action: 'log' | 'block' | 'challenge'
  description: string
}

interface IPReputation {
  ip: string
  score: number // 0-100, higher is more suspicious
  lastSeen: number
  violations: string[]
}

@inject()
export default class WAFProtectionMiddleware {
  private readonly ipReputationStore = new Map<string, IPReputation>()
  private readonly maxReputationEntries = 10000
  private readonly reputationCleanupInterval = 24 * 60 * 60 * 1000 // 24 hours
  
  // Comprehensive threat signatures
  private readonly threatSignatures: ThreatSignature[] = [
    // SQL Injection signatures
    {
      name: 'SQL_INJECTION_UNION',
      pattern: /(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)/i,
      severity: 'critical',
      action: 'block',
      description: 'SQL injection attempt using UNION SELECT'
    },
    {
      name: 'SQL_INJECTION_BOOLEAN',
      pattern: /(\bor\b.*1\s*=\s*1)|(\band\b.*1\s*=\s*1)|(\bor\b.*'.*'.*=.*'.*')/i,
      severity: 'critical',
      action: 'block',
      description: 'SQL injection attempt using boolean logic'
    },
    {
      name: 'SQL_INJECTION_STACKED',
      pattern: /;\s*(drop|delete|insert|update|create|alter)\b/i,
      severity: 'critical',
      action: 'block',
      description: 'SQL injection attempt using stacked queries'
    },
    
    // XSS signatures
    {
      name: 'XSS_SCRIPT_TAG',
      pattern: /<script[\s\S]*?>[\s\S]*?<\/script>/i,
      severity: 'high',
      action: 'block',
      description: 'Cross-site scripting attempt using script tags'
    },
    {
      name: 'XSS_EVENT_HANDLER',
      pattern: /on(load|click|mouseover|error|focus|blur)\s*=/i,
      severity: 'high',
      action: 'block',
      description: 'Cross-site scripting attempt using event handlers'
    },
    {
      name: 'XSS_JAVASCRIPT_PROTOCOL',
      pattern: /javascript\s*:/i,
      severity: 'high',
      action: 'block',
      description: 'Cross-site scripting attempt using javascript protocol'
    },
    
    // Command Injection signatures
    {
      name: 'COMMAND_INJECTION_BASIC',
      pattern: /[;&|`$(){}[\]\\]/,
      severity: 'critical',
      action: 'block',
      description: 'Command injection attempt using shell metacharacters'
    },
    {
      name: 'COMMAND_INJECTION_UNIX',
      pattern: /\b(cat\s|ls\s|pwd\s|whoami\s|id\s|uname\s|ps\s|netstat\s|ifconfig\s|wget\s|curl\s|nc\s|telnet\s|bash\s|sh\s)/i,
      severity: 'critical',
      action: 'block',
      description: 'Command injection attempt using Unix commands'
    },
    
    // Path Traversal signatures
    {
      name: 'PATH_TRAVERSAL_BASIC',
      pattern: /\.\.[\/\\]/,
      severity: 'high',
      action: 'block',
      description: 'Path traversal attempt using dot-dot-slash'
    },
    {
      name: 'PATH_TRAVERSAL_ENCODED',
      pattern: /%2e%2e[%2f%5c]/i,
      severity: 'high',
      action: 'block',
      description: 'Path traversal attempt using URL encoding'
    },
    
    // LDAP Injection signatures
    {
      name: 'LDAP_INJECTION',
      pattern: /[()&|!*]/,
      severity: 'medium',
      action: 'log',
      description: 'LDAP injection attempt'
    },
    
    // XML/XXE signatures
    {
      name: 'XXE_EXTERNAL_ENTITY',
      pattern: /<!ENTITY.*SYSTEM/i,
      severity: 'high',
      action: 'block',
      description: 'XML External Entity (XXE) attack attempt'
    },
    
    // SSRF signatures
    {
      name: 'SSRF_LOCALHOST',
      pattern: /(localhost|127\.0\.0\.1|0\.0\.0\.0|::1)/i,
      severity: 'medium',
      action: 'log',
      description: 'Server-Side Request Forgery attempt targeting localhost'
    },
    {
      name: 'SSRF_PRIVATE_IP',
      pattern: /(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)/,
      severity: 'medium',
      action: 'log',
      description: 'Server-Side Request Forgery attempt targeting private IP'
    },
    
    // File Inclusion signatures
    {
      name: 'LFI_ATTEMPT',
      pattern: /(\/etc\/passwd|\/proc\/self\/environ|\.\.\/)/i,
      severity: 'high',
      action: 'block',
      description: 'Local File Inclusion attempt'
    },
    
    // Scanner detection
    {
      name: 'SCANNER_USER_AGENT',
      pattern: /(sqlmap|nikto|nessus|burp|nmap|masscan|zap|gobuster|dirb|dirbuster|acunetix)/i,
      severity: 'medium',
      action: 'challenge',
      description: 'Security scanner detected'
    }
  ]

  private updateIPReputation(ip: string, violation: string, severityScore: number): void {
    let reputation = this.ipReputationStore.get(ip)
    
    if (!reputation) {
      reputation = {
        ip,
        score: 0,
        lastSeen: Date.now(),
        violations: []
      }
    }
    
    reputation.score = Math.min(100, reputation.score + severityScore)
    reputation.lastSeen = Date.now()
    reputation.violations.push(`${new Date().toISOString()}: ${violation}`)
    
    // Keep only last 10 violations
    if (reputation.violations.length > 10) {
      reputation.violations = reputation.violations.slice(-10)
    }
    
    this.ipReputationStore.set(ip, reputation)
    
    // Cleanup old entries if store is getting too large
    if (this.ipReputationStore.size > this.maxReputationEntries) {
      this.cleanupOldReputationEntries()
    }
  }

  private cleanupOldReputationEntries(): void {
    const now = Date.now()
    const cutoff = now - this.reputationCleanupInterval
    
    for (const [ip, reputation] of this.ipReputationStore.entries()) {
      if (reputation.lastSeen < cutoff) {
        this.ipReputationStore.delete(ip)
      }
    }
  }

  private getSeverityScore(severity: string): number {
    switch (severity) {
      case 'critical': return 25
      case 'high': return 15
      case 'medium': return 10
      case 'low': return 5
      default: return 0
    }
  }

  private analyzeRequest(request: any): { threats: ThreatSignature[]; riskScore: number } {
    const threats: ThreatSignature[] = []
    let riskScore = 0
    
    // Get request data to analyze
    const url = request.url()
    const userAgent = request.header('user-agent') || ''
    const referer = request.header('referer') || ''
    const queryParams = request.qs()
    const body = request.body()
    
    // Analyze different parts of the request with appropriate signatures
    for (const signature of this.threatSignatures) {
      // For command injection, only check query params and body data
      if (signature.name.startsWith('COMMAND_INJECTION')) {
        // For query parameters, check values only
        const queryValues = Object.values(queryParams).join(' ')
        if (signature.pattern.test(queryValues)) {
          threats.push(signature)
          riskScore += this.getSeverityScore(signature.severity)
          continue
        }
        
        // For body, only check string values
        if (body && typeof body === 'object') {
          const stringValues = Object.values(body)
            .filter(v => typeof v === 'string')
            .join(' ')
          if (signature.pattern.test(stringValues)) {
            threats.push(signature)
            riskScore += this.getSeverityScore(signature.severity)
          }
        }
        continue
      }
      
      // For other threats, check all request data
      const requestData = [
        url,
        userAgent,
        referer,
        JSON.stringify(queryParams),
        JSON.stringify(request.headers()),
        body ? JSON.stringify(body) : ''
      ].join(' ')
      
      if (signature.pattern.test(requestData)) {
        threats.push(signature)
        riskScore += this.getSeverityScore(signature.severity)
      }
    }
    
    return { threats, riskScore }
  }

  private logThreatDetection(
    ip: string,
    url: string,
    method: string,
    threats: ThreatSignature[],
    riskScore: number,
    userAgent: string
  ): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event_type: 'waf_threat_detection',
      ip_address: ip,
      url: url,
      method: method,
      user_agent: userAgent,
      threats_detected: threats.map(t => ({
        name: t.name,
        severity: t.severity,
        description: t.description,
        action: t.action
      })),
      risk_score: riskScore,
      ip_reputation: this.ipReputationStore.get(ip)
    }
    
    console.warn('[WAF] Threat Detection:', JSON.stringify(logEntry, null, 2))
  }

  async handle(ctx: HttpContext, next: () => Promise<void>) {
    const { request, response } = ctx
    const ip = request.ip()
    const url = request.url()
    const method = request.method()
    const userAgent = request.header('user-agent') || ''
    
    // Check IP reputation first
    const reputation = this.ipReputationStore.get(ip)
    if (reputation && reputation.score > 80) {
      this.logThreatDetection(ip, url, method, [], reputation.score, userAgent)
      return response.status(403).send({
        error: 'Access denied - IP reputation too low',
        status: 403,
        timestamp: new Date().toISOString()
      })
    }
    
    // Analyze request for threats
    const analysis = this.analyzeRequest(request)
    
    if (analysis.threats.length > 0) {
      // Update IP reputation
      for (const threat of analysis.threats) {
        this.updateIPReputation(ip, threat.name, this.getSeverityScore(threat.severity))
      }
      
      // Log the detection
      this.logThreatDetection(ip, url, method, analysis.threats, analysis.riskScore, userAgent)
      
      // Determine action based on highest severity threat
      const highestSeverityThreat = analysis.threats.reduce((prev, current) => {
        const severityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 }
        return severityOrder[current.severity] > severityOrder[prev.severity] ? current : prev
      })
      
      // Take action based on threat
      switch (highestSeverityThreat.action) {
        case 'block':
          return response.status(403).send({
            error: 'Request blocked by WAF',
            status: 403,
            timestamp: new Date().toISOString(),
            threat_id: highestSeverityThreat.name
          })
          
        case 'challenge':
          // In a real implementation, this could trigger CAPTCHA or other challenges
          response.header('X-WAF-Challenge', 'required')
          response.header('X-WAF-Threat-Level', highestSeverityThreat.severity)
          break
          
        case 'log':
          // Just log and continue
          response.header('X-WAF-Monitored', 'true')
          break
      }
    }
    
    // Add WAF headers to response
    response.header('X-WAF-Status', 'active')
    response.header('X-WAF-Request-ID', `waf-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`)
    
    await next()
  }
} 