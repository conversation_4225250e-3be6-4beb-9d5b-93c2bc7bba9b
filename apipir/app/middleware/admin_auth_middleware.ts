import type { HttpContext } from '@adonisjs/core/http'

export default class AdminAuthMiddleware {
  async handle(ctx: HttpContext, next: () => Promise<void>) {
    const { request, response } = ctx
    const url = request.url()
    
    // Only handle admin login requests
    if (!url.includes('/admin/auth/login')) {
      return next()
    }

    // Allow JSON content type
    const contentType = request.header('content-type')
    if (!contentType?.includes('application/json')) {
      return response.status(415).send({
        error: 'Unsupported Media Type - Only JSON is accepted',
        status: 415,
        timestamp: new Date().toISOString(),
      })
    }

    // Basic content length check
    const contentLength = request.header('content-length')
    if (!contentLength) {
      return response.status(411).send({
        error: 'Length Required',
        status: 411,
        timestamp: new Date().toISOString(),
      })
    }

    // Lenient content length validation for login
    const rawBody = request.raw() || ''
    const bodyLength = Buffer.byteLength(rawBody)
    const declaredLength = Number.parseInt(contentLength, 10)
    
    // More permissive length check for login requests
    if (isNaN(declaredLength) || declaredLength <= 0 || declaredLength > 10240) { // Max 10KB for login
      return response.status(400).send({
        error: 'Invalid Content Length',
        status: 400,
        timestamp: new Date().toISOString(),
      })
    }

    // Set security headers specific to admin login
    response.header('X-Content-Type-Options', 'nosniff')
    response.header('X-Frame-Options', 'DENY')
    response.header('Cache-Control', 'no-store, no-cache, must-revalidate')
    response.header('Pragma', 'no-cache')

    await next()
  }
}
