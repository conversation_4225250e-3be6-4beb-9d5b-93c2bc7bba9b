import type { HttpContext } from '@adonisjs/core/http'
import env from '#start/env'

export default class HttpSecurityMiddleware {
  private readonly MAX_CONTENT_LENGTH = 8 * 1024 * 1024 // 8MB for regular requests (reduced from 10MB)
  private readonly API_MAX_LENGTH = 20 * 1024 * 1024 // 20MB for API requests (reduced from 50MB)
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB for file uploads
  
  // List of security-critical headers that could be used in request smuggling
  private readonly BLOCKED_HEADERS = new Set([
    'proxy-connection',
    'x-http-method-override',
    'x-method-override'
  ])

  // Headers that are allowed when coming from trusted proxies
  private readonly PROXY_HEADERS = new Set([
    'x-forwarded-for',
    'x-forwarded-proto',
    'x-forwarded-host',
    'x-real-ip',
    'forwarded'
  ])

  // Whitelist of allowed HTTP methods
  private readonly ALLOWED_METHODS = new Set(['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'])

  private isApiRequest(url: string, method: string): boolean {
    const isApiPath = url.includes('/be/') || url.includes('/api/')
    const isApiMethod = this.ALLOWED_METHODS.has(method.toUpperCase())
    return isApiPath && isApiMethod
  }

  private isValidContentLength(contentLength: string | undefined, isApi: boolean): boolean {
    if (!contentLength) return true

    const length = Number.parseInt(contentLength, 10)
    if (Number.isNaN(length) || length < 0) return false

    // Different size limits for API vs regular requests
    const maxSize = isApi ? this.API_MAX_LENGTH : this.MAX_CONTENT_LENGTH
    return length <= maxSize
  }

  private isTrustedProxy(request: HttpContext['request']): boolean {
    // Get the actual client IP
    const clientIp = request.ip()
    
    // Get trusted proxy IPs from environment
    const trustedProxies = env.get('TRUSTED_PROXIES', '127.0.0.1,::1')
      .split(',')
      .map(ip => ip.trim())
      .filter(Boolean)
    
    // Check if the client IP is in the trusted list
    return trustedProxies.includes(clientIp)
  }

  // Enhanced CORS headers with strict origin validation
  private setCorsHeaders(response: HttpContext['response'], request: HttpContext['request']) {
    // Get allowed domains from environment with a fallback to prevent empty list
    const configuredDomains = env.get('APP_DOMAIN', '')
    const allowedDomains = configuredDomains ? 
      configuredDomains.split(',').map((domain) => domain.trim()).filter(Boolean) : 
      []
      
    // Always include the application's own domain
    const appHost = env.get('HOST', 'localhost')
    if (!allowedDomains.includes(appHost)) {
      allowedDomains.push(appHost)
    }
    
    const requestOrigin = request.header('Origin')

    // Only set CORS headers if origin is in the allowed list
    if (requestOrigin && allowedDomains.length > 0 && allowedDomains.includes(requestOrigin)) {
      response.header('Access-Control-Allow-Origin', requestOrigin)
      response.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS')
      response.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-CSRF-Token')
      response.header('Access-Control-Allow-Credentials', 'true')
      response.header('Vary', 'Origin')
    } else if (requestOrigin && allowedDomains.length === 0) {
      // If no domains are configured, default to same-origin policy
      response.header('Access-Control-Allow-Origin', appHost)
    }
  }

  async handle(ctx: HttpContext, next: () => Promise<void>) {
    const { request, response } = ctx
    const url = request.url()
    const method = request.method()
    
    // 1. Validate HTTP method - reject non-standard methods
    if (!this.ALLOWED_METHODS.has(method.toUpperCase())) {
      return response.status(405).send({
        error: 'Method not allowed',
        status: 405,
        timestamp: new Date().toISOString(),
      })
    }
    
    // 2. Check for blocked headers that could be used in request smuggling
    const headers = request.headers()
    for (const headerName of Object.keys(headers)) {
      const lowerHeaderName = headerName.toLowerCase()
      
      // Always block security-critical headers
      if (this.BLOCKED_HEADERS.has(lowerHeaderName)) {
        return response.status(400).send({
          error: 'Request contains blocked headers',
          status: 400,
          timestamp: new Date().toISOString(),
        })
      }
      
      // Allow proxy headers only from trusted sources
      if (this.PROXY_HEADERS.has(lowerHeaderName)) {
        const isTrustedProxy = this.isTrustedProxy(request)
        if (!isTrustedProxy) {
          return response.status(400).send({
            error: 'Proxy headers only allowed from trusted sources',
            status: 400,
            timestamp: new Date().toISOString(),
          })
        }
      }
    }
    
    const contentTypeHeader = request.header('content-type') || ''
    const rawBody = request.raw() || ''

    // 3. Strict validation of Transfer-Encoding and Content-Length headers
    const contentLengthHeader = request.header('content-length')
    const transferEncodingHeader = request.header('transfer-encoding')

    // Reject any form of Transfer-Encoding to prevent request smuggling
    if (transferEncodingHeader) {
      return response.status(400).send({
        error: 'Transfer-Encoding header is not allowed',
        status: 400,
        timestamp: new Date().toISOString(),
      })
    }

    // 4. Set CORS headers with strict origin validation
    this.setCorsHeaders(response, request)

    // 5. Check for request smuggling patterns in the body
    // Look for HTTP methods followed by a path and then an HTTP version
    const smugglingPatterns = [
      /^([A-Z]+)\s+\/[^\s]*\s+HTTP\/[0-9.]+/im,  // HTTP method pattern
      /\r\n\r\n([A-Z]+)\s+\/[^\s]*\s+HTTP\/[0-9.]+/im,  // Pattern after headers
      /Content-Length:\s*\d+\s*\r\n/im,  // Embedded Content-Length header
      /Transfer-Encoding:\s*chunked/im,  // Embedded Transfer-Encoding header
    ];
    
    for (const pattern of smugglingPatterns) {
      if (pattern.test(rawBody)) {
        return response.status(400).send({
          error: 'Potential HTTP request smuggling detected',
          status: 400,
          timestamp: new Date().toISOString(),
        })
      }
    }

    const isApi = this.isApiRequest(url, method)

    // 6. Set comprehensive security headers for all responses
    response.header('X-Content-Type-Options', 'nosniff')
    response.header('X-Frame-Options', 'DENY')
    response.header('X-XSS-Protection', '1; mode=block')
    response.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
    response.header('Referrer-Policy', 'strict-origin-when-cross-origin')
    response.header('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), interest-cohort=()')
    
    // Add Expect-CT header for Certificate Transparency monitoring
    response.header('Expect-CT', 'max-age=86400, enforce, report-uri="https://example.com/ct-report"')
    
    // Add additional security headers
    response.header('X-Permitted-Cross-Domain-Policies', 'none')
    response.header('Cross-Origin-Embedder-Policy', 'require-corp')
    response.header('Cross-Origin-Opener-Policy', 'same-origin')
    response.header('Cross-Origin-Resource-Policy', 'same-origin')
    
    // 7. Standardize connection handling to prevent desync
    response.header('Connection', 'close')
    
    // 8. Validate Content-Length
    if (contentLengthHeader) {
      if (!this.isValidContentLength(contentLengthHeader, isApi)) {
        return response.status(413).send({
          error: 'Content length exceeds limit',
          status: 413,
          timestamp: new Date().toISOString(),
        })
      }
      
      // Validate that Content-Length is a positive integer
      const length = Number.parseInt(contentLengthHeader, 10)
      if (isNaN(length) || length < 0 || length.toString() !== contentLengthHeader.trim()) {
        return response.status(400).send({
          error: 'Invalid Content-Length header',
          status: 400,
          timestamp: new Date().toISOString(),
        })
      }
    }

    // 9. Skip body validation for GET/HEAD/OPTIONS requests
    if (['GET', 'HEAD', 'OPTIONS'].includes(method.toUpperCase())) {
      await next()
      return
    }

    // 10. Allow empty bodies for DELETE requests
    if (method.toUpperCase() === 'DELETE' && !contentLengthHeader) {
      await next()
      return
    }

    // 11. Validate request body for write operations
    if (['PUT', 'PATCH', 'POST'].includes(method.toUpperCase())) {
      // Ensure Content-Type is set and valid
      if (!contentTypeHeader) {
        return response.status(415).send({
          error: 'Content-Type header is required',
          status: 415,
          timestamp: new Date().toISOString(),
        })
      }
      
      // Validate content type based on endpoint
      const validContentTypes = [
        'application/json',
        'application/x-www-form-urlencoded',
        'multipart/form-data',
        'text/plain'
      ]
      
      const hasValidContentType = validContentTypes.some(type => 
        contentTypeHeader.toLowerCase().startsWith(type.toLowerCase())
      )
      
      if (!hasValidContentType) {
        return response.status(415).send({
          error: 'Unsupported Media Type',
          status: 415,
          timestamp: new Date().toISOString(),
        })
      }
      
      // For non-multipart requests, validate body size
      if (!contentTypeHeader.toLowerCase().startsWith('multipart/form-data')) {
        const bodyLength = Buffer.byteLength(rawBody)
        const maxLength = isApi ? this.API_MAX_LENGTH : this.MAX_CONTENT_LENGTH

        if (bodyLength > maxLength) {
          return response.status(413).send({
            error: 'Payload too large',
            status: 413,
            timestamp: new Date().toISOString(),
          })
        }
        
        // Validate Content-Length against actual body size
        if (contentLengthHeader) {
          const declaredLength = Number.parseInt(contentLengthHeader, 10)
          // Allow for a small difference to account for encoding issues
          const allowedDiff = 128 // Strict 128 byte tolerance

          if (Math.abs(declaredLength - bodyLength) > allowedDiff) {
            return response.status(400).send({
              error: 'Content-Length mismatch with actual body size',
              status: 400,
              timestamp: new Date().toISOString(),
            })
          }
        }
      } else {
        // For multipart/form-data, we'll rely on the framework's built-in limits
        // but ensure a Content-Length header is present
        if (!contentLengthHeader) {
          return response.status(411).send({
            error: 'Length Required for multipart requests',
            status: 411,
            timestamp: new Date().toISOString(),
          })
        }
      }
    }

    await next()
  }
}