import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'

interface BodyParserConfig {
  bodyLimit: string
  processManually: string[]
  multipart: {
    maxFields: number
    maxFiles: number
  }
  encoding: string
}

@inject()
export default class SecureBodyParserMiddleware {
  private readonly MAX_CONTENT_LENGTH = 1 * 1024 * 1024 // 10MB
  private readonly ALLOWED_CONTENT_TYPES = new Set([
    'application/json',
    'application/x-www-form-urlencoded',
    'multipart/form-data',
  ])

  private readonly BLOCKED_PATTERNS = [
    /[^\x20-\x7E]+/g, // Non-printable characters
    /<script[\s\S]*?<\/script>/gi, // Script tags
    /javascript:/gi,
    /data:/gi,
    /%([0-9A-F]{2})/gi, // Percent encoding
    /\{\{[\s\S]*?\}\}/g, // Template injection
  ]

  constructor(private config: BodyParserConfig) {}

  private isValidContentType(contentType: string | undefined | null): boolean {
    if (!contentType) return false
    const baseType = contentType.split(';')[0].toLowerCase().trim()
    return this.ALLOWED_CONTENT_TYPES.has(baseType)
  }

  private containsMaliciousPatterns(body: string): boolean {
    return this.BLOCKED_PATTERNS.some((pattern) => pattern.test(body))
  }

  private validateMultipart(body: string): boolean {
    const boundaries = body.match(/boundary=([^\s;]+)/g)
    if (!boundaries) return true
    // Check for duplicate or malformed boundaries
    return boundaries.length === 1 && boundaries[0].length < 70
  }

  private validateJsonStructure(body: string): boolean {
    try {
      const parsed = JSON.parse(body)
      
      // Prevent prototype pollution
      const jsonString = JSON.stringify(parsed)
      const hasProto = 
        jsonString.includes('__proto__') ||
        jsonString.includes('constructor') ||
        jsonString.includes('prototype')
      
      if (hasProto) return false

      // Check for deeply nested structures
      const depth = (obj: any): number => {
        if (typeof obj !== 'object' || obj === null) return 0
        return 1 + Math.max(...Object.values(obj).map(depth), 0)
      }
      return depth(parsed) <= 20 // Max depth of 20
    } catch {
      return false
    }
  }

  async handle(ctx: HttpContext, next: () => Promise<void>) {
    const { request, response } = ctx
    const contentType = request.header('content-type')
    const contentLength = request.header('content-length')

    // Validate content type
    if (!this.isValidContentType(contentType)) {
      return response.status(415).send({
        error: 'Unsupported Media Type',
        status: 415,
        timestamp: new Date().toISOString(),
      })
    }

    // Enforce content length
    if (!contentLength) {
      return response.status(411).send({
        error: 'Length Required',
        status: 411,
        timestamp: new Date().toISOString(),
      })
    }

    // Check content length
    const length = Number.parseInt(contentLength, 10)
    if (Number.isNaN(length) || length > this.MAX_CONTENT_LENGTH) {
      return response.status(413).send({
        error: 'Payload Too Large',
        status: 413,
        timestamp: new Date().toISOString(),
      })
    }

    // Get raw body
    const rawBody = request.raw() || ''
    
    // Check for malicious patterns
    if (this.containsMaliciousPatterns(rawBody.toString())) {
      return response.status(400).send({
        error: 'Malicious Content Detected',
        status: 400,
        timestamp: new Date().toISOString(),
      })
    }

    // Specific checks based on content type
    const contentTypeStr = contentType || ''
    if (contentTypeStr.includes('application/json')) {
      if (!this.validateJsonStructure(rawBody.toString())) {
        return response.status(400).send({
          error: 'Invalid JSON Structure',
          status: 400,
          timestamp: new Date().toISOString(),
        })
      }
    }

    if (contentTypeStr.includes('multipart/form-data')) {
      if (!this.validateMultipart(rawBody.toString())) {
        return response.status(400).send({
          error: 'Invalid Multipart Format',
          status: 400,
          timestamp: new Date().toISOString(),
        })
      }
    }

    // Add security headers
    response.header('X-Content-Type-Options', 'nosniff')
    response.header('Content-Security-Policy', "default-src 'none'")
    response.header('X-Frame-Options', 'DENY')
    response.header('Transfer-Encoding', 'identity')

    // Override the original body parser config with secure defaults
    this.config.bodyLimit = '1mb'
    this.config.processManually = []
    this.config.multipart.maxFields = 50
    this.config.multipart.maxFiles = 10
    this.config.encoding = 'utf-8'

    await next()
  }
}