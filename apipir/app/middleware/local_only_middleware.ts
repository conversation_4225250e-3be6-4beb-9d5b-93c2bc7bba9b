import type { HttpContext } from '@adonisjs/core/http';

export default class LocalOnlyMiddleware {
  public async handle({ request, response }: HttpContext, next: () => Promise<void>) {
    // Deteksi IP Client
    const clientIp = request.ip();

    // Deteksi IP Server
    const serverIp = request.request.socket.localAddress;

    // Logging untuk debugging
    // console.log('Client IP:', clientIp);
    // console.log('Server IP:', serverIp);

    // Validasi: Batasi akses jika bukan dari IP lokal
    // const allowedIps = ['127.0.0.1', '::1']; // Tambahkan IP lain jika diperlukan
    // if (!allowedIps.includes(clientIp)) {
    //   return response.unauthorized({ message: 'Access denied. Localhost only.' });
    // }

    await next();
  }
}
