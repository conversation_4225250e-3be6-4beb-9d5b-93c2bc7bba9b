import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'

interface RateLimitEntry {
  count: number
  resetTime: number
  blocked: boolean
  blockUntil?: number
}

@inject()
export default class RateLimitingMiddleware {
  private readonly rateLimitStore = new Map<string, RateLimitEntry>()
  
  // Rate limit configurations
  private readonly configs = {
    // Authentication endpoints - stricter limits
    auth: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5,
      blockDurationMs: 30 * 60 * 1000, // 30 minutes block
    },
    // API endpoints - moderate limits
    api: {
      windowMs: 1 * 60 * 1000, // 1 minute
      maxRequests: 100,
      blockDurationMs: 5 * 60 * 1000, // 5 minutes block
    },
    // General endpoints - lenient limits
    general: {
      windowMs: 1 * 60 * 1000, // 1 minute
      maxRequests: 200,
      blockDurationMs: 2 * 60 * 1000, // 2 minutes block
    },
    // File upload endpoints - very strict
    upload: {
      windowMs: 5 * 60 * 1000, // 5 minutes
      maxRequests: 10,
      blockDurationMs: 15 * 60 * 1000, // 15 minutes block
    }
  }

  private getEndpointType(url: string, method: string): keyof typeof this.configs {
    // Authentication endpoints
    if (url.includes('/auth/') || url.includes('/login') || url.includes('/register') || 
        url.includes('/forgot') || url.includes('/reset')) {
      return 'auth'
    }
    
    // File upload endpoints
    if ((method === 'POST' || method === 'PUT') && 
        (url.includes('/upload') || url.includes('/file'))) {
      return 'upload'
    }
    
    // API endpoints
    if (url.includes('/api/') || url.includes('/be/')) {
      return 'api'
    }
    
    return 'general'
  }

  private getRateLimitKey(ip: string, endpointType: string): string {
    return `${ip}:${endpointType}`
  }

  private cleanupExpiredEntries(): void {
    const now = Date.now()
    for (const [key, entry] of this.rateLimitStore.entries()) {
      // Remove entries that are past their reset time and not blocked
      if (now > entry.resetTime && (!entry.blocked || (entry.blockUntil && now > entry.blockUntil))) {
        this.rateLimitStore.delete(key)
      }
    }
  }

  private logSecurityEvent(
    ip: string, 
    url: string, 
    method: string, 
    eventType: 'rate_limit_exceeded' | 'rate_limit_blocked',
    details: any
  ): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event_type: eventType,
      ip_address: ip,
      url: url,
      method: method,
      user_agent: details.userAgent || 'unknown',
      details: {
        current_count: details.currentCount,
        max_requests: details.maxRequests,
        window_ms: details.windowMs,
        block_duration_ms: details.blockDurationMs
      }
    }
    
    // Log to console (in production, this should go to a proper logging system)
    console.warn('[SECURITY] Rate Limit Event:', JSON.stringify(logEntry, null, 2))
  }

  async handle(ctx: HttpContext, next: () => Promise<void>) {
    const { request, response } = ctx
    const ip = request.ip()
    const url = request.url()
    const method = request.method()
    const userAgent = request.header('user-agent')
    
    // Clean up expired entries periodically
    if (Math.random() < 0.01) { // 1% chance to cleanup on each request
      this.cleanupExpiredEntries()
    }
    
    const endpointType = this.getEndpointType(url, method)
    const config = this.configs[endpointType]
    const key = this.getRateLimitKey(ip, endpointType)
    
    const now = Date.now()
    let entry = this.rateLimitStore.get(key)
    
    // Initialize entry if it doesn't exist
    if (!entry) {
      entry = {
        count: 0,
        resetTime: now + config.windowMs,
        blocked: false
      }
      this.rateLimitStore.set(key, entry)
    }
    
    // Check if currently blocked
    if (entry.blocked && entry.blockUntil && now < entry.blockUntil) {
      this.logSecurityEvent(ip, url, method, 'rate_limit_blocked', {
        userAgent,
        currentCount: entry.count,
        maxRequests: config.maxRequests,
        windowMs: config.windowMs,
        blockDurationMs: config.blockDurationMs
      })
      
      return response.status(429).send({
        error: 'Too Many Requests - IP temporarily blocked',
        status: 429,
        timestamp: new Date().toISOString(),
        retry_after: Math.ceil((entry.blockUntil - now) / 1000)
      })
    }
    
    // Reset counter if window has expired
    if (now > entry.resetTime) {
      entry.count = 0
      entry.resetTime = now + config.windowMs
      entry.blocked = false
      entry.blockUntil = undefined
    }
    
    // Increment counter
    entry.count++
    
    // Check if limit exceeded
    if (entry.count > config.maxRequests) {
      entry.blocked = true
      entry.blockUntil = now + config.blockDurationMs
      
      this.logSecurityEvent(ip, url, method, 'rate_limit_exceeded', {
        userAgent,
        currentCount: entry.count,
        maxRequests: config.maxRequests,
        windowMs: config.windowMs,
        blockDurationMs: config.blockDurationMs
      })
      
      return response.status(429).send({
        error: 'Too Many Requests',
        status: 429,
        timestamp: new Date().toISOString(),
        retry_after: Math.ceil(config.blockDurationMs / 1000)
      })
    }
    
    // Add rate limit headers
    response.header('X-RateLimit-Limit', config.maxRequests.toString())
    response.header('X-RateLimit-Remaining', (config.maxRequests - entry.count).toString())
    response.header('X-RateLimit-Reset', Math.ceil(entry.resetTime / 1000).toString())
    response.header('X-RateLimit-Window', Math.ceil(config.windowMs / 1000).toString())
    
    await next()
  }
} 