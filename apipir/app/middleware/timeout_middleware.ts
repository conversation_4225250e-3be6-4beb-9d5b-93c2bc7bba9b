import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'

export default class TimeoutMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    ctx.response.response.setTimeout(300000, () => { // 5 minutes = 300000ms
      ctx.response.status(408).json({
        success: false,
        message: 'Request Timeout - Proses melebihi 5 menit'
      });
    });
    await next();
  }
}