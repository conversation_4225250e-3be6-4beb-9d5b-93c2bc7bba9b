import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import fs from 'fs/promises'
import path from 'path'

interface SecurityEvent {
  timestamp: string
  event_type: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  ip_address: string
  user_agent: string
  url: string
  method: string
  headers: Record<string, string>
  details: any
  user_id?: number
  session_id?: string
}

@inject()
export default class SecurityLoggingMiddleware {
  private readonly logDir = 'logs/security'
  private readonly maxLogFileSize = 10 * 1024 * 1024 // 10MB
  
  constructor() {
    this.ensureLogDirectory()
  }

  private async ensureLogDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.logDir, { recursive: true })
    } catch (error) {
      console.error('Failed to create security log directory:', error)
    }
  }

  private async writeSecurityLog(event: SecurityEvent): Promise<void> {
    try {
      const logFileName = `security-${new Date().toISOString().split('T')[0]}.log`
      const logFilePath = path.join(this.logDir, logFileName)
      
      // Check file size and rotate if necessary
      try {
        const stats = await fs.stat(logFilePath)
        if (stats.size > this.maxLogFileSize) {
          const rotatedFileName = `security-${new Date().toISOString().split('T')[0]}-${Date.now()}.log`
          await fs.rename(logFilePath, path.join(this.logDir, rotatedFileName))
        }
      } catch (error) {
        // File doesn't exist, which is fine
      }
      
      const logEntry = JSON.stringify(event) + '\n'
      await fs.appendFile(logFilePath, logEntry)
      
      // Also log to console for immediate visibility
      if (event.severity === 'high' || event.severity === 'critical') {
        console.warn(`[SECURITY ${event.severity.toUpperCase()}]`, JSON.stringify(event, null, 2))
      }
    } catch (error) {
      console.error('Failed to write security log:', error)
    }
  }

  private detectSuspiciousPatterns(request: any): { detected: boolean; patterns: string[] } {
    const suspiciousPatterns = []
    const url = request.url()
    const userAgent = request.header('user-agent') || ''
    const headers = request.headers()
    
    // SQL Injection patterns
    const sqlPatterns = [
      /(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)/i,
      /(\bdrop\b.*\btable\b)|(\btable\b.*\bdrop\b)/i,
      /(\binsert\b.*\binto\b)|(\binto\b.*\binsert\b)/i,
      /(\bdelete\b.*\bfrom\b)|(\bfrom\b.*\bdelete\b)/i,
      /(\bupdate\b.*\bset\b)|(\bset\b.*\bupdate\b)/i,
      /(\bor\b.*1\s*=\s*1)|(\band\b.*1\s*=\s*1)/i,
      /(\bor\b.*'.*'.*=.*'.*')|(\band\b.*'.*'.*=.*'.*')/i
    ]
    
    // XSS patterns
    const xssPatterns = [
      /<script[\s\S]*?>[\s\S]*?<\/script>/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /<iframe[\s\S]*?>/i,
      /eval\s*\(/i,
      /expression\s*\(/i
    ]
    
    // Path traversal patterns
    const pathTraversalPatterns = [
      /\.\.\//,
      /\.\.\\/, 
      /%2e%2e%2f/i,
      /%2e%2e%5c/i,
      /\.\.%2f/i,
      /\.\.%5c/i
    ]
    
    // Command injection patterns
    const commandInjectionPatterns = [
      /;\s*(ls|cat|pwd|whoami|id|uname)/i,
      /\|\s*(ls|cat|pwd|whoami|id|uname)/i,
      /`.*`/,
      /\$\(.*\)/,
      /&&\s*(ls|cat|pwd|whoami|id|uname)/i
    ]
    
    // Check URL and query parameters
    const fullUrl = url + (request.qs() ? '?' + new URLSearchParams(request.qs()).toString() : '')
    
    if (sqlPatterns.some(pattern => pattern.test(fullUrl))) {
      suspiciousPatterns.push('sql_injection_attempt')
    }
    
    if (xssPatterns.some(pattern => pattern.test(fullUrl))) {
      suspiciousPatterns.push('xss_attempt')
    }
    
    if (pathTraversalPatterns.some(pattern => pattern.test(fullUrl))) {
      suspiciousPatterns.push('path_traversal_attempt')
    }
    
    if (commandInjectionPatterns.some(pattern => pattern.test(fullUrl))) {
      suspiciousPatterns.push('command_injection_attempt')
    }
    
    // Check User-Agent for suspicious patterns
    const suspiciousUserAgents = [
      /sqlmap/i,
      /nikto/i,
      /nessus/i,
      /burp/i,
      /nmap/i,
      /masscan/i,
      /zap/i,
      /gobuster/i,
      /dirb/i,
      /dirbuster/i
    ]
    
    if (suspiciousUserAgents.some(pattern => pattern.test(userAgent))) {
      suspiciousPatterns.push('suspicious_user_agent')
    }
    
    // Check for suspicious headers
    const suspiciousHeaders = [
      'x-forwarded-for',
      'x-real-ip', 
      'x-originating-ip',
      'x-remote-ip',
      'x-remote-addr'
    ]
    
    for (const header of suspiciousHeaders) {
      if (headers[header] && headers[header].includes('127.0.0.1')) {
        suspiciousPatterns.push('localhost_spoofing_attempt')
      }
    }
    
    // Check for multiple suspicious headers (potential header injection)
    const headerCount = Object.keys(headers).length
    if (headerCount > 50) {
      suspiciousPatterns.push('excessive_headers')
    }
    
    return {
      detected: suspiciousPatterns.length > 0,
      patterns: suspiciousPatterns
    }
  }

  private getSeverity(patterns: string[]): 'low' | 'medium' | 'high' | 'critical' {
    const criticalPatterns = ['sql_injection_attempt', 'command_injection_attempt']
    const highPatterns = ['xss_attempt', 'path_traversal_attempt']
    const mediumPatterns = ['suspicious_user_agent', 'localhost_spoofing_attempt']
    
    if (patterns.some(p => criticalPatterns.includes(p))) return 'critical'
    if (patterns.some(p => highPatterns.includes(p))) return 'high'
    if (patterns.some(p => mediumPatterns.includes(p))) return 'medium'
    return 'low'
  }

  async handle(ctx: HttpContext, next: () => Promise<void>) {
    const { request, response, auth } = ctx
    const startTime = Date.now()
    
    // Detect suspicious patterns
    const suspiciousActivity = this.detectSuspiciousPatterns(request)
    
    // Log suspicious activity immediately
    if (suspiciousActivity.detected) {
      const securityEvent: SecurityEvent = {
        timestamp: new Date().toISOString(),
        event_type: 'suspicious_request',
        severity: this.getSeverity(suspiciousActivity.patterns),
        ip_address: request.ip(),
        user_agent: request.header('user-agent') || 'unknown',
        url: request.url(),
        method: request.method(),
        headers: request.headers(),
        details: {
          detected_patterns: suspiciousActivity.patterns,
          query_params: request.qs(),
          body_size: request.header('content-length') || 0
        },
        user_id: auth.user?.id,
        session_id: request.header('x-session-id')
      }
      
      await this.writeSecurityLog(securityEvent)
    }
    
    // Continue with request processing
    try {
      await next()
      
      const responseTime = Date.now() - startTime
      
      // Log slow requests (potential DoS attempts)
      if (responseTime > 5000) { // 5 seconds
        const securityEvent: SecurityEvent = {
          timestamp: new Date().toISOString(),
          event_type: 'slow_request',
          severity: 'medium',
          ip_address: request.ip(),
          user_agent: request.header('user-agent') || 'unknown',
          url: request.url(),
          method: request.method(),
          headers: request.headers(),
          details: {
            response_time_ms: responseTime,
            status_code: response.getStatus()
          },
          user_id: auth.user?.id,
          session_id: request.header('x-session-id')
        }
        
        await this.writeSecurityLog(securityEvent)
      }
      
    } catch (error) {
      // Log errors that might indicate attacks
      const securityEvent: SecurityEvent = {
        timestamp: new Date().toISOString(),
        event_type: 'request_error',
        severity: 'medium',
        ip_address: request.ip(),
        user_agent: request.header('user-agent') || 'unknown',
        url: request.url(),
        method: request.method(),
        headers: request.headers(),
        details: {
          error_message: error.message,
          error_code: error.code,
          stack_trace: error.stack?.split('\n').slice(0, 5) // First 5 lines only
        },
        user_id: auth.user?.id,
        session_id: request.header('x-session-id')
      }
      
      await this.writeSecurityLog(securityEvent)
      throw error
    }
  }
} 