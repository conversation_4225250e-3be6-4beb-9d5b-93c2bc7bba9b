import type { HttpContext } from '@adonisjs/core/http'

/**
 * SQL Injection Protection Middleware
 * 
 * This middleware provides protection against SQL injection attacks by:
 * 1. Validating and sanitizing query parameters and URL paths
 * 2. Blocking requests with suspicious SQL patterns
 * 3. Logging potential attack attempts
 * 4. Special handling for common URI parameters like q=
 */

export default class SqlInjectionProtectionMiddleware {
  // SQL injection patterns to detect in query parameters
  private static readonly SQL_INJECTION_PATTERNS = [
    /'\s*or\s*'1'\s*=\s*'1/i,       // ' or '1'='1
    /'\s*or\s*1\s*=\s*1/i,          // ' or 1=1
    /'\s*or\s*'\w+'\s*=\s*'\w+/i,    // ' or 'a'='a
    /'\s*or\s*\d+\s*=\s*\d+/i,       // ' or 1=1
    /'\s*and\s*\d+\s*=\s*\d+/i,      // ' and 1=1
    /'\s*and\s*'\w+'\s*=\s*'\w+/i,   // ' and 'a'='a
    /'\s*;\s*(?:SELECT|DELETE|UPDATE|INSERT|DROP|ALTER)/i, // SQL commands
    /UNION\s+(?:ALL\s+)?SELECT/i,    // UNION SELECT attacks
    /--\s+/i,                        // SQL comments
    /\/\*[\w\s=]+\*\//i,             // C-style comments
    /xp_cmdshell/i,                  // xp_cmdshell
    /exec(\s|\+)+(s|x)p\w+/i,        // exec sp / exec xp
    /declare\s+@\w+/i,               // variable declaration
    /WAITFOR\s+DELAY/i,              // time-based attacks
    /CONVERT\s*\(/i,                 // CONVERT injection
    /CAST\s*\(/i,                    // CAST injection
    /;\s*SHUTDOWN/i,                 // Shutdown command
    /;\s*SLEEP/i,                    // Sleep command
  ]

  /**
   * Sanitize a single query parameter value
   */
  private static sanitizeValue(value: string): string {
    if (typeof value !== 'string') return value
    
    // Basic sanitization (remove dangerous characters)
    return value
      .replace(/'/g, "'")   // Replace single quotes with smart quotes
      .replace(/"/g, '"')   // Replace double quotes with smart quotes
      .replace(/;/g, '')    // Remove semicolons
      .replace(/--/g, '')   // Remove SQL comment markers
      .replace(/\/\*/g, '') // Remove C-style comment start
      .replace(/\*\//g, '') // Remove C-style comment end
      .replace(/\\/g, '')   // Remove backslashes
      .replace(/\s{2,}/g, ' ') // Normalize whitespace
  }

  /**
   * Sanitize object by recursively cleaning all string values
   */
  private static sanitizeObject(obj: any): any {
    if (obj === null || obj === undefined) return obj
    
    if (typeof obj === 'string') {
      return SqlInjectionProtectionMiddleware.sanitizeValue(obj)
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item))
    }
    
    if (typeof obj === 'object') {
      const sanitized = {}
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = this.sanitizeObject(value)
      }
      return sanitized
    }
    
    return obj
  }

  /**
   * Check if a value contains SQL injection patterns
   */
  private static hasSqlInjectionPattern(value: string): boolean {
    if (typeof value !== 'string') return false
    
    // Test for SQL injection patterns
    return SqlInjectionProtectionMiddleware.SQL_INJECTION_PATTERNS.some(pattern => pattern.test(value))
  }

  /**
   * Check object recursively for SQL injection patterns
   */
  private static checkObjectForSqlInjection(obj: any): boolean {
    if (obj === null || obj === undefined) return false
    
    if (typeof obj === 'string') {
      return SqlInjectionProtectionMiddleware.hasSqlInjectionPattern(obj)
    }
    
    if (Array.isArray(obj)) {
      return obj.some(item => this.checkObjectForSqlInjection(item))
    }
    
    if (typeof obj === 'object') {
      return Object.values(obj).some(value => this.checkObjectForSqlInjection(value))
    }
    
    return false
  }

  /**
   * Extract URI parameters from a URL path
   * Handles common patterns like /search/q=something or /path?q=something
   */
  private static extractUriParameters(url: string): Record<string, string> {
    const params: Record<string, string> = {}
    
    // Handle path segments that contain parameter patterns like /path/q=value
    const pathSegments = url.split('/')
    for (const segment of pathSegments) {
      // Look for key=value patterns in the path
      if (segment.includes('=')) {
        const [key, value] = segment.split('=', 2)
        if (key && value) {
          params[key] = value
        }
      }
    }
    
    return params
  }

  /**
   * Check if a URL path contains SQL injection patterns
   */
  public static checkPathForSqlInjection(url: string): boolean {
    // Check the raw URL for SQL injection patterns
    if (SqlInjectionProtectionMiddleware.hasSqlInjectionPattern(url)) {
      return true;
    }

    // Enhanced: Block encoded SQLi and suspicious operators in /be/ and /admin/ subpaths
    const decodedUrl = decodeURIComponent(url);
    // Block suspicious SQL operators and encoded patterns in path segments
    const sqliPathPatterns = [
      /\b(or|and)\b.*[=><]/i, // logical operators with comparison
      /\bselect\b.*\bfrom\b/i,
      /\bunion\b.*\bselect\b/i,
      /%20or%20/i, // encoded ' or '
      /%20and%20/i, // encoded ' and '
      /\d+\s*or\s*\d+=\d+/i, // 1 or 1=1
      /\d+%20or%20\d+%3d\d+/i, // encoded variant
      /\d+ or \d+=\d+/i,
      /\d+ and \d+=\d+/i,
      /\d+%20and%20\d+%3d\d+/i,
      /\bwhere\b.*[=><]/i,
      /\bupdate\b.*\bset\b/i,
      /\bdelete\b.*\bfrom\b/i,
      /\binsert\b.*\binto\b/i,
      /\bdrop\b.*\btable\b/i
    ];
    // Only apply for /be/ and /admin/ subpaths
    if (/^\/(be|admin)\//i.test(decodedUrl)) {
      const segments = decodedUrl.split('/').filter(Boolean);
      for (const seg of segments) {
        for (const pattern of sqliPathPatterns) {
          if (pattern.test(seg)) {
            return true;
          }
        }
      }
    }

    // Extract and check URI parameters that might be embedded in the path
    const uriParams = SqlInjectionProtectionMiddleware.extractUriParameters(url);
    return SqlInjectionProtectionMiddleware.checkObjectForSqlInjection(uriParams);
  }

  async handle(ctx: HttpContext, next: () => Promise<void>) {
    const { request, response } = ctx
    
    // Get query parameters and request body
    const queryParams = request.qs()
    const body = request.body()
    const url = request.url()
    
    // Check for SQL injection patterns in URL path
    if (SqlInjectionProtectionMiddleware.checkPathForSqlInjection(url)) {
      // Log potential attack attempt
      console.warn(`[SECURITY] Potential SQL injection detected in URL path: ${url}`)
      
      // Return 403 Forbidden
      return response.status(403).json({
        success: false,
        message: 'Request blocked due to security concerns',
        error: 'Potentially malicious input detected'
      })
    }
    
    // Check for SQL injection patterns in query params
    if (SqlInjectionProtectionMiddleware.checkObjectForSqlInjection(queryParams)) {
      // Log potential attack attempt
      console.warn(`[SECURITY] Potential SQL injection detected in query params: ${JSON.stringify(queryParams)}`)
      
      // Return 403 Forbidden
      return response.status(403).json({
        success: false,
        message: 'Request blocked due to security concerns',
        error: 'Potentially malicious input detected'
      })
    }
    
    // Check for SQL injection patterns in request body
    if (SqlInjectionProtectionMiddleware.checkObjectForSqlInjection(body)) {
      // Log potential attack attempt
      console.warn(`[SECURITY] Potential SQL injection detected in request body: ${JSON.stringify(body)}`)
      
      // Return 403 Forbidden
      return response.status(403).json({
        success: false,
        message: 'Request blocked due to security concerns',
        error: 'Potentially malicious input detected'
      })
    }
    
    // Sanitize URL path parameters
    const uriParams = SqlInjectionProtectionMiddleware.extractUriParameters(url)
    const sanitizedUriParams = SqlInjectionProtectionMiddleware.sanitizeObject(uriParams)
    
    // Sanitize query parameters
    request.updateQs(SqlInjectionProtectionMiddleware.sanitizeObject(queryParams))
    
    // Continue to next middleware
    await next()
  }
}
