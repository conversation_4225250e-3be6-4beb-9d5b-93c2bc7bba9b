import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import type { Authenticators } from '@adonisjs/auth/types'
import { Authenticator } from '@adonisjs/auth'
import { log } from 'console'
import { PrismaClient } from '@prisma/client'
const prisma = new PrismaClient()


export default class CheckRoleMiddleware {
 public async handle(ctx: HttpContext, next: () => Promise<void>, allowedRoles: string[]) {
    
    const user = await ctx.auth.check()
    const userr = await ctx.auth.user!
    if (user) {
      const userId = userr.currentAccessToken.identifier
      const now = new Date();
      const futureDate = new Date(now);
      futureDate.setMinutes(now.getMinutes() + 30);
      const newDate = futureDate.toISOString();
      const updatePost = await prisma.auth_access_tokens.update({
        where: {
          id: userId,
        },
        data: {
          expires_at: newDate
        },
      })
      const roleId = ctx.auth.user?.$attributes.role_id
      const userRole = await prisma.role.findFirst({
                             where: {
                                 id: roleId,
                             }
                         })
      const role = userRole?.role_name

      if (allowedRoles==undefined) {
        return next()
      }
      if (allowedRoles.length==0 ) {
        return next()
      }
      if (allowedRoles.includes(role) ) {
        return next()
      }
      return ctx.response.status(401).json({ message: 'Role Unauthorized ' });
    }else{
      return ctx.response.status(401).json({ message: 'User Unauthorized' });
    }
    


  }
}