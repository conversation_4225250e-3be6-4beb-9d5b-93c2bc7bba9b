import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'

export default class CsrfMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    // Pastikan route ada sebelum validasi CSRF
    if (!ctx.route) {
      const url = ctx.request.url()
      const method = ctx.request.method()
      
      // Set default route properties
      Object.defineProperty(ctx, 'route', {
        value: {
          pattern: url,
          name: `${method} ${url}`,
          middleware: [],
        },
        writable: true,
        configurable: true
      })
    }

    await next()
  }
}
