import path from "node:path";
import url from "node:url";

// export default {
//   path: path.dirname(url.fileURLToPath(import.meta.url)) + "/../", // for AdonisJS v6
//   title: "Foo", // use info instead
//   version: "1.0.0", // use info instead
//   description: "", // use info instead
//   tagIndex: 2,
//   info: {
//     title: "Docs API",
//     version: "1.0.0",
//     description: "",
//   },
//   snakeCase: true,

//   debug: true, // set to true, to get some useful debug output
//   ignore: ["/swagger", "/docs"],
//   preferredPutPatch: "PUT", // if PUT/PATCH are provided for the same route, prefer PUT
//   common: {
//     parameters: {}, // OpenAPI conform parameters that are commonly used
//     headers: {}, // OpenAPI conform headers that are commonly used
//   },
//   securitySchemes: {}, // optional
//   authMiddlewares: ["auth", "auth:api"], // optional
//   defaultSecurityScheme: "BearerAuth", // optional
//   persistAuthorization: true, // persist authorization between reloads on the swagger page
//   showFullPath: true, // the path displayed after endpoint summary
// };

export default {
  path: path.dirname(url.fileURLToPath(import.meta.url)) + "/../", // for AdonisJS v6
  title: "Api PIR BKPM",
  version: "1.0.0",
  tagIndex: 1, 
  defaultSecurityScheme: "BearerAuth", // optional
  ignore: ["/swagger", "/docs", "/v1", "/", "/something/*", "*/something","/api"],
  common: {
    parameters: {
      sortable: [
        {
          in: "query",
          name: "sortBy",
          schema: { type: "string", example: "foo" },
        },
        {
          in: "query",
          name: "sortType",
          schema: { type: "string", example: "ASC" },
        },
      ],
    },
    headers: {
      paginated: {
        "X-Total-Pages": {
          description: "Total amount of pages",
          schema: { type: "integer", example: 5 },
        },
        "X-Total": {
          description: "Total amount of results",
          schema: { type: "integer", example: 100 },
        },
        "X-Per-Page": {
          description: "Results per page",
          schema: { type: "integer", example: 20 },
        },
      },
    },
  },
  swaggerUIOptions: {
    docExpansion: "none", // Collapse all tag groups by default
  },
};