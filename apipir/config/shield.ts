import { defineConfig } from '@adonisjs/shield'

const shieldConfig = defineConfig({
  /**
   * Configure CSP policies for your app. Refer documentation
   * to learn more
   */
  csp: {
    enabled: true, // Enable Content Security Policy
    directives: {
      defaultSrc: ["'self'"], // Only allow sources from same domain
      scriptSrc: ["'self'", "'nonce-{{cspNonce}}'",'https://cdn.jsdelivr.net'], // Allow scripts from same domain and with nonce
      styleSrc: ["'self'", "'nonce-{{cspNonce}}'",'https://cdn.jsdelivr.net'],  // Allow stylesheets from same domain and with nonce
      imgSrc: ["'self'", 'data:' ,'https://cdn.jsdelivr.net'],              // Allow images from same domain and data URI
      objectSrc: ["'none'"],                    // Block all objects (like Flash)
      connectSrc: ["'self'"],                   // Restrict API connections to same origin
      frameSrc: ["'none'"],                     // Prevent iframe embedding
      formAction: ["'self'"],                   // Restrict form submissions to same origin
    },
    reportOnly: false,
  },

  /**
   * Configure CSRF protection options. Refer documentation
   * to learn more
   */
  csrf: {
    enabled: true,
    methods: ['POST', 'PUT', 'PATCH', 'DELETE'],
    exceptRoutes: [
      '/be/login',
      '/be/register',
      '/login',
      '/csrf',
    ]
  },

  /**
   * Control how your website should be embedded inside
   * iFrames
   */
  xFrame: {
    enabled: true,
    action: 'DENY',
  },

  /**
   * Force browser to always use HTTPS
   */
  hsts: {
    enabled: true,
    maxAge: '180 days',
    includeSubDomains: true,
    preload: true,
  },

  /**
   * Disable browsers from sniffing the content type of a
   * response and always rely on the "content-type" header.
   */
  contentTypeSniffing: {
    enabled: true,
  },
})

export default shieldConfig
