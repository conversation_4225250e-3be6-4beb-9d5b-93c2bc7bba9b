import { defineConfig } from '@adonisjs/shield'

const shieldConfig = defineConfig({
  /**
   * Configure CSP policies for your app. Refer documentation
   * to learn more
   */
  csp: {
    enabled: true, // Enable Content Security Policy
    directives: {
      defaultSrc: ["'self'"], // Only allow sources from same domain
      scriptSrc: ["'self'", "'nonce-{{cspNonce}}'",'https://cdn.jsdelivr.net'], // Allow scripts from same domain and with nonce
      styleSrc: ["'self'", "'nonce-{{cspNonce}}'",'https://cdn.jsdelivr.net'],  // Allow stylesheets from same domain and with nonce
      imgSrc: ["'self'", 'data:' ,'https://cdn.jsdelivr.net'],              // Allow images from same domain and data URI
      objectSrc: ["'none'"],                    // Block all objects (like Flash)
      connectSrc: ["'self'"],                   // Restrict API connections to same origin
      frameSrc: ["'none'"],                     // Prevent iframe embedding
      formAction: ["'self'"],                   // Restrict form submissions to same origin
    },
    reportOnly: false,
  },

  /**
   * Configure CSRF protection options. Refer documentation
   * to learn more
   */
  csrf: {
    enabled: true,
    exceptRoutes: ['/login', '/register','/be/login','/be/register'], // Exclude authentication endpoints
    enableXsrfCookie: true,
    methods: ['GET','POST', 'PUT', 'PATCH', 'DELETE'],
  },

  /**
   * Control how your website should be embedded inside
   * iFrames
   */
  xFrame: {
    enabled: true,
    action: 'DENY',
  },

  /**
   * Force browser to always use HTTPS
   */
  hsts: {
    enabled: true,
    maxAge: '180 days',
  },

  /**
   * Disable browsers from sniffing the content type of a
   * response and always rely on the "content-type" header.
   */
  contentTypeSniffing: {
    enabled: true,
  },
})

export default shieldConfig