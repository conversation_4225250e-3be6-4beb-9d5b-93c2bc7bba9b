import { defineConfig } from '@adonisjs/auth'
import { tokensGuard, tokensUserProvider } from '@adonisjs/auth/access_tokens'
import type { InferAuthEvents, Authenticators } from '@adonisjs/auth/types'

const authConfig = defineConfig({
  default: 'api',
  guards: {
    api: tokensGuard({
      provider: tokensUserProvider({
        tokens: 'accessTokens',
        model: () => import('#models/user')
      }),
    }),
  },
})

// import { defineConfig } from '@adonisjs/auth'
// import { InferAuthEvents, Authenticators } from '@adonisjs/auth/types'
// import { sessionGuard, sessionUserProvider } from '@adonisjs/auth/session'
// import { jwtGuard } from '@maximemrf/adonisjs-jwt/jwt_config'

// const authConfig = defineConfig({
//   // define the default authenticator to jwt
//   default: 'jwt',
//   guards: {
//     web: sessionGuard({
//       useRememberMeTokens: false,
//       provider: sessionUserProvider({
//         model: () => import('#models/user'),
//       }),
//     }),
//     // add the jwt guard
//     jwt: jwtGuard({
//       // tokenExpiresIn can be a string or a number, it can be optional
//       tokenExpiresIn: '1h',
//       // if you want to use cookies for the authentication instead of the bearer token (optional)
//       useCookies: true,
//       provider: sessionUserProvider({
//         model: () => import('#models/user'),
//       }),
//     }),
//   },
// })

export default authConfig

/**
 * Inferring types from the configured auth
 * guards.
 */
declare module '@adonisjs/auth/types' {
  interface Authenticators extends InferAuthenticators<typeof authConfig> {}
}
declare module '@adonisjs/core/types' {
  interface EventsList extends InferAuthEvents<Authenticators> {}
}