{
  "extends": "@adonisjs/tsconfig/tsconfig.app.json",
    "compilerOptions": {
      "module": "ESNext",
      "target": "ES2020",
      // "moduleResolution": "node",
      "moduleResolution": "bundler",
      "outDir": "build",
      "strict": false,
      "esModuleInterop": true,
      "skipLibCheck": true,
      "rootDir": "./",
      "baseUrl": "./",
      "paths": {
        "~/*": ["./*"],
        "@/*": ["./*"],
      },
      "noImplicitAny": false
  },
  "include": ["./**/*.ts"],
  "exclude": ["node_modules", "build"],
  
}
